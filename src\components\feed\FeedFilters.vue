<template>
  <q-card flat bordered class="filter-card">
    <q-card-section>
      <div class="text-h6 text-primary">Filters</div>
      <q-separator class="q-my-md" />

      <!-- Common Filters (Search & Date Range) -->
      <div class="common-filters">
        <!-- Search -->
        <div class="q-mb-md">
          <q-input
            v-model="filterStore.searchQuery"
            outlined
            dense
            placeholder="Search..."
            @update:model-value="applyFilters"
          >
            <template v-slot:append>
              <q-icon name="search" />
            </template>
          </q-input>
        </div>

        <!-- Date Range (Dropdown) -->
        <div class="q-mb-md">
          <div class="text-subtitle2 q-mb-sm">Date Range</div>
          <q-select
            v-model="filterStore.dateRange"
            :options="filterOptions.dateRangeOptions"
            outlined
            dense
            emit-value
            map-options
            option-value="value"
            option-label="label"
            @update:model-value="applyFilters"
            class="date-range-select"
          >
            <template v-slot:prepend>
              <q-icon name="event" color="primary" />
            </template>

            <template v-slot:append>
              <q-icon
                name="close"
                class="cursor-pointer"
                @click.stop="clearDateRange"
                v-if="filterStore.dateRange !== 'all'"
              />
            </template>

            <template v-slot:option="{ opt, selected }">
              <q-item :active="selected">
                <q-item-section avatar>
                  <q-radio :model-value="selected" :val="true" />
                </q-item-section>
                <q-item-section avatar>
                  <q-icon :name="opt.icon || 'filter_list'" color="primary" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ opt.label }}</q-item-label>
                </q-item-section>
              </q-item>
            </template>
          </q-select>
        </div>

        <!-- Selected Filters Summary -->
        <div v-if="hasActiveFilters" class="selected-filters q-mb-md">
          <div class="text-subtitle2 q-mb-sm">Selected Filters</div>
          <q-card flat bordered class="filter-summary">
            <q-card-section class="q-pa-sm">
              <div v-if="searchQuery" class="q-mb-xs">
                <q-chip dense color="primary" text-color="white" removable @remove="clearSearchQuery">
                  <q-icon name="search" size="xs" class="q-mr-xs" />
                  <span>{{ searchQuery }}</span>
                </q-chip>
              </div>

              <div v-if="filterStore.dateRange !== 'all'" class="q-mb-xs">
                <q-chip dense color="primary" text-color="white" removable @remove="clearDateRange">
                  <q-icon :name="dateRange.icon || 'event'" size="xs" class="q-mr-xs" />
                  <span>{{ dateRange.label }}</span>
                </q-chip>
              </div>

              <div v-if="activeTabFilters.length > 0" class="filter-chips">
                <q-chip
                  v-for="filter in activeTabFilters"
                  :key="filter.id"
                  dense
                  color="primary"
                  text-color="white"
                  removable
                  @remove="removeFilter(filter)"
                >
                  <q-icon :name="filter.icon" size="xs" class="q-mr-xs" />
                  <span>{{ filter.label }}</span>
                </q-chip>
              </div>

              <div v-if="activeTabFilters.length === 0 && !searchQuery && filterStore.dateRange === 'all'" class="text-grey text-center">
                No filters applied
              </div>
            </q-card-section>
          </q-card>
        </div>
      </div>

      <!-- Dynamic Filters based on active tab -->
      <div class="dynamic-filters">
        <!-- Feed Tab Filters -->
        <template v-if="activeTab === 'feed'">
          <!-- Post Type Filter -->
          <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-sm">Post Type</div>
            <q-select
              v-model="localPostTypes"
              :options="filterOptions.postTypeOptions"
              option-value="value"
              option-label="label"
              multiple
              dense
              outlined
              emit-value
              map-options
              use-chips
              @update:model-value="handlePostTypeChange"
            >
              <template v-slot:option="{ opt, selected }">
                <q-item v-bind="opt" :active="selected">
                  <q-item-section avatar>
                    <q-checkbox :model-value="selected" />
                  </q-item-section>
                  <q-item-section avatar>
                    <q-icon :name="opt.icon" color="primary" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ opt.label }}</q-item-label>
                  </q-item-section>
                </q-item>
              </template>
              <template v-slot:selected-item="{ opt, removeAtIndex }">
                <q-chip
                  dense
                  color="primary"
                  text-color="white"
                  removable
                  @remove="removeAtIndex(opt)"
                >
                  <q-icon :name="opt.icon" size="xs" class="q-mr-xs" />
                  <span>{{ opt.label }}</span>
                </q-chip>
              </template>
            </q-select>
          </div>

          <!-- Categories -->
          <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-sm">Categories</div>
            <q-select
              v-model="localCategories"
              :options="filterOptions.categoryOptions"
              option-value="value"
              option-label="label"
              multiple
              dense
              outlined
              emit-value
              map-options
              use-chips
              @update:model-value="handleCategoryChange"
            >
              <template v-slot:option="{ opt, selected }">
                <q-item v-bind="opt" :active="selected">
                  <q-item-section avatar>
                    <q-checkbox :model-value="selected" />
                  </q-item-section>
                  <q-item-section avatar>
                    <q-icon :name="opt.icon" color="primary" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ opt.label }}</q-item-label>
                  </q-item-section>
                </q-item>
              </template>
              <template v-slot:selected-item="{ opt, removeAtIndex }">
                <q-chip
                  dense
                  color="primary"
                  text-color="white"
                  removable
                  @remove="removeAtIndex(opt)"
                >
                  <q-icon :name="opt.icon" size="xs" class="q-mr-xs" />
                  <span>{{ opt.label }}</span>
                </q-chip>
              </template>
            </q-select>
          </div>

          <!-- Opportunity Type (only shown when opportunity posts are selected) -->
          <div v-if="showOpportunityFilters" class="q-mb-md">
            <div class="text-subtitle2 q-mb-sm">Opportunity Type</div>
            <q-select
              v-model="localOpportunityTypes"
              :options="filterOptions.opportunityTypeOptions"
              option-value="value"
              option-label="label"
              multiple
              dense
              outlined
              emit-value
              map-options
              use-chips
              @update:model-value="handleOpportunityTypeChange"
            >
              <template v-slot:option="{ opt, selected }">
                <q-item v-bind="opt" :active="selected">
                  <q-item-section avatar>
                    <q-checkbox :model-value="selected" />
                  </q-item-section>
                  <q-item-section avatar>
                    <q-icon :name="opt.icon" color="primary" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ opt.label }}</q-item-label>
                  </q-item-section>
                </q-item>
              </template>
              <template v-slot:selected-item="{ opt, removeAtIndex }">
                <q-chip
                  dense
                  color="primary"
                  text-color="white"
                  removable
                  @remove="removeAtIndex(opt)"
                >
                  <q-icon :name="opt.icon" size="xs" class="q-mr-xs" />
                  <span>{{ opt.label }}</span>
                </q-chip>
              </template>
            </q-select>
          </div>
        </template>

        <!-- Profile Tab Filters -->
        <template v-else-if="activeTab === 'profiles'">
          <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-sm">Profile Types</div>
            <q-select
              v-model="localProfileTypes"
              :options="filterOptions.profileTypeOptions"
              option-value="value"
              option-label="label"
              multiple
              dense
              outlined
              emit-value
              map-options
              use-chips
              @update:model-value="handleProfileTypeChange"
            >
              <template v-slot:option="{ opt, selected }">
                <q-item v-bind="opt" :active="selected">
                  <q-item-section avatar>
                    <q-checkbox :model-value="selected" />
                  </q-item-section>
                  <q-item-section avatar>
                    <q-icon :name="opt.icon" color="primary" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ opt.label }}</q-item-label>
                  </q-item-section>
                </q-item>
              </template>
              <template v-slot:selected-item="{ opt, removeAtIndex }">
                <q-chip
                  dense
                  color="primary"
                  text-color="white"
                  removable
                  @remove="removeAtIndex(opt)"
                >
                  <q-icon :name="opt.icon" size="xs" class="q-mr-xs" />
                  <span>{{ opt.label }}</span>
                </q-chip>
              </template>
            </q-select>
          </div>

          <!-- Expertise Areas - Hidden for now -->
          <!-- <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-sm">Expertise Areas</div>
            <q-select
              v-model="localExpertiseAreas"
              :options="filterOptions.expertiseAreaOptions"
              option-value="value"
              option-label="label"
              multiple
              dense
              outlined
              emit-value
              map-options
              use-chips
              @update:model-value="handleExpertiseAreaChange"
            >
              <template v-slot:option="{ opt, selected }">
                <q-item v-bind="opt" :active="selected">
                  <q-item-section avatar>
                    <q-checkbox :model-value="selected" />
                  </q-item-section>
                  <q-item-section avatar>
                    <q-icon :name="opt.icon" color="primary" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ opt.label }}</q-item-label>
                  </q-item-section>
                </q-item>
              </template>
              <template v-slot:selected-item="{ opt, removeAtIndex }">
                <q-chip
                  dense
                  color="primary"
                  text-color="white"
                  removable
                  @remove="removeAtIndex(opt)"
                >
                  <q-icon :name="opt.icon" size="xs" class="q-mr-xs" />
                  <span>{{ opt.label }}</span>
                </q-chip>
              </template>
            </q-select>
          </div> -->
        </template>

        <!-- Blog Tab Filters -->
        <template v-else-if="activeTab === 'blog'">
          <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-sm">Blog Categories</div>
            <q-select
              v-model="localBlogCategories"
              :options="filterOptions.blogCategoryOptions"
              option-value="value"
              option-label="label"
              multiple
              dense
              outlined
              emit-value
              map-options
              use-chips
              @update:model-value="handleBlogCategoryChange"
            >
              <template v-slot:option="{ opt, selected }">
                <q-item v-bind="opt" :active="selected">
                  <q-item-section avatar>
                    <q-checkbox :model-value="selected" />
                  </q-item-section>
                  <q-item-section avatar>
                    <q-icon :name="opt.icon" color="primary" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ opt.label }}</q-item-label>
                  </q-item-section>
                </q-item>
              </template>
              <template v-slot:selected-item="{ opt, removeAtIndex }">
                <q-chip
                  dense
                  color="primary"
                  text-color="white"
                  removable
                  @remove="removeAtIndex(opt)"
                >
                  <q-icon :name="opt.icon" size="xs" class="q-mr-xs" />
                  <span>{{ opt.label }}</span>
                </q-chip>
              </template>
            </q-select>
          </div>

          <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-sm">Read Time</div>
            <q-select
              v-model="localReadTime"
              :options="filterOptions.readTimeOptions"
              option-value="value"
              option-label="label"
              dense
              outlined
              emit-value
              map-options
              @update:model-value="handleReadTimeChange"
            >
              <template v-slot:option="{ opt, selected }">
                <q-item v-bind="opt" :active="selected">
                  <q-item-section avatar>
                    <q-radio :model-value="selected" :val="opt.value" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ opt.label }}</q-item-label>
                  </q-item-section>
                </q-item>
              </template>
            </q-select>
          </div>
        </template>

        <!-- Events Tab Filters -->
        <template v-else-if="activeTab === 'events'">
          <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-sm">Event Types</div>
            <q-select
              v-model="localEventTypes"
              :options="filterOptions.eventTypeOptions"
              option-value="value"
              option-label="label"
              multiple
              dense
              outlined
              emit-value
              map-options
              use-chips
              @update:model-value="handleEventTypeChange"
            >
              <template v-slot:option="{ opt, selected }">
                <q-item v-bind="opt" :active="selected">
                  <q-item-section avatar>
                    <q-checkbox :model-value="selected" />
                  </q-item-section>
                  <q-item-section avatar>
                    <q-icon :name="opt.icon" color="primary" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ opt.label }}</q-item-label>
                  </q-item-section>
                </q-item>
              </template>
              <template v-slot:selected-item="{ opt, removeAtIndex }">
                <q-chip
                  dense
                  color="primary"
                  text-color="white"
                  removable
                  @remove="removeAtIndex(opt)"
                >
                  <q-icon :name="opt.icon" size="xs" class="q-mr-xs" />
                  <span>{{ opt.label }}</span>
                </q-chip>
              </template>
            </q-select>
          </div>

          <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-sm">Event Format</div>
            <q-select
              v-model="localEventFormat"
              :options="filterOptions.eventFormatOptions"
              option-value="value"
              option-label="label"
              multiple
              dense
              outlined
              emit-value
              map-options
              use-chips
              @update:model-value="handleEventFormatChange"
            >
              <template v-slot:option="{ opt, selected }">
                <q-item v-bind="opt" :active="selected">
                  <q-item-section avatar>
                    <q-checkbox :model-value="selected" />
                  </q-item-section>
                  <q-item-section avatar>
                    <q-icon :name="opt.icon" color="primary" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ opt.label }}</q-item-label>
                  </q-item-section>
                </q-item>
              </template>
              <template v-slot:selected-item="{ opt, removeAtIndex }">
                <q-chip
                  dense
                  color="primary"
                  text-color="white"
                  removable
                  @remove="removeAtIndex(opt)"
                >
                  <q-icon :name="opt.icon" size="xs" class="q-mr-xs" />
                  <span>{{ opt.label }}</span>
                </q-chip>
              </template>
            </q-select>
          </div>
        </template>

        <!-- Groups Tab Filters -->
        <template v-else-if="activeTab === 'groups'">
          <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-sm">Group Categories</div>
            <q-select
              v-model="localGroupCategories"
              :options="filterOptions.groupCategoryOptions"
              option-value="value"
              option-label="label"
              multiple
              dense
              outlined
              emit-value
              map-options
              use-chips
              @update:model-value="handleGroupCategoryChange"
            >
              <template v-slot:option="{ opt, selected }">
                <q-item v-bind="opt" :active="selected">
                  <q-item-section avatar>
                    <q-checkbox :model-value="selected" />
                  </q-item-section>
                  <q-item-section avatar>
                    <q-icon :name="opt.icon" color="primary" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ opt.label }}</q-item-label>
                  </q-item-section>
                </q-item>
              </template>
              <template v-slot:selected-item="{ opt, removeAtIndex }">
                <q-chip
                  dense
                  color="primary"
                  text-color="white"
                  removable
                  @remove="removeAtIndex(opt)"
                >
                  <q-icon :name="opt.icon" size="xs" class="q-mr-xs" />
                  <span>{{ opt.label }}</span>
                </q-chip>
              </template>
            </q-select>
          </div>
        </template>

        <!-- Marketplace Tab Filters -->
        <template v-else-if="activeTab === 'marketplace'">
          <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-sm">Listing Types</div>
            <q-select
              v-model="localListingTypes"
              :options="filterOptions.listingTypeOptions"
              option-value="value"
              option-label="label"
              multiple
              dense
              outlined
              emit-value
              map-options
              use-chips
              @update:model-value="handleListingTypeChange"
            >
              <template v-slot:option="{ opt, selected }">
                <q-item v-bind="opt" :active="selected">
                  <q-item-section avatar>
                    <q-checkbox :model-value="selected" />
                  </q-item-section>
                  <q-item-section avatar>
                    <q-icon :name="opt.icon" color="primary" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ opt.label }}</q-item-label>
                  </q-item-section>
                </q-item>
              </template>
              <template v-slot:selected-item="{ opt, removeAtIndex }">
                <q-chip
                  dense
                  color="primary"
                  text-color="white"
                  removable
                  @remove="removeAtIndex(opt)"
                >
                  <q-icon :name="opt.icon" size="xs" class="q-mr-xs" />
                  <span>{{ opt.label }}</span>
                </q-chip>
              </template>
            </q-select>
          </div>
        </template>
      </div>

      <!-- Reset Filters -->
      <div class="q-mt-lg">
        <q-btn
          outline
          color="grey"
          label="Reset Filters"
          class="full-width q-mb-md"
          @click="resetAndApplyFilters"
        />
        <q-btn
          color="primary"
          label="Apply Filters"
          class="full-width"
          @click="applyFilters"
        />
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, watch, computed, ref } from 'vue';
import { useFilterStore } from '../../stores/filterStore';
import { filterOptions } from '../../services/filterOptionsService';

const props = defineProps({
  activeTab: {
    type: String,
    default: 'feed'
  }
});

const emit = defineEmits(['filter-changed']);

// Use the filter store
const filterStore = useFilterStore();

// Local state variables for v-model binding
const localPostTypes = ref([]);
const localCategories = ref([]);
const localOpportunityTypes = ref([]);

// Profile tab local state
const localProfileTypes = ref([]);

// Blog tab local state
const localBlogCategories = ref([]);
const localReadTime = ref(null);

// Event tab local state
const localEventTypes = ref([]);
const localEventFormat = ref([]);

// Group tab local state
const localGroupCategories = ref([]);

// Marketplace tab local state
const localListingTypes = ref([]);

// Set the active tab in the store when the component receives it as a prop
watch(() => props.activeTab, (newTab) => {
  filterStore.setActiveTab(newTab);
}, { immediate: true });

// Computed properties to access store state
const activeTab = computed(() => filterStore.activeTab);
const searchQuery = computed({
  get: () => filterStore.searchQuery,
  set: (value) => filterStore.setSearchQuery(value)
});
const dateRange = computed({
  get: () => {
    // For the dropdown, we need to return the full option object
    const currentValue = filterStore.dateRange;
    return filterOptions.dateRangeOptions.find(option => option.value === currentValue) || filterOptions.dateRangeOptions[0];
  },
  set: (value) => {
    // Handle both string values and option objects
    const dateRangeValue = typeof value === 'string' ? value : value.value;
    filterStore.setDateRange(dateRangeValue);
  }
});
const feedFilters = computed(() => filterStore.feedFilters);
const profileFilters = computed(() => filterStore.profileFilters);
const blogFilters = computed(() => filterStore.blogFilters);
const eventFilters = computed(() => filterStore.eventFilters);
const groupFilters = computed(() => filterStore.groupFilters);
const marketplaceFilters = computed(() => filterStore.marketplaceFilters);

// Computed property to determine if opportunity filters should be shown
const showOpportunityFilters = computed(() => {
  return activeTab.value === 'feed' &&
    (feedFilters.value.postTypes.includes('opportunity') ||
     feedFilters.value.postTypes.length === 0);
});

// Computed property to check if there are any active filters
const hasActiveFilters = computed(() => {
  return searchQuery.value !== '' ||
         filterStore.dateRange !== 'all' ||
         activeTabFilters.value.length > 0;
});

// Computed property to get active filters for the current tab
const activeTabFilters = computed(() => {
  const filters = [];

  switch (activeTab.value) {
    case 'feed':
      // Add post type filters
      if (feedFilters.value.postTypes.length > 0) {
        feedFilters.value.postTypes.forEach(type => {
          const option = filterOptions.postTypeOptions.find(opt => opt.value === type);
          if (option) {
            filters.push({
              id: `post-type-${type}`,
              type: 'postType',
              value: type,
              label: option.label,
              icon: option.icon || 'label'
            });
          }
        });
      }

      // Add category filters
      if (feedFilters.value.categories.length > 0) {
        feedFilters.value.categories.forEach(category => {
          const option = filterOptions.categoryOptions.find(opt => opt.value === category);
          if (option) {
            filters.push({
              id: `category-${category}`,
              type: 'category',
              value: category,
              label: option.label,
              icon: option.icon || 'label'
            });
          }
        });
      }

      // Add opportunity type filters
      if (feedFilters.value.opportunityTypes.length > 0) {
        feedFilters.value.opportunityTypes.forEach(type => {
          const option = filterOptions.opportunityTypeOptions.find(opt => opt.value === type);
          if (option) {
            filters.push({
              id: `opportunity-type-${type}`,
              type: 'opportunityType',
              value: type,
              label: option.label,
              icon: option.icon || 'label'
            });
          }
        });
      }
      break;

    case 'profiles':
      // Add profile type filters
      if (profileFilters.value.profileTypes.length > 0) {
        profileFilters.value.profileTypes.forEach(type => {
          const option = filterOptions.profileTypeOptions.find(opt => opt.value === type);
          if (option) {
            filters.push({
              id: `profile-type-${type}`,
              type: 'profileType',
              value: type,
              label: option.label,
              icon: option.icon || 'person'
            });
          }
        });
      }


      break;

    case 'blog':
      // Add blog category filters
      if (blogFilters.value.blogCategories.length > 0) {
        blogFilters.value.blogCategories.forEach(category => {
          const option = filterOptions.blogCategoryOptions.find(opt => opt.value === category);
          if (option) {
            filters.push({
              id: `blog-category-${category}`,
              type: 'blogCategory',
              value: category,
              label: option.label,
              icon: option.icon || 'article'
            });
          }
        });
      }

      // Add read time filter
      if (blogFilters.value.readTime !== 'any') {
        const option = filterOptions.readTimeOptions.find(opt => opt.value === blogFilters.value.readTime);
        if (option) {
          filters.push({
            id: `read-time-${blogFilters.value.readTime}`,
            type: 'readTime',
            value: blogFilters.value.readTime,
            label: option.label,
            icon: 'schedule'
          });
        }
      }
      break;

    case 'events':
      // Add event type filters
      if (eventFilters.value.eventTypes.length > 0) {
        eventFilters.value.eventTypes.forEach(type => {
          const option = filterOptions.eventTypeOptions.find(opt => opt.value === type);
          if (option) {
            filters.push({
              id: `event-type-${type}`,
              type: 'eventType',
              value: type,
              label: option.label,
              icon: option.icon || 'event'
            });
          }
        });
      }

      // Add event format filters
      if (eventFilters.value.eventFormat.length > 0) {
        eventFilters.value.eventFormat.forEach(format => {
          const option = filterOptions.eventFormatOptions.find(opt => opt.value === format);
          if (option) {
            filters.push({
              id: `event-format-${format}`,
              type: 'eventFormat',
              value: format,
              label: option.label,
              icon: option.icon || 'event_note'
            });
          }
        });
      }
      break;

    case 'groups':
      // Add group category filters
      if (groupFilters.value.groupCategories.length > 0) {
        groupFilters.value.groupCategories.forEach(category => {
          const option = filterOptions.groupCategoryOptions.find(opt => opt.value === category);
          if (option) {
            filters.push({
              id: `group-category-${category}`,
              type: 'groupCategory',
              value: category,
              label: option.label,
              icon: option.icon || 'group'
            });
          }
        });
      }
      break;

    case 'marketplace':
      // Add listing type filters
      if (marketplaceFilters.value.listingTypes.length > 0) {
        marketplaceFilters.value.listingTypes.forEach(type => {
          const option = filterOptions.listingTypeOptions.find(opt => opt.value === type);
          if (option) {
            filters.push({
              id: `listing-type-${type}`,
              type: 'listingType',
              value: type,
              label: option.label,
              icon: option.icon || 'store'
            });
          }
        });
      }
      break;
  }

  return filters;
});

// Methods to update store state
function updateSearchQuery(value) {
  filterStore.setSearchQuery(value);
  // Emit filter changed event to update the results
  emit('filter-changed', filterStore.currentFilters);
}

function updateDateRange(value) {
  // Handle both string values and option objects
  const dateRangeValue = typeof value === 'string' ? value : value.value;
  filterStore.setDateRange(dateRangeValue);
  // Emit filter changed event to update the results
  emit('filter-changed', filterStore.currentFilters);
}

function clearDateRange() {
  // Reset date range to 'all'
  filterStore.setDateRange('all');
  // Emit filter changed event to update the results
  emit('filter-changed', filterStore.currentFilters);
}

function clearSearchQuery() {
  // Reset search query
  filterStore.setSearchQuery('');
  // Emit filter changed event to update the results
  emit('filter-changed', filterStore.currentFilters);
}

// Generic method to remove a filter from the selected filters
function removeFilter(filter) {
  switch (filter.type) {
    case 'postType':
      removePostType(filter.value);
      break;
    case 'category':
      removeCategory(filter.value);
      break;
    case 'opportunityType':
      removeOpportunityType(filter.value);
      break;
    case 'profileType':
      removeProfileType(filter.value);
      break;

    case 'blogCategory':
      removeBlogCategory(filter.value);
      break;
    case 'readTime':
      updateBlogFilters({ readTime: 'any' });
      break;
    case 'eventType':
      removeEventType(filter.value);
      break;
    case 'eventFormat':
      removeEventFormat(filter.value);
      break;
    case 'groupCategory':
      removeGroupCategory(filter.value);
      break;
    case 'listingType':
      removeListingType(filter.value);
      break;
  }

  // Emit filter changed event to update the results
  emit('filter-changed', filterStore.currentFilters);
}

function updateFeedFilters(filters) {
  filterStore.updateFeedFilters(filters);
  // Emit filter changed event to update the results
  emit('filter-changed', filterStore.currentFilters);
}

function updateProfileFilters(filters) {
  filterStore.updateProfileFilters(filters);
  // Emit filter changed event to update the results
  emit('filter-changed', filterStore.currentFilters);
}

function updateBlogFilters(filters) {
  filterStore.updateBlogFilters(filters);
  // Emit filter changed event to update the results
  emit('filter-changed', filterStore.currentFilters);
}

function updateEventFilters(filters) {
  filterStore.updateEventFilters(filters);
  // Emit filter changed event to update the results
  emit('filter-changed', filterStore.currentFilters);
}

function updateGroupFilters(filters) {
  filterStore.updateGroupFilters(filters);
  // Emit filter changed event to update the results
  emit('filter-changed', filterStore.currentFilters);
}

function updateMarketplaceFilters(filters) {
  filterStore.updateMarketplaceFilters(filters);
  // Emit filter changed event to update the results
  emit('filter-changed', filterStore.currentFilters);
}

// Helper methods for removing individual items from filter arrays
function removePostType(value) {
  const updatedPostTypes = feedFilters.value.postTypes.filter(type => type !== value);
  updateFeedFilters({ postTypes: updatedPostTypes });
}

function removeCategory(value) {
  const updatedCategories = feedFilters.value.categories.filter(cat => cat !== value);
  updateFeedFilters({ categories: updatedCategories });
}

function removeOpportunityType(value) {
  const updatedOpportunityTypes = feedFilters.value.opportunityTypes.filter(type => type !== value);
  updateFeedFilters({ opportunityTypes: updatedOpportunityTypes });
}

function removeProfileType(value) {
  const updatedProfileTypes = profileFilters.value.profileTypes.filter(type => type !== value);
  updateProfileFilters({ profileTypes: updatedProfileTypes });
}



function removeBlogCategory(value) {
  const updatedBlogCategories = blogFilters.value.blogCategories.filter(cat => cat !== value);
  updateBlogFilters({ blogCategories: updatedBlogCategories });
}

function removeEventType(value) {
  const updatedEventTypes = eventFilters.value.eventTypes.filter(type => type !== value);
  updateEventFilters({ eventTypes: updatedEventTypes });
}

function removeEventFormat(value) {
  const updatedEventFormat = eventFilters.value.eventFormat.filter(format => format !== value);
  updateEventFilters({ eventFormat: updatedEventFormat });
}

function removeGroupCategory(value) {
  const updatedGroupCategories = groupFilters.value.groupCategories.filter(cat => cat !== value);
  updateGroupFilters({ groupCategories: updatedGroupCategories });
}

function removeListingType(value) {
  const updatedListingTypes = marketplaceFilters.value.listingTypes.filter(type => type !== value);
  updateMarketplaceFilters({ listingTypes: updatedListingTypes });
}

function resetFilters() {
  filterStore.resetCurrentTabFilters();
  emit('filter-changed', filterStore.currentFilters);
}

function applyFilters() {
  console.log('Emitting filter-changed event with filters:', filterStore.currentFilters);
  emit('filter-changed', filterStore.currentFilters);
}

function resetAndApplyFilters() {
  filterStore.resetCurrentTabFilters();
  console.log('Filters reset, applying new filters:', filterStore.currentFilters);

  // Reset local state variables based on active tab
  switch (filterStore.activeTab) {
    case 'feed':
      localPostTypes.value = [];
      localCategories.value = [];
      localOpportunityTypes.value = [];
      break;
    case 'profiles':
      localProfileTypes.value = [];
      break;
    case 'blog':
      localBlogCategories.value = [];
      localReadTime.value = null;
      break;
    case 'events':
      localEventTypes.value = [];
      localEventFormat.value = [];
      break;
    case 'groups':
      localGroupCategories.value = [];
      break;
    case 'marketplace':
      localListingTypes.value = [];
      break;
  }

  emit('filter-changed', filterStore.currentFilters);
}

// Handler functions for local state changes
function handlePostTypeChange(value) {
  console.log('Post type changed:', value);
  filterStore.updateFeedFilters({ postTypes: value });
  applyFilters();
}

function handleCategoryChange(value) {
  console.log('Category changed:', value);
  filterStore.updateFeedFilters({ categories: value });
  applyFilters();
}

function handleOpportunityTypeChange(value) {
  console.log('Opportunity type changed:', value);
  filterStore.updateFeedFilters({ opportunityTypes: value });
  applyFilters();
}

// Handler functions for profile tab
function handleProfileTypeChange(value) {
  console.log('Profile type changed:', value);
  filterStore.updateProfileFilters({ profileTypes: value });
  applyFilters();
}



// Handler functions for blog tab
function handleBlogCategoryChange(value) {
  console.log('Blog category changed:', value);
  filterStore.updateBlogFilters({ blogCategories: value });
  applyFilters();
}

function handleReadTimeChange(value) {
  console.log('Read time changed:', value);
  filterStore.updateBlogFilters({ readTime: value });
  applyFilters();
}

// Handler functions for event tab
function handleEventTypeChange(value) {
  console.log('Event type changed:', value);
  filterStore.updateEventFilters({ eventTypes: value });
  applyFilters();
}

function handleEventFormatChange(value) {
  console.log('Event format changed:', value);
  filterStore.updateEventFilters({ eventFormat: value });
  applyFilters();
}

// Handler functions for group tab
function handleGroupCategoryChange(value) {
  console.log('Group category changed:', value);
  filterStore.updateGroupFilters({ groupCategories: value });
  applyFilters();
}

// Handler functions for marketplace tab
function handleListingTypeChange(value) {
  console.log('Listing type changed:', value);
  filterStore.updateMarketplaceFilters({ listingTypes: value });
  applyFilters();
}

// Initialize local state from store - Feed tab
watch(() => filterStore.feedFilters.postTypes, (newValue) => {
  localPostTypes.value = newValue;
}, { immediate: true });

watch(() => filterStore.feedFilters.categories, (newValue) => {
  localCategories.value = newValue;
}, { immediate: true });

watch(() => filterStore.feedFilters.opportunityTypes, (newValue) => {
  localOpportunityTypes.value = newValue;
}, { immediate: true });

// Initialize local state from store - Profile tab
watch(() => filterStore.profileFilters.profileTypes, (newValue) => {
  localProfileTypes.value = newValue;
}, { immediate: true });



// Initialize local state from store - Blog tab
watch(() => filterStore.blogFilters.blogCategories, (newValue) => {
  localBlogCategories.value = newValue;
}, { immediate: true });

watch(() => filterStore.blogFilters.readTime, (newValue) => {
  localReadTime.value = newValue;
}, { immediate: true });

// Initialize local state from store - Event tab
watch(() => filterStore.eventFilters.eventTypes, (newValue) => {
  localEventTypes.value = newValue;
}, { immediate: true });

watch(() => filterStore.eventFilters.eventFormat, (newValue) => {
  localEventFormat.value = newValue;
}, { immediate: true });

// Initialize local state from store - Group tab
watch(() => filterStore.groupFilters.groupCategories, (newValue) => {
  localGroupCategories.value = newValue;
}, { immediate: true });

// Initialize local state from store - Marketplace tab
watch(() => filterStore.marketplaceFilters.listingTypes, (newValue) => {
  localListingTypes.value = newValue;
}, { immediate: true });
</script>

<style scoped>
.common-filters,
.dynamic-filters {
  transition: all 0.3s ease;
}

.dynamic-filters {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px dashed rgba(0, 0, 0, 0.1);
}

.q-option-group {
  max-height: 200px;
  overflow-y: auto;
}

/* Add a subtle highlight to the active tab's filters */
.dynamic-filters {
  position: relative;
}

.dynamic-filters::before {
  content: '';
  position: absolute;
  left: -12px;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: var(--q-primary);
  opacity: 0.5;
  border-radius: 3px;
}

/* Date range dropdown styling */
.date-range-select {
  width: 100%;
}

.date-range-select :deep(.q-field__control) {
  background-color: rgba(0, 0, 0, 0.02);
}

.date-range-select :deep(.q-field__control:hover) {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Filter options with icons styling */
.filter-options-with-icons {
  max-height: 200px;
  overflow-y: auto;
  padding-right: 8px;
}

.filter-options-with-icons .q-checkbox {
  display: block;
  margin-bottom: 8px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.filter-options-with-icons .q-checkbox:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

/* Selected filters styling */
.selected-filters {
  margin-top: 1rem;
}

.filter-summary {
  background-color: rgba(0, 0, 0, 0.02);
}

.filter-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.filter-chips .q-chip {
  margin-bottom: 4px;
}

.filter-summary {
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
}

.filter-summary .q-card-section {
  padding: 8px 12px;
}

/* Make sure the filter card is properly displayed */
.filter-card {
  position: sticky;
  top: 20px;
  max-height: calc(100vh - 40px);
  overflow-y: auto;
}

/* Make sure the filter options are properly displayed */
.q-select {
  width: 100%;
}

/* Make sure the filter options are properly displayed on mobile */
@media (max-width: 767px) {
  .filter-card {
    position: static;
    max-height: none;
    overflow-y: visible;
  }
}

/* Style the q-select dropdown */
.q-menu {
  max-height: 300px;
  overflow-y: auto;
}
</style>
