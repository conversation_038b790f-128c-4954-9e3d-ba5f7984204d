<template>
  <q-card class="content-match-card q-mb-md">
    <q-card-section class="row items-center">
      <div class="col-auto">
        <q-img
          :src="match.image || getDefaultImage(match.contentType)"
          :ratio="16/9"
          style="width: 120px; border-radius: 8px;"
        />
      </div>
      
      <div class="col q-ml-md">
        <div class="text-h6">{{ match.title }}</div>
        <div class="text-subtitle2">{{ formatContentType(match.contentType) }}</div>
        <div v-if="match.date" class="text-caption q-mt-xs">
          <q-icon name="event" size="xs" class="q-mr-xs" />
          {{ formatDate(match.date) }}
        </div>
      </div>
      
      <div class="col-auto">
        <div class="row items-center">
          <q-circular-progress
            :value="match.matchScore * 100"
            size="50px"
            :thickness="0.2"
            color="primary"
            center-color="white"
            track-color="grey-3"
            class="q-mr-sm"
          >
            <div class="text-caption">{{ (match.matchScore * 100).toFixed(0) }}%</div>
          </q-circular-progress>
          
          <q-btn flat round color="primary" icon="info" size="sm">
            <q-tooltip max-width="300px">
              <div class="text-subtitle2 q-mb-sm">Match Criteria</div>
              <div v-for="(score, reason) in match.matchReasons" :key="reason" class="q-mb-xs">
                <div class="row items-center">
                  <div class="col-8">{{ formatReason(reason) }}:</div>
                  <div class="col-4 text-right">{{ (score * 100).toFixed(0) }}%</div>
                </div>
                <q-linear-progress :value="score" color="primary" class="q-mt-xs" />
              </div>
            </q-tooltip>
          </q-btn>
        </div>
      </div>
    </q-card-section>
    
    <q-separator />
    
    <q-card-section>
      <div class="text-body2 q-mb-sm">
        {{ match.description }}
      </div>
      
      <div class="row q-col-gutter-sm q-mb-md">
        <div class="col-12 text-subtitle2 q-mb-xs">Why this matches your profile:</div>
        <div class="col-12">
          <q-chip 
            v-for="(score, reason) in sortedMatchReasons" 
            :key="reason" 
            :color="getReasonColor(score)" 
            text-color="white"
            dense
          >
            {{ formatReason(reason) }}
          </q-chip>
        </div>
      </div>
    </q-card-section>
    
    <q-separator />
    
    <q-card-actions align="right">
      <q-btn
        flat
        color="grey"
        icon="close"
        label="Dismiss"
        @click="dismissMatch"
        :disable="loading"
      />
      <q-btn
        flat
        :color="match.isSaved ? 'positive' : 'primary'"
        :icon="match.isSaved ? 'bookmark' : 'bookmark_border'"
        :label="match.isSaved ? 'Saved' : 'Save'"
        @click="saveMatch"
        :disable="loading"
      />
      <q-btn
        flat
        color="primary"
        :icon="getActionIcon(match.contentType)"
        :label="getActionLabel(match.contentType)"
        @click="viewContent"
        :disable="loading"
      />
    </q-card-actions>
  </q-card>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useNotificationStore } from '@/stores/notifications';

// Props
const props = defineProps({
  match: {
    type: Object,
    required: true
  }
});

// Stores
const notificationStore = useNotificationStore();

// State
const loading = ref(false);

// Computed
const sortedMatchReasons = computed(() => {
  if (!props.match.matchReasons) return {};
  
  // Sort reasons by score (highest first)
  const entries = Object.entries(props.match.matchReasons);
  entries.sort((a, b) => b[1] - a[1]);
  
  // Convert back to object
  const result = {};
  entries.forEach(([key, value]) => {
    result[key] = value;
  });
  
  return result;
});

// Methods
function formatContentType(type) {
  // Convert snake_case to Title Case
  return type
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

function formatReason(reason) {
  // Convert snake_case to Title Case with spaces
  return reason
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

function getReasonColor(score) {
  // Return color based on score
  if (score >= 0.8) return 'positive';
  if (score >= 0.6) return 'primary';
  if (score >= 0.4) return 'secondary';
  if (score >= 0.2) return 'orange';
  return 'grey';
}

function formatDate(dateString) {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

function getDefaultImage(contentType) {
  switch (contentType) {
    case 'event':
      return 'https://picsum.photos/id/20/300/200';
    case 'blog':
      return 'https://picsum.photos/id/24/300/200';
    case 'opportunity':
      return 'https://picsum.photos/id/28/300/200';
    case 'group':
      return 'https://picsum.photos/id/91/300/200';
    case 'marketplace':
      return 'https://picsum.photos/id/48/300/200';
    default:
      return 'https://picsum.photos/id/42/300/200';
  }
}

function getActionIcon(contentType) {
  switch (contentType) {
    case 'event':
      return 'event_available';
    case 'blog':
      return 'article';
    case 'opportunity':
      return 'assignment_turned_in';
    case 'group':
      return 'group_add';
    case 'marketplace':
      return 'shopping_cart';
    default:
      return 'visibility';
  }
}

function getActionLabel(contentType) {
  switch (contentType) {
    case 'event':
      return 'Register';
    case 'blog':
      return 'Read';
    case 'opportunity':
      return 'Apply';
    case 'group':
      return 'Join';
    case 'marketplace':
      return 'View';
    default:
      return 'View';
  }
}

async function saveMatch() {
  loading.value = true;
  try {
    // This would be implemented to save the content match
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Toggle saved state
    props.match.isSaved = !props.match.isSaved;
    
    notificationStore.success(
      props.match.isSaved 
        ? 'Content saved to your bookmarks' 
        : 'Content removed from your bookmarks'
    );
  } catch (error) {
    console.error('Error saving content match:', error);
    notificationStore.error('Failed to save content match');
  } finally {
    loading.value = false;
  }
}

async function dismissMatch() {
  loading.value = true;
  try {
    // This would be implemented to dismiss the content match
    await new Promise(resolve => setTimeout(resolve, 500));
    
    notificationStore.success('Content match dismissed');
  } catch (error) {
    console.error('Error dismissing content match:', error);
    notificationStore.error('Failed to dismiss content match');
  } finally {
    loading.value = false;
  }
}

function viewContent() {
  // This would be implemented to view the content
  notificationStore.info(`Viewing ${props.match.contentType}: ${props.match.title}`);
}
</script>

<style scoped>
.content-match-card {
  transition: all 0.3s ease;
}

.content-match-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}
</style>
