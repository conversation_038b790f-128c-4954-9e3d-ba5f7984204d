-- Direct version to run in SQL Editor
-- This creates the exec_sql function without checking if it exists first

-- Create the exec_sql function
CREATE OR REPLACE FUNCTION public.exec_sql(sql text)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
BEGIN
  EXECUTE sql;
END;
$function$;

-- Grant execute permission to authenticated users and service role
GRANT EXECUTE ON FUNCTION public.exec_sql TO authenticated;
GRANT EXECUTE ON FUNCTION public.exec_sql TO service_role;
