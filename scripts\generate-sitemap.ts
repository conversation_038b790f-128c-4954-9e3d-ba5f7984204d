import { generateSitemapRoutes, generateSitemapXML, writeSitemapToFile, pingSearchEngines } from '../src/utils/sitemap';
import { router } from '../src/router';
import { createApp } from 'vue';
import App from '../src/App.vue';

// Create a Vue app instance to ensure all routes are registered
const app = createApp(App);
app.use(router);

async function generateSitemap() {
  try {
    console.log('Generating sitemap...');

    // Generate URLs from router
    const urls = generateSitemapRoutes(router);
    console.log(`Generated ${urls.length} URLs`);

    // Create XML content
    const xmlContent = generateSitemapXML(urls);

    // Write to file
    await writeSitemapToFile(xmlContent);
    console.log('Sitemap generated successfully!');

    // Ping search engines to notify about the updated sitemap
    if (process.env.NODE_ENV === 'production') {
      await pingSearchEngines();
    } else {
      console.log('Skipping search engine ping in development environment');
    }
  } catch (error) {
    console.error('Error generating sitemap:', error);
    process.exit(1);
  }
}

// Run the script
generateSitemap()
  .then(() => {
    console.log('Sitemap generation complete');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Unhandled error during sitemap generation:', error);
    process.exit(1);
  });