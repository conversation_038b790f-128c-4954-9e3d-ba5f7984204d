// Type definitions for Quasar
// This is a minimal type declaration file to fix the TypeScript error

declare module 'quasar' {
  import { App, Component, Plugin } from 'vue';

  // Define the Quasar plugin
  export const Quasar: Plugin;

  // Define common Quasar components
  export const QBtn: Component;
  export const QPage: Component;
  export const QLayout: Component;
  export const QHeader: Component;
  export const QFooter: Component;
  export const QDrawer: Component;
  export const QPageContainer: Component;
  export const QToolbar: Component;
  export const QToolbarTitle: Component;
  export const QList: Component;
  export const QItem: Component;
  export const QItemSection: Component;
  export const QItemLabel: Component;
  export const QCard: Component;
  export const QCardSection: Component;
  export const QCardActions: Component;
  export const QInput: Component;
  export const QSelect: Component;
  export const QCheckbox: Component;
  export const QRadio: Component;
  export const QToggle: Component;
  export const QBtnToggle: Component;
  export const QIcon: Component;
  export const QSeparator: Component;
  export const QSpinner: Component;
  export const QSpinnerDots: Component;
  export const QBadge: Component;
  export const QChip: Component;
  export const QAvatar: Component;
  export const QTable: Component;
  export const QTh: Component;
  export const QTr: Component;
  export const QTd: Component;
  export const QImg: Component;
  export const QDialog: Component;
  export const QMenu: Component;
  export const QPopupProxy: Component;
  export const QTooltip: Component;
  export const QBtnDropdown: Component;
  export const QTabs: Component;
  export const QTab: Component;
  export const QTabPanels: Component;
  export const QTabPanel: Component;
  export const QStepper: Component;
  export const QStep: Component;
  export const QStepperNavigation: Component;
  export const QForm: Component;
  export const QUploader: Component;
  export const QInfiniteScroll: Component;
  export const QPullToRefresh: Component;
  export const QSlider: Component;
  export const QRange: Component;
  export const QKnob: Component;
  export const QExpansionItem: Component;
  export const QCarousel: Component;
  export const QCarouselSlide: Component;
  export const QCarouselControl: Component;
  export const QParallax: Component;
  export const QTimeline: Component;
  export const QTimelineEntry: Component;
  export const QInnerLoading: Component;
  export const QSpinnerBars: Component;
  export const QSpinnerGears: Component;
  export const QSpinnerHourglass: Component;
  export const QSpinnerIos: Component;
  export const QSpinnerOval: Component;
  export const QSpinnerPuff: Component;
  export const QSpinnerRings: Component;
  export const QSpinnerTail: Component;
  export const QCircularProgress: Component;
  export const QLinearProgress: Component;
  export const QSkeleton: Component;
  export const QMarkupTable: Component;
  export const QTree: Component;
  export const QFab: Component;
  export const QFabAction: Component;
  export const QPageScroller: Component;
  export const QPageSticky: Component;
  export const QBtnGroup: Component;
  export const QTime: Component;
  export const QDate: Component;
  export const QPopupEdit: Component;
  export const QColor: Component;
  export const QField: Component;
  export const QFile: Component;
  export const QRating: Component;
  export const QScrollArea: Component;
  export const QVideo: Component;
  export const QVirtualScroll: Component;
  export const QTimePicker: Component;
  export const QDatePicker: Component;
  export const QEditor: Component;
  export const QOptionGroup: Component;
  export const QBanner: Component;
  export const QBar: Component;
  export const QSpace: Component;
  export const QBreadcrumbs: Component;
  export const QBreadcrumbsEl: Component;
  export const QChatMessage: Component;
  export const QCollapsible: Component;
  export const QSlideTransition: Component;
  export const QResizeObserver: Component;
  export const QScrollObserver: Component;
  export const QIntersectionObserver: Component;
  export const QToggleIndeterminate: Component;
  export const QNoSsr: Component;
  export const QMarkupEditor: Component;
  export const QMarkupPreview: Component;
  export const QAjaxBar: Component;
  export const QBtnToggleGroup: Component;
  export const QCarouselSlider: Component;
  export const QChatMessageSent: Component;
  export const QChatMessageReceived: Component;
  export const QColorPicker: Component;
  export const QContextMenu: Component;
  export const QDatetimePicker: Component;
  export const QDrawerContainer: Component;
  export const QFabMain: Component;
  export const QGallery: Component;
  export const QGalleryCarousel: Component;
  export const QInfiniteLoad: Component;
  export const QInnerCircular: Component;
  export const QKnobSelect: Component;
  export const QLayoutDrawer: Component;
  export const QLayoutFooter: Component;
  export const QLayoutHeader: Component;
  export const QListHeader: Component;
  export const QModalLayout: Component;
  export const QPageContainer: Component;
  export const QPageSticky: Component;
  export const QPopover: Component;
  export const QProgress: Component;
  export const QRouteTab: Component;
  export const QScrollArea: Component;
  export const QSearch: Component;
  export const QTableColumns: Component;
  export const QTablePagination: Component;
  export const QTabPane: Component;
  export const QTdNumeric: Component;
  export const QWindowResizeObservable: Component;

  // Define Quasar plugins
  export interface QuasarPluginOptions {
    plugins?: Record<string, any>;
    config?: Record<string, any>;
    components?: Record<string, Component>;
    directives?: Record<string, any>;
  }

  // Define Quasar utilities
  export const colors: any;
  export const date: any;
  export const dom: any;
  export const event: any;
  export const format: any;
  export const platform: any;
  export const screen: any;
  export const utils: any;

  // Define Quasar global methods
  export function notify(options: any): void;
  export const Notify: any;
  export function dialog(options: any): any;
  export const Dialog: any;
  export function loading(options?: any): { hide: () => void };
  export const Loading: any;
  export function loadingBar(options?: any): { start: () => void; stop: () => void; increment: (amount: number) => void };
  export function bottomSheet(options: any): any;
  export const localStorage: any;
  export const sessionStorage: any;
  export const cookies: any;
  export const dark: { isActive: boolean; set: (value: boolean) => void; toggle: () => void };
  export const fullscreen: { isActive: boolean; request: () => void; exit: () => void; toggle: () => void };
  export const addressbarColor: { set: (color: string) => void };
  export function exportFile(fileName: string, rawData: any, mimeType?: string): boolean;
  export function uid(): string;
  export function openURL(url: string): void;
  export function copyToClipboard(text: string): Promise<void>;
  export function debounce(fn: Function, wait?: number, immediate?: boolean): Function;
  export function throttle(fn: Function, limit: number): Function;
  export const scroll: any;
  export const meta: any;
}

// Declare global properties added by Quasar
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $q: any;
  }
}
