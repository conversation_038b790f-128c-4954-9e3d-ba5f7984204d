/**
 * Profile Utilities
 * 
 * This file contains utility functions for working with profile data,
 * particularly for handling JSON fields and common operations.
 */

import { supabase } from '../lib/supabase'
import { BaseProfile, PersonalDetails } from './profileService'

/**
 * Process JSON fields in profile data
 * 
 * @param data The profile data containing JSON fields
 * @param profileType The profile type
 * @returns The processed data with properly formatted JSON fields
 */
export function processJsonFields(data: Record<string, any>, profileType: string): Record<string, any> {
  if (!data) return {}

  // Create a copy of the data to avoid modifying the original
  const processedData = { ...data }

  // Define common JSON fields across all profile types
  const jsonFields = [
    'expertise_areas', 'areas_of_expertise', 'target_markets', 'innovation_focus',
    'collaboration_types', 'preferred_partners', 'resources_offered',
    'short_term_goals', 'long_term_goals', 'current_challenges',
    'looking_for', 'collaboration_interests', 'sdg_alignment',
    'investment_focus', 'investment_geography', 'certifications',
    'skills', 'languages', 'preferred_stages', 'preferred_locations',
    // Academic institution specific JSON fields
    'research_areas', 'other_research_areas', 'research_centers',
    'academic_programs', 'industry_partnerships'
  ]

  // Process all JSON fields
  jsonFields.forEach(field => {
    if (processedData[field] !== undefined) {
      // If it's a string, try to parse it as JSON
      if (typeof processedData[field] === 'string' && processedData[field] !== '') {
        try {
          processedData[field] = JSON.parse(processedData[field])
        } catch (e) {
          // If parsing fails, treat it as a single-item array
          processedData[field] = [processedData[field]]
        }
      }

      // Ensure it's an array if null or undefined
      if (processedData[field] === null || processedData[field] === undefined) {
        processedData[field] = []
      }

      // If it's still not an array or object, convert it to an array
      if (!Array.isArray(processedData[field]) && typeof processedData[field] !== 'object') {
        processedData[field] = [processedData[field]]
      }

      // For academic institution profile, ensure research fields are properly formatted for database
      if (profileType === 'academic_institution' &&
          ['research_areas', 'other_research_areas', 'research_centers'].includes(field)) {
        // Convert to JSON string for database compatibility
        if (Array.isArray(processedData[field])) {
          processedData[field] = JSON.stringify(processedData[field])
        }
      }
    }
  })

  return processedData
}

/**
 * Set personal details from profile data
 * 
 * @param profile The profile data
 * @returns The personal details object
 */
export function setPersonalDetailsFromProfile(profile: Partial<BaseProfile>): PersonalDetails {
  return {
    first_name: profile.first_name || '',
    last_name: profile.last_name || '',
    email: profile.email || '',
    phone_country_code: profile.phone_country_code || '',
    phone_number: profile.phone_number || '',
    gender: profile.gender || '',
    bio: profile.bio || ''
  }
}

/**
 * Verify database update by fetching the data again
 * 
 * @param tableName The table name
 * @param userId The user ID
 * @returns The fetched data or null if not found
 */
export async function verifyDatabaseUpdate(tableName: string, userId: string): Promise<Record<string, any> | null> {
  try {
    const { data, error } = await supabase
      .from(tableName)
      .select('*')
      .eq('user_id', userId)
      .single()

    if (error) {
      console.error(`Error verifying data in ${tableName}:`, error)
      return null
    }

    return data
  } catch (error) {
    console.error(`Error in verifyDatabaseUpdate:`, error)
    return null
  }
}

/**
 * Build a profile update object based on profile type
 * 
 * @param profileData The profile data to update
 * @param profileType The profile type
 * @returns The update object with type-specific fields
 */
export function buildProfileUpdateObject(
  profileData: Record<string, any>,
  profileType: string
): Record<string, any> {
  // Process JSON fields
  const processedData = processJsonFields(profileData, profileType)
  
  // Common fields for all profile types
  const updateObject = {
    // Include all fields from processed data
    ...processedData,
    
    // Common fields across all profile types
    profile_name: processedData.profile_name,
    is_public: processedData.is_public,
    bio: processedData.bio,
    
    // Contact fields
    contact_email: processedData.contact_email,
    contact_phone_country_code: processedData.contact_phone_country_code,
    contact_phone_number: processedData.contact_phone_number,
    whatsapp_country_code: processedData.whatsapp_country_code,
    whatsapp_number: processedData.whatsapp_number,
    telegram: processedData.telegram,
    skype: processedData.skype,
    preferred_contact_method: processedData.preferred_contact_method,
    
    // Social media fields
    website: processedData.website,
    linkedin: processedData.linkedin,
    twitter: processedData.twitter,
    facebook: processedData.facebook,
    instagram: processedData.instagram,
    youtube: processedData.youtube,
    github: processedData.github,
    medium: processedData.medium,
    other_social: processedData.other_social,
    
    // Location fields
    address: processedData.address,
    city: processedData.city,
    state_province: processedData.state_province,
    country: processedData.country,
    postal_code: processedData.postal_code,
    willing_to_relocate: processedData.willing_to_relocate,
    preferred_locations: processedData.preferred_locations,
    
    // Goals fields
    short_term_goals: processedData.short_term_goals,
    long_term_goals: processedData.long_term_goals,
    current_challenges: processedData.current_challenges,
    looking_for: processedData.looking_for,
    collaboration_interests: processedData.collaboration_interests,
    sdg_alignment: processedData.sdg_alignment,
    additional_interests: processedData.additional_interests,
    
    // Add updated timestamp
    updated_at: new Date().toISOString()
  }
  
  return updateObject
}

/**
 * Get the table name for a profile type
 * 
 * @param profileType The profile type
 * @returns The table name
 */
export function getProfileTableName(profileType: string): string {
  if (!profileType) return 'unknown_profiles'
  
  // Normalize the profile type
  const normalizedType = profileType.trim().toLowerCase()
  
  // Always use plural naming convention
  return normalizedType.endsWith('_profiles') ? normalizedType : `${normalizedType}_profiles`
}

/**
 * Format a profile type for display
 * 
 * @param profileType The profile type
 * @returns The formatted profile type
 */
export function formatProfileType(profileType: string | null): string {
  if (!profileType) return 'Unknown'
  
  return profileType
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}
