/**
 * API endpoint to generate matches for a user
 * 
 * This endpoint generates matches for a user based on their profile type
 * and stores the results in the matchmaking_results table.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { MatchmakingService, ProfileType } from '@/lib/matchmaking/matchmakingService';
import { initializeMatchmakingRules } from '@/lib/matchmaking/initMatchmakingRules';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  
  // Create Supabase server client
  const supabase = createServerSupabaseClient({ req, res });
  
  // Check if user is authenticated
  const {
    data: { session },
  } = await supabase.auth.getSession();
  
  if (!session) {
    return res.status(401).json({ error: 'Unauthorized' });
  }
  
  try {
    // Get the profile type from the request body
    const { profileType } = req.body as { profileType: ProfileType };
    
    if (!profileType) {
      return res.status(400).json({ error: 'Profile type is required' });
    }
    
    // Initialize matchmaking rules if needed
    await initializeMatchmakingRules(supabase);
    
    // Create matchmaking service
    const matchmakingService = new MatchmakingService(supabase);
    
    // Generate matches for the user
    const matches = await matchmakingService.generateMatches(session.user.id, profileType);
    
    // Store the matches in the database
    const matchesToInsert = matches.map(match => ({
      user_id: session.user.id,
      matched_entity_id: match.entityId,
      entity_type: match.entityType,
      match_score: match.score,
      match_reasons: match.reasons,
      is_viewed: false,
      is_saved: false
    }));
    
    // Delete existing matches for this user and profile type
    await supabase
      .from('matchmaking_results')
      .delete()
      .eq('user_id', session.user.id)
      .eq('entity_type', profileType);
    
    // Insert new matches
    if (matchesToInsert.length > 0) {
      const { error: insertError } = await supabase
        .from('matchmaking_results')
        .insert(matchesToInsert);
        
      if (insertError) {
        console.error('Error inserting matches:', insertError);
        return res.status(500).json({ error: 'Failed to store matches' });
      }
    }
    
    return res.status(200).json({ 
      success: true, 
      message: `Generated ${matches.length} matches for ${profileType} profile`,
      matches
    });
  } catch (error) {
    console.error('Error generating matches:', error);
    return res.status(500).json({ error: 'Failed to generate matches' });
  }
}
