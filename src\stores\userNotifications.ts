import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { supabase } from '@/lib/supabase';
import { useNotificationService, UserNotification } from '@/services/notificationService';

export const useUserNotificationsStore = defineStore('userNotifications', () => {
  // State
  const notifications = ref<UserNotification[]>([]);
  const unreadCount = ref<number>(0);
  const loading = ref(false);
  const error = ref<string | null>(null);

  // Services
  const notificationService = useNotificationService();

  // Computed properties
  const getNotifications = computed(() => notifications.value);
  const getUnreadCount = computed(() => unreadCount.value);
  const isLoading = computed(() => loading.value);
  const getError = computed(() => error.value);

  /**
   * Fetch user notifications
   *
   * @param limit Maximum number of notifications to return
   * @param page Page number for pagination
   * @param unreadOnly Whether to only return unread notifications
   * @returns Array of user notifications
   */
  async function fetchNotifications(
    limit: number = 10,
    page: number = 1,
    unreadOnly: boolean = false
  ): Promise<UserNotification[]> {
    try {
      loading.value = true;
      error.value = null;

      const result = await notificationService.getUserNotifications(limit, page, unreadOnly);
      notifications.value = result;
      return result;
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch notifications';
      return [];
    } finally {
      loading.value = false;
    }
  }

  /**
   * Fetch unread notification count
   *
   * @returns Count of unread notifications
   */
  async function fetchUnreadCount(): Promise<number> {
    try {
      loading.value = true;
      error.value = null;

      const count = await notificationService.getUnreadCount();
      unreadCount.value = count;
      return count;
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch unread count';
      return 0;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Mark notifications as read
   *
   * @param notificationIds Array of notification IDs to mark as read
   * @returns Success status
   */
  async function markAsRead(notificationIds: string[]): Promise<boolean> {
    if (!notificationIds.length) return true;

    try {
      loading.value = true;
      error.value = null;

      const success = await notificationService.markAsRead(notificationIds);
      
      if (success) {
        // Update local state
        notifications.value = notifications.value.map(notification => {
          if (notificationIds.includes(notification.id)) {
            return { ...notification, is_read: true };
          }
          return notification;
        });

        // Update unread count
        await fetchUnreadCount();
      }

      return success;
    } catch (err: any) {
      error.value = err.message || 'Failed to mark notifications as read';
      return false;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Mark all notifications as read
   *
   * @returns Success status
   */
  async function markAllAsRead(): Promise<boolean> {
    try {
      loading.value = true;
      error.value = null;

      const success = await notificationService.markAllAsRead();
      
      if (success) {
        // Update local state
        notifications.value = notifications.value.map(notification => ({
          ...notification,
          is_read: true
        }));

        // Update unread count
        unreadCount.value = 0;
      }

      return success;
    } catch (err: any) {
      error.value = err.message || 'Failed to mark all notifications as read';
      return false;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Initialize the store
   */
  async function initialize() {
    // Skip initialization if already done recently (within 2 minutes)
    const lastInit = sessionStorage.getItem('userNotificationsLastInit')
    const now = Date.now()
    const twoMinutes = 2 * 60 * 1000

    if (lastInit && (now - parseInt(lastInit)) < twoMinutes) {
      console.log('User notifications already initialized recently, skipping')
      return
    }

    try {
      await Promise.allSettled([
        fetchNotifications(10, 1, false),
        fetchUnreadCount()
      ]);

      // Set up real-time notifications only once
      if (!sessionStorage.getItem('notificationServiceInitialized')) {
        notificationService.initialize();
        sessionStorage.setItem('notificationServiceInitialized', 'true')
      }

      sessionStorage.setItem('userNotificationsLastInit', now.toString())
    } catch (error) {
      console.error('Error initializing user notifications:', error)
    }
  }

  /**
   * Clean up the store
   */
  function cleanup() {
    notificationService.cleanup();
  }

  return {
    notifications,
    unreadCount,
    loading,
    error,
    getNotifications,
    getUnreadCount,
    isLoading,
    getError,
    fetchNotifications,
    fetchUnreadCount,
    markAsRead,
    markAllAsRead,
    initialize,
    cleanup
  };
});
