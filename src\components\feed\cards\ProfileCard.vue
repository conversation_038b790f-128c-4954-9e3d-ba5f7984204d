<template>
  <q-card class="profile-card">
    <div class="profile-header" :style="`background-color: ${getProfileTypeBackgroundColor(profile.profile_type || profile.profileType)}; height: 80px;`"></div>

    <div class="profile-avatar-container">
      <user-avatar
        :name="getProfileName()"
        :email="profile.email"
        :avatar-url="profile.avatar_url || profile.avatar"
        :user-id="profile.user_id || profile.id"
        size="80px"
        class="profile-avatar"
        :clickable="false"
      />
    </div>

    <q-card-section class="q-pt-xl">
      <div class="text-center">
        <div class="text-h6">{{ getProfileName() }}</div>
        <div class="text-subtitle2">{{ profile.title || getProfileTitle() }}</div>

        <q-badge
          v-if="profile.profile_type || profile.profileType"
          :color="getProfileTypeColor(profile.profile_type || profile.profileType)"
          class="q-mt-sm"
        >
          {{ formatProfileType(profile.profile_type || profile.profileType) }}
        </q-badge>

        <q-badge
          v-if="getProfileCompletion() > 0"
          color="green"
          class="q-mt-sm q-ml-sm"
        >
          {{ getProfileCompletion() }}% Complete
        </q-badge>
      </div>
    </q-card-section>

    <q-card-section>
      <div v-if="profile.organization" class="row items-center q-mb-sm">
        <q-icon name="business" size="xs" class="q-mr-xs" />
        <span class="text-caption">{{ profile.organization }}</span>
      </div>

      <div v-if="profile.location" class="row items-center q-mb-sm">
        <q-icon name="location_on" size="xs" class="q-mr-xs" />
        <span class="text-caption">{{ profile.location }}</span>
      </div>

      <!-- Contact Information - Email removed for privacy -->
      <!-- Email display removed from profile cards for privacy reasons -->

      <div v-if="profile.website" class="row items-center q-mb-sm">
        <q-icon name="language" size="xs" class="q-mr-xs" />
        <span class="text-caption">{{ formatWebsite(profile.website) }}</span>
      </div>

      <div v-if="profile.linkedin" class="row items-center q-mb-sm">
        <q-icon name="link" size="xs" class="q-mr-xs" />
        <span class="text-caption">LinkedIn</span>
      </div>

      <p v-if="getBio()" class="text-body2 q-mt-sm bio-text">{{ getBio() }}</p>

      <!-- Skills/Interests -->
      <div v-if="profile.skills && profile.skills.length" class="q-mt-sm">
        <div class="text-caption text-weight-bold q-mb-xs">Skills & Interests</div>
        <div class="row q-gutter-xs">
          <q-chip
            v-for="(skill, index) in profile.skills.slice(0, 3)"
            :key="index"
            dense
            size="sm"
            color="grey-3"
            text-color="grey-8"
          >
            {{ skill }}
          </q-chip>
          <q-chip
            v-if="profile.skills.length > 3"
            dense
            size="sm"
            color="grey-3"
            text-color="grey-8"
          >
            +{{ profile.skills.length - 3 }} more
          </q-chip>
        </div>
      </div>

      <!-- Profile Completion -->
      <div v-if="getProfileCompletion() > 0" class="q-mt-md">
        <div class="row items-center justify-between q-mb-xs">
          <div class="text-caption">Profile Completion</div>
          <div class="text-caption">{{ getProfileCompletion() }}%</div>
        </div>
        <q-linear-progress
          :value="getProfileCompletion() / 100"
          :color="getCompletionColor(getProfileCompletion())"
          size="xs"
        />
      </div>
    </q-card-section>

    <q-card-actions align="center">
      <q-btn flat color="primary" label="View Profile" @click="handleViewProfile" />
      <!-- If current user: show edit button -->
      <q-btn v-if="isCurrentUser" flat color="primary" icon="edit" @click="handleEditProfile">
        <q-tooltip>Edit Your Profile</q-tooltip>
      </q-btn>
      <!-- If NOT current user: show message and connect buttons -->
      <template v-else>
        <q-btn flat color="primary" icon="message" @click="handleMessage">
          <q-tooltip>Message</q-tooltip>
        </q-btn>
        <q-btn
          flat
          :color="connectionButton.buttonConfig.color"
          :icon="connectionButton.buttonConfig.icon"
          :disabled="connectionButton.buttonConfig.disabled"
          :loading="connectionButton.buttonConfig.loading"
          @click="connectionButton.handleConnect"
        >
          <q-tooltip>{{ connectionButton.tooltipText }}</q-tooltip>
        </q-btn>
      </template>
    </q-card-actions>
  </q-card>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { supabase } from '../../../lib/supabase';
import { useConnectionButton } from '../../../composables/useConnectionButton';
import { useAuthStore } from '../../../stores/auth';
import UserAvatar from '../../common/UserAvatar.vue';
import { getNameFromEmail } from '../../../utils/nameUtils';

const props = defineProps({
  profile: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['view', 'message', 'connect']);
const router = useRouter();
const authStore = useAuthStore();

// Use unified connection button logic
const userId = props.profile.user_id || props.profile.id;
const connectionButton = useConnectionButton(userId);

// Simple current user check
const isCurrentUser = computed(() => {
  const currentUser = authStore.user;
  const profileUserId = props.profile.user_id || props.profile.id;
  const isCurrentFromAuth = currentUser && currentUser.id === profileUserId;
  const isCurrentFromService = props.profile.isCurrentUser;

  // Use service flag if available, otherwise fall back to auth store check
  const result = isCurrentFromService !== undefined ? isCurrentFromService : isCurrentFromAuth;

  // Debug logging for troubleshooting
  if (isCurrentFromAuth || isCurrentFromService) {
    console.log('ProfileCard: Current user check:', {
      profileUserId,
      currentUserId: currentUser?.id,
      isCurrentFromAuth,
      isCurrentFromService,
      result,
      authStoreUser: currentUser,
      profileData: props.profile
    });
  }

  return result;
});

// Methods
function handleViewProfile() {
  const userId = props.profile.user_id || props.profile.id;
  emit('view', userId);

  // Navigate to profile page
  router.push({ name: 'user-profile', params: { id: userId } });
}

function handleMessage() {
  const userId = props.profile.user_id || props.profile.id;
  emit('message', userId);
}

function handleEditProfile() {
  router.push({ name: 'dashboard', query: { tab: 'profile' } });
}

// Helper function to get profile name
function getProfileName(): string {
  // First check if we have a profile_name
  if (props.profile.profile_name) {
    return props.profile.profile_name;
  }

  // Then check if we have a name
  if (props.profile.name) {
    return props.profile.name;
  }

  // Then check if we have first_name or last_name
  const fullName = `${props.profile.first_name || ''} ${props.profile.last_name || ''}`.trim();
  if (fullName) {
    return fullName;
  }

  // If we have an email, generate a name from it
  if (props.profile.email) {
    return getNameFromEmail(props.profile.email);
  }

  // Final fallback
  return 'Anonymous User';
}

// Helper function to get profile title
function getProfileTitle(): string {
  if (props.profile.title) {
    return props.profile.title;
  }

  const profileType = props.profile.profile_type || props.profile.profileType;
  if (profileType) {
    return formatProfileType(profileType);
  }

  return '';
}

// Helper function to get bio
function getBio(): string {
  if (props.profile.specialized_bio) {
    return props.profile.specialized_bio;
  }

  if (props.profile.base_bio) {
    return props.profile.base_bio;
  }

  if (props.profile.bio) {
    return props.profile.bio;
  }

  return '';
}

// Helper function to check if profile has contact info - removed since email is no longer displayed

// Helper function to format website URL
function formatWebsite(website: string): string {
  if (!website) return '';

  // Remove protocol for display
  return website.replace(/^https?:\/\//, '');
}

// Helper function to get profile completion
function getProfileCompletion(): number {
  if (typeof props.profile.profile_completion === 'number') {
    return props.profile.profile_completion;
  }

  if (typeof props.profile.completion === 'number') {
    return props.profile.completion;
  }

  return 0;
}

// Helper function to format profile type
function formatProfileType(type: string): string {
  if (!type) return '';

  // Convert snake_case to Title Case
  if (type.includes('_')) {
    return type
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  // If already in camel case or other format, just capitalize first letter
  return type.charAt(0).toUpperCase() + type.slice(1);
}

// Helper function to get profile type background color
function getProfileTypeBackgroundColor(type: string): string {
  if (!type) return 'rgba(116, 181, 36, 0.05)'; // Default light green

  const typeColors: Record<string, string> = {
    'innovator': 'rgba(116, 181, 36, 0.1)',
    'investor': 'rgba(33, 150, 243, 0.1)',
    'mentor': 'rgba(156, 39, 176, 0.1)',
    'professional': 'rgba(0, 150, 136, 0.1)',
    'industry_expert': 'rgba(255, 152, 0, 0.1)',
    'academic_student': 'rgba(63, 81, 181, 0.1)',
    'academic_institution': 'rgba(255, 87, 34, 0.1)',
    'organisation': 'rgba(233, 30, 99, 0.1)'
  };

  return typeColors[type.toLowerCase()] || 'rgba(116, 181, 36, 0.05)';
}

// Helper function to get profile type color
function getProfileTypeColor(type: string): string {
  if (!type) return 'primary';

  const typeColors: Record<string, string> = {
    'innovator': 'green',
    'investor': 'blue',
    'mentor': 'purple',
    'professional': 'teal',
    'industry_expert': 'orange',
    'academic_student': 'indigo',
    'academic_institution': 'deep-orange',
    'organisation': 'pink',
    // Legacy mappings
    'Entrepreneur': 'green',
    'Investor': 'blue',
    'Mentor': 'primary',
    'Corporate': 'teal',
    'Academic': 'deep-orange',
    'Student': 'indigo',
    'Startup': 'red',
    'Service Provider': 'amber'
  };

  return typeColors[type] || 'primary';
}

// Helper function to get completion color
function getCompletionColor(completion: number): string {
  if (completion < 30) return 'red';
  if (completion < 70) return 'orange';
  return 'green';
}
</script>

<style scoped>
.profile-card {
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
}

.profile-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.profile-avatar-container {
  position: absolute;
  top: 40px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
}

.profile-avatar {
  border: 3px solid white;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.1);
}

.profile-header {
  position: relative;
}

.bio-text {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 4.5em; /* Approximately 3 lines */
}

/* Mobile responsiveness improvements */
@media (max-width: 599px) {
  .profile-card {
    margin: 0 8px 16px 8px;
    width: calc(100% - 16px);
  }

  .q-card-section {
    padding: 16px;
  }

  .profile-avatar-container {
    top: 30px;
  }

  /* Ensure content doesn't overflow */
  .row.q-gutter-xs {
    margin-left: 0;
    padding-left: 0;
  }
}
</style>
