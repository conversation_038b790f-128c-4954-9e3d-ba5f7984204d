import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { corsHeaders } from '../_shared/cors.ts';

const DEEPSEEK_API_URL = 'https://api.deepseek.com/v1/chat/completions';
const DEEPSEEK_API_KEY = '***********************************';

interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

interface ChatRequest {
  message: string;
  conversation_history?: ChatMessage[];
  user_context?: {
    profile_type?: string;
    user_id?: string;
  };
}

const SYSTEM_PROMPT = `You are ZbInnovation AI Assistant, a helpful AI companion for the ZbInnovation platform - Zimbabwe's premier digital innovation ecosystem.

PLATFORM CONTEXT:
ZbInnovation connects innovators, investors, mentors, professionals, academic institutions, industry experts, and organizations in Zimbabwe's innovation landscape. The platform facilitates:
- Intelligent matchmaking between different user types
- Innovation project collaboration
- Investment opportunities
- Mentorship connections
- Knowledge sharing and community building

USER TYPES:
- Innovators: Entrepreneurs with ideas or early-stage startups
- Investors: Individuals or organizations looking to fund innovations
- Mentors: Experienced professionals offering guidance
- Industry Experts: Specialists in various fields
- Professionals: Working individuals seeking opportunities
- Academic Students: Students working on innovative projects
- Academic Institutions: Universities and research centers
- Organizations: Companies and institutions supporting innovation

YOUR ROLE:
- Help users navigate the platform
- Provide guidance on innovation, entrepreneurship, and business development
- Suggest potential connections and collaborations
- Answer questions about Zimbabwe's innovation ecosystem
- Offer advice on funding, mentorship, and business growth
- Be encouraging and supportive of innovation efforts

TONE: Professional, encouraging, knowledgeable about African innovation, and specifically familiar with Zimbabwe's business environment.

Keep responses concise but helpful. Always relate advice back to the ZbInnovation platform when relevant.`;

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  console.log('AI Chat function called with method:', req.method);

  try {
    const requestBody = await req.json();
    console.log('Request body:', requestBody);

    const { message, conversation_history = [], user_context = {} }: ChatRequest = requestBody;

    if (!message || typeof message !== 'string') {
      console.error('Invalid message:', message);
      return new Response(
        JSON.stringify({ error: 'Message is required and must be a string' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Build conversation messages
    const messages: ChatMessage[] = [
      { role: 'system', content: SYSTEM_PROMPT },
      ...conversation_history,
      { role: 'user', content: message }
    ];

    // Add user context to system prompt if available
    if (user_context.profile_type) {
      messages[0].content += `\n\nCURRENT USER: The user you're helping is a ${user_context.profile_type}. Tailor your responses accordingly.`;
    }

    console.log('Calling DeepSeek API with messages:', messages.length);

    // Call DeepSeek API
    const response = await fetch(DEEPSEEK_API_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: messages,
        max_tokens: 1000,
        temperature: 0.7,
        stream: false
      })
    });

    console.log('DeepSeek API response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('DeepSeek API error:', response.status, errorText);
      throw new Error(`DeepSeek API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log('DeepSeek API response data:', data);

    const aiResponse = data.choices?.[0]?.message?.content;

    if (!aiResponse) {
      console.error('No AI response in data:', data);
      throw new Error('No response from AI model');
    }

    return new Response(
      JSON.stringify({ 
        response: aiResponse,
        conversation_id: crypto.randomUUID() // Generate a conversation ID for tracking
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error: any) {
    console.error('AI Chat error:', error);

    // Return a more helpful error message
    const errorMessage = error.message || 'Unknown error occurred';

    return new Response(
      JSON.stringify({
        error: 'Failed to process chat request',
        details: errorMessage,
        timestamp: new Date().toISOString()
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});
