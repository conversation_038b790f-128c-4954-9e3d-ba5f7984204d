<template>
  <div class="phone-number-input-container">
    <div class="row q-col-gutter-md">
      <div class="col-12 col-sm-6">
        <q-select
          v-model="countryCode"
          :options="countryCodes"
          label="Country"
          outlined
          use-input
          input-debounce="300"
          option-label="country_name"
          option-value="value"
          emit-value
          map-options
          :loading="loading"
          @filter="filterCountries"
          class="country-select"
        >
          <template v-slot:no-option>
            <q-item>
              <q-item-section class="text-grey">
                No results
              </q-item-section>
            </q-item>
          </template>
          <template v-slot:append>
            <q-icon name="arrow_drop_down" />
          </template>
          <template v-slot:selected>
            <div>{{ getSelectedCountryName() }}</div>
          </template>
        </q-select>
      </div>
      <div class="col-12 col-sm-6">
        <q-input
          v-model="phoneNumber"
          label="Phone Number"
          outlined
          :rules="[
            (val: string) => !val || /^\d{1,15}$/.test(val) || 'Please enter a valid phone number'
          ]"
          hint="Enter digits only (e.g., 771234567)"
          class="phone-input"
        >
          <template v-slot:prepend>
            <q-icon name="phone" />
          </template>
        </q-input>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { QSelect, QInput, QIcon } from 'quasar'
import { supabase } from '@/lib/supabase'

const props = defineProps<{
  modelValue: {
    countryCode?: string
    number?: string
  }
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: { countryCode: string, number: string }): void
}>()

const countryCode = ref(props.modelValue?.countryCode || '+263') // Default to Zimbabwe
const phoneNumber = ref(props.modelValue?.number || '')
const loading = ref(false)
const allCountryCodes = ref<any[]>([])
const countryCodes = ref<any[]>([])

// Fetch countries from Supabase
async function fetchCountries() {
  loading.value = true
  try {
    const { data, error } = await supabase
      .from('ref_countries')
      .select('country_name, country_code, phone_code')
      .order('country_name')

    if (error) throw error

    if (data) {
      // Format country codes for display
      allCountryCodes.value = data.map(country => ({
        label: country.country_name,
        value: `+${country.phone_code}`,
        country_name: country.country_name,
        country_code: country.country_code,
        phone_code: country.phone_code
      }))

      // Initialize the displayed list
      countryCodes.value = [...allCountryCodes.value]
    }
  } catch (error) {
    console.error('Error fetching countries:', error)
    // Fallback to a basic list if the fetch fails
    setFallbackCountryCodes()
  } finally {
    loading.value = false
  }
}

// Filter countries based on user input
function filterCountries(val, update) {
  if (val === '') {
    update(() => {
      countryCodes.value = allCountryCodes.value
    })
    return
  }

  update(() => {
    const needle = val.toLowerCase()
    countryCodes.value = allCountryCodes.value.filter(
      v => v.country_name.toLowerCase().indexOf(needle) > -1
    )
  })
}

// Get the selected country name for display
function getSelectedCountryName() {
  if (!countryCode.value) return ''

  const selectedCountry = allCountryCodes.value.find(c => c.value === countryCode.value)
  return selectedCountry ? selectedCountry.country_name : ''
}

// Fallback country codes in case the API call fails
function setFallbackCountryCodes() {
  allCountryCodes.value = [
    { label: 'Zimbabwe', value: '+263', country_name: 'Zimbabwe' },
    { label: 'South Africa', value: '+27', country_name: 'South Africa' },
    { label: 'Botswana', value: '+267', country_name: 'Botswana' },
    { label: 'Zambia', value: '+260', country_name: 'Zambia' },
    { label: 'Mozambique', value: '+258', country_name: 'Mozambique' },
    { label: 'Namibia', value: '+264', country_name: 'Namibia' },
    { label: 'Kenya', value: '+254', country_name: 'Kenya' },
    { label: 'Nigeria', value: '+234', country_name: 'Nigeria' },
    { label: 'Ghana', value: '+233', country_name: 'Ghana' },
    { label: 'United States', value: '+1', country_name: 'United States' },
    { label: 'United Kingdom', value: '+44', country_name: 'United Kingdom' },
    { label: 'India', value: '+91', country_name: 'India' },
    { label: 'China', value: '+86', country_name: 'China' },
    { label: 'Australia', value: '+61', country_name: 'Australia' },
  ]
  countryCodes.value = [...allCountryCodes.value]
}

// Watch for changes in the local values and emit updates
watch([countryCode, phoneNumber], () => {
  emit('update:modelValue', {
    countryCode: countryCode.value,
    number: phoneNumber.value
  })
}, { immediate: true })

// Watch for changes in the modelValue prop
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    // Only update if the values are different to avoid circular updates
    if (countryCode.value !== newValue.countryCode && newValue.countryCode) {
      countryCode.value = newValue.countryCode
    }
    if (phoneNumber.value !== newValue.number && newValue.number) {
      phoneNumber.value = newValue.number
    }
  }
}, { deep: true })



onMounted(() => {
  fetchCountries()
})
</script>

<style scoped>
.phone-number-input-container {
  width: 100%;
  padding: 0;
}

.country-select, .phone-input {
  width: 100%;
}

/* Mobile-specific styles */
@media (max-width: 599px) {
  .phone-number-input-container {
    padding: 0 4px;
  }

  .row.q-col-gutter-md {
    margin: 0 -8px;
  }

  .q-field__control {
    padding: 0 8px;
  }

  .q-field__marginal {
    height: 48px;
  }
}
</style>