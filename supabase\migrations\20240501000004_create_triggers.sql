-- Migration: Create triggers
-- Description: Creates triggers for automatic timestamp updates and other functionality

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for profiles table
DROP TRIGGER IF EXISTS update_profiles_updated_at ON public.profiles;
CREATE TRIGGER update_profiles_updated_at
BEFORE UPDATE ON public.profiles
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Create trigger for innovator_profiles table
DROP TRIGGER IF EXISTS update_innovator_profiles_updated_at ON public.innovator_profiles;
CREATE TRIGGER update_innovator_profiles_updated_at
BEFORE UPDATE ON public.innovator_profiles
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();
