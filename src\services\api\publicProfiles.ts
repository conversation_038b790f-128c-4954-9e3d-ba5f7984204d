import { supabase } from '../../lib/supabase'
import type { BaseProfile } from '../profileService'

/**
 * Interface for public profile data
 */
export interface PublicProfile {
  user_id: string
  first_name: string | null
  last_name: string | null
  email: string | null
  profile_name: string | null
  profile_type: string | null
  profile_state: string | null
  profile_visibility: string | null
  profile_completion: number | null
  base_bio: string | null
  specialized_bio: string | null
  avatar_url: string | null
  website: string | null
  linkedin: string | null
  contact_email: string | null
  created_at: string | null
  updated_at: string | null
}

/**
 * Fetch a public profile by user ID
 *
 * @param userId The user ID to fetch the profile for
 * @returns The public profile data or null if not found/not public
 */
export async function fetchPublicProfile(userId: string): Promise<PublicProfile | null> {
  try {
    console.log(`fetchPublicProfile: Fetching profile for user ${userId}`)

    // Use personal_details table directly - fetch all profiles regardless of completion
    const { data, error } = await supabase
      .from('personal_details')
      .select('*')
      .eq('user_id', userId)
      .single()

    console.log(`fetchPublicProfile: Query result for user ${userId}:`, { data, error })

    if (error) {
      // If the error is that no rows were returned, the profile doesn't exist
      if (error.code === 'PGRST116') {
        console.log(`fetchPublicProfile: No profile found for user ${userId}`)
        return null
      }

      console.error('fetchPublicProfile: Error fetching profile:', error)
      throw error
    }

    // Convert the personal_details record to a PublicProfile format
    const publicProfile: PublicProfile = {
      user_id: data.user_id,
      first_name: data.first_name,
      last_name: data.last_name,
      email: data.email,
      profile_name: data.profile_name,
      profile_type: data.profile_type,
      profile_state: data.profile_state,
      profile_visibility: 'public', // Always set to public - all profiles are publicly visible
      profile_completion: data.profile_completion,
      base_bio: data.bio,
      specialized_bio: null, // We don't have this in personal_details
      avatar_url: data.avatar_url,
      website: null,
      linkedin: null,
      contact_email: data.email,
      created_at: data.created_at,
      updated_at: data.updated_at
    }

    console.log(`fetchPublicProfile: Profile successfully fetched for user ${userId}:`, publicProfile)
    return publicProfile
  } catch (err) {
    console.error('Error in fetchPublicProfile:', err)
    throw err
  }
}

/**
 * Load public profiles with pagination and filtering
 *
 * @param page Page number for pagination
 * @param limit Number of profiles per page
 * @param filters Additional filters to apply
 * @returns An array of public profiles with pagination info
 */
export async function loadPublicProfiles(
  page: number = 1,
  limit: number = 12,
  filters: Record<string, any> = {}
): Promise<{ profiles: PublicProfile[], hasMore: boolean, total: number }> {
  try {
    console.log('Loading profiles with filters:', filters)

    // Calculate the range for pagination
    const from = (page - 1) * limit
    const to = from + limit - 1

    // Build the query using personal_details table
    let query = supabase
      .from('personal_details')
      .select('*', { count: 'exact' })
      .order('profile_completion', { ascending: false })
      .range(from, to)

    // Apply additional filters if provided
    if (filters.profileType && filters.profileType.length > 0) {
      query = query.in('profile_type', filters.profileType)
    }

    if (filters.searchQuery && filters.searchQuery.trim() !== '') {
      const searchTerm = `%${filters.searchQuery.trim().toLowerCase()}%`
      query = query.or(`first_name.ilike.${searchTerm},last_name.ilike.${searchTerm},bio.ilike.${searchTerm}`)
    }

    const { data, error, count } = await query

    if (error) {
      console.error('Error loading profiles:', error)
      throw error
    }

    const totalCount = count || 0
    const hasMore = totalCount > (page * limit)

    console.log(`Loaded ${data?.length || 0} profiles (total: ${totalCount})`)

    // Convert the personal_details records to PublicProfile format
    const profiles = (data || []).map(record => ({
      user_id: record.user_id,
      first_name: record.first_name,
      last_name: record.last_name,
      email: record.email,
      profile_name: record.profile_name,
      profile_type: record.profile_type,
      profile_state: record.profile_state,
      profile_visibility: 'public', // Always set to public - all profiles are publicly visible
      profile_completion: record.profile_completion,
      base_bio: record.bio,
      specialized_bio: null, // We don't have this in personal_details
      avatar_url: record.avatar_url,
      website: null,
      linkedin: null,
      contact_email: record.email,
      created_at: record.created_at,
      updated_at: record.updated_at
    } as PublicProfile));

    return {
      profiles,
      hasMore,
      total: totalCount
    }
  } catch (err) {
    console.error('Error in loadPublicProfiles:', err)
    throw err
  }
}

/**
 * Convert a PublicProfile to a BaseProfile format for compatibility
 *
 * @param publicProfile The public profile to convert
 * @returns A BaseProfile object
 */
export function convertToBaseProfile(publicProfile: PublicProfile): BaseProfile {
  return {
    user_id: publicProfile.user_id,
    email: publicProfile.email || '',
    first_name: publicProfile.first_name || '',
    last_name: publicProfile.last_name || '',
    profile_name: publicProfile.profile_name || '',
    profile_state: publicProfile.profile_state as any || 'ACTIVE',
    profile_type: publicProfile.profile_type || null,
    profile_visibility: publicProfile.profile_visibility as any || 'public',
    role: 'user',
    is_verified: true,
    profile_completion: publicProfile.profile_completion || 0,
    created_at: publicProfile.created_at || new Date().toISOString(),
    updated_at: publicProfile.updated_at || new Date().toISOString(),
    last_login_at: null,
    bio: publicProfile.specialized_bio || publicProfile.base_bio || '',
    avatar_url: publicProfile.avatar_url || '',
    // Add any other required fields with default values
    phone_country_code: null,
    phone_number: null,
    gender: null,
    hear_about_us: null
  }
}
