declare module '@chenfengyuan/vue-countdown' {
  import { DefineComponent } from 'vue'
  
  interface CountdownProps {
    autoStart?: boolean
    emit?: string[]
    tag?: string
    time: number
    transform?: (props: { days: number; hours: number; minutes: number; seconds: number; milliseconds: number }) => string
  }

  const component: DefineComponent<CountdownProps>
  export default component
} 