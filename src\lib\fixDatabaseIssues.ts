import { supabase } from './supabase';
import { applyPostsViewMigration } from './applyPostsViewMigration';
import { applyConnectionsMigration } from './applyConnectionsMigration';

/**
 * Fixes database issues by applying necessary migrations
 */
export async function fixDatabaseIssues() {
  try {
    console.log('Fixing database issues...');
    
    // Step 1: Apply the posts_with_authors view migration
    const postsViewResult = await applyPostsViewMigration();
    if (!postsViewResult.success) {
      console.error('Failed to apply posts_with_authors view migration:', postsViewResult.error);
      return { 
        success: false, 
        error: postsViewResult.error,
        postsViewFixed: false,
        connectionsTableFixed: false
      };
    }
    
    // Step 2: Apply the user_connections table migration
    const connectionsResult = await applyConnectionsMigration();
    if (!connectionsResult.success) {
      console.error('Failed to apply user_connections table migration:', connectionsResult.error);
      return { 
        success: false, 
        error: connectionsResult.error,
        postsViewFixed: true,
        connectionsTableFixed: false
      };
    }
    
    console.log('Database issues fixed successfully');
    return { 
      success: true,
      postsViewFixed: true,
      connectionsTableFixed: true
    };
  } catch (error) {
    console.error('Error fixing database issues:', error);
    return { 
      success: false, 
      error,
      postsViewFixed: false,
      connectionsTableFixed: false
    };
  }
}
