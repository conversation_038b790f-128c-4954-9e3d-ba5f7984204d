<template>
  <q-card class="event-card">
    <!-- Event Categories at the top -->
    <div class="q-pa-sm bg-grey-2 row items-center">
      <q-badge
        color="orange"
        class="q-mr-sm"
        size="lg"
      >
        Event
      </q-badge>
      <q-badge
        v-if="event.type"
        :color="getEventTypeColor(event.type)"
        class="q-mr-sm"
        size="lg"
      >
        {{ event.type }}
      </q-badge>
      <q-badge
        v-if="event.category && event.category !== event.type"
        color="secondary"
        class="q-mr-sm"
        size="lg"
      >
        {{ event.category }}
      </q-badge>
    </div>

    <div class="event-image-container">
      <q-img
        :src="eventImage"
        :ratio="16/9"
        class="event-image"
        @error="handleImageError"
        no-spinner
        no-transition
      >
        <template v-slot:error>
          <div class="absolute-full flex flex-center bg-grey-3 text-grey-8">
            <div class="text-center">
              <q-icon name="broken_image" size="3em" />
              <div>Image failed to load</div>
              <q-btn
                v-if="isImageUrlFixable"
                flat
                color="primary"
                label="Try Fix URL"
                class="q-mt-sm"
                @click="tryFixImageUrl"
              />
            </div>
          </div>
        </template>
      </q-img>
    </div>

    <q-card-section>
      <div class="text-h6">{{ event.title }}</div>

      <div class="row items-center q-mb-sm">
        <q-icon name="event" size="xs" class="q-mr-xs" />
        <span class="text-caption">{{ event.date }}</span>
      </div>

      <div class="row items-center q-mb-sm">
        <q-icon name="location_on" size="xs" class="q-mr-xs" />
        <span class="text-caption">{{ event.location }}</span>
      </div>

      <div v-if="event.theme" class="row items-center q-mb-sm">
        <q-icon name="label" size="xs" class="q-mr-xs" />
        <span class="text-caption">{{ event.theme }}</span>
      </div>

      <p class="text-body2">{{ truncatedDescription }}</p>

      <!-- Tags -->
      <div v-if="eventTags.length > 0" class="q-mt-sm">
        <q-chip
          v-for="tag in eventTags"
          :key="tag"
          size="sm"
          outline
          color="primary"
          class="q-mr-xs"
        >
          #{{ tag }}
        </q-chip>
      </div>
    </q-card-section>

    <q-separator />
    <q-card-actions>
      <interaction-buttons
        :content-id="event.id"
        content-type="post"
        :content-data="getContentData()"
        :is-saved="false"
        :likes-count="event.likes || 0"
        :comments-count="event.commentsCount || event.comments_count || event.comments || 0"
        :show-comments="showComments"
        :show-contact="false"
        :show-view-details="true"
        :dynamic-c-t-a="dynamicCTA"
        size="md"
        @comment="toggleComments"
        @share="handleShare"
        @save="handleSave"
        @view-details="handleViewDetails"
        @dynamic-c-t-a="handleDynamicCTA"
      />

      <!-- Additional event-specific button -->
      <q-btn flat color="grey" icon="event" @click="handleAddToCalendar" class="q-ml-sm">
        <q-tooltip>Add to Calendar</q-tooltip>
      </q-btn>
    </q-card-actions>

    <!-- Comments Section -->
    <q-card-section v-if="showComments">
      <q-separator class="q-my-md" />
      <div class="text-h6 q-mb-md">Comments</div>

      <div v-if="loadingComments" class="text-center q-pa-sm">
        <q-spinner color="primary" size="2em" />
        <p class="q-ma-none">Loading comments...</p>
      </div>

      <div v-else-if="!commentsData.length" class="text-center q-pa-sm">
        <p class="q-ma-none">No comments yet. Be the first to comment!</p>
      </div>

      <div v-else>
        <q-list>
          <q-item v-for="comment in commentsData" :key="comment.id" class="q-py-sm">
            <q-item-section avatar top>
              <user-avatar
                :name="comment.author"
                :email="comment.email"
                :avatar-url="comment.avatar"
                :user-id="comment.authorId"
                size="32px"
                @click="comment.authorId && navigateToUserProfile(comment.authorId)"
              />
            </q-item-section>
            <q-item-section>
              <q-item-label class="text-weight-bold">{{ comment.author }}</q-item-label>
              <q-item-label caption>{{ comment.date }}</q-item-label>
              <div class="q-mt-xs">{{ comment.content }}</div>
            </q-item-section>
          </q-item>
        </q-list>
      </div>

      <!-- Comment Form -->
      <div class="q-mt-md">
        <div class="row items-center">
          <user-avatar
            name="Current User"
            size="32px"
            class="q-mr-sm"
            :clickable="false"
          />
          <q-input
            v-model="newComment"
            dense
            outlined
            placeholder="Write a comment..."
            class="col"
            @keyup.enter="!submittingComment && submitComment()"
          >
            <template v-slot:after>
              <q-btn
                round
                dense
                flat
                color="primary"
                icon="send"
                @click="submitComment"
                :disable="!newComment.trim() || submittingComment"
                :loading="submittingComment"
              />
            </template>
          </q-input>
        </div>
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useRouter } from 'vue-router';
import InteractionButtons from '../../common/InteractionButtons.vue';
import UserAvatar from '../../common/UserAvatar.vue';
import { truncateText, stripHtml } from '../../../utils/textUtils';
import { useContentInteractions } from '../../../composables/useContentInteractions';

const router = useRouter();
const contentInteractions = useContentInteractions();

// Local state for fixed image URL
const fixedImageUrl = ref('');

// Comment state
const showComments = ref(false);
const newComment = ref('');
const loadingComments = ref(false);
const submittingComment = ref(false);
const commentsData = ref([]);

const props = defineProps({
  event: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['register', 'share', 'save', 'addToCalendar', 'viewDetails', 'comment']);

// Get the event image
const eventImage = computed(() => {
  // If we have a fixed URL from a previous fix attempt, use that
  if (fixedImageUrl.value) {
    return fixedImageUrl.value;
  }

  const imageUrl = props.event.image || '';

  // Only log in development mode
  if (process.env.NODE_ENV === 'development') {
    console.log('Event image URL for event ID:', props.event.id, 'URL:', imageUrl);
  }

  return imageUrl || 'https://picsum.photos/id/3/500/300';
});

// Check if the image URL is potentially fixable
const isImageUrlFixable = computed(() => {
  const url = props.event.image || '';

  // If it's empty, it's not fixable
  if (!url) return false;

  // If it already has the correct full Supabase URL format, it's not fixable
  if (url.includes('dpicnvisvxpmgjtbeicf.supabase.co/storage/v1/object/public/imagefiles/') &&
      !url.includes('imagefiles/imagefiles/')) {
    return false;
  }

  // If it's an external URL (not Supabase), it's not fixable
  if (url.startsWith('http') && !url.includes('supabase') && !url.includes('imagefiles')) {
    return false;
  }

  // If it has the wrong format (includes imagefiles but not in the right place), it's fixable
  if (url.includes('imagefiles/') &&
      (!url.includes('storage/v1/object/public/imagefiles/') ||
       url.includes('imagefiles/imagefiles/'))) {
    return true;
  }

  // Otherwise, we might be able to fix it
  return true;
});

// Truncate event description for feed view
const truncatedDescription = computed(() => {
  let description = props.event.description || '';

  // Extract content from JSON if needed
  if (typeof description === 'string' && (description.startsWith('{') || description.startsWith('['))) {
    try {
      const parsedContent = JSON.parse(description);

      // Handle different JSON structures
      if (parsedContent.description) {
        description = parsedContent.description;
      } else if (parsedContent.eventDetails && parsedContent.eventDetails.description) {
        description = parsedContent.eventDetails.description;
      } else if (parsedContent.content) {
        description = parsedContent.content;
      } else if (typeof parsedContent === 'object') {
        // If it's an object but doesn't have expected fields, stringify it
        description = JSON.stringify(parsedContent);
      }
    } catch (e) {
      // If parsing fails, it's not valid JSON, keep the original content
      console.log('Failed to parse content as JSON:', e);
    }
  }

  // Strip HTML tags if present
  if (/<[a-z][\s\S]*>/i.test(description)) {
    description = stripHtml(description);
  }

  return truncateText(description, 250);
});

// Process event tags to ensure they're displayed correctly
const eventTags = computed(() => {
  if (!props.event.tags) return [];

  // If tags is a string (possibly from HTML), convert it to an array
  if (typeof props.event.tags === 'string') {
    // Try to parse as JSON if it looks like a JSON array
    if (props.event.tags.startsWith('[') && props.event.tags.endsWith(']')) {
      try {
        return JSON.parse(props.event.tags);
      } catch (e) {
        console.log('Failed to parse tags JSON:', e);
        // If parsing fails, split by comma as fallback
        return props.event.tags.replace(/[\[\]"']/g, '').split(',').map((tag: string) => tag.trim());
      }
    }
    // If it's not JSON-like, split by comma
    return props.event.tags.split(',').map((tag: string) => tag.trim());
  }

  // If it's already an array, process each item to handle object tags
  return props.event.tags.map((tag: any) => {
    // Check if the tag is an object with label/value properties (from select components)
    if (typeof tag === 'object' && tag !== null) {
      // Always prioritize the value property for display
      if (tag.value) return tag.value;
      // If no value, but has a label property, use that
      if (tag.label) return tag.label;
      // Otherwise return a generic tag name instead of stringifying
      return 'Tag';
    }
    // If it's a string or number, return as is
    return tag;
  });
});

// Dynamic CTA for events (Register button)
const dynamicCTA = computed(() => {
  return {
    label: 'Register',
    icon: 'event_available',
    color: 'orange',
    action: 'register'
  };
});

// Content data for InteractionButtons
const getContentData = () => {
  return {
    id: props.event.id,
    title: props.event.title || props.event.name,
    content: props.event.content || props.event.description,
    description: props.event.description,
    postType: 'event',
    subType: 'event',
    userId: props.event.userId || props.event.organizerId,
    author: props.event.organizer || props.event.author,
    isSaved: false,
    isFavorite: false
  };
};

// Methods
function handleDynamicCTA() {
  // Handle the register action from dynamic CTA
  handleRegister();
}

function handleRegister() {
  emit('register', props.event.id);

  // If the event has a registration link, open it
  if (props.event.link) {
    window.open(props.event.link, '_blank');
  }
}

function handleShare() {
  emit('share', props.event.id);
}

function handleSave() {
  emit('save', props.event.id);
}

function handleAddToCalendar() {
  emit('addToCalendar', props.event.id);
}

function handleViewDetails() {
  emit('viewDetails', props.event.id);

  // Navigate to the post details view
  router.push({
    name: 'post-details',
    params: { id: props.event.id }
  });
}

// Comment methods using unified system
async function toggleComments() {
  await contentInteractions.toggleComments(
    props.event.id,
    'post',
    showComments,
    commentsData,
    loadingComments
  );
}

async function submitComment() {
  const success = await contentInteractions.submitComment(
    props.event.id,
    'post',
    newComment.value,
    commentsData,
    newComment,
    submittingComment
  );

  if (success) {
    // Note: Comment count is already updated by the posts store, no need to duplicate here
    emit('comment', {
      postId: props.event.id,
      content: newComment.value
    });
  }
}

// Navigation helper
function navigateToUserProfile(userId: string) {
  router.push({ name: 'user-profile', params: { id: userId } });
}

// Helper function to get event type color
function getEventTypeColor(type: string): string {
  const typeColors: Record<string, string> = {
    'Physical': 'green',
    'Virtual': 'blue',
    'Hybrid': 'purple',
    'Workshop': 'teal',
    'Conference': 'deep-orange',
    'Networking': 'indigo',
    'Hackathon': 'red',
    'Pitch Competition': 'amber'
  };

  return typeColors[type] || 'primary';
}

// Handle image loading errors
function handleImageError(err: Error) {
  // Only log in development mode
  if (process.env.NODE_ENV === 'development') {
    console.error('Image failed to load for event ID:', props.event.id, 'Error:', err);
    console.error('Failed image URL:', eventImage.value);
  }

  // If the URL is from Supabase but doesn't have the full path, suggest a fix
  if (isImageUrlFixable.value && process.env.NODE_ENV === 'development') {
    console.log('This image URL might be fixable. Try clicking the "Try Fix URL" button.');
  }
}

// Try to fix the image URL by adding the Supabase storage prefix
function tryFixImageUrl() {
  const originalUrl = props.event.image || '';

  // If the URL is empty, there's nothing to fix
  if (!originalUrl) return;

  // Only log in development mode
  if (process.env.NODE_ENV === 'development') {
    console.log('Attempting to fix image URL:', originalUrl);
  }

  // If it's a relative path or partial Supabase path
  if (!originalUrl.startsWith('http')) {
    // If it already includes the bucket name
    if (originalUrl.includes('imagefiles/')) {
      // Extract the file path after 'imagefiles/'
      const match = originalUrl.match(/imagefiles\/(.+)$/);
      if (match && match[1]) {
        fixedImageUrl.value = `https://dpicnvisvxpmgjtbeicf.supabase.co/storage/v1/object/public/imagefiles/${match[1]}`;
      } else {
        fixedImageUrl.value = `https://dpicnvisvxpmgjtbeicf.supabase.co/storage/v1/object/public/${originalUrl}`;
      }
    } else {
      // Assume it's in the imagefiles bucket
      fixedImageUrl.value = `https://dpicnvisvxpmgjtbeicf.supabase.co/storage/v1/object/public/imagefiles/${originalUrl}`;
    }
  } else if (originalUrl.includes('supabase')) {
    if (!originalUrl.includes('/storage/v1/object/public/')) {
      // It's a Supabase URL but missing the storage path
      const parts = originalUrl.split('/');
      const fileName = parts[parts.length - 1];
      fixedImageUrl.value = `https://dpicnvisvxpmgjtbeicf.supabase.co/storage/v1/object/public/imagefiles/${fileName}`;
    } else if (originalUrl.includes('imagefiles/') && !originalUrl.includes('/storage/v1/object/public/imagefiles/')) {
      // It has the wrong format for the bucket path
      const match = originalUrl.match(/imagefiles\/(.+)$/);
      if (match && match[1]) {
        fixedImageUrl.value = `https://dpicnvisvxpmgjtbeicf.supabase.co/storage/v1/object/public/imagefiles/${match[1]}`;
      }
    }
  }

  // Only log in development mode
  if (process.env.NODE_ENV === 'development') {
    console.log('Fixed image URL:', fixedImageUrl.value);
  }
}
</script>

<style scoped>
.event-card {
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  overflow: hidden;
}

.event-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Event image styling */
.event-image-container {
  position: relative;
  overflow: hidden;
}

.event-image {
  width: 100%;
  transition: transform 0.3s ease;
}

.event-image:hover {
  transform: scale(1.02);
}

/* Event card actions styling */
.event-card-actions {
  background-color: white;
  position: relative;
  z-index: 2;
  padding-top: 4px;
}

@media (max-width: 599px) {
  .event-card {
    margin: 0 8px 16px 8px;
    width: calc(100% - 16px);
  }

  /* Adjust action buttons for mobile */
  .event-card-actions .q-btn {
    padding: 4px 8px;
    min-height: 32px;
  }

  /* Stack buttons on small screens */
  .event-card-actions {
    flex-wrap: wrap;
    justify-content: flex-end;
  }
}
</style>
