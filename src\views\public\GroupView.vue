<template>
  <q-page class="group-view-page q-pa-md">
    <div class="container">
      <div v-if="loading" class="text-center q-pa-lg">
        <q-spinner color="primary" size="3em" />
        <p>Loading group...</p>
      </div>

      <div v-else-if="error" class="text-center q-pa-lg text-negative">
        <p>{{ error }}</p>
        <q-btn color="primary" label="Go Back" @click="$router.go(-1)" />
      </div>

      <template v-else-if="group">
        <!-- Group Header -->
        <q-card flat bordered class="group-header q-mb-md">
          <q-img
            :src="group.image"
            height="200px"
          />

          <q-card-section>
            <div class="row items-center">
              <div class="col-12 col-md-8">
                <div class="text-h4">{{ group.name }}</div>
                <div class="text-subtitle1 q-mt-sm">
                  <q-badge color="primary" class="q-mr-sm">{{ group.category }}</q-badge>
                  <span class="text-grey-7">{{ group.members }} members</span>
                </div>
              </div>

              <div class="col-12 col-md-4 text-right q-gutter-sm">
                <q-btn
                  :color="isJoined ? 'negative' : 'primary'"
                  :label="isJoined ? 'Leave Group' : 'Join Group'"
                  @click="toggleJoinGroup"
                />
                <q-btn color="grey" icon="share" flat round>
                  <q-tooltip>Share Group</q-tooltip>
                </q-btn>
              </div>
            </div>
          </q-card-section>
        </q-card>

        <!-- Group Content -->
        <div class="row q-col-gutter-md">
          <!-- Left Column - Info -->
          <div class="col-12 col-md-4">
            <q-card flat bordered>
              <q-card-section>
                <div class="text-h6">About</div>
                <q-separator class="q-my-md" />

                <p>{{ group.description }}</p>

                <div class="text-subtitle2 q-mt-lg">Topics</div>
                <div class="q-gutter-xs q-mt-sm">
                  <q-chip
                    v-for="(topic, index) in group.topics"
                    :key="index"
                    color="grey-3"
                    text-color="grey-8"
                  >
                    {{ topic }}
                  </q-chip>
                </div>

                <div class="text-subtitle2 q-mt-lg">Recent Activity</div>
                <div class="q-mt-sm">
                  <q-item dense>
                    <q-item-section avatar>
                      <q-icon name="update" color="primary" />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>{{ group.lastActivity }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </div>
              </q-card-section>
            </q-card>

            <!-- Members -->
            <q-card flat bordered class="q-mt-md">
              <q-card-section>
                <div class="text-h6">Members</div>
                <q-separator class="q-my-md" />

                <div class="text-center q-pa-md">
                  <q-avatar v-for="n in 5" :key="n" size="40px" class="q-mr-sm">
                    <img :src="`https://cdn.quasar.dev/img/avatar${n}.jpg`" />
                  </q-avatar>

                  <div class="q-mt-sm">
                    <q-btn flat color="primary" label="View All Members" />
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>

          <!-- Right Column - Activity -->
          <div class="col-12 col-md-8">
            <q-card flat bordered>
              <q-tabs
                v-model="activeTab"
                class="text-primary"
                active-color="primary"
                indicator-color="primary"
                align="justify"
                narrow-indicator
              >
                <q-tab name="discussions" label="Discussions" icon="forum" />
                <q-tab name="events" label="Events" icon="event" />
                <q-tab name="resources" label="Resources" icon="folder" />
              </q-tabs>

              <q-separator />

              <q-tab-panels v-model="activeTab" animated>
                <q-tab-panel name="discussions">
                  <div class="row items-center justify-between q-mb-md">
                    <div class="text-h6">Recent Discussions</div>
                    <q-btn color="primary" label="New Post" />
                  </div>

                  <q-list separator>
                    <q-item v-for="(post, index) in groupPosts" :key="index" class="q-py-md">
                      <q-item-section avatar top>
                        <q-avatar>
                          <img :src="post.avatar" />
                        </q-avatar>
                      </q-item-section>

                      <q-item-section>
                        <q-item-label class="text-weight-bold">{{ post.author }}</q-item-label>
                        <q-item-label caption>{{ post.date }}</q-item-label>
                        <div class="q-mt-sm">{{ post.content }}</div>

                        <div class="row q-gutter-sm q-mt-sm">
                          <q-btn flat dense size="sm" color="grey" icon="thumb_up" :label="post.likes" />
                          <q-btn flat dense size="sm" color="grey" icon="chat_bubble_outline" :label="post.comments" />
                        </div>
                      </q-item-section>
                    </q-item>
                  </q-list>

                  <div class="text-center q-mt-md">
                    <q-btn outline color="primary" label="Load More" />
                  </div>
                </q-tab-panel>

                <q-tab-panel name="events">
                  <div class="text-h6">Upcoming Events</div>
                  <p class="text-grey text-center q-pa-md">
                    No upcoming events
                  </p>
                </q-tab-panel>

                <q-tab-panel name="resources">
                  <div class="text-h6">Shared Resources</div>
                  <p class="text-grey text-center q-pa-md">
                    No resources shared yet
                  </p>
                </q-tab-panel>
              </q-tab-panels>
            </q-card>
          </div>
        </div>
      </template>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { usePostsStore } from '../../stores/posts';

const route = useRoute();
const groupId = route.params.id as string;

const group = ref(null);
const loading = ref(true);
const error = ref(null);
const activeTab = ref('discussions');
const isJoined = ref(false);
const groupPosts = ref([]);

onMounted(async () => {
  try {
    loading.value = true;
    error.value = null;

    // Fetch group from database (groups are stored as posts)
    const postsStore = usePostsStore();
    const groupPost = await postsStore.getPostById(Number(groupId));

    if (groupPost && groupPost.postType === 'group') {
      // Map post data to group format
      group.value = {
        id: groupPost.id,
        name: groupPost.title || 'Untitled Group',
        description: groupPost.content || '',
        image: groupPost.featuredImage || groupPost.imageUrl || 'https://placehold.co/600x200/e0e0e0/ffffff?text=Group',
        category: groupPost.category || 'General',
        members: 0, // TODO: Get actual member count from group_memberships table
        createdAt: groupPost.createdAt,
        creator: groupPost.author || 'Unknown'
      };

      // Fetch posts related to this group
      await postsStore.fetchPosts({ groupId: Number(groupId) });
      groupPosts.value = postsStore.posts.slice(0, 3);
    } else {
      error.value = 'Group not found';
    }
  } catch (err) {
    console.error('Error fetching group:', err);
    error.value = 'Failed to load group';
  } finally {
    loading.value = false;
  }
});

function toggleJoinGroup() {
  isJoined.value = !isJoined.value;

  if (isJoined.value) {
    // In a real implementation, this would be an API call to join the group
    console.log('Joined group:', groupId);
  } else {
    // In a real implementation, this would be an API call to leave the group
    console.log('Left group:', groupId);
  }
}
</script>

<style scoped>
.group-view-page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}
</style>
