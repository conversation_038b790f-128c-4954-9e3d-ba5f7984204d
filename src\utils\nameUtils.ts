/**
 * Utility functions for handling user names
 */

/**
 * Extracts a display name from an email address
 * 
 * @param email - The email address to extract a name from
 * @returns A formatted display name
 */
export function getNameFromEmail(email: string): string {
  if (!email || typeof email !== 'string') {
    return 'User';
  }

  try {
    // Extract the part before the @ symbol
    const namePart = email.split('@')[0];
    
    if (!namePart) {
      return 'User';
    }

    // Remove numbers, dots, underscores, and other special characters
    const cleanName = namePart
      .replace(/[0-9]/g, '') // Remove numbers
      .replace(/[._-]/g, ' ') // Replace dots, underscores, and hyphens with spaces
      .replace(/\+.*$/, '') // Remove everything after a plus sign (common in email aliases)
      .trim();
    
    if (!cleanName) {
      return 'User';
    }

    // Capitalize each word
    return cleanName
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  } catch (error) {
    console.error('Error extracting name from email:', error);
    return 'User';
  }
}

/**
 * Gets initials from a name or email
 * 
 * @param nameOrEmail - The name or email to extract initials from
 * @returns The initials (up to 2 characters)
 */
export function getInitials(nameOrEmail: string): string {
  if (!nameOrEmail || typeof nameOrEmail !== 'string') {
    return 'U';
  }

  try {
    // If it looks like an email, extract the name part first
    if (nameOrEmail.includes('@')) {
      nameOrEmail = getNameFromEmail(nameOrEmail);
    }

    // Split by spaces and get the first letter of each part
    const parts = nameOrEmail.split(' ').filter(part => part.length > 0);
    
    if (parts.length === 0) {
      return 'U';
    }
    
    if (parts.length === 1) {
      // If only one part, return the first letter
      return parts[0].charAt(0).toUpperCase();
    }
    
    // Return first letter of first part and first letter of last part
    return (parts[0].charAt(0) + parts[parts.length - 1].charAt(0)).toUpperCase();
  } catch (error) {
    console.error('Error getting initials:', error);
    return 'U';
  }
}
