<template>
  <q-icon :name="iconName" :size="size" :color="color" />
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps({
  /**
   * Icon name
   * For Material Icons, use the icon name directly (e.g., 'home', 'person')
   */
  name: {
    type: String,
    required: true
  },

  /**
   * Icon size
   * Can be 'xs', 'sm', 'md', 'lg', 'xl', or a specific size (e.g., '24px')
   */
  size: {
    type: String,
    default: 'md'
  },

  /**
   * Icon color
   * Can be any valid CSS color or Quasar color (e.g., 'primary', 'secondary')
   */
  color: {
    type: String,
    default: 'primary'
  }
});

// Map of custom icon names to Material Icons
const iconMap: Record<string, string> = {
  // Social media icons
  'facebook': 'fab fa-facebook',
  'twitter': 'fab fa-twitter',
  'linkedin': 'fab fa-linkedin',
  'instagram': 'fab fa-instagram',
  
  // Common icons
  'arrow_back': 'arrow_back',
  'arrow_forward': 'arrow_forward',
  'event': 'event',
  'content_copy': 'content_copy',
  'person': 'person',
  'mail': 'mail',
  'phone': 'phone',
  'location_on': 'location_on',
  'search': 'search',
  'menu': 'menu',
  'close': 'close',
  'check': 'check',
  'info': 'info',
  'warning': 'warning',
  'error': 'error',
  'help': 'help',
  'settings': 'settings',
  'edit': 'edit',
  'delete': 'delete',
  'add': 'add',
  'remove': 'remove',
  'favorite': 'favorite',
  'star': 'star',
  'home': 'home',
  'dashboard': 'dashboard',
  'calendar': 'calendar',
  'chat': 'chat',
  'notifications': 'notifications',
  'account_circle': 'account_circle',
  'groups': 'groups',
  'business': 'business',
  'school': 'school',
  'work': 'work',
  'attach_money': 'attach_money',
  'trending_up': 'trending_up',
  'trending_down': 'trending_down',
  'cloud_upload': 'cloud_upload',
  'cloud_download': 'cloud_download',
  'file_download': 'file_download',
  'file_upload': 'file_upload',
  'share': 'share',
  'link': 'link',
  'send': 'send',
  'visibility': 'visibility',
  'visibility_off': 'visibility_off',
  'lock': 'lock',
  'lock_open': 'lock_open',
  'security': 'security',
  'verified': 'verified',
  'thumb_up': 'thumb_up',
  'thumb_down': 'thumb_down',
  'comment': 'comment',
  'forum': 'forum',
  'email': 'email',
  'call': 'call',
  'videocam': 'videocam',
  'photo_camera': 'photo_camera',
  'image': 'image',
  'movie': 'movie',
  'music_note': 'music_note',
  'book': 'book',
  'description': 'description',
  'article': 'article',
  'assignment': 'assignment',
  'folder': 'folder',
  'folder_open': 'folder_open',
  'cloud': 'cloud',
  'code': 'code',
  'terminal': 'terminal',
  'laptop': 'laptop',
  'desktop_windows': 'desktop_windows',
  'smartphone': 'smartphone',
  'tablet': 'tablet',
  'watch': 'watch',
  'keyboard': 'keyboard',
  'mouse': 'mouse',
  'print': 'print',
  'scanner': 'scanner',
  'wifi': 'wifi',
  'bluetooth': 'bluetooth',
  'network_wifi': 'network_wifi',
  'network_cell': 'network_cell',
  'battery_full': 'battery_full',
  'battery_charging_full': 'battery_charging_full',
  'power': 'power',
  'ac_unit': 'ac_unit',
  'eco': 'eco',
  'spa': 'spa',
  'local_florist': 'local_florist',
  'local_cafe': 'local_cafe',
  'local_dining': 'local_dining',
  'local_bar': 'local_bar',
  'local_hotel': 'local_hotel',
  'local_airport': 'local_airport',
  'local_atm': 'local_atm',
  'local_parking': 'local_parking',
  'local_pharmacy': 'local_pharmacy',
  'local_hospital': 'local_hospital',
  'local_grocery_store': 'local_grocery_store',
  'local_mall': 'local_mall',
  'local_offer': 'local_offer',
  'local_shipping': 'local_shipping',
  'local_taxi': 'local_taxi',
  'directions_car': 'directions_car',
  'directions_bus': 'directions_bus',
  'directions_railway': 'directions_railway',
  'directions_bike': 'directions_bike',
  'directions_walk': 'directions_walk',
  'directions_boat': 'directions_boat',
  'flight': 'flight',
  'hotel': 'hotel',
  'restaurant': 'restaurant',
  'store': 'store',
  'shopping_cart': 'shopping_cart',
  'shopping_basket': 'shopping_basket',
  'add_shopping_cart': 'add_shopping_cart',
  'credit_card': 'credit_card',
  'account_balance': 'account_balance',
  'account_balance_wallet': 'account_balance_wallet',
  'payment': 'payment',
  'money': 'money',
  'receipt': 'receipt',
  'euro': 'euro',
  'attach_file': 'attach_file',
  'attach_email': 'attach_email',
  'cloud_circle': 'cloud_circle',
  'cloud_done': 'cloud_done',
  'cloud_off': 'cloud_off',
  'cloud_queue': 'cloud_queue',
  'cloud_upload': 'cloud_upload',
  'cloud_download': 'cloud_download',
  'backup': 'backup',
  'restore': 'restore',
  'save': 'save',
  'save_alt': 'save_alt',
  'print': 'print',
  'share': 'share',
  'send': 'send',
  'delete': 'delete',
  'delete_forever': 'delete_forever',
  'delete_outline': 'delete_outline',
  'delete_sweep': 'delete_sweep',
  'report': 'report',
  'report_problem': 'report_problem',
  'warning': 'warning',
  'error': 'error',
  'error_outline': 'error_outline',
  'info': 'info',
  'info_outline': 'info_outline',
  'help': 'help',
  'help_outline': 'help_outline',
  'feedback': 'feedback',
  'announcement': 'announcement',
  'notifications': 'notifications',
  'notifications_active': 'notifications_active',
  'notifications_none': 'notifications_none',
  'notifications_off': 'notifications_off',
  'notifications_paused': 'notifications_paused',
  'notifications_important': 'notifications_important',
  'priority_high': 'priority_high',
  'flag': 'flag',
  'bookmark': 'bookmark',
  'bookmark_border': 'bookmark_border',
  'favorite': 'favorite',
  'favorite_border': 'favorite_border',
  'star': 'star',
  'star_border': 'star_border',
  'star_half': 'star_half',
  'stars': 'stars',
  'grade': 'grade',
  'thumb_up': 'thumb_up',
  'thumb_down': 'thumb_down',
  'thumbs_up_down': 'thumbs_up_down',
  'sentiment_satisfied': 'sentiment_satisfied',
  'sentiment_dissatisfied': 'sentiment_dissatisfied',
  'sentiment_neutral': 'sentiment_neutral',
  'sentiment_very_satisfied': 'sentiment_very_satisfied',
  'sentiment_very_dissatisfied': 'sentiment_very_dissatisfied',
  'mood': 'mood',
  'mood_bad': 'mood_bad',
  'face': 'face',
  'face_retouching_natural': 'face_retouching_natural',
  'face_unlock': 'face_unlock',
  'fingerprint': 'fingerprint',
  'security': 'security',
  'verified_user': 'verified_user',
  'verified': 'verified',
  'lock': 'lock',
  'lock_open': 'lock_open',
  'lock_outline': 'lock_outline',
  'enhanced_encryption': 'enhanced_encryption',
  'no_encryption': 'no_encryption',
  'wifi_lock': 'wifi_lock',
  'wifi_tethering': 'wifi_tethering',
  'vpn_key': 'vpn_key',
  'vpn_lock': 'vpn_lock',
  'security': 'security',
  'shield': 'shield',
  'gpp_good': 'gpp_good',
  'gpp_maybe': 'gpp_maybe',
  'gpp_bad': 'gpp_bad',
  'admin_panel_settings': 'admin_panel_settings',
  'settings': 'settings',
  'settings_applications': 'settings_applications',
  'settings_backup_restore': 'settings_backup_restore',
  'settings_bluetooth': 'settings_bluetooth',
  'settings_brightness': 'settings_brightness',
  'settings_cell': 'settings_cell',
  'settings_ethernet': 'settings_ethernet',
  'settings_input_antenna': 'settings_input_antenna',
  'settings_input_component': 'settings_input_component',
  'settings_input_composite': 'settings_input_composite',
  'settings_input_hdmi': 'settings_input_hdmi',
  'settings_input_svideo': 'settings_input_svideo',
  'settings_overscan': 'settings_overscan',
  'settings_phone': 'settings_phone',
  'settings_power': 'settings_power',
  'settings_remote': 'settings_remote',
  'settings_voice': 'settings_voice',
  'tune': 'tune',
  'equalizer': 'equalizer',
  'graphic_eq': 'graphic_eq',
  'vibration': 'vibration',
  'mobile_friendly': 'mobile_friendly',
  'mobile_off': 'mobile_off',
  'signal_cellular_4_bar': 'signal_cellular_4_bar',
  'signal_cellular_alt': 'signal_cellular_alt',
  'signal_cellular_connected_no_internet_4_bar': 'signal_cellular_connected_no_internet_4_bar',
  'signal_cellular_no_sim': 'signal_cellular_no_sim',
  'signal_cellular_null': 'signal_cellular_null',
  'signal_cellular_off': 'signal_cellular_off',
  'signal_wifi_4_bar': 'signal_wifi_4_bar',
  'signal_wifi_4_bar_lock': 'signal_wifi_4_bar_lock',
  'signal_wifi_off': 'signal_wifi_off',
  'storage': 'storage',
  'usb': 'usb',
  'wallpaper': 'wallpaper',
  'widgets': 'widgets',
  'wifi_tethering': 'wifi_tethering',
  'add_to_home_screen': 'add_to_home_screen',
  'device_thermostat': 'device_thermostat',
  'phonelink': 'phonelink',
  'phonelink_off': 'phonelink_off',
  'phonelink_ring': 'phonelink_ring',
  'phonelink_setup': 'phonelink_setup',
  'keyboard_voice': 'keyboard_voice',
  'keyboard_hide': 'keyboard_hide',
  'keyboard_arrow_down': 'keyboard_arrow_down',
  'keyboard_arrow_left': 'keyboard_arrow_left',
  'keyboard_arrow_right': 'keyboard_arrow_right',
  'keyboard_arrow_up': 'keyboard_arrow_up',
  'keyboard_backspace': 'keyboard_backspace',
  'keyboard_capslock': 'keyboard_capslock',
  'keyboard_return': 'keyboard_return',
  'keyboard_tab': 'keyboard_tab',
  'keyboard_control': 'keyboard_control',
  'keyboard_alt': 'keyboard_alt',
  'keyboard_voice': 'keyboard_voice',
  'laptop': 'laptop',
  'laptop_chromebook': 'laptop_chromebook',
  'laptop_mac': 'laptop_mac',
  'laptop_windows': 'laptop_windows',
  'desktop_mac': 'desktop_mac',
  'desktop_windows': 'desktop_windows',
  'computer': 'computer',
  'monitor': 'monitor',
  'tablet': 'tablet',
  'tablet_android': 'tablet_android',
  'tablet_mac': 'tablet_mac',
  'phone_android': 'phone_android',
  'phone_iphone': 'phone_iphone',
  'smartphone': 'smartphone',
  'speaker': 'speaker',
  'speaker_group': 'speaker_group',
  'headset': 'headset',
  'headset_mic': 'headset_mic',
  'mic': 'mic',
  'mic_none': 'mic_none',
  'mic_off': 'mic_off',
  'volume_up': 'volume_up',
  'volume_down': 'volume_down',
  'volume_mute': 'volume_mute',
  'volume_off': 'volume_off',
  'cast': 'cast',
  'cast_connected': 'cast_connected',
  'cast_for_education': 'cast_for_education',
  'computer': 'computer',
  'desktop_mac': 'desktop_mac',
  'desktop_windows': 'desktop_windows',
  'developer_board': 'developer_board',
  'device_hub': 'device_hub',
  'devices_other': 'devices_other',
  'dock': 'dock',
  'gamepad': 'gamepad',
  'headset': 'headset',
  'headset_mic': 'headset_mic',
  'keyboard': 'keyboard',
  'keyboard_arrow_down': 'keyboard_arrow_down',
  'keyboard_arrow_left': 'keyboard_arrow_left',
  'keyboard_arrow_right': 'keyboard_arrow_right',
  'keyboard_arrow_up': 'keyboard_arrow_up',
  'keyboard_backspace': 'keyboard_backspace',
  'keyboard_capslock': 'keyboard_capslock',
  'keyboard_hide': 'keyboard_hide',
  'keyboard_return': 'keyboard_return',
  'keyboard_tab': 'keyboard_tab',
  'keyboard_voice': 'keyboard_voice',
  'laptop': 'laptop',
  'laptop_chromebook': 'laptop_chromebook',
  'laptop_mac': 'laptop_mac',
  'laptop_windows': 'laptop_windows',
  'memory': 'memory',
  'mouse': 'mouse',
  'phone_android': 'phone_android',
  'phone_iphone': 'phone_iphone',
  'phonelink': 'phonelink',
  'phonelink_off': 'phonelink_off',
  'power_input': 'power_input',
  'router': 'router',
  'scanner': 'scanner',
  'security': 'security',
  'sim_card': 'sim_card',
  'smartphone': 'smartphone',
  'speaker': 'speaker',
  'speaker_group': 'speaker_group',
  'tablet': 'tablet',
  'tablet_android': 'tablet_android',
  'tablet_mac': 'tablet_mac',
  'toys': 'toys',
  'tv': 'tv',
  'videogame_asset': 'videogame_asset',
  'watch': 'watch',
};

// Get the icon name based on the provided name
const iconName = computed(() => {
  // If the name is in our map, return the mapped icon
  if (iconMap[props.name]) {
    return iconMap[props.name];
  }
  
  // Otherwise, return the name as is (assuming it's a valid Material Icon name)
  return props.name;
});
</script>
