// Professional Profile Questions
import { ProfileType } from '../types';
import { commonOptions } from '../common';

// Professional profile questions
export const professionalProfile: ProfileType = {
  type: 'professional',
  displayName: 'Professional',
  sections: [
    {
      title: 'Professional Details',
      icon: 'work',
      description: 'Tell us about your professional background',
      questions: [
        {
          id: 'industry',
          name: 'industry',
          label: 'Industry',
          type: 'select',
          required: true,
          options: 'industryOptions',
          hint: 'What industry do you work in?'
        },
        {
          id: 'job_title',
          name: 'job_title',
          label: 'Job Title',
          type: 'text',
          required: true,
          hint: 'What is your current job title?'
        },
        {
          id: 'company',
          name: 'company',
          label: 'Company',
          type: 'text',
          required: true,
          hint: 'What company do you work for?'
        },
        {
          id: 'company_size',
          name: 'company_size',
          label: 'Company Size',
          type: 'select',
          options: 'companySizeOptions',
          hint: 'How large is your company?'
        },
        {
          id: 'department',
          name: 'department',
          label: 'Department',
          type: 'select',
          options: 'departmentOptions',
          hint: 'Which department do you work in?'
        },
        {
          id: 'years_of_experience',
          name: 'years_of_experience',
          label: 'Years of Experience',
          type: 'number',
          required: true,
          hint: 'How many years of professional experience do you have?'
        }
      ]
    },
    {
      title: 'Skills & Expertise',
      icon: 'psychology',
      description: 'Tell us about your skills and expertise',
      questions: [
        {
          id: 'skills',
          name: 'skills',
          label: 'Skills',
          type: 'multi-select',
          required: true,
          options: 'skillsOptions',
          fullWidth: true,
          hint: 'What are your key professional skills?'
        },
        {
          id: 'certifications',
          name: 'certifications',
          label: 'Certifications',
          type: 'multi-select-free',
          fullWidth: true,
          hint: 'What professional certifications do you hold?'
        },
        {
          id: 'achievements',
          name: 'achievements',
          label: 'Professional Achievements',
          type: 'multi-select-free',
          fullWidth: true,
          hint: 'What are your key professional achievements?'
        },
        {
          id: 'languages',
          name: 'languages',
          label: 'Languages',
          type: 'multi-select',
          options: 'languageOptions',
          hint: 'What languages do you speak?'
        }
      ]
    },
    {
      title: 'Professional Presence',
      icon: 'public',
      description: 'Share your professional online presence',
      questions: [
        {
          id: 'linkedin',
          name: 'linkedin',
          label: 'LinkedIn Profile',
          type: 'text',
          hint: 'URL to your LinkedIn profile'
        },
        {
          id: 'website',
          name: 'website',
          label: 'Portfolio URL',
          type: 'text',
          hint: 'URL to your professional portfolio'
        },
        {
          id: 'github',
          name: 'github',
          label: 'GitHub Profile',
          type: 'text',
          hint: 'URL to your GitHub profile (if applicable)'
        },
        {
          id: 'other_social',
          name: 'other_social',
          label: 'Other Professional Profiles',
          type: 'multi-select-free',
          fullWidth: true,
          hint: 'URLs to other professional profiles or websites'
        }
      ]
    }
  ],
  options: {
    ...commonOptions,
    companySizeOptions: [
      '1-10 employees', '11-50 employees', '51-200 employees',
      '201-500 employees', '501-1000 employees', '1001-5000 employees',
      '5001-10000 employees', '10000+ employees', 'Other'
    ],
    departmentOptions: [
      'Executive Leadership', 'Information Technology', 'Engineering',
      'Research & Development', 'Product Management', 'Marketing',
      'Sales', 'Business Development', 'Operations', 'Finance',
      'Human Resources', 'Legal', 'Customer Service', 'Administration', 'Other'
    ],
    skillsOptions: [
      // Technical Skills
      'Software Development', 'Data Analysis', 'Project Management',
      'Cloud Computing', 'Cybersecurity', 'AI/Machine Learning',
      'DevOps', 'Database Management', 'Network Administration', 'UI/UX Design',

      // Business Skills
      'Strategic Planning', 'Business Analysis', 'Financial Management',
      'Risk Management', 'Change Management', 'Process Optimization',
      'Team Leadership', 'Stakeholder Management', 'Negotiation', 'Problem Solving',

      // Soft Skills
      'Communication', 'Team Collaboration', 'Critical Thinking',
      'Decision Making', 'Time Management', 'Adaptability',
      'Innovation', 'Mentoring', 'Presentation Skills', 'Conflict Resolution',
      'Other'
    ]
  }
}
