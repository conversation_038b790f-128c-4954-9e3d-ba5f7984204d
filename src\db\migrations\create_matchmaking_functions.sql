-- Create matchmaking functions

-- Function to calculate array intersection score
CREATE OR REP<PERSON>CE FUNCTION public.array_intersection_score(
  array1 TEXT[],
  array2 TEXT[]
) RETURNS FLOAT AS $$
DECLARE
  intersection_count INTEGER;
BEGIN
  IF array1 IS NULL OR array2 IS NULL OR array_length(array1, 1) IS NULL OR array_length(array2, 1) IS NULL THEN
    RETURN 0;
  END IF;
  
  SELECT COUNT(*) INTO intersection_count
  FROM (
    SELECT UNNEST(array1) AS item
    INTERSECT
    SELECT UNNEST(array2) AS item
  ) AS intersection;
  
  RETURN intersection_count::FLOAT / SQRT(array_length(array1, 1) * array_length(array2, 1));
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to calculate text similarity score
CREATE OR REPLACE FUNCTION public.text_similarity_score(
  text1 TEXT,
  text2 TEXT
) RETURNS FLOAT AS $$
DECLARE
  words1 TEXT[];
  words2 TEXT[];
BEGIN
  IF text1 IS NULL OR text2 IS NULL THEN
    RETURN 0;
  END IF;
  
  -- Normalize texts
  text1 := LOWER(text1);
  text2 := LOWER(text2);
  
  -- Check for exact match
  IF text1 = text2 THEN
    RETURN 1;
  END IF;
  
  -- Check if one contains the other
  IF position(text1 IN text2) > 0 OR position(text2 IN text1) > 0 THEN
    RETURN 0.7;
  END IF;
  
  -- Split into words and check for word overlap
  words1 := regexp_split_to_array(text1, '\s+');
  words2 := regexp_split_to_array(text2, '\s+');
  
  RETURN public.array_intersection_score(words1, words2);
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to extract interests from a profile
CREATE OR REPLACE FUNCTION public.extract_profile_interests(
  profile_id UUID,
  profile_type TEXT
) RETURNS SETOF public.user_interests AS $$
DECLARE
  user_id UUID;
  interest_record RECORD;
BEGIN
  -- Get the user_id for the profile
  EXECUTE format('SELECT user_id FROM %I WHERE %I = $1', 
    profile_type || '_profiles', 
    CASE 
      WHEN profile_type = 'innovator' THEN 'id'
      ELSE profile_type || '_profile_id'
    END
  ) USING profile_id INTO user_id;
  
  IF user_id IS NULL THEN
    RETURN;
  END IF;
  
  -- Delete existing interests for this user with source = profile_type
  DELETE FROM public.user_interests 
  WHERE user_id = user_id AND source = profile_type;
  
  -- Extract common interests across all profile types
  IF profile_type = 'innovator' THEN
    -- Extract interests from innovator profile
    FOR interest_record IN
      SELECT 
        jsonb_array_elements_text(interests) AS value
      FROM innovator_profiles
      WHERE id = profile_id AND interests IS NOT NULL
    LOOP
      INSERT INTO public.user_interests (user_id, interest_type, interest_value, weight, source)
      VALUES (user_id, 'general', interest_record.value, 1.0, profile_type)
      ON CONFLICT (user_id, interest_type, interest_value) DO NOTHING
      RETURNING *;
    END LOOP;
    
    -- Extract industry
    FOR interest_record IN
      SELECT 
        jsonb_array_elements_text(industry) AS value
      FROM innovator_profiles
      WHERE id = profile_id AND industry IS NOT NULL
    LOOP
      INSERT INTO public.user_interests (user_id, interest_type, interest_value, weight, source)
      VALUES (user_id, 'industry', interest_record.value, 1.0, profile_type)
      ON CONFLICT (user_id, interest_type, interest_value) DO NOTHING
      RETURNING *;
    END LOOP;
    
    -- Extract challenges
    FOR interest_record IN
      SELECT 
        jsonb_array_elements_text(current_challenges) AS value
      FROM innovator_profiles
      WHERE id = profile_id AND current_challenges IS NOT NULL
    LOOP
      INSERT INTO public.user_interests (user_id, interest_type, interest_value, weight, source)
      VALUES (user_id, 'challenge', interest_record.value, 1.0, profile_type)
      ON CONFLICT (user_id, interest_type, interest_value) DO NOTHING
      RETURNING *;
    END LOOP;
    
  ELSIF profile_type = 'investor' THEN
    -- Extract interests from investor profile
    FOR interest_record IN
      SELECT 
        jsonb_array_elements_text(investment_focus) AS value
      FROM investor_profiles
      WHERE investor_profile_id = profile_id AND investment_focus IS NOT NULL
    LOOP
      INSERT INTO public.user_interests (user_id, interest_type, interest_value, weight, source)
      VALUES (user_id, 'industry', interest_record.value, 1.0, profile_type)
      ON CONFLICT (user_id, interest_type, interest_value) DO NOTHING
      RETURNING *;
    END LOOP;
    
  ELSIF profile_type = 'mentor' THEN
    -- Extract interests from mentor profile
    FOR interest_record IN
      SELECT 
        jsonb_array_elements_text(expertise_areas) AS value
      FROM mentor_profiles
      WHERE mentor_profile_id = profile_id AND expertise_areas IS NOT NULL
    LOOP
      INSERT INTO public.user_interests (user_id, interest_type, interest_value, weight, source)
      VALUES (user_id, 'expertise', interest_record.value, 1.0, profile_type)
      ON CONFLICT (user_id, interest_type, interest_value) DO NOTHING
      RETURNING *;
    END LOOP;
    
    FOR interest_record IN
      SELECT 
        jsonb_array_elements_text(industry_experience) AS value
      FROM mentor_profiles
      WHERE mentor_profile_id = profile_id AND industry_experience IS NOT NULL
    LOOP
      INSERT INTO public.user_interests (user_id, interest_type, interest_value, weight, source)
      VALUES (user_id, 'industry', interest_record.value, 1.0, profile_type)
      ON CONFLICT (user_id, interest_type, interest_value) DO NOTHING
      RETURNING *;
    END LOOP;
  END IF;
  
  -- Extract SDG alignment for all profile types
  EXECUTE format('
    SELECT jsonb_array_elements_text(sdg_alignment) AS value
    FROM %I
    WHERE %I = $1 AND sdg_alignment IS NOT NULL',
    profile_type || '_profiles',
    CASE 
      WHEN profile_type = 'innovator' THEN 'id'
      ELSE profile_type || '_profile_id'
    END
  ) USING profile_id INTO interest_record;
  
  IF interest_record.value IS NOT NULL THEN
    INSERT INTO public.user_interests (user_id, interest_type, interest_value, weight, source)
    VALUES (user_id, 'sdg', interest_record.value, 1.0, profile_type)
    ON CONFLICT (user_id, interest_type, interest_value) DO NOTHING
    RETURNING *;
  END IF;
  
  RETURN QUERY SELECT * FROM public.user_interests WHERE user_id = user_id AND source = profile_type;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to generate matches for a user
CREATE OR REPLACE FUNCTION public.generate_user_matches(
  user_id UUID,
  profile_type TEXT
) RETURNS SETOF public.matchmaking_results AS $$
DECLARE
  source_profile_id UUID;
  target_profile_record RECORD;
  rule_record RECORD;
  match_score FLOAT;
  match_reasons JSONB := '{}'::JSONB;
  match_id UUID;
BEGIN
  -- Get the profile ID for the user
  EXECUTE format('SELECT %I FROM %I WHERE user_id = $1', 
    CASE 
      WHEN profile_type = 'innovator' THEN 'id'
      ELSE profile_type || '_profile_id'
    END,
    profile_type || '_profiles'
  ) USING user_id INTO source_profile_id;
  
  IF source_profile_id IS NULL THEN
    RETURN;
  END IF;
  
  -- Delete existing matches for this user and profile type
  DELETE FROM public.matchmaking_results 
  WHERE user_id = user_id AND entity_type = profile_type;
  
  -- Get all matching rules for this profile type
  FOR rule_record IN
    SELECT * FROM public.matchmaking_rules
    WHERE source_profile_type = profile_type
  LOOP
    -- Get all profiles of the target type
    EXECUTE format('
      SELECT 
        user_id AS target_user_id,
        %I AS target_profile_id,
        *
      FROM %I
      WHERE user_id != $1',
      CASE 
        WHEN rule_record.target_profile_type = 'innovator' THEN 'id'
        ELSE rule_record.target_profile_type || '_profile_id'
      END,
      rule_record.target_profile_type || '_profiles'
    ) USING user_id INTO target_profile_record;
    
    -- Calculate match score based on rule
    -- This is a simplified implementation - in a real system, you would implement
    -- more sophisticated matching logic based on the rule type
    match_score := 0.5; -- Default score
    
    -- Insert the match result
    INSERT INTO public.matchmaking_results (
      user_id,
      matched_entity_id,
      entity_type,
      match_score,
      match_reasons,
      is_viewed,
      is_saved
    ) VALUES (
      user_id,
      target_profile_record.target_user_id,
      rule_record.target_profile_type,
      match_score,
      match_reasons,
      false,
      false
    )
    ON CONFLICT (user_id, matched_entity_id, entity_type) 
    DO UPDATE SET 
      match_score = EXCLUDED.match_score,
      match_reasons = EXCLUDED.match_reasons,
      updated_at = NOW()
    RETURNING id INTO match_id;
    
    RETURN QUERY SELECT * FROM public.matchmaking_results WHERE id = match_id;
  END LOOP;
  
  RETURN;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger function to extract interests when a profile is created or updated
CREATE OR REPLACE FUNCTION public.extract_interests_on_profile_change() RETURNS TRIGGER AS $$
DECLARE
  profile_id UUID;
  profile_type TEXT;
BEGIN
  profile_type := TG_TABLE_NAME;
  profile_type := REPLACE(profile_type, '_profiles', '');
  
  IF profile_type = 'innovator' THEN
    profile_id := NEW.id;
  ELSE
    profile_id := NEW.profile_id;
  END IF;
  
  PERFORM public.extract_profile_interests(profile_id, profile_type);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers for each profile type
DO $$
DECLARE
  profile_type TEXT;
BEGIN
  FOR profile_type IN 
    SELECT table_name FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_name LIKE '%_profiles' 
    AND table_name NOT LIKE 'deleted_%'
  LOOP
    EXECUTE format('
      DROP TRIGGER IF EXISTS extract_interests_trigger ON %I;
      CREATE TRIGGER extract_interests_trigger
      AFTER INSERT OR UPDATE
      ON %I
      FOR EACH ROW
      EXECUTE FUNCTION public.extract_interests_on_profile_change();
    ', profile_type, profile_type);
  END LOOP;
END;
$$;
