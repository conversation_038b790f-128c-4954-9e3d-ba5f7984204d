<template>
  <q-page padding>
    <div class="row q-col-gutter-md">
      <div class="col-12">
        <div class="row items-center q-mb-md">
          <div class="col">
            <h3 class="q-mt-none q-mb-none">Matchmaking System</h3>
            <p class="q-mt-sm q-mb-none text-body1">
              Find connections and content based on your profile information, goals, and interests.
            </p>
          </div>
        </div>

        <!-- Tabs for different matchmaking types -->
        <q-tabs
          v-model="activeTab"
          class="text-primary q-mb-md"
          active-color="primary"
          indicator-color="primary"
          align="left"
          narrow-indicator
        >
          <q-tab name="profiles" label="Profile Matches" icon="people" />
          <q-tab name="content" label="Content Matches" icon="article" />
        </q-tabs>

        <q-separator class="q-mb-md" />

        <!-- Profile Matchmaking Tab -->
        <div v-if="activeTab === 'profiles'" class="row q-col-gutter-md">
          <div class="col-12 col-md-3">
            <q-card class="q-mb-md">
              <q-card-section>
                <div class="text-h6">How Matchmaking Works</div>
                <p class="q-mt-sm q-mb-none text-body2">
                  Our AI-powered matchmaking system analyzes your profile data to find the best connections for you.
                </p>
              </q-card-section>
              <q-separator />
              <q-card-section>
                <div class="row q-col-gutter-sm">
                  <div class="col-12">
                    <q-item>
                      <q-item-section avatar>
                        <q-icon name="person_search" color="primary" size="sm" />
                      </q-item-section>
                      <q-item-section>
                        <q-item-label>Profile Analysis</q-item-label>
                        <q-item-label caption>
                          We analyze your goals, interests, and profile data
                        </q-item-label>
                      </q-item-section>
                    </q-item>
                  </div>
                  <div class="col-12">
                    <q-item>
                      <q-item-section avatar>
                        <q-icon name="auto_awesome" color="primary" size="sm" />
                      </q-item-section>
                      <q-item-section>
                        <q-item-label>Smart Matching</q-item-label>
                        <q-item-label caption>
                          Our algorithm finds compatible connections
                        </q-item-label>
                      </q-item-section>
                    </q-item>
                  </div>
                  <div class="col-12">
                    <q-item>
                      <q-item-section avatar>
                        <q-icon name="handshake" color="primary" size="sm" />
                      </q-item-section>
                      <q-item-section>
                        <q-item-label>Connect</q-item-label>
                        <q-item-label caption>
                          Reach out to your matches and collaborate
                        </q-item-label>
                      </q-item-section>
                    </q-item>
                  </div>
                </div>
              </q-card-section>
            </q-card>

            <q-card>
              <q-card-section>
                <div class="text-h6">Match Filters</div>
                <p class="q-mt-sm q-mb-none text-body2">
                  Refine your matches based on profile type and match quality
                </p>
              </q-card-section>

              <q-separator />

              <q-card-section>
                <div class="q-mb-md">
                  <div class="text-subtitle2 q-mb-sm">Profile Type</div>
                  <q-option-group
                    v-model="selectedEntityType"
                    :options="entityTypeOptions"
                    color="primary"
                    type="radio"
                  />
                </div>

                <div class="q-mb-md">
                  <div class="text-subtitle2 q-mb-sm">
                    Match Quality ({{ Math.round(minScore * 100) }}%)
                    <q-tooltip>Higher percentages show only the strongest matches</q-tooltip>
                  </div>
                  <q-slider
                    v-model="minScore"
                    :min="0"
                    :max="1"
                    :step="0.05"
                    label
                    label-always
                    color="primary"
                    :label-value="`${Math.round(minScore * 100)}%`"
                  />
                </div>

                <div class="q-mt-lg">
                  <q-btn
                    color="primary"
                    icon="refresh"
                    label="Generate Matches"
                    class="full-width"
                    :loading="matchmakingStore.isLoading"
                    @click="generateMatches"
                  />
                </div>
              </q-card-section>
            </q-card>
          </div>

          <div class="col-12 col-md-9">
            <div v-if="!matchmakingStore.initialized" class="text-center q-pa-lg">
              <q-spinner color="primary" size="3em" />
              <div class="q-mt-sm">Initializing matchmaking system...</div>
            </div>
            <match-list
              v-else
              :title="matchListTitle"
              :profile-type="userProfileType"
              :entity-type="selectedEntityType === 'all' ? undefined : selectedEntityType"
              :min-score="minScore"
              :limit="10"
              @update="refreshMatches"
            />
          </div>
        </div>

        <!-- Content Matchmaking Tab -->
        <div v-else-if="activeTab === 'content'" class="row q-col-gutter-md">
          <div class="col-12 text-center">
            <!-- CONTENT MATCHMAKING LINK - TEMPORARILY DISABLED -->
            <!-- <q-btn
              color="primary"
              icon="open_in_new"
              label="Go to Content Matchmaking"
              to="/dashboard/content-matchmaking"
              class="q-mb-md"
            /> -->
            <q-btn
              color="grey"
              icon="construction"
              label="Content Matchmaking - Coming Soon"
              disable
              class="q-mb-md"
            />
            <p class="text-body1">
              Content matchmaking feature is temporarily disabled.
            </p>
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useMatchmakingStore } from '@/stores/matchmaking';
import { useNotificationStore } from '@/stores/notifications';
import { useAuthStore } from '@/stores/auth';
import { useProfileStore } from '@/stores/profile';
import { ProfileType, EntityType } from '@/services/matchmakingService';
import MatchList from '@/components/matchmaking/MatchList.vue';

// Stores
const matchmakingStore = useMatchmakingStore();
const notificationStore = useNotificationStore();
const authStore = useAuthStore();
const profileStore = useProfileStore();

// State
const activeTab = ref('profiles');
const selectedEntityType = ref<EntityType | 'all'>('all');
const minScore = ref(0.3);

// Get the user's profile type from the profile store
const userProfileType = computed<ProfileType>(() => {
  if (profileStore.currentProfile?.profile_type) {
    return profileStore.currentProfile.profile_type as ProfileType;
  }
  // Default to innovator if no profile type is found
  return 'innovator';
});

// Computed
const entityTypeOptions = computed(() => {
  const options = [
    { label: 'All', value: 'all' }
  ];

  // Add options based on user's profile type
  if (userProfileType.value === 'innovator') {
    options.push(
      { label: 'Investors', value: 'investor' },
      { label: 'Mentors', value: 'mentor' }
    );
  } else if (userProfileType.value === 'investor') {
    options.push(
      { label: 'Innovators', value: 'innovator' },
      { label: 'Organizations', value: 'organisation' }
    );
  } else if (userProfileType.value === 'mentor') {
    options.push(
      { label: 'Innovators', value: 'innovator' },
      { label: 'Academic Students', value: 'academic_student' }
    );
  } else if (userProfileType.value === 'professional') {
    options.push(
      { label: 'Innovators', value: 'innovator' },
      { label: 'Organizations', value: 'organisation' }
    );
  } else if (userProfileType.value === 'industry_expert') {
    options.push(
      { label: 'Innovators', value: 'innovator' },
      { label: 'Academic Institutions', value: 'academic_institution' }
    );
  } else if (userProfileType.value === 'academic_student') {
    options.push(
      { label: 'Mentors', value: 'mentor' },
      { label: 'Academic Institutions', value: 'academic_institution' }
    );
  } else if (userProfileType.value === 'academic_institution') {
    options.push(
      { label: 'Academic Students', value: 'academic_student' },
      { label: 'Industry Experts', value: 'industry_expert' }
    );
  } else if (userProfileType.value === 'organisation') {
    options.push(
      { label: 'Innovators', value: 'innovator' },
      { label: 'Investors', value: 'investor' }
    );
  }

  return options;
});

const matchListTitle = computed(() => {
  if (selectedEntityType.value === 'all') {
    return 'All Matches';
  }

  // Convert snake_case to Title Case
  return selectedEntityType.value
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ') + ' Matches';
});

// Methods
async function generateMatches() {
  if (!authStore.isAuthenticated) {
    notificationStore.error('You must be logged in to generate matches');
    return;
  }

  try {
    const success = await matchmakingStore.generateMatches(userProfileType.value);

    if (!success) {
      notificationStore.error('Failed to generate matches');
    }
  } catch (error: any) {
    console.error('Error generating matches:', error);
    notificationStore.error(`Error generating matches: ${error.message}`);
  }
}

// Initialize matchmaking when component mounts
onMounted(async () => {
  try {
    // Load user profiles if not already loaded
    if (profileStore.userProfiles.length === 0) {
      await profileStore.loadUserProfiles();
    }

    if (!matchmakingStore.initialized) {
      await matchmakingStore.initialize();
    }
  } catch (error) {
    console.error('Error initializing matchmaking:', error);
    notificationStore.error('Failed to initialize matchmaking system. Please try again later.');
  }
});

// Watch for changes in user profile type
watch(userProfileType, () => {
  // Reset entity type to 'all' when profile type changes
  selectedEntityType.value = 'all';
});

async function refreshMatches() {
  await matchmakingStore.loadMatches(
    selectedEntityType.value === 'all' ? undefined : selectedEntityType.value,
    10,
    0,
    minScore.value
  );
}
</script>
