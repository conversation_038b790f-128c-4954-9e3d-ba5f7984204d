<template>
  <q-badge
    v-if="count > 0"
    :color="color"
    :text-color="textColor"
    :floating="floating"
    :rounded="rounded"
    :class="{ 'pulse-animation': animate && count > 0 }"
  >
    {{ formattedCount }}
  </q-badge>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps({
  count: {
    type: Number,
    default: 0
  },
  maxDisplay: {
    type: Number,
    default: 99
  },
  color: {
    type: String,
    default: 'red'
  },
  textColor: {
    type: String,
    default: 'white'
  },
  floating: {
    type: Boolean,
    default: false
  },
  rounded: {
    type: Boolean,
    default: false
  },
  animate: {
    type: Boolean,
    default: false
  }
});

// Format the count to display with a "+" if it exceeds maxDisplay
const formattedCount = computed(() => {
  if (props.count <= props.maxDisplay) {
    return props.count;
  }
  return `${props.maxDisplay}+`;
});
</script>

<style scoped>
.pulse-animation {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(255, 0, 0, 0.7);
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(255, 0, 0, 0);
  }

  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(255, 0, 0, 0);
  }
}

/* Mobile responsive styles */
@media (max-width: 599px) {
  .q-badge {
    font-size: 0.6rem;
    height: 14px;
    min-width: 14px;
    padding: 0 2px;
  }

  .q-badge.floating {
    top: -4px;
    right: -4px;
  }
}
</style>
