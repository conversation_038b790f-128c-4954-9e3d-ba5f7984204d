<template>
  <div class="event-post-form">
    <q-form @submit="handleSubmit" class="q-gutter-md">
      <!-- Title -->
      <q-input
        v-model="formData.title"
        label="Event Title"
        outlined
        :rules="[val => !!val || 'Title is required', val => val.length <= 100 || 'Maximum 100 characters']"
        counter
        maxlength="100"
      />

      <!-- Event Type -->
      <q-select
        v-model="formData.eventType"
        :options="eventTypeOptions"
        label="Event Type"
        outlined
        emit-value
        map-options
        :rules="[val => !!val || 'Event type is required']"
      >
        <template v-slot:prepend>
          <q-icon name="event" />
        </template>
      </q-select>

      <!-- Event Format -->
      <q-select
        v-model="formData.eventFormat"
        :options="eventFormatOptions"
        label="Event Format"
        outlined
        emit-value
        map-options
        :rules="[val => !!val || 'Event format is required']"
      >
        <template v-slot:prepend>
          <q-icon name="format_list_bulleted" />
        </template>
      </q-select>

      <!-- Event Date & Time -->
      <div class="row q-col-gutter-md">
        <div class="col-12 col-md-6">
          <q-input
            v-model="formData.eventDate"
            label="Event Date"
            outlined
            mask="date"
            :rules="[val => !!val || 'Event date is required', 'date']"
            hint="Format: YYYY/MM/DD"
          >
            <template v-slot:append>
              <q-icon name="event" class="cursor-pointer">
                <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                  <q-date v-model="formData.eventDate" mask="YYYY/MM/DD">
                    <div class="row items-center justify-end">
                      <q-btn v-close-popup label="Close" color="primary" flat />
                    </div>
                  </q-date>
                </q-popup-proxy>
              </q-icon>
            </template>
          </q-input>
        </div>

        <div class="col-12 col-md-6">
          <q-input
            v-model="formData.eventTime"
            label="Event Time"
            outlined
            mask="time"
            :rules="[val => !!val || 'Event time is required', 'time']"
            hint="Format: HH:MM (24-hour)"
          >
            <template v-slot:append>
              <q-icon name="access_time" class="cursor-pointer">
                <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                  <q-time v-model="formData.eventTime" format24h mask="HH:mm">
                    <div class="row items-center justify-end">
                      <q-btn v-close-popup label="Close" color="primary" flat />
                    </div>
                  </q-time>
                </q-popup-proxy>
              </q-icon>
            </template>
          </q-input>
        </div>
      </div>

      <!-- Location -->
      <q-input
        v-model="formData.location"
        label="Location"
        outlined
        :rules="[val => !!val || 'Location is required']"
      >
        <template v-slot:prepend>
          <q-icon name="location_on" />
        </template>
      </q-input>

      <!-- Description -->
      <q-input
        v-model="formData.content"
        type="textarea"
        label="Description"
        outlined
        autogrow
        :rules="[val => !!val || 'Description is required', val => val.length <= 2000 || 'Maximum 2000 characters']"
        counter
        maxlength="2000"
        rows="5"
      />

      <!-- Theme -->
      <q-select
        v-model="formData.eventTheme"
        :options="eventThemeOptions"
        label="Event Theme (optional)"
        outlined
        emit-value
        map-options
      >
        <template v-slot:prepend>
          <q-icon name="style" />
        </template>
      </q-select>

      <!-- Registration URL -->
      <q-input
        v-model="formData.registrationUrl"
        label="Registration URL (optional)"
        outlined
        type="url"
        :rules="[val => !val || isValidUrl(val) || 'Please enter a valid URL']"
      >
        <template v-slot:prepend>
          <q-icon name="link" />
        </template>
      </q-input>

      <!-- Image Upload -->
      <div class="q-mb-md">
        <q-file
          v-model="imageFile"
          label="Add Event Image"
          outlined
          accept=".jpg, .jpeg, .png, .gif"
          @update:model-value="handleImageUpload"
          max-file-size="5242880"
          @rejected="onRejected"
          :rules="[val => !!val || 'Event image is required']"
        >
          <template v-slot:prepend>
            <q-icon name="attach_file" />
          </template>
        </q-file>

        <!-- Image Preview -->
        <div v-if="formData.image" class="image-preview q-mt-sm">
          <q-img :src="formData.image" style="max-height: 200px; max-width: 100%;" />
          <q-btn
            round
            color="negative"
            icon="delete"
            size="sm"
            class="absolute-top-right"
            @click="removeImage"
          />
        </div>
      </div>

      <!-- Predefined Tags -->
      <div class="q-mb-md">
        <div class="text-subtitle2 q-mb-xs">Tags (select multiple)</div>
        <q-select
          v-model="selectedPredefinedTag"
          :options="predefinedTagsFiltered"
          label="Select from predefined tags"
          outlined
          clearable
          use-input
          hide-selected
          fill-input
          @filter="filterTags"
          @update:model-value="addPredefinedTag"
        >
          <template v-slot:prepend>
            <q-icon name="local_offer" />
          </template>
          <template v-slot:no-option>
            <q-item>
              <q-item-section class="text-grey">
                No results
              </q-item-section>
            </q-item>
          </template>
          <template v-slot:after>
            <q-badge color="primary" floating>
              {{ formData.tags.length }}
            </q-badge>
          </template>
        </q-select>
        <div class="text-caption text-grey q-mt-xs">
          Select a tag and click to add it. You can add multiple tags.
        </div>
      </div>

      <!-- Custom Tags -->
      <q-input
        v-model="tagsInput"
        label="Or add custom tags (comma-separated)"
        outlined
        hint="Enter tags separated by commas"
        @blur="processTags"
      >
        <template v-slot:prepend>
          <q-icon name="add_circle" />
        </template>
      </q-input>

      <!-- Tags Display -->
      <div v-if="formData.tags && formData.tags.length > 0" class="q-mb-md q-mt-sm">
        <div class="text-subtitle2 q-mb-xs">Selected Tags:</div>
        <div class="row q-gutter-xs">
          <q-chip
            v-for="(tag, index) in formData.tags"
            :key="index"
            removable
            @remove="removeTag(index)"
            color="primary"
            text-color="white"
            size="md"
          >
            {{ tag }}
          </q-chip>
        </div>
      </div>

      <!-- Visibility -->
      <div class="q-mb-md">
        <q-select
          v-model="formData.visibility"
          :options="visibilityOptions"
          label="Visibility"
          outlined
          emit-value
          map-options
        >
          <template v-slot:prepend>
            <q-icon name="visibility" />
          </template>
        </q-select>
      </div>

      <!-- Action Buttons -->
      <div class="row justify-end q-gutter-sm">
        <q-btn
          label="Cancel"
          color="grey"
          flat
          @click="$emit('cancel')"
        />
        <q-btn
          label="Post"
          type="submit"
          color="primary"
          :loading="loading"
          :disable="loading"
        >
          <template v-slot:loading>
            <q-spinner-dots size="24px" />
          </template>
        </q-btn>
      </div>
    </q-form>
  </div>
</template>

<script setup lang="ts">
import { ref, defineEmits, defineProps, computed } from 'vue';
import { useQuasar } from 'quasar';

const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['submit', 'cancel']);
const $q = useQuasar();

// State
const localLoading = ref(false);
// Computed loading state that combines local and parent loading
const loading = computed(() => {
  return props.loading || localLoading.value;
});

const imageFile = ref(null);
const tagsInput = ref('');
const selectedPredefinedTag = ref(null);

// Get current date in YYYY/MM/DD format for default
const today = new Date();
const year = today.getFullYear();
const month = String(today.getMonth() + 1).padStart(2, '0');
const day = String(today.getDate()).padStart(2, '0');
const defaultDate = `${year}/${month}/${day}`;

// Get current time in HH:MM format for default
const hours = String(today.getHours()).padStart(2, '0');
const minutes = String(today.getMinutes()).padStart(2, '0');
const defaultTime = `${hours}:${minutes}`;

const formData = ref({
  title: '',
  content: '',
  image: null,
  eventType: '',
  eventFormat: '',
  eventDate: defaultDate,
  eventTime: defaultTime,
  location: '',
  eventTheme: '',
  registrationUrl: '',
  tags: [],
  visibility: 'public',
  postType: 'event'
});

// Options
const eventTypeOptions = [
  { label: 'Workshop', value: 'workshop', icon: 'build' },
  { label: 'Conference', value: 'conference', icon: 'groups' },
  { label: 'Networking', value: 'networking', icon: 'handshake' },
  { label: 'Hackathon', value: 'hackathon', icon: 'code' },
  { label: 'Pitch Competition', value: 'pitch_competition', icon: 'campaign' },
  { label: 'Training Session', value: 'training', icon: 'school' },
  { label: 'Webinar', value: 'webinar', icon: 'video_camera_front' },
  { label: 'Meetup', value: 'meetup', icon: 'people' },
  { label: 'Exhibition', value: 'exhibition', icon: 'view_quilt' },
  { label: 'Accelerator Program', value: 'accelerator', icon: 'speed' },
  { label: 'Incubator Program', value: 'incubator', icon: 'egg' },
  { label: 'Funding Round', value: 'funding_round', icon: 'attach_money' }
];

const eventFormatOptions = [
  { label: 'In-Person', value: 'physical', icon: 'location_on' },
  { label: 'Virtual', value: 'virtual', icon: 'computer' },
  { label: 'Hybrid', value: 'hybrid', icon: 'sync_alt' },
  { label: 'On-Demand', value: 'on_demand', icon: 'schedule' }
];

const eventThemeOptions = [
  { label: 'Innovation', value: 'innovation', icon: 'lightbulb' },
  { label: 'Technology', value: 'technology', icon: 'devices' },
  { label: 'Business', value: 'business', icon: 'business' },
  { label: 'Entrepreneurship', value: 'entrepreneurship', icon: 'rocket_launch' },
  { label: 'Funding', value: 'funding', icon: 'attach_money' },
  { label: 'Networking', value: 'networking', icon: 'people' },
  { label: 'Training', value: 'training', icon: 'school' },
  { label: 'Social Impact', value: 'social_impact', icon: 'public' }
];

const visibilityOptions = [
  { label: 'Public', value: 'public', icon: 'public' },
  { label: 'Connections Only', value: 'connections', icon: 'people' },
  { label: 'Private', value: 'private', icon: 'lock' }
];

// Predefined tags based on event types and themes
const predefinedTagOptions = [
  // Event type related tags
  { label: 'workshop', value: 'workshop' },
  { label: 'conference', value: 'conference' },
  { label: 'networking', value: 'networking' },
  { label: 'hackathon', value: 'hackathon' },
  { label: 'pitch-competition', value: 'pitch-competition' },
  { label: 'training', value: 'training' },
  { label: 'webinar', value: 'webinar' },
  { label: 'meetup', value: 'meetup' },
  { label: 'exhibition', value: 'exhibition' },
  { label: 'accelerator', value: 'accelerator' },
  { label: 'incubator', value: 'incubator' },
  { label: 'funding-round', value: 'funding-round' },

  // Event format related tags
  { label: 'in-person', value: 'in-person' },
  { label: 'virtual', value: 'virtual' },
  { label: 'hybrid', value: 'hybrid' },
  { label: 'on-demand', value: 'on-demand' },

  // Event theme related tags
  { label: 'innovation', value: 'innovation' },
  { label: 'technology', value: 'technology' },
  { label: 'business', value: 'business' },
  { label: 'entrepreneurship', value: 'entrepreneurship' },
  { label: 'funding', value: 'funding' },
  { label: 'networking', value: 'networking' },
  { label: 'training', value: 'training' },
  { label: 'social-impact', value: 'social-impact' },

  // Industry specific tags
  { label: 'healthcare', value: 'healthcare' },
  { label: 'education', value: 'education' },
  { label: 'agriculture', value: 'agriculture' },
  { label: 'energy', value: 'energy' },
  { label: 'finance', value: 'finance' },
  { label: 'technology', value: 'technology' },
  { label: 'social-impact', value: 'social-impact' },

  // General event tags
  { label: 'free', value: 'free' },
  { label: 'paid', value: 'paid' },
  { label: 'registration-required', value: 'registration-required' },
  { label: 'limited-seats', value: 'limited-seats' },
  { label: 'certificate', value: 'certificate' },
  { label: 'networking-opportunity', value: 'networking-opportunity' },
  { label: 'speakers', value: 'speakers' },
  { label: 'panel-discussion', value: 'panel-discussion' },
  { label: 'q-and-a', value: 'q-and-a' },
  { label: 'hands-on', value: 'hands-on' }
];

// Filtered predefined tags
const predefinedTagsFiltered = ref(predefinedTagOptions);

// Methods
function handleImageUpload(file) {
  if (!file) return;

  // Create a data URL for preview
  const reader = new FileReader();
  reader.onload = (e) => {
    formData.value.image = e.target.result;
  };
  reader.readAsDataURL(file);

  // Note: The actual upload to Supabase Storage will happen in the posts store
  // when the post is created. We just store the data URL here for preview.
}

function removeImage() {
  formData.value.image = null;
  imageFile.value = null;
}

function onRejected(rejectedEntries) {
  // Display notification for rejected files
  rejectedEntries.forEach(entry => {
    let message = '';
    if (entry.failedPropValidation === 'accept') {
      message = 'Please upload only image files (JPG, PNG, GIF)';
    } else if (entry.failedPropValidation === 'max-file-size') {
      message = 'File is too large. Maximum size is 5MB';
    } else {
      message = 'File upload failed';
    }

    $q.notify({
      type: 'negative',
      message
    });
  });
}

// Filter predefined tags based on user input
function filterTags(val, update) {
  if (val === '') {
    update(() => {
      // Show all options when no search term
      predefinedTagsFiltered.value = predefinedTagOptions;
    });
    return;
  }

  update(() => {
    const needle = val.toLowerCase();
    // Filter options based on search term
    predefinedTagsFiltered.value = predefinedTagOptions.filter(
      v => v.label.toLowerCase().indexOf(needle) > -1
    );
  });
}

// Add a predefined tag to the list
function addPredefinedTag(tag) {
  if (!tag) return;

  // Extract the tag value if it's an object
  let tagValue;
  if (typeof tag === 'object' && tag !== null) {
    // Use the value property if available, otherwise use label
    tagValue = tag.value || tag.label;
  } else {
    tagValue = tag;
  }

  // Add the tag if it's not already in the list
  if (tagValue && !formData.value.tags.includes(tagValue)) {
    formData.value.tags.push(tagValue);
  }

  // Clear the selection for next use
  selectedPredefinedTag.value = null;
}

// Process manually entered tags
function processTags() {
  if (!tagsInput.value) return;

  // Split by comma and trim whitespace
  const newTags = tagsInput.value.split(',')
    .map(tag => tag.trim())
    .filter(tag => tag && !formData.value.tags.includes(tag)); // Only add tags that don't already exist

  // Add new tags to the existing tags
  if (newTags.length > 0) {
    formData.value.tags = [...formData.value.tags, ...newTags];
  }

  // Clear the input
  tagsInput.value = '';
}

// Remove a tag from the list
function removeTag(index) {
  formData.value.tags.splice(index, 1);
}

function isValidUrl(url) {
  try {
    new URL(url);
    return true;
  } catch (e) {
    return false;
  }
}

async function handleSubmit() {
  localLoading.value = true;
  console.log('Submit button clicked, setting loading state to true');

  try {
    // Process tags one more time before submission
    processTags();

    // Validate required fields
    if (!formData.value.title) {
      throw new Error('Event title is required');
    }
    if (!formData.value.eventType) {
      throw new Error('Event type is required');
    }
    if (!formData.value.eventFormat) {
      throw new Error('Event format is required');
    }
    if (!formData.value.eventDate) {
      throw new Error('Event date is required');
    }
    if (!formData.value.eventTime) {
      throw new Error('Event time is required');
    }
    if (!formData.value.location) {
      throw new Error('Event location is required');
    }
    if (!formData.value.content) {
      throw new Error('Event description is required');
    }

    // Automatically add main category and subcategory as tags
    // For events, the main category is always 'event'
    if (!formData.value.tags.includes('event')) {
      formData.value.tags.push('event');
    }

    // Add event type as a tag if it exists and is not already included
    if (formData.value.eventType && !formData.value.tags.includes(formData.value.eventType.toLowerCase())) {
      formData.value.tags.push(formData.value.eventType.toLowerCase());
    }

    // Add event format as a tag if it exists and is not already included
    if (formData.value.eventFormat && !formData.value.tags.includes(formData.value.eventFormat.toLowerCase())) {
      formData.value.tags.push(formData.value.eventFormat.toLowerCase());
    }

    // Add event theme as a tag if it exists and is not already included
    if (formData.value.eventTheme && !formData.value.tags.includes(formData.value.eventTheme.toLowerCase())) {
      formData.value.tags.push(formData.value.eventTheme.toLowerCase());
    }

    // If still no tags were added, add a default 'event' tag
    if (!formData.value.tags || formData.value.tags.length === 0) {
      formData.value.tags = ['event'];
    }

    // Double-check that 'event' is in the tags (this is critical for filtering)
    if (!formData.value.tags.includes('event')) {
      formData.value.tags.push('event');
    }

    console.log('Event tags before submission:', formData.value.tags);

    // Format the date and time for display
    const formattedDate = formData.value.eventDate;
    const formattedTime = formData.value.eventTime;
    const dateTimeDisplay = `${formattedDate} at ${formattedTime}`;

    // Create an event details object to store in the content field
    const eventDetails = {
      description: formData.value.content || '',
      eventDetails: {
        eventType: formData.value.eventType || 'general',
        eventFormat: formData.value.eventFormat || '',
        eventDate: formData.value.eventDate || '',
        eventTime: formData.value.eventTime || '',
        location: formData.value.location || '',
        eventTheme: formData.value.eventTheme || '',
        registrationUrl: formData.value.registrationUrl || '',
        visibility: formData.value.visibility || 'public',
        dateTimeDisplay: dateTimeDisplay
      }
    };

    // Create a valid date object from the date and time
    let eventDateTime;
    try {
      console.log('Date value:', formData.value.eventDate);
      console.log('Time value:', formData.value.eventTime);

      // Handle different date formats (YYYY/MM/DD or YYYY-MM-DD)
      let dateParts;
      if (formData.value.eventDate.includes('/')) {
        dateParts = formData.value.eventDate.split('/');
      } else if (formData.value.eventDate.includes('-')) {
        dateParts = formData.value.eventDate.split('-');
      } else {
        throw new Error('Date format not recognized. Please use YYYY/MM/DD or YYYY-MM-DD');
      }

      // Ensure we have 3 parts (year, month, day)
      if (dateParts.length !== 3) {
        throw new Error('Invalid date format. Please use YYYY/MM/DD or YYYY-MM-DD');
      }

      const year = parseInt(dateParts[0]);
      const month = parseInt(dateParts[1]) - 1; // JS months are 0-indexed
      const day = parseInt(dateParts[2]);

      // Validate date parts
      if (isNaN(year) || isNaN(month) || isNaN(day)) {
        throw new Error('Invalid date components');
      }

      // Parse the time in HH:MM format
      const timeParts = formData.value.eventTime.split(':');
      if (timeParts.length !== 2) {
        throw new Error('Invalid time format. Please use HH:MM');
      }

      const hours = parseInt(timeParts[0]);
      const minutes = parseInt(timeParts[1]);

      // Validate time parts
      if (isNaN(hours) || isNaN(minutes)) {
        throw new Error('Invalid time components');
      }

      console.log('Parsed date components:', { year, month, day, hours, minutes });

      // Create a valid date object
      // Note: Using new Date(year, month, day, hours, minutes) can be problematic with timezones
      // Instead, create a date string in ISO format and parse it
      const dateString = `${year}-${String(month+1).padStart(2, '0')}-${String(day).padStart(2, '0')}T${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:00`;
      console.log('Creating date from ISO string:', dateString);
      eventDateTime = new Date(dateString);
      console.log('Created date object:', eventDateTime, 'ISO:', eventDateTime.toISOString());

      // Validate the date
      if (isNaN(eventDateTime.getTime())) {
        throw new Error('Invalid date or time format');
      }
    } catch (e) {
      console.error('Error parsing date/time:', e);
      throw new Error('Please enter a valid date and time: ' + e.message);
    }

    // Calculate end time (1 hour after start time by default)
    const endDateTime = new Date(eventDateTime);
    endDateTime.setHours(endDateTime.getHours() + 1);

    // Create post object with postType and subType
    const post = {
      // Frontend fields for UI categorization
      postType: 'event',
      subType: formData.value.eventType || 'general',

      // Database fields - post_type must be 'platform', 'admin', or 'automated' per DB constraint
      post_type: 'platform', // User-created posts are always 'platform'
      sub_type: 'event', // This identifies it as an event post

      // Event-specific fields
      title: formData.value.title,
      event_title: formData.value.title,
      event_type: formData.value.eventType || 'general',
      event_theme: formData.value.eventTheme || null,
      event_start_datetime: eventDateTime.toISOString(),
      event_end_datetime: endDateTime.toISOString(),
      event_location: formData.value.location || null,
      event_registration_url: formData.value.registrationUrl || null,

      // Store all event details as JSON in the content field
      content: JSON.stringify(eventDetails),
      featuredImage: formData.value.image,
      status: 'published', // Must be lowercase to match database constraint

      // Include these fields for the frontend display - use camelCase for frontend fields
      eventTitle: formData.value.title,
      eventType: formData.value.eventType,
      eventTheme: formData.value.eventTheme,
      eventStartDatetime: eventDateTime.toISOString(),
      eventLocation: formData.value.location,
      eventRegistrationUrl: formData.value.registrationUrl,
      eventDateTime: dateTimeDisplay,

      // Include tags
      tags: formData.value.tags || [],

      // Ensure visibility is set
      visibility: formData.value.visibility || 'public'
    };

    console.log('Submitting event post:', post);
    // Emit the submit event with the post data
    // The parent component will handle the actual submission
    emit('submit', post);
    console.log('Event emitted to parent component');
  } catch (error) {
    console.error('Error creating event:', error);
    $q.notify({
      type: 'negative',
      message: error.message || 'Failed to create event. Please try again.'
    });
  } finally {
    // Keep the loading state active for at least 500ms to show the loading animation
    // This ensures the user sees the loading state even if the operation is very fast
    console.log('Operation completed, will set loading state to false after delay');
    setTimeout(() => {
      localLoading.value = false;
      console.log('Loading state set to false after delay');
    }, 1000);
  }
}
</script>

<style scoped>
.image-preview {
  position: relative;
  display: inline-block;
}
</style>
