// Type definitions for Quasar
declare module 'quasar' {
  import { ComponentPublicInstance, Plugin } from 'vue';

  export const Quasar: Plugin;
  
  // Export Quasar components and directives
  export const QBtn: ComponentPublicInstance;
  export const QPage: ComponentPublicInstance;
  export const QLayout: ComponentPublicInstance;
  export const QHeader: ComponentPublicInstance;
  export const QFooter: ComponentPublicInstance;
  export const QDrawer: ComponentPublicInstance;
  export const QPageContainer: ComponentPublicInstance;
  export const QToolbar: ComponentPublicInstance;
  export const QToolbarTitle: ComponentPublicInstance;
  export const QList: ComponentPublicInstance;
  export const QItem: ComponentPublicInstance;
  export const QItemSection: ComponentPublicInstance;
  export const QItemLabel: ComponentPublicInstance;
  export const QCard: ComponentPublicInstance;
  export const QCardSection: ComponentPublicInstance;
  export const QCardActions: ComponentPublicInstance;
  export const QInput: ComponentPublicInstance;
  export const QSelect: ComponentPublicInstance;
  export const QCheckbox: ComponentPublicInstance;
  export const QRadio: ComponentPublicInstance;
  export const QToggle: ComponentPublicInstance;
  export const QBtnToggle: ComponentPublicInstance;
  export const QIcon: ComponentPublicInstance;
  export const QSeparator: ComponentPublicInstance;
  export const QSpinner: ComponentPublicInstance;
  export const QSpinnerDots: ComponentPublicInstance;
  export const QBadge: ComponentPublicInstance;
  export const QChip: ComponentPublicInstance;
  export const QAvatar: ComponentPublicInstance;
  export const QTable: ComponentPublicInstance;
  export const QTh: ComponentPublicInstance;
  export const QTr: ComponentPublicInstance;
  export const QTd: ComponentPublicInstance;
  export const QImg: ComponentPublicInstance;
  export const QDialog: ComponentPublicInstance;
  export const QMenu: ComponentPublicInstance;
  export const QPopupProxy: ComponentPublicInstance;
  export const QTooltip: ComponentPublicInstance;
  export const QBtnDropdown: ComponentPublicInstance;
  export const QTabs: ComponentPublicInstance;
  export const QTab: ComponentPublicInstance;
  export const QTabPanels: ComponentPublicInstance;
  export const QTabPanel: ComponentPublicInstance;
  export const QStepper: ComponentPublicInstance;
  export const QStep: ComponentPublicInstance;
  export const QStepperNavigation: ComponentPublicInstance;
  export const QForm: ComponentPublicInstance;
  export const QUploader: ComponentPublicInstance;
  export const QInfiniteScroll: ComponentPublicInstance;
  export const QPullToRefresh: ComponentPublicInstance;
  export const QSlider: ComponentPublicInstance;
  export const QRange: ComponentPublicInstance;
  export const QKnob: ComponentPublicInstance;
  export const QExpansionItem: ComponentPublicInstance;
  export const QCarousel: ComponentPublicInstance;
  export const QCarouselSlide: ComponentPublicInstance;
  export const QCarouselControl: ComponentPublicInstance;
  export const QParallax: ComponentPublicInstance;
  export const QTimeline: ComponentPublicInstance;
  export const QTimelineEntry: ComponentPublicInstance;
  export const QInnerLoading: ComponentPublicInstance;
  export const QSpinnerBars: ComponentPublicInstance;
  export const QSpinnerGears: ComponentPublicInstance;
  export const QSpinnerHourglass: ComponentPublicInstance;
  export const QSpinnerIos: ComponentPublicInstance;
  export const QSpinnerOval: ComponentPublicInstance;
  export const QSpinnerPuff: ComponentPublicInstance;
  export const QSpinnerRings: ComponentPublicInstance;
  export const QSpinnerTail: ComponentPublicInstance;
  export const QCircularProgress: ComponentPublicInstance;
  export const QLinearProgress: ComponentPublicInstance;
  export const QSkeleton: ComponentPublicInstance;
  export const QMarkupTable: ComponentPublicInstance;
  export const QTree: ComponentPublicInstance;
  export const QFab: ComponentPublicInstance;
  export const QFabAction: ComponentPublicInstance;
  export const QPageScroller: ComponentPublicInstance;
  export const QPageSticky: ComponentPublicInstance;
  export const QBtnGroup: ComponentPublicInstance;
  export const QTime: ComponentPublicInstance;
  export const QDate: ComponentPublicInstance;
  export const QPopupEdit: ComponentPublicInstance;
  export const QColor: ComponentPublicInstance;
  export const QField: ComponentPublicInstance;
  export const QFile: ComponentPublicInstance;
  export const QRating: ComponentPublicInstance;
  export const QScrollArea: ComponentPublicInstance;
  export const QVideo: ComponentPublicInstance;
  export const QVirtualScroll: ComponentPublicInstance;
  export const QTimePicker: ComponentPublicInstance;
  export const QDatePicker: ComponentPublicInstance;
  export const QEditor: ComponentPublicInstance;
  export const QOptionGroup: ComponentPublicInstance;
  export const QBanner: ComponentPublicInstance;
  export const QBar: ComponentPublicInstance;
  export const QSpace: ComponentPublicInstance;
  export const QBreadcrumbs: ComponentPublicInstance;
  export const QBreadcrumbsEl: ComponentPublicInstance;
  export const QChatMessage: ComponentPublicInstance;
  export const QCollapsible: ComponentPublicInstance;
  export const QSlideTransition: ComponentPublicInstance;
  export const QResizeObserver: ComponentPublicInstance;
  export const QScrollObserver: ComponentPublicInstance;
  export const QIntersectionObserver: ComponentPublicInstance;
  export const QToggleIndeterminate: ComponentPublicInstance;
  export const QNoSsr: ComponentPublicInstance;
  export const QMarkupEditor: ComponentPublicInstance;
  export const QMarkupPreview: ComponentPublicInstance;
  export const QAjaxBar: ComponentPublicInstance;
  export const QBtnToggleGroup: ComponentPublicInstance;
  export const QCarouselSlider: ComponentPublicInstance;
  export const QChatMessageSent: ComponentPublicInstance;
  export const QChatMessageReceived: ComponentPublicInstance;
  export const QColorPicker: ComponentPublicInstance;
  export const QContextMenu: ComponentPublicInstance;
  export const QDatetimePicker: ComponentPublicInstance;
  export const QDrawerContainer: ComponentPublicInstance;
  export const QFabMain: ComponentPublicInstance;
  export const QGallery: ComponentPublicInstance;
  export const QGalleryCarousel: ComponentPublicInstance;
  export const QInfiniteLoad: ComponentPublicInstance;
  export const QInnerCircular: ComponentPublicInstance;
  export const QKnobSelect: ComponentPublicInstance;
  export const QLayoutDrawer: ComponentPublicInstance;
  export const QLayoutFooter: ComponentPublicInstance;
  export const QLayoutHeader: ComponentPublicInstance;
  export const QListHeader: ComponentPublicInstance;
  export const QModalLayout: ComponentPublicInstance;
  export const QPopover: ComponentPublicInstance;
  export const QProgress: ComponentPublicInstance;
  export const QRouteTab: ComponentPublicInstance;
  export const QSearch: ComponentPublicInstance;
  export const QTableColumns: ComponentPublicInstance;
  export const QTablePagination: ComponentPublicInstance;
  export const QTabPane: ComponentPublicInstance;
  export const QTdNumeric: ComponentPublicInstance;
  export const QWindowResizeObservable: ComponentPublicInstance;

  // Quasar plugins
  export const Dialog: any;
  export const Notify: any;
  export const Loading: any;
  export const LoadingBar: any;
  export const BottomSheet: any;
  export const LocalStorage: any;
  export const SessionStorage: any;
  export const Cookies: any;
  export const Dark: any;
  export const Fullscreen: any;
  export const AddressbarColor: any;
  export const AppFullscreen: any;
  export const AppVisibility: any;
  export const Screen: any;
  export const Platform: any;
  export const History: any;
  export const Meta: any;
  export const ClosePopup: any;
  export const Ripple: any;
  export const TouchHold: any;
  export const TouchPan: any;
  export const TouchRepeat: any;
  export const TouchSwipe: any;
  export const Intersection: any;
  export const Mutation: any;
  export const Morph: any;
  export const Scroll: any;
  export const ScrollFire: any;
  export const Resize: any;
  export const GoBack: any;
  export const KeepAlive: any;
  export const FocusManager: any;
  export const Portal: any;
  export const Scroll: any;
  export const Meta: any;

  // Quasar utilities
  export const colors: any;
  export const date: any;
  export const dom: any;
  export const event: any;
  export const format: any;
  export const platform: any;
  export const screen: any;
  export const utils: any;

  // Quasar global methods
  export function notify(options: any): void;
  export function dialog(options: any): any;
  export function loading(options?: any): { hide: () => void };
  export function loadingBar(options?: any): { start: () => void; stop: () => void; increment: (amount: number) => void };
  export function bottomSheet(options: any): any;
  export const localStorage: any;
  export const sessionStorage: any;
  export const cookies: any;
  export const dark: { isActive: boolean; set: (value: boolean) => void; toggle: () => void };
  export const fullscreen: { isActive: boolean; request: () => void; exit: () => void; toggle: () => void };
  export const addressbarColor: { set: (color: string) => void };
  export function exportFile(fileName: string, rawData: any, mimeType?: string): boolean;
  export function uid(): string;
  export function openURL(url: string): void;
  export function copyToClipboard(text: string): Promise<void>;
  export function debounce(fn: Function, wait?: number, immediate?: boolean): Function;
  export function throttle(fn: Function, limit: number): Function;
  export const scroll: any;
  export const meta: any;
}

// Declare global properties added by Quasar
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $q: any;
  }
}
