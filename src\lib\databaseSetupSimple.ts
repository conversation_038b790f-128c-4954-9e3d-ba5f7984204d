import { supabase } from './supabase';

/**
 * Initializes the profiles table with proper RLS policies
 */
export async function initializeProfilesTable(): Promise<boolean> {
  try {
    console.log('Initializing profiles table and RLS policies...');

    // Enable RLS
    const enableRls = await supabase.rpc('enable_rls_on_profiles');
    if (enableRls.error) {
      console.error('Error enabling RLS:', enableRls.error);
      return false;
    }

    // Create RLS policies
    const { error: policiesError } = await supabase.rpc('create_profiles_policies');
    if (policiesError) {
      console.error('Error creating RLS policies:', policiesError);
      return false;
    }

    console.log('✅ Profiles table initialized with RLS policies');
    return true;
  } catch (error) {
    console.error('Error initializing profiles table:', error);
    return false;
  }
}

/**
 * Checks if the profiles table exists
 */
export async function checkProfilesTable(): Promise<boolean> {
  try {
    console.log('Checking if profiles table exists...');

    // First check if table exists
    const { error } = await supabase.from('profiles').select('id').limit(1);

    if (error) {
      console.log('❌ Profiles table does not exist or is not accessible');

      // Create the table and set up RLS
      const { error: createError } = await supabase.rpc('setup_profiles_table');
      if (createError) {
        console.error('Error creating profiles table:', createError);
        return false;
      }

      // Initialize RLS policies
      return await initializeProfilesTable();
    }

    console.log('✅ Profiles table exists and is accessible');
    return true;
  } catch (error) {
    console.error('Error checking profiles table:', error);
    return false;
  }
}

/**
 * Ensures all required columns exist in the profiles table
 */
export async function ensureProfilesColumns(): Promise<{ success: boolean; message: string }> {
  try {
    console.log('Ensuring all required columns exist in profiles table...');

    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        -- Add missing columns to profiles table if they don't exist
        ALTER TABLE profiles ADD COLUMN IF NOT EXISTS is_verified BOOLEAN DEFAULT false;
        ALTER TABLE profiles ADD COLUMN IF NOT EXISTS profile_state TEXT DEFAULT 'IN_PROGRESS';
        ALTER TABLE profiles ADD COLUMN IF NOT EXISTS profile_name TEXT DEFAULT 'My Profile';
        ALTER TABLE profiles ADD COLUMN IF NOT EXISTS profile_visibility TEXT DEFAULT 'private';
        ALTER TABLE profiles ADD COLUMN IF NOT EXISTS user_id UUID;
        ALTER TABLE profiles ADD COLUMN IF NOT EXISTS profile_completion FLOAT DEFAULT 0;
        ALTER TABLE profiles ADD COLUMN IF NOT EXISTS profile_type TEXT;
        ALTER TABLE profiles ADD COLUMN IF NOT EXISTS role TEXT DEFAULT 'early_access';
      `
    });

    if (error) {
      console.error('Error ensuring profiles columns:', error.message);
      return { success: false, message: error.message };
    }

    return { success: true, message: 'All required profiles columns added successfully' };
  } catch (err: any) {
    console.error('Error ensuring profiles columns:', err.message);
    return { success: false, message: err.message };
  }
}

/**
 * Ensures the innovator_profiles table exists with proper RLS policies
 */
export async function ensureInnovatorProfilesTable(): Promise<{ success: boolean; message: string }> {
  try {
    console.log('Ensuring innovator_profiles table exists...');

    // Create the innovator_profiles table and set up RLS policies
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        -- Create the innovator_profiles table if it doesn't exist
        CREATE TABLE IF NOT EXISTS public.innovator_profiles (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            profile_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,

            -- Basic Innovation Information
            innovation_area TEXT,
            innovation_stage TEXT,
            innovation_description TEXT,

            -- Team Information
            team_size INTEGER,
            team_description TEXT,

            -- Funding Information
            funding_needs BOOLEAN DEFAULT false,
            funding_amount NUMERIC,
            funding_stage TEXT,

            -- Prototype Information
            has_prototype BOOLEAN DEFAULT false,
            prototype_description TEXT,

            -- Goals and Challenges
            goals TEXT[],
            challenges TEXT[],

            -- Online Presence
            website TEXT,
            social_links JSONB DEFAULT '{}'::jsonb,

            -- Contact Information
            contact_email TEXT,
            contact_phone TEXT,
            contact_address TEXT,

            -- Bio and Achievements
            bio TEXT,
            achievements TEXT[],
            awards TEXT[],

            -- Additional Information
            target_market TEXT,
            business_model TEXT,
            competitive_advantage TEXT,

            -- Timestamps
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Enable Row Level Security on the table
        ALTER TABLE public.innovator_profiles ENABLE ROW LEVEL SECURITY;

        -- Drop existing policies first to avoid conflicts
        DROP POLICY IF EXISTS "Temporary permissive policy for innovator profiles" ON public.innovator_profiles;

        -- Create a temporary permissive policy for testing
        -- This allows all operations for authenticated users
        CREATE POLICY "Temporary permissive policy for innovator profiles"
            ON public.innovator_profiles
            USING (auth.role() = 'authenticated');

        -- Grant permissions to authenticated users
        GRANT ALL ON public.innovator_profiles TO authenticated;
        GRANT ALL ON public.innovator_profiles TO service_role;
      `
    });

    if (error) {
      console.error('Error ensuring innovator_profiles table:', error.message);
      return { success: false, message: error.message };
    }

    return { success: true, message: 'Innovator profiles table created or updated successfully' };
  } catch (err: any) {
    console.error('Error ensuring innovator_profiles table:', err.message);
    return { success: false, message: err.message };
  }
}

export async function refreshSchemaCache(): Promise<{ success: boolean; message: string }> {
  try {
    console.log('Refreshing Supabase schema cache...');

    // First, execute a simple query to force a schema cache refresh
    const { error } = await supabase.rpc('exec_sql', {
      sql: 'SELECT 1;'
    });

    if (error) {
      console.error('Error refreshing schema cache:', error.message);
      return { success: false, message: error.message };
    }

    // Next, execute a more specific query to ensure all columns are recognized
    const { error: columnsError } = await supabase.rpc('exec_sql', {
      sql: `
        SELECT
          column_name, data_type
        FROM
          information_schema.columns
        WHERE
          table_name = 'profiles';
      `
    });

    if (columnsError) {
      console.error('Error querying columns:', columnsError.message);
      // Continue anyway, this is just for information
    }

    // Now try to query the profiles table with all columns to ensure the cache is updated
    const { error: queryError } = await supabase
      .from('profiles')
      .select('id, user_id, email, first_name, last_name, profile_name, profile_state, profile_type, profile_visibility, role, is_verified, profile_completion')
      .limit(1);

    if (queryError) {
      console.error('Error querying profiles after refresh:', queryError.message);

      // If there's an error, try a simpler query
      const { error: simpleQueryError } = await supabase
        .from('profiles')
        .select('id')
        .limit(1);

      if (simpleQueryError) {
        console.error('Error with simple query:', simpleQueryError.message);
        return { success: false, message: queryError.message };
      }
    }

    return { success: true, message: 'Schema cache refreshed successfully' };
  } catch (err: any) {
    console.error('Error refreshing schema cache:', err.message);
    return { success: false, message: err.message };
  }
}

/**
 * Creates a profile for a user manually
 */
export async function createUserProfile(userId: string, email: string): Promise<{ success: boolean; message: string }> {
  try {
    console.log('Creating user profile for:', userId);

    const { error } = await supabase
      .from('personal_details')
      .insert({
        user_id: userId,
        email,
        first_name: 'New',
        last_name: 'User',
        profile_name: 'My Profile',
        profile_state: 'IN_PROGRESS',
        profile_visibility: 'private',
        role: 'early_access',
        profile_completion: 0
      });

    if (error) {
      console.error('Error creating user profile:', error.message);
      return { success: false, message: error.message };
    }

    console.log('✅ User profile created successfully');
    return { success: true, message: 'User profile created successfully' };
  } catch (error: any) {
    console.error('Error creating user profile:', error.message);
    return { success: false, message: error.message };
  }
}

/**
 * Fetches the user's profile
 */
export async function getUserProfile(userId: string): Promise<any> {
  try {
    console.log('Fetching user profile for:', userId);

    const { data, error } = await supabase
      .from('personal_details')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error) {
      console.error('Error fetching user profile:', error.message);
      return null;
    }

    console.log('✅ User profile fetched successfully');
    return data;
  } catch (error: any) {
    console.error('Error fetching user profile:', error.message);
    return null;
  }
}
