# Resend Email Service Setup Guide

This guide will help you set up Resend for email delivery in your ZB Innovation Hub application.

## 1. Create a Resend Account

1. Go to [Resend's website](https://resend.com/)
2. Sign up for a free account (allows 100 emails per day)
3. Complete the verification process

## 2. Create an API Key

1. Log in to your Resend account
2. Navigate to the API Keys section
3. Click "Create API Key"
4. Name your key (e.g., "ZB Innovation Hub")
5. Copy the API key (it will only be shown once)

## 3. Verify a Domain

Resend requires you to verify a domain to send emails:

1. Navigate to the Domains section
2. Click "Add Domain"
3. Enter your domain name (e.g., zbinnovation.co.zw)
4. Follow the DNS verification steps
5. Wait for the domain to be verified (can take up to 24-48 hours)

## 4. Update the Supabase Environment

1. Create a `.env.resend` file in the `supabase` directory with the following content:
   ```
   RESEND_KEY=your_resend_api_key_here
   ```
2. Replace `your_resend_api_key_here` with your actual Resend API key
3. Run the following command to update the Supabase environment:
   ```
   npx supabase secrets set --env-file .env.resend --project-ref dpicnvisvxpmgjtbeicf
   ```

## 5. Update the Email Service

1. Edit the `src/services/emailService.ts` file to use the `send-email-resend` function instead of `send-email-fixed`
2. Deploy the changes to your application

## 6. Test Email Delivery

1. Create a test email using the `simple-test-email.html` file
2. Check if the email is delivered successfully

## Troubleshooting

If emails are still not being delivered:

1. Check the Resend dashboard to see if the emails are being sent
2. Verify that your domain is properly verified
3. Make sure the API key has the correct permissions
4. Look for any error messages in the Supabase Edge Function logs

## Resend Documentation

For more information, refer to the official Resend documentation:

- [Resend API Documentation](https://resend.com/docs/api-reference/introduction)
- [Resend Node.js SDK](https://resend.com/docs/sdk/nodejs/introduction)
- [Domain Verification](https://resend.com/docs/dashboard/domains/introduction)
