export interface ProfileType {
  value: string
  label: string
  description?: string
  icon?: string
}

export const profileTypes: ProfileType[] = [
  { 
    value: 'innovator', 
    label: 'Innovator',
    description: 'Entrepreneurs and startups with innovative ideas',
    icon: 'lightbulb'
  },
  { 
    value: 'investor', 
    label: 'Business Investor',
    description: 'Investors looking to fund innovative projects',
    icon: 'attach_money'
  },
  { 
    value: 'mentor', 
    label: '<PERSON><PERSON>',
    description: 'Experienced professionals who mentor innovators',
    icon: 'school'
  },
  { 
    value: 'professional', 
    label: 'Professional',
    description: 'Professionals offering services to innovators',
    icon: 'work'
  },
  { 
    value: 'industry_expert', 
    label: 'Industry Expert',
    description: 'Experts with deep industry knowledge',
    icon: 'engineering'
  },
  { 
    value: 'academic_student', 
    label: 'Academic Student',
    description: 'Students with academic projects',
    icon: 'menu_book'
  },
  { 
    value: 'academic_institution', 
    label: 'Academic Institution',
    description: 'Educational institutions with innovation programs',
    icon: 'account_balance'
  },
  { 
    value: 'organisation', 
    label: 'Organisation',
    description: 'Organizations supporting innovation',
    icon: 'business'
  }
]

/**
 * Get profile type by value
 */
export function getProfileType(value: string): ProfileType | undefined {
  return profileTypes.find(type => type.value === value)
}

/**
 * Format profile type for display
 */
export function formatProfileType(type: string | null | undefined): string {
  if (!type) return 'No Type'

  const profileType = getProfileType(type)
  if (profileType) {
    return profileType.label
  }

  // Fallback: Convert snake_case to Title Case
  return type
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}

/**
 * Get profile type options for select components
 */
export function getProfileTypeOptions(): { label: string; value: string }[] {
  return profileTypes.map(type => ({
    label: type.label,
    value: type.value
  }))
}

/**
 * Get icon for profile type
 */
export function getProfileTypeIcon(type: string | null | undefined): string {
  if (!type) return 'person'

  const profileType = getProfileType(type)
  if (profileType && profileType.icon) {
    return profileType.icon
  }

  // Fallback icons
  const icons: Record<string, string> = {
    'innovator': 'lightbulb',
    'investor': 'attach_money',
    'mentor': 'school',
    'professional': 'work',
    'industry_expert': 'engineering',
    'academic_student': 'menu_book',
    'academic_institution': 'account_balance',
    'organisation': 'business'
  }

  return icons[type] || 'person'
}
