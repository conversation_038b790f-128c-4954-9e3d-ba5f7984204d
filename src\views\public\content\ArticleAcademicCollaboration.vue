<template>
  <div class="article-page">
    <!-- Loading State -->
    <div v-if="loading" class="text-center q-pa-xl">
      <q-spinner-dots color="primary" size="40px" />
      <div class="text-subtitle1 q-mt-sm">Loading article...</div>
    </div>

    <!-- Error State -->
    <div v-else-if="!article" class="text-center q-pa-xl">
      <p class="text-h6">Article not found</p>
      <q-btn
        color="primary"
        :to="{ name: 'news' }"
        label="Back to News"
        class="q-mt-md"
      />
    </div>

    <!-- Article Content -->
    <template v-else>
      <!-- Hero Section -->
      <div class="article-hero" style="background-image: url('https://picsum.photos/id/180/1920/600')">
        <div class="overlay" />
      </div>

      <!-- Article Header Card -->
      <div class="container q-mx-auto q-px-md article-header-container">
        <div class="article-header-card">
          <!-- Back Button -->
          <q-btn
            flat
            color="primary"
            no-caps
            class="q-mb-md"
            :to="{ name: 'news' }"
          >
            <q-icon name="arrow_back" class="q-mr-xs" />
            Back to News
          </q-btn>

          <q-chip
            color="green"
            text-color="white"
            class="q-mb-md category-chip"
          >
            Partnership
          </q-chip>
          <h3 class="q-mb-md title">{{ article.title }}</h3>
          <div class="article-meta row items-center q-gutter-x-md">
            <div>
              <q-icon name="person" size="18px" class="q-mr-xs" />
              {{ article.author }}
            </div>
            <div>
              <q-icon name="work" size="18px" class="q-mr-xs" />
              {{ article.authorRole }}
            </div>
            <div>
              <q-icon name="today" size="18px" class="q-mr-xs" />
              {{ formatDate(new Date(article.date)) }}
            </div>
            <div>
              <q-icon name="schedule" size="18px" class="q-mr-xs" />
              {{ article.readTime }}
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="container q-mx-auto q-px-md q-py-xl">
        <div class="row q-col-gutter-lg">
          <!-- Share Sidebar -->
          <div class="col-md-1 col-sm-12 col-xs-12">
            <div class="share-sidebar">
              <div class="text-caption q-mb-sm">Share</div>
              <q-btn round flat color="primary" icon="facebook" class="q-mb-sm" @click="shareArticle('facebook')" />
              <q-btn round flat color="primary" icon="twitter" class="q-mb-sm" @click="shareArticle('twitter')" />
              <q-btn round flat color="primary" icon="linkedin" class="q-mb-sm" @click="shareArticle('linkedin')" />
              <q-btn round flat color="primary" icon="email" @click="shareArticle('email')" />
            </div>
          </div>

          <!-- Article Body -->
          <div class="col-md-8 col-sm-12 col-xs-12">
            <div class="content-wrapper">
              <!-- Article Excerpt -->
              <div class="article-excerpt text-h6 text-weight-regular text-grey-8 q-mb-xl">
                {{ article.excerpt }}
              </div>

              <!-- Article Body -->
              <div class="article-body" v-html="formatContent(article.content)"></div>
            </div>
          </div>

          <!-- Related Articles Sidebar -->
          <div class="col-md-3 col-sm-12 col-xs-12">
            <div class="sidebar">
              <div class="text-h6 q-mb-md">Related Articles</div>
              <q-card class="q-mb-md">
                <q-img src="https://picsum.photos/id/3/500/300" height="150px" />
                <q-card-section>
                  <q-chip size="sm" color="accent" text-color="white" class="q-mb-sm">Event</q-chip>
                  <div class="text-subtitle1 q-mb-xs">ZbInnovation Virtual Hub Launch Event</div>
                  <div class="text-caption text-grey">May 7, 2025</div>
                  <q-btn flat color="primary" size="sm" class="q-mt-sm" :to="{ name: 'article', params: { slug: 'ZbInnovation-virtual-hub-launch-event' } }">
                    Read More
                  </q-btn>
                </q-card-section>
              </q-card>
            </div>
          </div>
        </div>
      </div>

      <!-- Related Stories Section -->
      <div class="related-stories-section q-py-xl">
        <div class="container q-mx-auto q-px-md">
          <h2 class="text-h4 q-mb-lg">Related Stories</h2>
          <div class="row q-col-gutter-lg">
            <div class="col-12 col-md-4">
              <q-card class="related-story-card" flat bordered>
                <q-card-section>
                  <q-chip
                    color="accent"
                    text-color="white"
                    class="q-mb-sm"
                    size="sm"
                  >
                    Event
                  </q-chip>
                  <h3 class="text-h6 q-mb-sm">ZbInnovation Virtual Hub Launch Event</h3>
                  <p class="text-body2 text-grey-8 q-mb-md">Join us for the groundbreaking launch of the ZbInnovation Virtual Hub on May 7, 2025.</p>
                  <q-btn
                    flat
                    color="primary"
                    :to="{ name: 'article', params: { slug: 'ZbInnovation-virtual-hub-launch-event' }}"
                    label="Read More"
                    no-caps
                    class="q-px-md"
                  />
                </q-card-section>
              </q-card>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useNewsStore } from '../../../stores/news'

const router = useRouter()
const newsStore = useNewsStore()
const loading = ref(true)
const article = ref(null)

onMounted(async () => {
  try {
    // Get article data from the news store
    article.value = newsStore.newsItems.find(item => item.slug === 'academic-collaboration')
    loading.value = false
  } catch (error) {
    console.error('Error loading article:', error)
    loading.value = false
  }
})

// Format date for display
function formatDate(date: Date): string {
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// Format content for display
function formatContent(content: string): string {
  if (!content) return ''

  // Convert markdown to HTML (simple implementation)
  let formatted = content
    .replace(/^# (.+)$/gm, '<h1>$1</h1>')
    .replace(/^## (.+)$/gm, '<h2>$1</h2>')
    .replace(/^### (.+)$/gm, '<h3>$1</h3>')
    .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.+?)\*/g, '<em>$1</em>')
    .replace(/^- (.+)$/gm, '<li>$1</li>')
    .replace(/^\d+\. (.+)$/gm, '<li>$1</li>')
    .replace(/^\s*\n\s*$/gm, '</ul><p></p><ul>')
    .replace(/^---$/gm, '<hr>')

  // Wrap lists
  formatted = formatted.replace(/<li>/g, '<ul><li>').replace(/<\/li>/g, '</li></ul>')
  formatted = formatted.replace(/<\/ul><ul>/g, '')

  // Split into paragraphs and wrap non-element text
  const paragraphs = formatted.split('\n\n')
  formatted = paragraphs
    .map(para => {
      // Don't wrap if it's already a header, list, or other HTML element
      if (para.startsWith('<')) return para
      return `<p>${para}</p>`
    })
    .join('\n\n')

  return formatted
}

// Share article functionality
function shareArticle(platform: string): void {
  const url = window.location.href
  const title = article.value?.title || 'Academic Partnerships Article'
  const text = article.value?.excerpt || 'Learn about academic partnerships'

  switch (platform) {
    case 'facebook':
      window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`, '_blank')
      break
    case 'twitter':
      window.open(`https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(text)}`, '_blank')
      break
    case 'linkedin':
      window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`, '_blank')
      break
    case 'email':
      window.location.href = `mailto:?subject=${encodeURIComponent(title)}&body=${encodeURIComponent(text + '\n\n' + url)}`
      break
  }
}
</script>

<style scoped>
.article-hero {
  position: relative;
  height: 160px;
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: flex-end;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(0,0,0,0.2), rgba(0,0,0,0.4));
}

.article-header-container {
  margin-top: -60px;
  position: relative;
  z-index: 2;
}

.article-header-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  max-width: 800px;
  margin: 0 auto;
}

.title {
  font-weight: 600;
  line-height: 1.2;
  color: #1d1d1d;
}

.category-chip {
  font-weight: 500;
}

.article-meta {
  color: #546E7A;
  font-size: 0.95rem;
}

.share-sidebar {
  position: sticky;
  top: 100px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.content-wrapper {
  background: white;
  border-radius: 12px;
  padding: 3rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  margin-top: 2rem;
}

.article-excerpt {
  font-size: 1.4rem;
  line-height: 1.6;
  color: #546E7A;
  border-left: 4px solid var(--q-primary);
  padding-left: 1.5rem;
}

.article-body {
  font-size: 1.2rem;
  line-height: 1.8;
  color: #37474F;
}

.article-body p {
  margin-bottom: 1.8rem;
}

.article-body h2 {
  font-size: 2rem;
  margin: 2.5rem 0 1.5rem;
  font-weight: 600;
}

.article-body h3 {
  font-size: 1.5rem;
  margin: 2rem 0 1rem;
  font-weight: 600;
}

.article-body img {
  width: 100%;
  border-radius: 8px;
  margin: 2rem 0;
}

.article-body blockquote {
  border-left: 4px solid var(--q-primary);
  margin: 2rem 0;
  padding: 1rem 0 1rem 2rem;
  font-style: italic;
  color: #546E7A;
}

.related-stories-section {
  background-color: #f5f5f5;
}

.related-story-card {
  height: 100%;
  transition: all 0.3s ease;
}

.related-story-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.sidebar {
  position: sticky;
  top: 100px;
}

@media (max-width: 600px) {
  .article-header-card h1 {
    font-size: 1.8rem;
  }

  .content-wrapper {
    padding: 1.5rem;
  }
}
</style>
