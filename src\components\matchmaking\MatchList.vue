<template>
  <div class="match-list">
    <div class="row q-mb-md">
      <div class="col">
        <h5 class="q-mt-none q-mb-sm">{{ title }}</h5>
        <p v-if="description" class="q-mt-none text-body2">{{ description }}</p>
      </div>

      <div class="col-auto">
        <q-btn
          v-if="showRefreshButton"
          flat
          round
          color="primary"
          icon="refresh"
          :loading="matchmakingStore.isLoading"
          @click="refreshMatches"
        />
      </div>
    </div>

    <div v-if="matchmakingStore.isLoading && (!matches || !matches.length)" class="text-center q-pa-lg">
      <q-spinner color="primary" size="3em" />
      <div class="q-mt-sm">Loading matches...</div>
    </div>

    <div v-else-if="!matches || !matches.length" class="text-center q-pa-lg">
      <q-icon name="search_off" size="3em" color="grey-7" />
      <div class="q-mt-sm">No matches found</div>
      <div class="q-mt-xs text-caption">
        {{ noMatchesMessage }}
      </div>

      <q-btn
        v-if="showGenerateButton"
        class="q-mt-md"
        color="primary"
        :loading="matchmakingStore.isLoading"
        @click="generateMatches"
      >
        Generate Matches
      </q-btn>
    </div>

    <div v-else>
      <match-card
        v-for="match in (matches || [])"
        :key="match.id"
        :match="match"
        @update="$emit('update')"
      />

      <div class="text-center q-mt-md">
        <q-btn
          v-if="hasMoreMatches"
          flat
          color="primary"
          :loading="loadingMore"
          @click="loadMoreMatches"
        >
          Load More
        </q-btn>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useMatchmakingStore } from '@/stores/matchmaking';
import { useNotificationStore } from '@/stores/notifications';
import { useAuthStore } from '@/stores/auth';
import { ProfileType, EntityType } from '@/services/matchmakingService';
import MatchCard from './MatchCard.vue';

// Props
const props = defineProps({
  title: {
    type: String,
    default: 'Matches'
  },
  description: {
    type: String,
    default: ''
  },
  profileType: {
    type: String as () => ProfileType,
    required: true
  },
  entityType: {
    type: String as () => EntityType,
    default: undefined
  },
  limit: {
    type: Number,
    default: 10
  },
  minScore: {
    type: Number,
    default: 0.3
  },
  showRefreshButton: {
    type: Boolean,
    default: true
  },
  showGenerateButton: {
    type: Boolean,
    default: true
  },
  noMatchesMessage: {
    type: String,
    default: 'Try updating your profile with more information or generate matches'
  }
});

// Emit
const emit = defineEmits(['update']);

// Stores
const matchmakingStore = useMatchmakingStore();
const notificationStore = useNotificationStore();
const authStore = useAuthStore();

// State
const offset = ref(0);
const loadingMore = ref(false);
const hasMoreMatches = ref(true);

// Computed
const matches = computed(() => {
  try {
    if (props.entityType) {
      // Make sure getMatchesByType is defined and is a function
      if (matchmakingStore.getMatchesByType && typeof matchmakingStore.getMatchesByType.value === 'function') {
        return matchmakingStore.getMatchesByType.value(props.entityType) || [];
      }
    }
    // Return all matches or an empty array if undefined
    return matchmakingStore.getMatches?.value || [];
  } catch (error) {
    console.error('Error computing matches:', error);
    return [];
  }
});

// Methods
async function loadMatches() {
  if (!authStore.isAuthenticated) {
    notificationStore.error('You must be logged in to view matches');
    return;
  }

  try {
    const success = await matchmakingStore.loadMatches(
      props.entityType,
      props.limit,
      offset.value,
      props.minScore
    );

    if (!success) {
      notificationStore.error('Failed to load matches');
    }

    // Check if we have more matches to load
    hasMoreMatches.value = (matchmakingStore.getMatches?.value?.length || 0) >= props.limit;
  } catch (error: any) {
    console.error('Error loading matches:', error);
    notificationStore.error(`Error loading matches: ${error.message}`);
  }
}

async function loadMoreMatches() {
  loadingMore.value = true;

  try {
    offset.value += props.limit;
    await loadMatches();
  } catch (error: any) {
    console.error('Error loading more matches:', error);
    notificationStore.error(`Error loading more matches: ${error.message}`);
  } finally {
    loadingMore.value = false;
  }
}

async function refreshMatches() {
  offset.value = 0;
  await loadMatches();
}

async function generateMatches() {
  try {
    const success = await matchmakingStore.generateMatches(props.profileType);

    if (!success) {
      notificationStore.error('Failed to generate matches');
    }
  } catch (error: any) {
    console.error('Error generating matches:', error);
    notificationStore.error(`Error generating matches: ${error.message}`);
  }
}

// Lifecycle hooks
onMounted(async () => {
  // Only try to load matches if the store is initialized and we're authenticated
  if (matchmakingStore.initialized && authStore.isAuthenticated) {
    await loadMatches();
  }
});

// Watch for changes in initialization status
watch(
  () => matchmakingStore.initialized,
  async (isInitialized) => {
    if (isInitialized && authStore.isAuthenticated) {
      await loadMatches();
    }
  }
);
</script>

<style scoped>
.match-list {
  max-width: 800px;
  margin: 0 auto;
}
</style>
