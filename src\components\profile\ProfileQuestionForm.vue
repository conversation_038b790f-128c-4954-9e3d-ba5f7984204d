<template>
  <div class="row q-col-gutter-md">
    <div
      v-for="question in questions"
      :key="question.id"
      :class="getColumnClass(question)"
    >
      <!-- Text Input -->
      <q-input
        v-if="question.type === 'text'"
        :label="question.label + (question.required ? ' *' : '')"
        :hint="question.hint"
        outlined
        v-model="values[question.id]"
        @update:modelValue="val => updateValue(question.id, val)"
      />

      <!-- Number Input -->
      <q-input
        v-else-if="question.type === 'number'"
        type="number"
        :label="question.label + (question.required ? ' *' : '')"
        :hint="question.hint"
        outlined
        v-model="values[question.id]"
        @update:modelValue="val => updateValue(question.id, val)"
      />

      <!-- Textarea Input -->
      <q-input
        v-else-if="question.type === 'textarea'"
        type="textarea"
        autogrow
        :label="question.label + (question.required ? ' *' : '')"
        :hint="question.hint"
        outlined
        v-model="values[question.id]"
        @update:modelValue="val => updateValue(question.id, val)"
      />

      <!-- Single Select -->
      <EnhancedSelect
        v-else-if="question.type === 'select'"
        :label="question.label + (question.required ? ' *' : '')"
        :hint="question.hint"
        :options="getOptions(question.options)"
        outlined
        v-model="values[question.id]"
        @update:modelValue="val => updateValue(question.id, val)"
      />

      <!-- Multi Select -->
      <EnhancedSelect
        v-else-if="question.type === 'multi-select'"
        :label="question.label + (question.required ? ' *' : '')"
        :hint="question.hint"
        :options="getOptions(question.options)"
        outlined
        multiple
        use-chips
        v-model="values[question.id]"
        @update:modelValue="val => updateValue(question.id, val)"
      />

      <!-- Multi Select with Free Input -->
      <q-select
        v-else-if="question.type === 'multi-select-free'"
        :label="question.label + (question.required ? ' *' : '')"
        :hint="question.hint"
        outlined
        multiple
        use-chips
        use-input
        new-value-mode="add-unique"
        v-model="values[question.id]"
        @update:modelValue="val => updateValue(question.id, val)"
        hide-dropdown-icon
      >
        <template v-slot:append>
          <unified-icon name="arrow_drop_down" />
        </template>
      </q-select>

      <!-- Toggle -->
      <q-toggle
        v-else-if="question.type === 'toggle'"
        :label="question.label"
        v-model="values[question.id]"
        @update:modelValue="val => updateValue(question.id, val)"
      />
    </div>
  </div>
</template>

<script>
import UnifiedIcon from '../ui/UnifiedIcon.vue';
import CustomSelect from '../ui/CustomSelect.vue';
import EnhancedSelect from '../ui/EnhancedSelect.vue';

export default {
  name: 'ProfileQuestionForm',
  components: {
    UnifiedIcon,
    CustomSelect,
    EnhancedSelect
  },
  props: {
    questions: {
      type: Array,
      required: true
    },
    values: {
      type: Object,
      default: () => ({})
    },
    options: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    getColumnClass(question) {
      return question.fullWidth ? 'col-12' : 'col-12 col-md-6';
    },
    getOptions(optionKey) {
      if (!optionKey) return [];

      let options = [];

      // If optionKey is a string, look it up in the options object
      if (typeof optionKey === 'string') {
        options = this.options[optionKey] || [];
      }
      // If optionKey is already an array, use it directly
      else if (Array.isArray(optionKey)) {
        options = optionKey;
      }

      // Convert string array to object array if needed
      if (options.length > 0 && typeof options[0] === 'string') {
        return options.map(opt => ({
          label: opt,
          value: opt.toLowerCase().replace(/\s+/g, '_')
        }));
      }

      return options;
    },
    updateValue(id, value) {
      this.$emit('update', id, value);
    }
  }
}
</script>
