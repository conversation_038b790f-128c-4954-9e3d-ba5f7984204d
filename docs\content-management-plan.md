# Content Management and Feed Structure Plan

## Overview

This document outlines the content management system and feed structure for the ZB Innovation Hub platform. The platform serves as a social media platform with Twitter-like feed functionality that connects various participants in the innovation ecosystem.

## Content Types

The platform supports various types of content:

### 1. Post Types

- **Automated Posts**: System-generated posts based on user activity
  - Profile creation
  - Profile completion
  - Group creation
  - New marketplace listing
  - **Action Buttons**: Like, Comment, View Profile/Group/Listing

- **Admin Posts**: Platform announcements and notifications
  - General announcements
  - Platform updates
  - Important notifications
  - **Action Buttons**: Like, Comment, Share, Pin, Mark as Read

- **User-Generated Posts**:
  - **General Posts**: Standard posts about anything
    - **Action Buttons**: Like, Comment, Share, Save

  - **Opportunity Posts**:
    - Funding opportunities
    - Collaboration opportunities
    - Mentorship opportunities
    - **Action Buttons**: Like, Comment, Share, Apply, Contact, Save

  - **Blog Articles**: Longer-form content with categories
    - Funding
    - Research
    - Innovation
    - Training
    - Other categories (to be defined)
    - **Action Buttons**: Like, Comment, Share, Read More, Save

  - **Events**: Event announcements with details
    - Physical or virtual events
    - Various themes (funding, training, investment, pitch competitions, etc.)
    - **Action Buttons**: Like, Comment, Share, Register, Add to Calendar, Remind Me

  - **Resource Posts**: Shared resources valuable to the innovation community
    - Templates & Tools
    - Research Papers
    - Market Reports
    - Guides & Tutorials
    - **Action Buttons**: Like, Comment, Share, Download, Save

  - **Success Story Posts**: Highlighting achievements within the community
    - Funding Success
    - Product Launch
    - Market Expansion
    - Acquisition/Exit
    - **Action Buttons**: Like, Comment, Share, Congratulate, Connect

  - **Question/Help Posts**: Community members seeking advice or assistance
    - Technical questions
    - Business challenges
    - Seeking specific expertise
    - **Action Buttons**: Like, Comment, Share, Answer, Offer Help

  - **Job/Talent Posts**: Employment opportunities or talent availability
    - Full-time Positions
    - Part-time Roles
    - Freelance/Contract
    - Internships
    - **Action Buttons**: Like, Comment, Share, Apply, Contact, Save Job

  - **Innovation Challenge Posts**: Specific challenges seeking innovative solutions
    - Corporate Challenges
    - Social Impact Challenges
    - Technical Challenges
    - Design Challenges
    - **Action Buttons**: Like, Comment, Share, Submit Solution, Learn More

### 2. Content Categories and Subcategories

Posts can be categorized into various categories and subcategories to help with organization and filtering:

#### General
- **Subcategories**: Updates, Questions, Discussions, Announcements, Introductions
- **Tags**: #innovation, #startup, #question, #discussion

#### Funding
- **Subcategories**: Grants, Venture Capital, Angel Investment, Crowdfunding, Government Funding, Competitions
- **Tags**: #seed, #seriesA, #grant, #investment

#### Collaboration
- **Subcategories**: Research Partnerships, Co-Development, Market Access, Distribution Partnerships, Technology Transfer, Joint Ventures
- **Tags**: #partnership, #codevelopment, #jointventure

#### Mentorship
- **Subcategories**: Business Strategy, Technical Mentorship, Marketing & Sales, Financial Management, Product Development, Scaling & Growth
- **Tags**: #mentor, #advice, #guidance, #coaching

#### Innovation
- **Subcategories**: Product Innovation, Process Innovation, Business Model Innovation, Social Innovation
- **Tags**: #innovation, #disruption, #newproduct

#### Research
- **Subcategories**: Academic Research, Market Research, R&D, Patent Research, Competitive Analysis
- **Tags**: #research, #study, #analysis, #findings

#### Training
- **Subcategories**: Technical Skills, Business Skills, Soft Skills, Leadership, Entrepreneurship
- **Tags**: #learning, #skills, #development, #workshop

#### Industry-Specific
- **Subcategories**: Fintech, Agritech, Healthtech, Edtech, Cleantech, AI/ML
- **Tags**: Industry-specific tags (#fintech, #agritech, #healthtech)

### 3. Event Themes and Types

Events can have specific themes and types:

#### Event Themes
- Funding
- Training
- Investment
- Pitch Competition
- Networking
- Workshop
- Conference
- Hackathon
- Demo Day
- Product Launch

#### Event Types
- **Physical Events**: In-person gatherings at specific locations
- **Virtual Events**: Online events accessible remotely
- **Hybrid Events**: Combination of physical and virtual participation options

## Database Schema

### 1. Posts Table

```sql
CREATE TABLE IF NOT EXISTS public.posts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  post_type VARCHAR(50) NOT NULL,
  title TEXT,
  content TEXT NOT NULL,
  excerpt TEXT,
  image_url TEXT,
  category VARCHAR(50),
  sub_category VARCHAR(50),
  tags TEXT[],
  visibility VARCHAR(20) DEFAULT 'public',
  is_featured BOOLEAN DEFAULT false,
  is_automated BOOLEAN DEFAULT false,
  likes_count INTEGER DEFAULT 0,
  comments_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 2. Post Types Table

```sql
CREATE TABLE IF NOT EXISTS public.post_types (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(50) NOT NULL UNIQUE,
  description TEXT,
  icon TEXT,
  color VARCHAR(20),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 3. Post Categories Table

```sql
CREATE TABLE IF NOT EXISTS public.post_categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(50) NOT NULL UNIQUE,
  description TEXT,
  icon TEXT,
  color VARCHAR(20),
  parent_id UUID REFERENCES public.post_categories(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 4. Post Comments Table

```sql
CREATE TABLE IF NOT EXISTS public.post_comments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  post_id UUID REFERENCES public.posts(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  likes_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 5. Post Likes Table

```sql
CREATE TABLE IF NOT EXISTS public.post_likes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  post_id UUID REFERENCES public.posts(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(post_id, user_id)
);
```

## User Interface

The platform will have a tabbed interface with the following sections:

1. **Feed**: A Twitter-like timeline showing all content types
2. **Blog Articles**: Dedicated section for blog articles
3. **Events and Programs**: Calendar and list of upcoming events
4. **Profile Directory**: List of profiles that are at least 70% complete
5. **Marketplace Listings**: Advertisements and offerings from businesses
6. **Groups**: Communities based on common interests

## Implementation Plan

1. Create database tables and relationships
2. Implement TypeScript interfaces and types
3. Create Pinia stores for state management
4. Develop UI components for different content types
5. Implement the tabbed interface
6. Add filtering and search functionality
7. Implement post creation and interaction features

## Next Steps

- Define detailed UI mockups for each post type
- Create component hierarchy and reusable elements
- Implement database migrations
- Develop admin interface for content management
