-- Migration: Create profile completion function
-- Description: Creates a function to calculate profile completion percentage

-- Function to calculate profile completion percentage
CREATE OR REPLACE FUNCTION calculate_profile_completion(profile_id UUID)
RETURNS INTEGER AS $$
DECLARE
    profile_record RECORD;
    specialized_profile_record RECORD;
    profile_type TEXT;
    total_fields INTEGER := 0;
    completed_fields INTEGER := 0;
    completion_percentage INTEGER;
BEGIN
    -- Get the profile record
    SELECT * INTO profile_record FROM profiles WHERE id = profile_id;

    -- If profile doesn't exist, return 0
    IF profile_record IS NULL THEN
        RETURN 0;
    END IF;

    profile_type := profile_record.profile_type;

    -- Initialize completion percentage
    completion_percentage := 0;

    -- If profile type is set, check specialized profile fields (remaining 50%)
    IF profile_type = 'innovator' THEN
        -- Get the innovator profile record
        SELECT * INTO specialized_profile_record FROM innovator_profiles WHERE profile_id = profile_id;

        -- If specialized profile exists, calculate its completion
        IF specialized_profile_record IS NOT NULL THEN
            total_fields := 0;
            completed_fields := 0;

            -- Count innovator profile fields
            total_fields := total_fields + 6; -- innovation_area, innovation_stage, team_size, goals, challenges, website

            IF specialized_profile_record.innovation_area IS NOT NULL AND specialized_profile_record.innovation_area != '' THEN
                completed_fields := completed_fields + 1;
            END IF;

            IF specialized_profile_record.innovation_stage IS NOT NULL AND specialized_profile_record.innovation_stage != '' THEN
                completed_fields := completed_fields + 1;
            END IF;

            IF specialized_profile_record.team_size IS NOT NULL THEN
                completed_fields := completed_fields + 1;
            END IF;

            IF specialized_profile_record.goals IS NOT NULL AND array_length(specialized_profile_record.goals, 1) > 0 THEN
                completed_fields := completed_fields + 1;
            END IF;

            IF specialized_profile_record.challenges IS NOT NULL AND array_length(specialized_profile_record.challenges, 1) > 0 THEN
                completed_fields := completed_fields + 1;
            END IF;

            IF specialized_profile_record.website IS NOT NULL AND specialized_profile_record.website != '' THEN
                completed_fields := completed_fields + 1;
            END IF;

            -- Calculate completion based 100% on profile-specific fields
            completion_percentage := (completed_fields::FLOAT / total_fields::FLOAT) * 100;
        END IF;
    END IF;

    -- Round to nearest integer
    RETURN round(completion_percentage);
END;
$$ LANGUAGE plpgsql;

-- Function to update profile completion percentage
CREATE OR REPLACE FUNCTION update_profile_completion()
RETURNS TRIGGER AS $$
BEGIN
    -- Update the profile completion percentage
    UPDATE profiles
    SET profile_completion = calculate_profile_completion(NEW.profile_id)
    WHERE id = NEW.profile_id;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for innovator_profiles table
DROP TRIGGER IF EXISTS update_profile_completion_trigger ON public.innovator_profiles;
CREATE TRIGGER update_profile_completion_trigger
AFTER INSERT OR UPDATE ON public.innovator_profiles
FOR EACH ROW
EXECUTE FUNCTION update_profile_completion();
