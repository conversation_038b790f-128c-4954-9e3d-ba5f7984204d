/**
 * AI Profile-Based Suggestion Engine
 * 
 * This service provides intelligent, data-driven suggestions and recommendations
 * based on comprehensive user profile analysis and platform insights.
 */

import { type UserContext, type UserDetailedContext, type ActionButton } from './aiEnhancedService';

export interface ProfileSuggestion {
  id: string;
  type: 'action' | 'recommendation' | 'insight' | 'opportunity';
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  action?: ActionButton;
  category: 'profile' | 'content' | 'networking' | 'engagement' | 'growth';
  relevance_score: number;
}

export interface SuggestionEngineResult {
  suggestions: ProfileSuggestion[];
  insights: string[];
  next_steps: ActionButton[];
  growth_opportunities: string[];
}

/**
 * Generate comprehensive profile-based suggestions
 */
export function generateProfileSuggestions(userContext: UserContext): SuggestionEngineResult {
  const suggestions: ProfileSuggestion[] = [];
  const insights: string[] = [];
  const nextSteps: ActionButton[] = [];
  const growthOpportunities: string[] = [];

  if (!userContext.is_authenticated || !userContext.detailed_context) {
    return {
      suggestions: [],
      insights: ['Sign up to get personalized recommendations'],
      next_steps: [{
        type: 'action',
        label: 'Sign Up',
        icon: 'person_add',
        action: 'signup',
        color: 'primary'
      }],
      growth_opportunities: ['Join the ZbInnovation community to unlock growth opportunities']
    };
  }

  const detailedContext = userContext.detailed_context;
  const stats = detailedContext.stats;
  const profileInsights = detailedContext.insights;
  const profile = detailedContext.profile;

  // Generate profile completion suggestions
  if (stats && stats.profile_completion < 100) {
    suggestions.push(...generateProfileCompletionSuggestions(stats, profile));
  }

  // Generate content creation suggestions
  if (stats && stats.posts_count < 5) {
    suggestions.push(...generateContentSuggestions(userContext, stats));
  }

  // Generate networking suggestions
  if (stats && stats.connections_count < 20) {
    suggestions.push(...generateNetworkingSuggestions(userContext, stats));
  }

  // Generate engagement suggestions
  if (profileInsights && profileInsights.engagement_level !== 'high') {
    suggestions.push(...generateEngagementSuggestions(userContext, stats));
  }

  // Generate profile type specific suggestions
  if (userContext.profile_type) {
    suggestions.push(...generateProfileTypeSpecificSuggestions(userContext));
  }

  // Generate insights based on user data
  insights.push(...generateUserInsights(userContext));

  // Generate next steps
  nextSteps.push(...generateNextSteps(userContext));

  // Generate growth opportunities
  growthOpportunities.push(...generateGrowthOpportunities(userContext));

  // Sort suggestions by priority and relevance
  suggestions.sort((a, b) => {
    const priorityOrder = { high: 3, medium: 2, low: 1 };
    if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    }
    return b.relevance_score - a.relevance_score;
  });

  return {
    suggestions: suggestions.slice(0, 10), // Limit to top 10 suggestions
    insights: insights.slice(0, 5),
    next_steps: nextSteps.slice(0, 3),
    growth_opportunities: growthOpportunities.slice(0, 5)
  };
}

function generateProfileCompletionSuggestions(stats: any, profile: any): ProfileSuggestion[] {
  const suggestions: ProfileSuggestion[] = [];
  const completion = stats.profile_completion;

  if (completion < 30) {
    suggestions.push({
      id: 'profile-basic-info',
      type: 'action',
      priority: 'high',
      title: 'Complete Basic Profile Information',
      description: 'Add your name, bio, and profile picture to make a great first impression',
      action: {
        type: 'navigation',
        label: 'Complete Profile',
        icon: 'person',
        url: '/dashboard/profile?section=basic',
        color: 'primary'
      },
      category: 'profile',
      relevance_score: 0.9
    });
  }

  if (completion < 60) {
    suggestions.push({
      id: 'profile-skills-experience',
      type: 'action',
      priority: 'high',
      title: 'Add Skills and Experience',
      description: 'Showcase your expertise to attract the right connections and opportunities',
      action: {
        type: 'navigation',
        label: 'Add Skills',
        icon: 'psychology',
        url: '/dashboard/profile?section=skills',
        color: 'secondary'
      },
      category: 'profile',
      relevance_score: 0.8
    });
  }

  if (completion < 80) {
    suggestions.push({
      id: 'profile-innovation-details',
      type: 'action',
      priority: 'medium',
      title: 'Share Your Innovation Story',
      description: 'Tell the community about your innovations and what drives you',
      action: {
        type: 'navigation',
        label: 'Add Innovation Story',
        icon: 'lightbulb',
        url: '/dashboard/profile?section=innovations',
        color: 'orange'
      },
      category: 'profile',
      relevance_score: 0.7
    });
  }

  return suggestions;
}

function generateContentSuggestions(userContext: UserContext, stats: any): ProfileSuggestion[] {
  const suggestions: ProfileSuggestion[] = [];

  if (stats.posts_count === 0) {
    suggestions.push({
      id: 'first-post',
      type: 'action',
      priority: 'high',
      title: 'Create Your First Post',
      description: 'Introduce yourself to the community and share what you\'re working on',
      action: {
        type: 'action',
        label: 'Create Post',
        icon: 'edit',
        action: 'create-post',
        color: 'accent'
      },
      category: 'content',
      relevance_score: 0.9
    });
  } else if (stats.posts_count < 3) {
    suggestions.push({
      id: 'share-innovation',
      type: 'action',
      priority: 'medium',
      title: 'Share Your Innovation Project',
      description: 'Create a detailed post about your current innovation or project',
      action: {
        type: 'action',
        label: 'Share Innovation',
        icon: 'lightbulb',
        action: 'create-post',
        color: 'orange'
      },
      category: 'content',
      relevance_score: 0.8
    });
  }

  // Profile type specific content suggestions
  switch (userContext.profile_type) {
    case 'innovator':
      suggestions.push({
        id: 'innovation-showcase',
        type: 'recommendation',
        priority: 'medium',
        title: 'Showcase Your Innovation Process',
        description: 'Share behind-the-scenes content about how you develop innovations',
        category: 'content',
        relevance_score: 0.7
      });
      break;
    case 'investor':
      suggestions.push({
        id: 'investment-insights',
        type: 'recommendation',
        priority: 'medium',
        title: 'Share Investment Insights',
        description: 'Post about trends you\'re seeing in the innovation space',
        category: 'content',
        relevance_score: 0.7
      });
      break;
    case 'mentor':
      suggestions.push({
        id: 'mentorship-content',
        type: 'recommendation',
        priority: 'medium',
        title: 'Share Mentorship Wisdom',
        description: 'Create content that helps other innovators learn from your experience',
        category: 'content',
        relevance_score: 0.7
      });
      break;
  }

  return suggestions;
}

function generateNetworkingSuggestions(userContext: UserContext, stats: any): ProfileSuggestion[] {
  const suggestions: ProfileSuggestion[] = [];

  if (stats.connections_count < 5) {
    suggestions.push({
      id: 'first-connections',
      type: 'action',
      priority: 'high',
      title: 'Make Your First Connections',
      description: 'Connect with at least 5 people to start building your network',
      action: {
        type: 'navigation',
        label: 'Find People',
        icon: 'people',
        url: '/virtual-community?tab=profiles',
        color: 'info'
      },
      category: 'networking',
      relevance_score: 0.9
    });
  } else if (stats.connections_count < 15) {
    suggestions.push({
      id: 'expand-network',
      type: 'action',
      priority: 'medium',
      title: 'Expand Your Network',
      description: 'Connect with more innovators in your field to unlock opportunities',
      action: {
        type: 'navigation',
        label: 'Browse Profiles',
        icon: 'search',
        url: '/virtual-community?tab=profiles',
        color: 'secondary'
      },
      category: 'networking',
      relevance_score: 0.7
    });
  }

  // Profile type specific networking suggestions
  switch (userContext.profile_type) {
    case 'innovator':
      suggestions.push({
        id: 'connect-investors',
        type: 'opportunity',
        priority: 'medium',
        title: 'Connect with Potential Investors',
        description: 'Find investors who are interested in your type of innovation',
        action: {
          type: 'navigation',
          label: 'Find Investors',
          icon: 'account_balance',
          url: '/virtual-community?tab=profiles&filter=investor',
          color: 'green'
        },
        category: 'networking',
        relevance_score: 0.8
      });
      break;
    case 'investor':
      suggestions.push({
        id: 'connect-innovators',
        type: 'opportunity',
        priority: 'medium',
        title: 'Discover Promising Innovators',
        description: 'Connect with innovators who match your investment criteria',
        action: {
          type: 'navigation',
          label: 'Find Innovators',
          icon: 'lightbulb',
          url: '/virtual-community?tab=profiles&filter=innovator',
          color: 'orange'
        },
        category: 'networking',
        relevance_score: 0.8
      });
      break;
  }

  return suggestions;
}

function generateEngagementSuggestions(userContext: UserContext, stats: any): ProfileSuggestion[] {
  const suggestions: ProfileSuggestion[] = [];

  if (stats && stats.likes_received + stats.comments_received < 10) {
    suggestions.push({
      id: 'increase-engagement',
      type: 'recommendation',
      priority: 'medium',
      title: 'Increase Community Engagement',
      description: 'Like, comment, and share content to build relationships and visibility',
      category: 'engagement',
      relevance_score: 0.6
    });
  }

  if (stats && stats.activity_count < 20) {
    suggestions.push({
      id: 'be-more-active',
      type: 'recommendation',
      priority: 'medium',
      title: 'Be More Active on the Platform',
      description: 'Regular activity helps you stay visible and build stronger connections',
      category: 'engagement',
      relevance_score: 0.6
    });
  }

  return suggestions;
}

function generateProfileTypeSpecificSuggestions(userContext: UserContext): ProfileSuggestion[] {
  const suggestions: ProfileSuggestion[] = [];
  const stats = userContext.detailed_context?.stats;

  switch (userContext.profile_type) {
    case 'innovator':
      if (stats && stats.posts_count < 3) {
        suggestions.push({
          id: 'innovator-showcase',
          type: 'opportunity',
          priority: 'high',
          title: 'Showcase Your Innovation',
          description: 'Create detailed posts about your innovations to attract investors and collaborators',
          category: 'growth',
          relevance_score: 0.9
        });
      }
      break;

    case 'investor':
      if (stats && stats.connections_count < 20) {
        suggestions.push({
          id: 'investor-network',
          type: 'opportunity',
          priority: 'high',
          title: 'Build Your Deal Flow',
          description: 'Connect with more innovators to increase your investment opportunities',
          category: 'growth',
          relevance_score: 0.9
        });
      }
      break;

    case 'mentor':
      if (stats && stats.activity_count < 15) {
        suggestions.push({
          id: 'mentor-engagement',
          type: 'opportunity',
          priority: 'high',
          title: 'Engage with Innovators',
          description: 'Actively comment and provide guidance to build your mentorship reputation',
          category: 'growth',
          relevance_score: 0.9
        });
      }
      break;
  }

  return suggestions;
}

function generateUserInsights(userContext: UserContext): string[] {
  const insights: string[] = [];
  const stats = userContext.detailed_context?.stats;
  const profileInsights = userContext.detailed_context?.insights;

  if (stats) {
    if (stats.profile_completion >= 80) {
      insights.push('Your profile is well-completed, which helps attract quality connections');
    }

    if (stats.connections_count > 20) {
      insights.push('You have a strong network that can help amplify your innovations');
    }

    if (stats.posts_count > 5) {
      insights.push('Your content creation shows good platform engagement');
    }

    if (stats.likes_received > 20) {
      insights.push('Your content resonates well with the community');
    }
  }

  if (profileInsights) {
    if (profileInsights.engagement_level === 'high') {
      insights.push('Your high engagement level makes you a valuable community member');
    }

    if (profileInsights.network_size === 'large') {
      insights.push('Your large network provides excellent opportunities for collaboration');
    }
  }

  return insights;
}

function generateNextSteps(userContext: UserContext): ActionButton[] {
  const nextSteps: ActionButton[] = [];
  const stats = userContext.detailed_context?.stats;
  const insights = userContext.detailed_context?.insights;

  // Priority next steps based on current state
  if (stats?.profile_completion < 50) {
    nextSteps.push({
      type: 'navigation',
      label: 'Complete Profile',
      icon: 'person',
      url: '/dashboard/profile',
      color: 'primary'
    });
  }

  if (stats?.posts_count === 0) {
    nextSteps.push({
      type: 'action',
      label: 'Create First Post',
      icon: 'edit',
      action: 'create-post',
      color: 'accent'
    });
  }

  if (stats?.connections_count < 5) {
    nextSteps.push({
      type: 'navigation',
      label: 'Find Connections',
      icon: 'people',
      url: '/virtual-community?tab=profiles',
      color: 'info'
    });
  }

  return nextSteps;
}

function generateGrowthOpportunities(userContext: UserContext): string[] {
  const opportunities: string[] = [];
  const profileType = userContext.profile_type;
  const stats = userContext.detailed_context?.stats;

  switch (profileType) {
    case 'innovator':
      opportunities.push(
        'Participate in innovation challenges and competitions',
        'Apply for funding opportunities through the platform',
        'Collaborate with other innovators on joint projects',
        'Seek mentorship from experienced entrepreneurs'
      );
      break;

    case 'investor':
      opportunities.push(
        'Join investor groups and syndicate deals',
        'Host investment workshops and events',
        'Mentor promising innovators in your portfolio',
        'Share market insights to build thought leadership'
      );
      break;

    case 'mentor':
      opportunities.push(
        'Lead mentorship programs and workshops',
        'Write thought leadership content',
        'Connect innovators with relevant opportunities',
        'Build a reputation as a go-to expert in your field'
      );
      break;

    default:
      opportunities.push(
        'Explore different ways to contribute to the innovation ecosystem',
        'Attend platform events and networking sessions',
        'Learn from successful innovators and their journeys',
        'Find your unique role in the community'
      );
  }

  return opportunities;
}
