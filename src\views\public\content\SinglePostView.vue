<template>
  <q-page class="single-post-view">
    <div class="container q-mx-auto q-pa-md">
      <div class="row justify-center">
        <div class="col-12 col-md-8">
          <q-card flat bordered class="post-card q-mb-md">
            <!-- Back Button -->
            <q-card-section class="q-pb-none">
              <q-btn
                flat
                color="primary"
                icon="arrow_back"
                label="Back"
                @click="goBack"
              />
            </q-card-section>

            <!-- Loading State -->
            <div v-if="loading" class="text-center q-pa-xl">
              <q-spinner color="primary" size="3em" />
              <p>Loading post details...</p>
            </div>

            <!-- Error State -->
            <div v-else-if="error" class="text-center q-pa-xl text-negative">
              <q-icon name="error" size="3em" />
              <p>{{ error }}</p>
              <q-btn color="primary" label="Go Back" @click="goBack" />
            </div>

            <!-- Post Content -->
            <template v-else-if="post">
              <!-- Post Header -->
              <q-card-section class="q-pb-none">
                <!-- Post Type and Category Badges -->
                <div class="row items-center q-mb-sm">
                  <!-- Main Post Type Badge (Always displayed) -->
                  <q-badge
                    v-if="getMainPostType"
                    :color="getPostTypeColor(getMainPostType)"
                    class="q-mr-sm"
                    size="lg"
                  >
                    {{ getMainPostType }}
                  </q-badge>

                  <!-- Subcategory Badge (Second level) -->
                  <q-badge
                    v-if="getSubCategory"
                    :color="getSubCategoryColor(getSubCategory)"
                    class="q-mr-sm"
                    size="lg"
                  >
                    {{ getSubCategory }}
                  </q-badge>

                  <!-- Category Badge (Third level) -->
                  <q-badge
                    v-if="post.category && post.category !== getMainPostType && post.category !== getSubCategory"
                    :color="getCategoryColor(post.category)"
                    class="q-mr-sm"
                    size="lg"
                  >
                    {{ post.category }}
                  </q-badge>

                  <!-- Blog Category Badge (if different from other categories) -->
                  <q-badge
                    v-if="post.blogCategory && !isCategoryDuplicate(post.blogCategory)"
                    :color="getCategoryColor(post.blogCategory)"
                    class="q-mr-sm"
                    size="lg"
                  >
                    {{ post.blogCategory }}
                  </q-badge>
                </div>

                <div class="row items-center q-mb-sm">
                  <!-- User Avatar (clickable for user posts only) -->
                  <user-avatar
                    :name="post.author"
                    :email="post.authorEmail"
                    :avatar-url="post.avatar"
                    :user-id="post.authorId"
                    size="40px"
                    :clickable="isUserPost"
                    @click="isUserPost && navigateToUserProfile"
                  />

                  <!-- Author Info (clickable for user posts only) -->
                  <div
                    class="q-ml-sm user-info"
                    :class="{ 'cursor-pointer': isUserPost }"
                    @click="isUserPost && navigateToUserProfile"
                  >
                    <div class="text-weight-bold">
                      {{ post.author }}
                      <q-badge
                        v-if="!isUserPost && post.post_type === 'admin'"
                        color="red"
                        class="q-ml-xs"
                      >
                        Admin
                      </q-badge>
                      <q-badge
                        v-else-if="!isUserPost && post.post_type === 'automated'"
                        color="grey"
                        class="q-ml-xs"
                      >
                        System
                      </q-badge>
                    </div>
                    <div class="text-caption">{{ post.date }}</div>
                  </div>
                </div>
              </q-card-section>

              <!-- Post Title (if available) -->
              <q-card-section class="q-pt-none">
                <div v-if="post.title" class="text-h5 q-mb-sm">{{ post.title }}</div>

                <!-- Post Image (if available) -->
                <q-img
                  v-if="post.image"
                  :src="post.image"
                  class="rounded-borders q-mb-md"
                  style="max-height: 400px"
                />

                <!-- Post Content -->
                <div class="post-content q-mb-md" v-html="formattedContent"></div>

                <!-- Dynamic Post Metadata -->
                <div class="post-metadata q-mb-md">
                  <!-- Marketplace-specific metadata -->
                  <template v-if="isMarketplacePost">
                    <div v-if="marketplacePrice" class="row items-center q-mb-sm">
                      <q-icon name="attach_money" size="sm" class="q-mr-xs text-green-6" />
                      <span class="text-h6 text-green-6 text-weight-bold">{{ marketplacePrice }}</span>
                    </div>

                    <div v-if="post.location" class="row items-center q-mb-sm">
                      <q-icon name="location_on" size="xs" class="q-mr-xs text-grey-6" />
                      <span class="text-caption">{{ post.location }}</span>
                    </div>

                    <div v-if="post.category" class="row items-center q-mb-sm">
                      <q-icon name="category" size="xs" class="q-mr-xs text-grey-6" />
                      <span class="text-caption">Category: {{ post.category }}</span>
                    </div>

                    <div v-if="post.condition" class="row items-center q-mb-sm">
                      <q-icon name="verified" size="xs" class="q-mr-xs text-grey-6" />
                      <span class="text-caption">Condition: {{ post.condition }}</span>
                    </div>

                    <div v-if="post.listingType" class="row items-center q-mb-sm">
                      <q-icon name="sell" size="xs" class="q-mr-xs text-grey-6" />
                      <span class="text-caption">Type: {{ post.listingType }}</span>
                    </div>
                  </template>

                  <!-- Event-specific metadata -->
                  <template v-else-if="isEventPost">
                    <div v-if="eventStartDate" class="row items-center q-mb-sm">
                      <q-icon name="event" size="xs" class="q-mr-xs text-orange-6" />
                      <span class="text-caption">
                        <strong>Start:</strong> {{ eventStartDate }}
                      </span>
                    </div>

                    <div v-if="eventEndDate" class="row items-center q-mb-sm">
                      <q-icon name="event_available" size="xs" class="q-mr-xs text-orange-6" />
                      <span class="text-caption">
                        <strong>End:</strong> {{ eventEndDate }}
                      </span>
                    </div>

                    <div v-if="post.eventLocation || post.location" class="row items-center q-mb-sm">
                      <q-icon name="location_on" size="xs" class="q-mr-xs text-grey-6" />
                      <span class="text-caption">{{ post.eventLocation || post.location }}</span>
                    </div>

                    <div v-if="post.eventType" class="row items-center q-mb-sm">
                      <q-icon name="category" size="xs" class="q-mr-xs text-grey-6" />
                      <span class="text-caption">Type: {{ post.eventType }}</span>
                    </div>

                    <div v-if="post.eventOrganizer" class="row items-center q-mb-sm">
                      <q-icon name="person" size="xs" class="q-mr-xs text-grey-6" />
                      <span class="text-caption">Organizer: {{ post.eventOrganizer }}</span>
                    </div>

                    <div v-if="post.eventRegistrationUrl" class="row items-center q-mb-sm">
                      <q-icon name="link" size="xs" class="q-mr-xs text-blue-6" />
                      <a :href="post.eventRegistrationUrl" target="_blank" class="text-caption text-blue-6">
                        Registration Link
                      </a>
                    </div>
                  </template>

                  <!-- Blog-specific metadata -->
                  <template v-else-if="isBlogPost">
                    <div v-if="blogPublishDate" class="row items-center q-mb-sm">
                      <q-icon name="calendar_today" size="xs" class="q-mr-xs text-purple-6" />
                      <span class="text-caption">Published: {{ blogPublishDate }}</span>
                    </div>

                    <div v-if="post.blogCategory" class="row items-center q-mb-sm">
                      <q-icon name="category" size="xs" class="q-mr-xs text-grey-6" />
                      <span class="text-caption">Category: {{ post.blogCategory }}</span>
                    </div>

                    <div v-if="blogReadTime" class="row items-center q-mb-sm">
                      <q-icon name="schedule" size="xs" class="q-mr-xs text-grey-6" />
                      <span class="text-caption">{{ blogReadTime }}</span>
                    </div>
                  </template>

                  <!-- Opportunity-specific metadata -->
                  <template v-else-if="isOpportunityPost">
                    <div v-if="opportunityDeadline" class="row items-center q-mb-sm">
                      <q-icon name="schedule" size="xs" class="q-mr-xs text-red-6" />
                      <span class="text-caption">
                        <strong>Deadline:</strong> {{ opportunityDeadline }}
                      </span>
                    </div>

                    <div v-if="post.opportunityType" class="row items-center q-mb-sm">
                      <q-icon name="work" size="xs" class="q-mr-xs text-grey-6" />
                      <span class="text-caption">Type: {{ post.opportunityType }}</span>
                    </div>

                    <div v-if="post.applicationUrl" class="row items-center q-mb-sm">
                      <q-icon name="link" size="xs" class="q-mr-xs text-blue-6" />
                      <a :href="post.applicationUrl" target="_blank" class="text-caption text-blue-6">
                        Application Link
                      </a>
                    </div>
                  </template>

                  <!-- Job/Talent-specific metadata -->
                  <template v-else-if="isJobTalentPost">
                    <div v-if="post.company" class="row items-center q-mb-sm">
                      <q-icon name="business" size="xs" class="q-mr-xs text-grey-6" />
                      <span class="text-caption">Company: {{ post.company }}</span>
                    </div>

                    <div v-if="post.location" class="row items-center q-mb-sm">
                      <q-icon name="location_on" size="xs" class="q-mr-xs text-grey-6" />
                      <span class="text-caption">{{ post.location }}</span>
                    </div>

                    <div v-if="post.jobType" class="row items-center q-mb-sm">
                      <q-icon name="work" size="xs" class="q-mr-xs text-grey-6" />
                      <span class="text-caption">Type: {{ post.jobType }}</span>
                    </div>

                    <div v-if="jobCompensation" class="row items-center q-mb-sm">
                      <q-icon name="attach_money" size="xs" class="q-mr-xs text-green-6" />
                      <span class="text-caption">{{ jobCompensation }}</span>
                    </div>
                  </template>

                  <!-- Innovation Challenge-specific metadata -->
                  <template v-else-if="isInnovationChallengePost">
                    <div v-if="post.prize" class="row items-center q-mb-sm">
                      <q-icon name="emoji_events" size="xs" class="q-mr-xs text-yellow-6" />
                      <span class="text-caption">Prize: {{ post.prize }}</span>
                    </div>

                    <div v-if="challengeDeadline" class="row items-center q-mb-sm">
                      <q-icon name="schedule" size="xs" class="q-mr-xs text-red-6" />
                      <span class="text-caption">
                        <strong>Deadline:</strong> {{ challengeDeadline }}
                      </span>
                    </div>

                    <div v-if="post.submissionUrl" class="row items-center q-mb-sm">
                      <q-icon name="link" size="xs" class="q-mr-xs text-blue-6" />
                      <a :href="post.submissionUrl" target="_blank" class="text-caption text-blue-6">
                        Submission Link
                      </a>
                    </div>
                  </template>

                  <!-- Resource-specific metadata -->
                  <template v-else-if="isResourcePost">
                    <div v-if="post.resourceType" class="row items-center q-mb-sm">
                      <q-icon name="folder" size="xs" class="q-mr-xs text-grey-6" />
                      <span class="text-caption">Type: {{ post.resourceType }}</span>
                    </div>

                    <div v-if="post.fileType" class="row items-center q-mb-sm">
                      <q-icon name="description" size="xs" class="q-mr-xs text-grey-6" />
                      <span class="text-caption">Format: {{ post.fileType }}</span>
                    </div>

                    <div v-if="resourceFileSize" class="row items-center q-mb-sm">
                      <q-icon name="storage" size="xs" class="q-mr-xs text-grey-6" />
                      <span class="text-caption">Size: {{ resourceFileSize }}</span>
                    </div>

                    <div v-if="post.downloadUrl" class="row items-center q-mb-sm">
                      <q-icon name="download" size="xs" class="q-mr-xs text-blue-6" />
                      <a :href="post.downloadUrl" target="_blank" class="text-caption text-blue-6">
                        Download
                      </a>
                    </div>
                  </template>

                  <!-- Success Story-specific metadata -->
                  <template v-else-if="isSuccessStoryPost">
                    <div v-if="post.achievementType" class="row items-center q-mb-sm">
                      <q-icon name="star" size="xs" class="q-mr-xs text-yellow-6" />
                      <span class="text-caption">Achievement: {{ post.achievementType }}</span>
                    </div>

                    <div v-if="post.achievementDetails" class="row items-center q-mb-sm">
                      <q-icon name="info" size="xs" class="q-mr-xs text-grey-6" />
                      <span class="text-caption">{{ post.achievementDetails }}</span>
                    </div>
                  </template>

                  <!-- Question/Help-specific metadata -->
                  <template v-else-if="isQuestionHelpPost">
                    <div v-if="post.topicArea" class="row items-center q-mb-sm">
                      <q-icon name="topic" size="xs" class="q-mr-xs text-grey-6" />
                      <span class="text-caption">Topic: {{ post.topicArea }}</span>
                    </div>

                    <div v-if="post.isResolved !== undefined" class="row items-center q-mb-sm">
                      <q-icon
                        :name="post.isResolved ? 'check_circle' : 'help'"
                        size="xs"
                        class="q-mr-xs"
                        :class="post.isResolved ? 'text-green-6' : 'text-orange-6'"
                      />
                      <span class="text-caption">
                        {{ post.isResolved ? 'Resolved' : 'Open' }}
                      </span>
                    </div>
                  </template>

                  <!-- Announcement-specific metadata -->
                  <template v-else-if="isAnnouncementPost">
                    <div v-if="post.isPinned" class="row items-center q-mb-sm">
                      <q-icon name="push_pin" size="xs" class="q-mr-xs text-red-6" />
                      <span class="text-caption text-weight-bold">Pinned</span>
                    </div>

                    <div v-if="post.announcementPriority" class="row items-center q-mb-sm">
                      <q-icon name="priority_high" size="xs" class="q-mr-xs text-orange-6" />
                      <span class="text-caption">Priority: {{ post.announcementPriority }}</span>
                    </div>
                  </template>

                  <!-- Automated post metadata -->
                  <template v-else-if="isAutomatedPost">
                    <div v-if="post.activityType" class="row items-center q-mb-sm">
                      <q-icon name="smart_toy" size="xs" class="q-mr-xs text-blue-6" />
                      <span class="text-caption">Activity: {{ post.activityType }}</span>
                    </div>
                  </template>

                  <!-- General metadata for other post types -->
                  <template v-else>
                    <div v-if="post.location" class="row items-center q-mb-sm">
                      <q-icon name="location_on" size="xs" class="q-mr-xs text-grey-6" />
                      <span class="text-caption">{{ post.location }}</span>
                    </div>

                    <div v-if="post.eventDate" class="row items-center q-mb-sm">
                      <q-icon name="event" size="xs" class="q-mr-xs text-grey-6" />
                      <span class="text-caption">{{ post.eventDate }}</span>
                    </div>
                  </template>
                </div>

                <div v-if="post.tags && post.tags.length" class="row items-center q-mb-sm">
                  <q-icon name="label" size="xs" class="q-mr-xs" />
                  <div>
                    <div class="text-caption text-grey q-mb-xs">Tags:</div>
                    <q-chip
                      v-for="tag in processedTags"
                      :key="tag"
                      dense
                      size="sm"
                      outline
                      :color="getTagColor(tag)"
                      class="q-mr-xs"
                    >
                      #{{ tag }}
                    </q-chip>
                  </div>
                </div>
              </q-card-section>

              <!-- Post Actions -->
              <q-card-actions>
                <interaction-buttons
                  :content-id="post.id"
                  :content-type="getContentType()"
                  :content-data="getContentData()"
                  :is-liked="isLiked"
                  :is-saved="post.isSaved || false"
                  :likes-count="post.likesCount || 0"
                  :comments-count="post.commentsCount || 0"
                  :show-comments="showComments"
                  :show-view-details="false"
                  :dynamic-c-t-a="dynamicCTA"
                  size="md"

                  @comment="toggleComments"
                  @share="handleShare"
                  @save="handleSave"
                  @dynamic-c-t-a="handleDynamicCTA"
                />
              </q-card-actions>

              <!-- Comments Section -->
              <q-card-section v-if="showComments">
                <q-separator class="q-my-md" />
                <div class="text-h6 q-mb-md">Comments</div>

                <div v-if="loadingComments" class="text-center q-pa-sm">
                  <q-spinner color="primary" size="2em" />
                  <p class="q-ma-none">Loading comments...</p>
                </div>

                <div v-else-if="!commentsData.length" class="text-center q-pa-sm">
                  <p class="q-ma-none">No comments yet. Be the first to comment!</p>
                </div>

                <div v-else>
                  <q-list>
                    <q-item v-for="comment in commentsData" :key="comment.id" class="q-py-sm">
                      <q-item-section avatar top>
                        <user-avatar
                          :name="comment.author"
                          :email="comment.email"
                          :avatar-url="comment.avatar"
                          :user-id="comment.authorId"
                          size="32px"
                          @click="comment.authorId && navigateToUserProfile(comment.authorId)"
                        />
                      </q-item-section>
                      <q-item-section>
                        <q-item-label class="text-weight-bold">{{ comment.author }}</q-item-label>
                        <q-item-label caption>{{ comment.date }}</q-item-label>
                        <div class="q-mt-xs">{{ comment.content }}</div>
                      </q-item-section>
                    </q-item>
                  </q-list>
                </div>

                <!-- Comment Form -->
                <div class="q-mt-md">
                  <div class="row items-center">
                    <user-avatar
                      name="Current User"
                      size="32px"
                      class="q-mr-sm"
                      :clickable="false"
                    />
                    <q-input
                      v-model="newComment"
                      dense
                      outlined
                      placeholder="Write a comment..."
                      class="col"
                      @keyup.enter="!submittingComment && submitComment()"
                    >
                      <template v-slot:after>
                        <q-btn
                          round
                          dense
                          flat
                          color="primary"
                          icon="send"
                          @click="submitComment"
                          :disable="!newComment.trim() || submittingComment"
                          :loading="submittingComment"
                        />
                      </template>
                    </q-input>
                  </div>
                </div>
              </q-card-section>
            </template>
          </q-card>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { usePostsStore } from '../../../stores/posts';
import { useContentInteractions } from '../../../composables/useContentInteractions';
import { useUserInteractionsStore } from '../../../stores/userInteractions';
import UserAvatar from '../../../components/common/UserAvatar.vue';
import InteractionButtons from '../../../components/common/InteractionButtons.vue';

const route = useRoute();
const router = useRouter();
const postsStore = usePostsStore();
const contentInteractions = useContentInteractions();
const userInteractionsStore = useUserInteractionsStore();

// State
const loading = ref(true);
const error = ref<string | null>(null);
const post = ref<any>(null);
const showComments = ref(false);
const newComment = ref('');
const loadingComments = ref(false);
const submittingComment = ref(false);
const actionLoading = ref(false);
const commentsData = ref([]);

// Use isLiked from post data instead of local state
const isLiked = computed(() => post.value?.isLiked || false);

// Post type detection - comprehensive for all post types
const getPostTypeCategory = computed(() => {
  if (!post.value) return 'general';

  const subType = post.value.subType?.toLowerCase() || '';
  const postType = post.value.postType?.toLowerCase() || '';
  const dbPostType = post.value.post_type?.toLowerCase() || '';

  // Check subType first (most specific)
  if (subType === 'marketplace') return 'marketplace';
  if (subType === 'event') return 'event';
  if (subType === 'blog') return 'blog';
  if (subType === 'opportunity') return 'opportunity';
  if (subType === 'group') return 'group';

  // Check postType
  if (postType === 'marketplace') return 'marketplace';
  if (postType === 'event') return 'event';
  if (postType === 'blog') return 'blog';
  if (postType === 'opportunity') return 'opportunity';
  if (postType === 'group') return 'group';
  if (postType === 'resource') return 'resource';
  if (postType === 'success_story') return 'success_story';
  if (postType === 'question_help') return 'question_help';
  if (postType === 'job_talent') return 'job_talent';
  if (postType === 'innovation_challenge') return 'innovation_challenge';

  // Check database post_type
  if (dbPostType === 'admin') return 'announcement';
  if (dbPostType === 'automated') return 'automated';

  // Check for specific fields to determine type
  if (post.value.price !== undefined || post.value.condition) return 'marketplace';
  if (post.value.eventStartDatetime || post.value.eventDate || post.value.eventLocation) return 'event';
  if (post.value.blogCategory || post.value.blogTitle) return 'blog';
  if (post.value.opportunityType || post.value.deadline) return 'opportunity';
  if (post.value.isPinned !== undefined || post.value.announcementPriority !== undefined) return 'announcement';
  if (post.value.activityType || post.value.relatedEntityId) return 'automated';

  return 'general';
});

// Individual post type checks
const isMarketplacePost = computed(() => getPostTypeCategory.value === 'marketplace');
const isEventPost = computed(() => getPostTypeCategory.value === 'event');
const isBlogPost = computed(() => getPostTypeCategory.value === 'blog');
const isOpportunityPost = computed(() => getPostTypeCategory.value === 'opportunity');
const isGroupPost = computed(() => getPostTypeCategory.value === 'group');
const isAnnouncementPost = computed(() => getPostTypeCategory.value === 'announcement');
const isResourcePost = computed(() => getPostTypeCategory.value === 'resource');
const isSuccessStoryPost = computed(() => getPostTypeCategory.value === 'success_story');
const isQuestionHelpPost = computed(() => getPostTypeCategory.value === 'question_help');
const isJobTalentPost = computed(() => getPostTypeCategory.value === 'job_talent');
const isInnovationChallengePost = computed(() => getPostTypeCategory.value === 'innovation_challenge');
const isAutomatedPost = computed(() => getPostTypeCategory.value === 'automated');

// Marketplace-specific computed properties
const marketplacePrice = computed(() => {
  if (!post.value || !isMarketplacePost.value) return '';

  const price = post.value.price;
  const currency = post.value.currency || 'USD';

  if (!price || price === 0 || price === '0') {
    return 'Free';
  }

  // Format price with currency
  if (typeof price === 'number') {
    return `${currency} ${price.toLocaleString()}`;
  } else if (typeof price === 'string') {
    // Check if price already includes currency symbol
    if (price.includes('$') || price.includes('USD') || price.includes('ZWL')) {
      return price;
    }
    return `${currency} ${price}`;
  }

  return '';
});

// Event-specific computed properties
const eventStartDate = computed(() => {
  if (!post.value || !isEventPost.value) return '';

  // Try multiple possible field names for event start date
  const startDate = post.value.eventStartDatetime ||
                   post.value.eventStartDate ||
                   post.value.startDate ||
                   post.value.eventDate;

  if (!startDate) return '';

  try {
    return new Date(startDate).toLocaleString();
  } catch (e) {
    return startDate; // Return as-is if parsing fails
  }
});

const eventEndDate = computed(() => {
  if (!post.value || !isEventPost.value) return '';

  // Try multiple possible field names for event end date
  const endDate = post.value.eventEndDatetime ||
                 post.value.eventEndDate ||
                 post.value.endDate;

  if (!endDate) return '';

  try {
    return new Date(endDate).toLocaleString();
  } catch (e) {
    return endDate; // Return as-is if parsing fails
  }
});

// Blog-specific computed properties
const blogReadTime = computed(() => {
  if (!post.value || !isBlogPost.value) return '';

  // Calculate reading time if not provided
  if (post.value.readTime) {
    return `${post.value.readTime} min read`;
  }

  // Estimate reading time based on content length (average 200 words per minute)
  const content = post.value.content || post.value.blogFullContent || '';
  const wordCount = content.split(/\s+/).length;
  const readTime = Math.ceil(wordCount / 200);

  return `${readTime} min read`;
});

const blogPublishDate = computed(() => {
  if (!post.value || !isBlogPost.value) return '';

  try {
    return new Date(post.value.createdAt || '').toLocaleDateString();
  } catch (e) {
    return '';
  }
});

// Opportunity-specific computed properties
const opportunityDeadline = computed(() => {
  if (!post.value || !isOpportunityPost.value) return '';

  const deadline = post.value.deadline || post.value.opportunityDeadline;
  if (!deadline) return '';

  try {
    return new Date(deadline).toLocaleDateString();
  } catch (e) {
    return deadline;
  }
});

// Job/Talent-specific computed properties
const jobCompensation = computed(() => {
  if (!post.value || !isJobTalentPost.value) return '';

  return post.value.compensation || '';
});

// Innovation Challenge-specific computed properties
const challengeDeadline = computed(() => {
  if (!post.value || !isInnovationChallengePost.value) return '';

  const deadline = post.value.deadline;
  if (!deadline) return '';

  try {
    return new Date(deadline).toLocaleDateString();
  } catch (e) {
    return deadline;
  }
});

// Resource-specific computed properties
const resourceFileSize = computed(() => {
  if (!post.value || !isResourcePost.value) return '';

  const fileSize = post.value.fileSize;
  if (!fileSize) return '';

  // Convert bytes to human readable format
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  if (fileSize === 0) return '0 Bytes';

  const i = Math.floor(Math.log(fileSize) / Math.log(1024));
  return Math.round(fileSize / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
});

// Format the post content for proper HTML display
const formattedContent = computed(() => {
  if (!post.value?.content) return '';

  let content = post.value.content;

  // Check if content is a JSON string and parse it
  if (typeof content === 'string' && (content.startsWith('{') || content.startsWith('['))) {
    try {
      const parsedContent = JSON.parse(content);

      // Extract description based on post subType
      if (parsedContent.description) {
        content = parsedContent.description;
      } else if (parsedContent.eventDetails && post.value.subType?.toLowerCase() === 'event') {
        // For event posts with eventDetails
        content = parsedContent.eventDetails.description || content;
      } else if (parsedContent.marketplaceDetails && post.value.subType?.toLowerCase() === 'marketplace') {
        // For marketplace posts with marketplaceDetails
        content = parsedContent.marketplaceDetails.description || content;
      }
    } catch (e) {
      // If parsing fails, use the original content
      console.log('Failed to parse JSON content:', e);
    }
  }

  // If content already has HTML tags, return it as is
  if (/<[a-z][\s\S]*>/i.test(content)) {
    return content;
  }

  // For plain text, convert newlines to <br> tags for proper display
  return content.replace(/\n/g, '<br>');
});

// Computed properties
// Determine if this is a user post (for clickable avatar/name)
const isUserPost = computed(() => {
  // If post_type is missing or empty, check if userId exists (indicating a user post)
  if (!post.value?.post_type) {
    return !!post.value?.userId;
  }
  // If it's a platform post but has a userId, it's a user-created post
  if (post.value.post_type.toLowerCase() === 'platform' && post.value.userId) {
    return true;
  }
  return false;
});

// Get the main post type for display at the top
const getMainPostType = computed(() => {
  if (!post.value) return '';

  // Get the post type from the database
  const postType = post.value.post_type?.toLowerCase() || '';
  const subType = post.value.subType?.toLowerCase() || '';

  // For platform posts (user-created), show the special category based on subType
  if (postType === 'platform' || !postType) {
    if (subType === 'blog') {
      return 'Blog';
    } else if (subType === 'event') {
      return 'Event';
    } else if (subType === 'opportunity' || subType.includes('funding')) {
      return 'Opportunity';
    } else if (subType === 'group') {
      return 'Group';
    } else if (subType === 'marketplace') {
      return 'Marketplace';
    } else if (subType) {
      // Capitalize the first letter of subType if it exists
      return subType.charAt(0).toUpperCase() + subType.slice(1);
    }

    // Default for platform posts without subType
    return 'General';
  }

  // For admin or automated posts
  if (postType === 'admin') {
    return 'Admin Post';
  } else if (postType === 'automated') {
    return 'System Update';
  }

  // Fallback to capitalize the post_type if nothing else matches
  return postType.charAt(0).toUpperCase() + postType.slice(1);
});

// Get the subcategory for display at the top
const getSubCategory = computed(() => {
  if (!post.value) return '';

  // For events, check for event type or format
  if (getMainPostType.value === 'Event') {
    return post.value.eventType ||
           (post.value.content?.toLowerCase().includes('virtual') ? 'Virtual' :
            post.value.content?.toLowerCase().includes('physical') ? 'Physical' : '');
  }

  // For blog posts, use the blog category
  if (getMainPostType.value === 'Blog') {
    return post.value.blogCategory || '';
  }

  // For marketplace posts
  if (getMainPostType.value === 'Marketplace') {
    if (post.value.content?.toLowerCase().includes('product')) {
      return 'Product';
    } else if (post.value.content?.toLowerCase().includes('service')) {
      return 'Service';
    }
  }

  // For opportunity posts
  if (getMainPostType.value === 'Opportunity') {
    if (post.value.content?.toLowerCase().includes('funding')) {
      return 'Funding';
    } else if (post.value.content?.toLowerCase().includes('mentorship')) {
      return 'Mentorship';
    } else if (post.value.content?.toLowerCase().includes('collaboration')) {
      return 'Collaboration';
    }
  }

  return '';
});

// Check if a category is already displayed to avoid duplicates
function isCategoryDuplicate(category: string): boolean {
  if (!category) return false;

  const lowerCategory = category.toLowerCase();
  const lowerMainType = getMainPostType.value.toLowerCase();
  const lowerSubCategory = getSubCategory.value.toLowerCase();
  const lowerPostCategory = post.value.category?.toLowerCase() || '';

  return lowerCategory === lowerMainType ||
         lowerCategory === lowerSubCategory ||
         lowerCategory === lowerPostCategory;
}

// Get color for the main post type badge
function getPostTypeColor(type: string): string {
  const typeColors: Record<string, string> = {
    'Blog': 'purple',
    'Event': 'orange',
    'Opportunity': 'green',
    'Group': 'blue',
    'Marketplace': 'teal',
    'Admin Post': 'red',
    'System Update': 'grey',
    'General': 'primary'
  };

  return typeColors[type] || 'primary';
}

// Get color for subcategory badge
function getSubCategoryColor(subCat: string): string {
  const subCatColors: Record<string, string> = {
    'Virtual': 'blue',
    'Physical': 'green',
    'Hybrid': 'purple',
    'Workshop': 'teal',
    'Conference': 'deep-orange',
    'Networking': 'indigo',
    'Funding': 'green-9',
    'Mentorship': 'purple-7',
    'Collaboration': 'blue-7',
    'Product': 'blue-5',
    'Service': 'teal-5'
  };

  return subCatColors[subCat] || 'grey-7';
}

// Get color for category badge
function getCategoryColor(category: string): string {
  // Default colors for common categories
  const categoryColors: Record<string, string> = {
    'Technology': 'blue',
    'Business': 'green',
    'Education': 'purple',
    'Health': 'red',
    'Arts': 'pink',
    'Science': 'indigo',
    'Sports': 'orange',
    'Entertainment': 'deep-purple'
  };

  return categoryColors[category] || 'grey-8';
}

// Get color for tag chips
function getTagColor(tag: string): string {
  // Check if tag matches any main post type
  const mainPostTypes = {
    'blog': 'purple',
    'event': 'orange',
    'opportunity': 'green',
    'group': 'blue',
    'marketplace': 'teal',
    'general': 'primary'
  };

  // Check if tag matches any subcategory
  const subCategories = {
    'virtual': 'blue',
    'physical': 'green',
    'hybrid': 'purple',
    'workshop': 'teal',
    'conference': 'deep-orange',
    'networking': 'indigo',
    'funding': 'green-9',
    'mentorship': 'purple-7',
    'collaboration': 'blue-7',
    'product': 'blue-5',
    'service': 'teal-5'
  };

  const lowerTag = tag.toLowerCase();

  // First check if it's a main post type
  if (mainPostTypes[lowerTag]) {
    return mainPostTypes[lowerTag];
  }

  // Then check if it's a subcategory
  if (subCategories[lowerTag]) {
    return subCategories[lowerTag];
  }

  // Default color for regular tags
  return 'primary';
}

// Process tags to handle object-formatted tags
const processedTags = computed(() => {
  if (!post.value?.tags) return [];

  // If tags is a string (possibly from HTML), convert it to an array
  if (typeof post.value.tags === 'string') {
    // Try to parse as JSON if it looks like a JSON array
    if (post.value.tags.startsWith('[') && post.value.tags.endsWith(']')) {
      try {
        const parsedTags = JSON.parse(post.value.tags);
        // Process the parsed tags to extract value/label
        return parsedTags.map((tag: any) => {
          if (typeof tag === 'object' && tag !== null) {
            return tag.value || tag.label || 'Tag';
          }
          return tag;
        });
      } catch (e) {
        console.log('Failed to parse tags JSON:', e);

        // Check if it contains stringified objects with label/value pairs
        if (post.value.tags.includes('"label"') && post.value.tags.includes('"value"')) {
          // First try to extract values (preferred)
          const valueMatches = [...post.value.tags.matchAll(/"value":"([^"]+)"/g)];
          if (valueMatches.length > 0) {
            return valueMatches.map(match => match[1]);
          }

          // If no values found, try to extract labels
          const labelMatches = [...post.value.tags.matchAll(/"label":"([^"]+)"/g)];
          if (labelMatches.length > 0) {
            return labelMatches.map(match => match[1]);
          }
        }

        // If parsing fails, split by comma as fallback
        return post.value.tags.replace(/[\[\]"']/g, '').split(',').map((tag: string) => tag.trim());
      }
    }
    // If it's not JSON-like, split by comma
    return post.value.tags.split(',').map((tag: string) => tag.trim());
  }

  // If it's already an array, return as is but process any object tags
  return post.value.tags.map((tag: any) => {
    // Check if the tag is an object with label/value properties (from select components)
    if (typeof tag === 'object' && tag !== null) {
      // If it has a value property, use that (preferred)
      if (tag.value) return tag.value;
      // If no value, but has a label property, use that
      if (tag.label) return tag.label;
      // Otherwise return a generic tag name instead of stringifying
      return 'Tag';
    }
    // If it's a string or number, return as is
    return tag;
  });
});

// Dynamic CTA based on post type
const dynamicCTA = computed(() => {
  if (!post.value) return { label: '', icon: '', action: '', color: 'primary' };

  const postType = post.value.postType?.toLowerCase() || '';
  const subType = post.value.subType?.toLowerCase() || '';
  const category = post.value.category?.toLowerCase() || '';

  // Determine CTA based on post type and subtype
  if (postType === 'event' || subType === 'event') {
    return {
      label: 'Register',
      icon: 'event_available',
      action: 'register',
      color: 'orange'
    };
  } else if (postType === 'opportunity' || subType === 'opportunity' ||
             subType.includes('funding') || subType.includes('grant')) {
    return {
      label: 'Apply',
      icon: 'assignment_turned_in',
      action: 'apply',
      color: 'green'
    };
  } else if (postType === 'marketplace' || subType === 'marketplace') {
    return {
      label: 'Contact Seller',
      icon: 'contact_mail',
      action: 'contact',
      color: 'teal'
    };
  } else if (postType === 'group' || subType === 'group') {
    return {
      label: 'Join Group',
      icon: 'group_add',
      action: 'join',
      color: 'blue'
    };
  } else if (subType === 'mentorship' || subType.includes('mentor') ||
             category === 'mentorship') {
    return {
      label: 'Request Mentorship',
      icon: 'school',
      action: 'requestMentorship',
      color: 'purple'
    };
  } else if (subType === 'collaboration' || subType.includes('partner') ||
             category === 'collaboration') {
    return {
      label: 'Collaborate',
      icon: 'handshake',
      action: 'collaborate',
      color: 'blue-7'
    };
  } else if (subType === 'training' || subType.includes('workshop') ||
             category === 'training') {
    return {
      label: 'Register',
      icon: 'event_available',
      action: 'register',
      color: 'indigo'
    };
  }

  // Default for other post types
  return { label: '', icon: '', action: '', color: 'primary' };
});

// Fetch post data on mount
onMounted(async () => {
  try {
    loading.value = true;

    // Get post ID from route
    const postId = route.params.id || route.query.postId;

    if (!postId) {
      error.value = 'Post not found';
      return;
    }

    // Fetch post from store
    const fetchedPost = await postsStore.getPostById(Number(postId));

    if (fetchedPost) {
      post.value = fetchedPost;
    } else {
      error.value = 'Post not found';
    }
  } catch (err) {
    console.error('Error fetching post:', err);
    error.value = 'Failed to load post. Please try again.';
  } finally {
    loading.value = false;
  }
});

// Methods
function goBack() {
  router.back();
}

// Note: Like functionality is now handled directly by InteractionButtons component

function handleShare() {
  if (post.value) {
    contentInteractions.shareContent(post.value.id, 'post', post.value);
  }
}

function handleSave() {
  if (post.value) {
    contentInteractions.toggleSaveContent(post.value.id, 'post', post.value);
  }
}



async function handleDynamicCTA() {
  if (!post.value) return;

  actionLoading.value = true;

  try {
    const action = dynamicCTA.value.action;
    const postId = post.value.id;

    // Handle different actions based on post type
    switch(action) {
      case 'register':
        // If the post has a registration link, open it
        if (post.value.eventRegistrationUrl || post.value.link) {
          window.open(post.value.eventRegistrationUrl || post.value.link, '_blank');
        } else {
          // Otherwise, track the registration in the database
          await registerForEvent(postId);
        }
        break;

      case 'apply':
        // If the post has an application URL, open it
        if (post.value.applicationUrl) {
          window.open(post.value.applicationUrl, '_blank');
        } else {
          // Otherwise, show application form or track the application
          await applyForOpportunity(postId);
        }
        break;

      case 'contact':
        // If the post has contact information, show it
        await contactSeller(postId);
        break;

      case 'join':
        // Join the group
        await joinGroup(postId);
        break;

      case 'requestMentorship':
        // Request mentorship
        await requestMentorship(postId);
        break;

      case 'collaborate':
        // Request collaboration
        await requestCollaboration(postId);
        break;
    }
  } catch (err) {
    console.error(`Error handling ${dynamicCTA.value.action} action:`, err);
  } finally {
    actionLoading.value = false;
  }
}

// Comment methods using unified system
async function toggleComments() {
  await contentInteractions.toggleComments(
    post.value.id,
    'post',
    showComments,
    commentsData,
    loadingComments
  );
}

async function submitComment() {
  const success = await contentInteractions.submitComment(
    post.value.id,
    'post',
    newComment.value,
    commentsData,
    newComment,
    submittingComment
  );

  if (success) {
    // Note: Comment count is already updated by the posts store, no need to duplicate here
  }
}

// Helper functions for dynamic CTA actions
async function registerForEvent(postId: number) {
  await contentInteractions.registerForEvent(postId);
}

async function applyForOpportunity(postId: number) {
  await contentInteractions.applyForOpportunity(postId);
}

async function contactSeller(postId: number) {
  if (post.value) {
    await contentInteractions.contactUser(postId, 'post', post.value);
  }
}

async function joinGroup(postId: number) {
  try {
    // In a real implementation, this would call an API to join the group
    console.log('Joining group with ID:', postId);
    // For now, just show a success message
    alert('You have successfully joined the group!');
  } catch (err) {
    console.error('Error joining group:', err);
    alert('Failed to join group. Please try again later.');
  }
}

async function requestMentorship(postId: number) {
  try {
    // In a real implementation, this would call an API to request mentorship
    console.log('Requesting mentorship for post with ID:', postId);
    // For now, just show a success message
    alert('Mentorship request sent! You will be contacted shortly.');
  } catch (err) {
    console.error('Error requesting mentorship:', err);
    alert('Failed to request mentorship. Please try again later.');
  }
}

async function requestCollaboration(postId: number) {
  try {
    // In a real implementation, this would call an API to request collaboration
    console.log('Requesting collaboration for post with ID:', postId);
    // For now, just show a success message
    alert('Collaboration request sent! You will be contacted shortly.');
  } catch (err) {
    console.error('Error requesting collaboration:', err);
    alert('Failed to request collaboration. Please try again later.');
  }
}

// Navigate to user profile page
function navigateToUserProfile(specificAuthorId?: number | string) {
  // Only navigate if this is a user post or a specific author ID is provided
  if (!isUserPost.value && !specificAuthorId) {
    console.log('Not navigating: Post is not from a regular user');
    return;
  }

  // Use the specific author ID if provided, otherwise use the post author ID
  const authorId = specificAuthorId || (post.value ? post.value.authorId : null);

  if (authorId) {
    console.log('Navigating to user profile:', authorId);
    router.push({
      name: 'user-profile',
      params: { id: authorId }
    });
  } else {
    console.warn('Cannot navigate to user profile: No author ID available');
  }
}

// Helper functions for InteractionButtons component
function getContentType() {
  if (!post.value) return 'post';

  // Use the comprehensive post type detection
  const postTypeCategory = getPostTypeCategory.value;

  // Map post type categories to content types for InteractionButtons
  switch (postTypeCategory) {
    case 'marketplace':
      return 'post'; // Changed from 'listing' to 'post' for consistent routing
    case 'event':
      return 'event';
    case 'group':
      return 'group';
    case 'blog':
    case 'opportunity':
    case 'resource':
    case 'success_story':
    case 'question_help':
    case 'job_talent':
    case 'innovation_challenge':
    case 'announcement':
    case 'automated':
    default:
      return 'post';
  }
}

function getContentData() {
  if (!post.value) return {};

  // Base content data
  const baseData = {
    id: post.value.id,
    title: post.value.title,
    content: post.value.content,
    description: post.value.description,
    postType: post.value.postType,
    subType: post.value.subType,
    userId: post.value.userId,
    author: post.value.author,
    isSaved: post.value.isSaved,
    isFavorite: post.value.isFavorite
  };

  // Add type-specific fields based on post type
  const postTypeCategory = getPostTypeCategory.value;

  switch (postTypeCategory) {
    case 'marketplace':
      return {
        ...baseData,
        price: post.value.price,
        currency: post.value.currency,
        location: post.value.location,
        category: post.value.category,
        condition: post.value.condition,
        listingType: post.value.listingType
      };

    case 'event':
      return {
        ...baseData,
        eventStartDatetime: post.value.eventStartDatetime,
        eventEndDatetime: post.value.eventEndDatetime,
        eventLocation: post.value.eventLocation,
        eventType: post.value.eventType,
        eventOrganizer: post.value.eventOrganizer,
        eventRegistrationUrl: post.value.eventRegistrationUrl
      };

    case 'blog':
      return {
        ...baseData,
        blogCategory: post.value.blogCategory,
        blogTitle: post.value.blogTitle,
        blogFullContent: post.value.blogFullContent,
        slug: post.value.slug
      };

    case 'opportunity':
      return {
        ...baseData,
        opportunityType: post.value.opportunityType,
        deadline: post.value.deadline,
        applicationUrl: post.value.applicationUrl
      };

    default:
      return baseData;
  }
}

// This function is now defined above
</script>

<style scoped>
.post-metadata {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid #e0e0e0;
}

.post-metadata .row {
  align-items: center;
}

.post-metadata .text-caption {
  font-size: 0.875rem;
  line-height: 1.4;
}

.post-metadata .text-h6 {
  font-weight: 600;
}

/* Specific styling for different post types */
.post-metadata .text-green-6 {
  color: #43a047 !important;
}

.post-metadata .text-orange-6 {
  color: #fb8c00 !important;
}

.post-metadata .text-purple-6 {
  color: #8e24aa !important;
}

.post-metadata .text-red-6 {
  color: #e53935 !important;
}

.post-metadata .text-blue-6 {
  color: #1e88e5 !important;
}

.post-metadata .text-yellow-6 {
  color: #fdd835 !important;
}

.post-metadata .text-grey-6 {
  color: #757575 !important;
}

/* Link styling */
.post-metadata a {
  text-decoration: none;
  transition: color 0.2s ease;
}

.post-metadata a:hover {
  text-decoration: underline;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .post-metadata {
    padding: 12px;
    margin-left: -8px;
    margin-right: -8px;
    border-radius: 0;
  }

  .post-metadata .text-h6 {
    font-size: 1.1rem;
  }
}
</style>

<style scoped>
.single-post-view {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.container {
  max-width: 1200px;
}

.post-card {
  border-radius: 8px;
}

.post-content {
  white-space: normal;
  line-height: 1.6;
}

.post-content img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 12px 0;
  border-radius: 4px;
}

@media (max-width: 599px) {
  .container {
    padding: 8px;
  }
}
</style>
