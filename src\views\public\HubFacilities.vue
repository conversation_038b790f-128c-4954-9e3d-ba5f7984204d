<template>
  <q-page>
    <div class="facilities-hero">
      <div class="hero-background"></div>
      <div class="container q-mx-auto q-px-md">
        <div class="row justify-center">
          <div class="col-12 col-sm-10 col-md-8">
            <div class="hero-content-card">
              <div class="hero-badge">Innovation Space</div>
              <h1 class="text-h2 text-weight-light q-mb-md">Hub Facilities & Events</h1>
              <p class="text-body1 q-mb-lg">
                Discover our state-of-the-art innovation spaces and exciting events designed to inspire creativity,
                foster collaboration, and accelerate your business growth.
              </p>
              <!-- Hero actions removed as requested -->
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="facilities-overview q-py-xl">
      <div class="container q-mx-auto q-px-md">
        <div class="row justify-center">
          <div class="col-10 col-sm-10 col-md-8">
            <div class="overview-header text-center q-mb-xl">
              <div class="section-badge">About Our Space</div>
              <h2 class="text-h3 text-primary q-mb-md">World-Class Innovation Hub</h2>
              <p class="text-body1 q-mx-auto" style="max-width: 700px;">
                Our innovation hub provides a modern, flexible workspace designed to inspire creativity and foster collaboration.
              </p>
            </div>

            <div class="row q-col-gutter-xl">
              <div class="col-12 col-md-6">
                <div class="overview-content">
                  <p class="text-body1 q-mb-md">
                    With over 2,000 square meters of space, we offer everything innovators need to turn their ideas into reality. Our facilities are designed with flexibility in mind, allowing for various work styles and collaboration needs.
                  </p>
                  <p class="text-body1 q-mb-md">
                    Whether you're a solo entrepreneur, a small startup team, or an established company looking for innovation space, our facilities can accommodate your needs with flexible options.
                  </p>
                  <div class="overview-stats row q-col-gutter-md q-mt-lg">
                    <div class="col-6">
                      <div class="stat-card">
                        <div class="text-h3 text-primary">2,000+</div>
                        <div class="text-caption">Square Meters</div>
                      </div>
                    </div>
                    <div class="col-6">
                      <div class="stat-card">
                        <div class="text-h3 text-primary">24/7</div>
                        <div class="text-caption">Access Available</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-12 col-md-6">
                <div class="overview-image-container">
                  <q-img
                    src="https://images.unsplash.com/photo-1497366754035-f200968a6e72?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1469&q=80"
                    class="overview-image"
                  />
                  <div class="image-accent"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>





    <!-- Events & Programs Section -->
    <div class="events-section q-py-xl bg-grey-2">
      <div class="container q-mx-auto q-px-md">
        <div class="row justify-center">
          <div class="col-10 col-sm-10 col-md-8">
            <div class="overview-header text-center q-mb-xl">
              <div class="section-badge">Events & Programs</div>
              <h2 class="text-h3 text-primary q-mb-md">Upcoming Events</h2>
              <p class="text-body1 q-mx-auto" style="max-width: 700px;">
                Join our exciting events and programs designed to foster innovation and collaboration.
              </p>
            </div>

            <!-- Featured Event -->
            <q-card class="featured-event-card q-mb-xl">
              <div class="row q-col-gutter-none">
                <div class="col-12 col-md-6">
                  <q-img
                    src="https://images.unsplash.com/photo-1540575467063-178a50c2df87?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80"
                    style="height: 100%; min-height: 300px;"
                  >
                    <div class="absolute-top-left q-ma-md">
                      <q-badge color="primary" class="text-bold q-pa-sm text-subtitle1">Featured</q-badge>
                    </div>
                  </q-img>
                </div>
                <div class="col-12 col-md-6">
                  <q-card-section class="q-pa-lg">
                    <div class="text-overline text-primary">Innovation Summit</div>
                    <div class="text-h4 q-mb-md">Annual Innovation Summit 2025</div>
                    <div class="row items-center q-mb-md">
                      <q-icon name="event" color="primary" size="sm" class="q-mr-sm" />
                      <span class="text-body2">November 15-17, 2025</span>
                      <q-space />
                      <q-icon name="place" color="primary" size="sm" class="q-mr-sm" />
                      <span class="text-body2">ZbInnovation, Harare</span>
                    </div>
                    <p class="text-body1 q-mb-md">
                      Join us for our flagship annual event bringing together innovators, entrepreneurs, investors, and industry leaders for three days of inspiring talks, workshops, and networking opportunities.
                    </p>
                    <div class="row justify-between items-center">
                      <q-badge color="secondary" text-color="primary" class="q-pa-xs">
                        <q-icon name="people" size="xs" class="q-mr-xs" />
                        <span>500+ Attendees</span>
                      </q-badge>
                      <q-btn color="primary" label="Register Now" class="q-px-md" @click="registerForEvent('flagship-event')" />
                    </div>
                  </q-card-section>
                </div>
              </div>
            </q-card>

            <!-- Upcoming Events Grid -->
            <div class="row q-col-gutter-md q-mb-xl">
              <div class="col-12 col-md-6" v-for="(event, index) in upcomingEvents" :key="index">
                <q-card class="event-card" :class="{ 'animate-fade-in': isVisible }" :style="`animation-delay: ${index * 0.1}s`">
                  <q-img
                    :src="event.image"
                    :ratio="16/9"
                  >
                    <div class="absolute-bottom text-subtitle2 text-white q-pa-xs" :class="event.type === 'virtual' ? 'bg-blue-8' : 'bg-primary'">
                      {{ event.type === 'virtual' ? 'Virtual Event' : 'Physical Event' }}
                    </div>
                  </q-img>
                  <q-card-section>
                    <div class="text-subtitle2 text-primary">{{ event.category }}</div>
                    <div class="text-h6">{{ event.title }}</div>
                    <div class="row items-center q-mt-sm">
                      <q-icon name="event" color="grey" size="xs" class="q-mr-xs" />
                      <span class="text-caption text-grey">{{ event.date }}</span>
                      <q-space />
                      <q-chip size="sm" :color="event.type === 'virtual' ? 'blue-1' : 'green-1'" :text-color="event.type === 'virtual' ? 'blue-8' : 'primary'" dense>
                        {{ event.type === 'virtual' ? 'Online' : 'In-Person' }}
                      </q-chip>
                    </div>
                  </q-card-section>
                  <q-separator />
                  <q-card-actions>
                    <q-btn flat color="primary" label="Learn More" />
                    <q-space />
                    <q-btn flat color="primary" icon="event_available" label="Register" @click="registerForEvent(event.id || event.title)" />
                  </q-card-actions>
                </q-card>
              </div>
            </div>

            <!-- Programs Section -->
            <div class="overview-header text-center q-mb-lg">
              <div class="section-badge">Programs</div>
              <h3 class="text-h4 text-primary q-mb-md">Our Programs</h3>
            </div>

            <div class="row q-col-gutter-lg">
              <div class="col-12 col-md-4" v-for="(program, index) in programs" :key="index">
                <q-card class="program-card" :class="{ 'animate-fade-in': isVisible }" :style="`animation-delay: ${index * 0.2}s`">
                  <q-img
                    :src="program.image"
                    :ratio="1"
                  >
                    <div class="absolute-full flex flex-center program-overlay">
                      <div class="text-h5 text-white text-center">{{ program.title }}</div>
                    </div>
                  </q-img>
                  <q-card-section>
                    <p class="text-body2 q-mb-md">{{ program.description }}</p>
                    <q-list dense>
                      <q-item v-for="(feature, fIndex) in program.features" :key="fIndex" dense class="q-pa-none q-my-xs">
                        <q-item-section avatar class="q-ml-none">
                          <q-icon name="check_circle" color="primary" size="xs" />
                        </q-item-section>
                        <q-item-section>
                          <q-item-label class="text-caption">{{ feature }}</q-item-label>
                        </q-item-section>
                      </q-item>
                    </q-list>
                  </q-card-section>
                  <q-card-actions align="right">
                    <q-btn flat color="primary" label="View More" @click="viewProgramDetails(program)" />
                  </q-card-actions>
                </q-card>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- FAQ Section -->
    <div class="faq-section q-py-xl">
      <div class="container q-mx-auto q-px-md">
        <div class="row justify-center">
          <div class="col-10 col-sm-10 col-md-8">
            <div class="overview-header text-center q-mb-xl">
              <div class="section-badge">FAQ</div>
              <h2 class="text-h3 text-primary q-mb-md">Frequently Asked Questions</h2>
              <p class="text-body1 q-mx-auto" style="max-width: 700px;">
                Find answers to common questions about our events and programs.
              </p>
            </div>

            <q-card flat bordered>
              <q-list>
                <q-expansion-item
                  v-for="(faq, index) in facilitiesFAQs"
                  :key="index"
                  group="faq"
                  icon="help_outline"
                  :label="faq.question"
                  header-class="text-primary"
                  expand-icon-class="text-primary"
                >
                  <q-card>
                    <q-card-section>
                      {{ faq.answer }}
                    </q-card-section>
                  </q-card>
                </q-expansion-item>
              </q-list>
            </q-card>
          </div>
        </div>
      </div>
    </div>

    <!-- Facility Details Dialog -->
    <q-dialog v-model="showDetails" persistent maximized transition-show="slide-up" transition-hide="slide-down">
      <q-card class="facility-details-dialog">
        <q-bar class="bg-primary text-white">
          <div class="text-weight-bold">{{ facilities.find(f => f.id === activeTab)?.title }}</div>
          <q-space />
          <q-btn dense flat icon="close" v-close-popup>
            <q-tooltip>Close</q-tooltip>
          </q-btn>
        </q-bar>

        <q-card-section class="row items-center no-wrap">
          <div class="col-12 col-md-6">
            <q-img
              :src="facilities.find(f => f.id === activeTab)?.image"
              style="height: 400px; border-radius: 8px;"
              class="facility-detail-image"
            />
          </div>
          <div class="col-12 col-md-6 q-pa-md">
            <h2 class="text-h4 text-primary text-weight-light q-mb-md">{{ facilities.find(f => f.id === activeTab)?.title }}</h2>
            <p class="text-body1 q-mb-lg">{{ facilities.find(f => f.id === activeTab)?.description }}</p>

            <div class="text-h6 text-primary q-mb-md">Features</div>
            <q-list bordered separator>
              <q-item v-for="(feature, index) in facilities.find(f => f.id === activeTab)?.features" :key="index">
                <q-item-section avatar>
                  <q-icon name="check_circle" color="positive" />
                </q-item-section>
                <q-item-section>{{ feature }}</q-item-section>
              </q-item>
            </q-list>
          </div>
        </q-card-section>

        <q-card-actions align="right" class="bg-white">
          <q-btn flat label="Close" color="primary" v-close-popup class="action-btn" />
        </q-card-actions>
      </q-card>
    </q-dialog>



    <!-- Program Details Dialog -->
    <q-dialog v-model="showProgramDetails" persistent>
      <q-card style="width: 700px; max-width: 90vw;" class="form-dialog">
        <q-card-section class="bg-secondary text-white q-py-md q-px-lg">
          <div class="row items-center justify-between">
            <div class="text-h6 text-weight-light">{{ selectedProgram?.title }}</div>
            <q-btn dense flat icon="close" v-close-popup aria-label="Close dialog">
              <q-tooltip>Close</q-tooltip>
            </q-btn>
          </div>
        </q-card-section>

        <q-card-section class="q-pa-lg scroll" style="max-height: 60vh;">
          <div v-if="selectedProgram">
            <q-img
              :src="selectedProgram.image"
              style="height: 200px; border-radius: 8px;"
              class="q-mb-md"
            />
            <p class="text-body1 q-mb-lg">{{ selectedProgram.description }}</p>

            <div class="text-h6 text-primary q-mb-md">Program Features</div>
            <q-list bordered separator>
              <q-item v-for="(feature, index) in selectedProgram.features" :key="index">
                <q-item-section avatar>
                  <q-icon name="check_circle" color="positive" />
                </q-item-section>
                <q-item-section>{{ feature }}</q-item-section>
              </q-item>
            </q-list>
          </div>
        </q-card-section>

        <q-card-actions align="right" class="bg-white q-pa-lg">
          <q-btn flat label="Close" color="primary" v-close-popup class="action-btn" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { useHubFacilitiesStore } from '@/stores/hubFacilities';

const $q = useQuasar();
const router = useRouter();
const authStore = useAuthStore();
const hubFacilitiesStore = useHubFacilitiesStore();

// Interactive facility details
const showDetails = ref(false);
const activeTab = ref('coworking');

// Program details dialog
const showProgramDetails = ref(false);
const selectedProgram = ref(null);

// Facilities data
const facilities = ref([
  {
    id: 'coworking',
    title: 'Co-working Space',
    shortDescription: 'Open-plan workspace with high-speed internet and ergonomic furniture.',
    description: 'Our open-plan workspace is designed to foster collaboration and creativity. With high-speed internet, ergonomic furniture, and a vibrant atmosphere, it\'s the perfect place to work and connect with like-minded innovators.',
    features: ['24/7 access for members', 'High-speed fiber internet', 'Ergonomic workstations', 'Printing and scanning facilities', 'Coffee and refreshments included'],
    image: 'https://images.unsplash.com/photo-1577412647305-991150c7d163?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'
  },
  {
    id: 'meeting',
    title: 'Meeting Rooms',
    shortDescription: 'Professional meeting spaces with the latest technology for presentations.',
    description: 'Our professional meeting spaces are equipped with the latest technology for presentations and video conferencing. Perfect for client meetings, team collaborations, or workshops.',
    features: ['Various sizes (4-20 people)', 'HD video conferencing', 'Interactive whiteboards', 'Easy online booking system', 'Catering options available'],
    image: 'https://images.unsplash.com/photo-1517502884422-41eaead166d4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1025&q=80'
  },
  {
    id: 'event',
    title: 'Event Space',
    shortDescription: 'Versatile event area for workshops, seminars, and networking events.',
    description: 'Our versatile event area is perfect for workshops, seminars, networking events, and product launches. With flexible seating arrangements and professional AV equipment, we can accommodate a variety of event formats.',
    features: ['Capacity for up to 150 people', 'Professional AV equipment', 'Flexible seating arrangements', 'Catering options available', 'Event planning assistance'],
    image: 'https://images.unsplash.com/photo-1416339134316-0e91dc9ded92?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1467&q=80'
  },
  {
    id: 'prototyping',
    title: 'Prototyping Lab',
    shortDescription: 'Equipped with 3D printers and tools for hardware prototyping.',
    description: 'Our prototyping lab is equipped with cutting-edge technology including 3D printers, laser cutters, and tools for hardware prototyping and product development. Perfect for bringing your ideas to life.',
    features: ['3D printers and scanners', 'Laser cutting equipment', 'Electronics workbenches', 'Technical support available', 'Materials available for purchase'],
    image: 'https://images.unsplash.com/photo-1581091226033-d5c48150dbaa?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'
  },
  {
    id: 'private',
    title: 'Private Offices',
    shortDescription: 'Dedicated office spaces for startups and small teams.',
    description: 'Our private offices provide a dedicated, secure workspace for startups and small teams looking for a professional environment. Each office comes fully furnished and gives you access to all hub amenities.',
    features: ['Secure, lockable offices', 'Sizes for 2-10 people', 'Customizable workspace', 'Access to all hub amenities', '24/7 access'],
    image: 'https://images.unsplash.com/photo-1600132806370-bf17e65e942f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80'
  },
  {
    id: 'relaxation',
    title: 'Relaxation Areas',
    shortDescription: 'Comfortable spaces for breaks, casual meetings, and networking.',
    description: 'Our relaxation areas provide comfortable spaces for breaks, casual meetings, and networking with other innovators. Take a moment to recharge, enjoy a coffee, or have an informal discussion in our thoughtfully designed spaces.',
    features: ['Café and refreshment area', 'Comfortable seating', 'Game room for unwinding', 'Outdoor terrace', 'Quiet zones'],
    image: 'https://images.unsplash.com/photo-1525610553991-2bede1a236e2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'
  }
]);



// Events data
const upcomingEvents = ref([
  {
    id: 'startup-pitch-competition',
    title: 'Startup Pitch Competition',
    category: 'Entrepreneurship',
    date: 'October 25, 2025',
    type: 'physical',
    image: 'https://images.unsplash.com/photo-1475721027785-f74eccf877e2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'
  },
  {
    id: 'digital-marketing-masterclass',
    title: 'Digital Marketing Masterclass',
    category: 'Workshop',
    date: 'November 5, 2025',
    type: 'virtual',
    image: 'https://images.unsplash.com/photo-1432888498266-38ffec3eaf0a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1474&q=80'
  },
  {
    id: 'blockchain-technology-seminar',
    title: 'Blockchain Technology Seminar',
    category: 'Technology',
    date: 'November 12, 2025',
    type: 'physical',
    image: 'https://images.unsplash.com/photo-1639322537228-f710d846310a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1632&q-80'
  },
  {
    id: 'women-in-tech-networking',
    title: 'Women in Tech Networking',
    category: 'Networking',
    date: 'December 20, 2025',
    type: 'virtual',
    image: 'https://images.unsplash.com/photo-1573164713988-8665fc963095?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1469&q=80'
  }
]);

// Programs data
const programs = ref([
  {
    title: 'Startup Incubator',
    description: 'A 6-month program designed to help early-stage startups develop their business models and prepare for investment.',
    features: ['Mentorship from industry experts', 'Workspace access', 'Funding opportunities', 'Networking events'],
    image: 'https://images.unsplash.com/photo-1559136555-9303baea8ebd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'
  },
  {
    title: 'Innovation Accelerator',
    description: 'An intensive 3-month program for established startups looking to scale their operations and reach new markets.',
    features: ['Business strategy development', 'Market access support', 'Investor connections', 'Growth workshops'],
    image: 'https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'
  },
  {
    title: 'Youth Innovation Program',
    description: 'A program designed to nurture innovation and entrepreneurship skills among young people aged 16-24.',
    features: ['Skills development workshops', 'Mentorship', 'Project funding', 'Networking opportunities'],
    image: 'https://images.unsplash.com/photo-1529390079861-591de354faf5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'
  }
]);

// FAQ data for events and programs
const facilitiesFAQs = ref([
  {
    question: 'How can I register for upcoming events?',
    answer: 'You can register for events through our online platform or by clicking the "Register Now" button on any event listing. Some events may require advance registration due to limited capacity. We recommend registering early to secure your spot.'
  },
  {
    question: 'Are there any costs associated with attending events?',
    answer: 'Many of our events are free for members and the innovation community. Some specialized workshops or conferences may have a nominal fee to cover materials and refreshments. Pricing information is clearly displayed on each event listing.'
  },
  {
    question: 'What types of programs do you offer?',
    answer: 'We offer various programs including the Startup Incubator Program, Business Accelerator Program, and Youth Innovation Program. Each program is designed to support different stages of entrepreneurial development with mentorship, funding opportunities, and skill development.'
  },
  {
    question: 'How do I apply for your incubator or accelerator programs?',
    answer: 'Applications for our programs are typically open during specific periods throughout the year. You can apply through our website by submitting your business plan, team information, and completing the application form. Selection is based on innovation potential, market viability, and team capability.'
  },
  {
    question: 'Can I host my own event at the hub?',
    answer: 'Yes! We welcome community-driven events that align with our mission of fostering innovation and entrepreneurship. Please contact us directly to discuss hosting opportunities and we will work with you to make it happen.'
  },
  {
    question: 'Do you offer virtual events and programs?',
    answer: 'Absolutely! We offer both in-person and virtual events to accommodate different preferences and reach a wider audience. Virtual events include webinars, online workshops, and digital networking sessions. Check our event listings for format details.'
  }
]);

// Animation for facility cards
const isVisible = ref(false);
onMounted(() => {
  setTimeout(() => {
    isVisible.value = true;
  }, 500);
});

// Event registration handler - simple implementation for both logged in and non-logged in users
const registerForEvent = async (eventId: string) => {
  console.log('Register for event:', eventId);

  // Check if user is authenticated
  if (!authStore.isAuthenticated) {
    // For non-logged in users, redirect to sign-in with return path
    $q.notify({
      color: 'info',
      message: 'Please sign in to register for events',
      icon: 'info'
    });
    router.push({
      name: 'sign-in',
      query: { redirect: router.currentRoute.value.fullPath }
    });
    return;
  }

  try {
    // For logged in users, register for the event
    const success = await hubFacilitiesStore.registerForEvent(eventId);

    if (success) {
      $q.notify({
        color: 'positive',
        message: 'Successfully registered for event!',
        icon: 'event_available'
      });
    }
  } catch (error) {
    console.error('Error registering for event:', error);
    $q.notify({
      color: 'negative',
      message: 'Failed to register for event. Please try again.',
      icon: 'error'
    });
  }
};

// Program details handler
const viewProgramDetails = (program: any) => {
  selectedProgram.value = program;
  showProgramDetails.value = true;
};
</script>

<style scoped>
.container {
  width: 100%;
  max-width: 1400px;
}

.facilities-hero {
  padding: 0;
  position: relative;
  overflow: hidden;
  background-color: #fff;
  min-height: 400px;
  display: flex;
  align-items: center;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('https://images.unsplash.com/photo-1497366811353-6870744d04b2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1469&q=80');
  background-size: cover;
  background-position: center;
  opacity: 0.9;
  z-index: 0;
  height: 25%;
}

.hero-background::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(13, 138, 62, 0.8) 0%, rgba(0, 0, 0, 0.8) 100%);
  z-index: 1;
}

.hero-content-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 40px;
  position: relative;
  z-index: 2;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin-top: -40px;
  margin-bottom: -40px;
}

.hero-badge {
  display: inline-block;
  background-color: #0D8A3E;
  color: white;
  font-weight: 500;
  font-size: 14px;
  padding: 6px 16px;
  border-radius: 20px;
  margin-bottom: 20px;
  letter-spacing: 1px;
}

.hero-actions {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-top: 30px;
}

.action-btn {
  font-size: 14px;
  padding: 8px 16px;
  border-radius: 4px;
  text-transform: none;
  font-weight: 500;
}

@media (max-width: 599px) {
  .hero-actions {
    flex-direction: column;
    align-items: flex-start;
  }

  .action-btn {
    margin-left: 0 !important;
    margin-top: 8px;
    width: 100%;
  }

  .action-btn:first-child {
    margin-top: 0;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.facilities-overview, .facilities-gallery, .facilities-showcase {
  padding: 80px 0;
}

.overview-header {
  position: relative;
}

.section-badge {
  display: inline-block;
  background-color: rgba(13, 138, 62, 0.1);
  color: #0D8A3E;
  font-weight: 500;
  font-size: 14px;
  padding: 6px 16px;
  border-radius: 20px;
  margin-bottom: 20px;
  letter-spacing: 1px;
}

.overview-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.overview-image-container {
  position: relative;
  padding: 20px;
}

.overview-image {
  border-radius: 12px;
  height: 400px;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  transition: transform 0.5s ease;
}

.overview-image:hover {
  transform: scale(1.03);
}

.image-accent {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  border: 3px solid #0D8A3E;
  border-radius: 12px;
  transform: translate(15px, 15px);
  z-index: -1;
}

.stat-card {
  background-color: rgba(13, 138, 62, 0.05);
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
}

.stat-card:hover {
  background-color: rgba(13, 138, 62, 0.1);
  transform: translateY(-5px);
}

/* Gallery Styles */
.gallery-container {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.gallery-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
}

.gallery-pagination {
  display: flex;
  justify-content: center;
  margin: 0 20px;
}

.pagination-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(13, 138, 62, 0.2);
  margin: 0 5px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pagination-dot.active {
  background-color: #0D8A3E;
  transform: scale(1.2);
}

/* Facilities Categories */
.facilities-categories {
  margin-bottom: 40px;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px 10px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.3s ease;
  height: 100%;
  text-align: center;
  border-bottom: 3px solid transparent;
}

.category-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.category-item.active {
  border-bottom: 3px solid #0D8A3E;
  background-color: rgba(13, 138, 62, 0.05);
}

.category-name {
  font-size: 0.9rem;
  font-weight: 500;
  margin-top: 5px;
  color: #333;
}

.gallery-control-btn {
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.gallery-wrapper {
  position: relative;
  width: 100%;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.gallery-track {
  display: flex;
  transition: transform 0.5s cubic-bezier(0.65, 0, 0.35, 1);
  width: 100%;
}

.gallery-slide {
  flex: 0 0 100%;
  position: relative;
  transition: all 0.5s ease;
  transform: scale(0.9);
  opacity: 0.7;
  cursor: pointer;
}

.gallery-slide.active {
  transform: scale(1);
  opacity: 1;
  z-index: 2;
}

.gallery-slide.prev, .gallery-slide.next {
  z-index: 1;
}

.gallery-slide-content {
  padding: 20px;
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.gallery-slide-image {
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.gallery-slide-image:hover {
  transform: scale(1.02);
}

.gallery-slide-info {
  padding: 10px;
}

/* Feature Highlights */
.feature-highlights {
  margin-top: 60px;
}

.feature-card {
  padding: 30px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
  text-align: center;
  transition: all 0.3s ease;
  height: 100%;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  transition: transform 0.3s ease;
}

.feature-card:hover .feature-icon {
  transform: scale(1.2) rotate(10deg);
}

/* 3D Showcase */
.showcase-container {
  perspective: 1000px;
  margin: 60px auto;
  height: 400px;
  position: relative;
}

.showcase-item {
  width: 100%;
  height: 100%;
  position: relative;
  transform-style: preserve-3d;
  transition: transform 1s ease;
}

.showcase-face {
  position: absolute;
  width: 300px;
  height: 200px;
  top: 50%;
  left: 50%;
  margin-left: -150px;
  margin-top: -100px;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
  backface-visibility: hidden;
}

.showcase-face:hover {
  transform: rotateY(calc(var(--rotation) * 1deg)) translateZ(320px) !important;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

.showcase-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  font-weight: 500;
}

.showcase-controls {
  text-align: center;
  margin-top: 30px;
}

.text-primary {
  color: #0D8A3E !important;
}

.text-secondary {
  color: #a4ca39 !important;
}

.bg-primary {
  background-color: #0D8A3E !important;
}

.bg-secondary {
  background-color: #a4ca39 !important;
}

/* Animation classes */
.animate-fade-in {
  animation: fadeIn 0.8s ease forwards;
  opacity: 0;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Dialog styles */
.facility-details-dialog {
  border-radius: 8px;
  overflow: hidden;
}

.facility-detail-image {
  transition: transform 0.5s ease;
  position: relative;
  overflow: hidden;
}

/* Form Dialog Styles */
.form-dialog {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.form-field {
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.02);
}

.form-field .q-field__control {
  padding: 0 12px;
}

.form-field .q-field__marginal {
  height: 56px;
}

.form-checkbox {
  padding: 8px;
}

.form-dialog .q-card__section {
  padding: 20px 24px;
}

.form-dialog .q-card__actions {
  padding: 16px 24px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.facility-detail-image:hover {
  transform: scale(1.02);
}

.facility-detail-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(13, 138, 62, 0.2) 0%, rgba(164, 202, 57, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.5s ease;
}

.facility-detail-image:hover::after {
  opacity: 1;
}

/* Events Section Styles */
.events-section {
  background-color: #f8f9fa;
}

.featured-event-card {
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.featured-event-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.15);
}

.event-card {
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.event-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.program-card {
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.program-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.program-overlay {
  background: linear-gradient(to bottom, rgba(13, 138, 62, 0.5), rgba(13, 138, 62, 0.8));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.program-card:hover .program-overlay {
  opacity: 1;
}

/* FAQ Section Styles */
.faq-section {
  background-color: #ffffff;
}

.faq-section .q-expansion-item {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.faq-section .q-expansion-item:last-child {
  border-bottom: none;
}
</style>
