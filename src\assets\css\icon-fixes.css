/* Icon fixes for Quasar */

/* Fix for icon font loading */
@font-face {
  font-family: 'Material Icons';
  font-style: normal;
  font-weight: 400;
  src: url(https://fonts.gstatic.com/s/materialicons/v140/flUhRq6tzZclQEJ-Vdg-IuiaDsNcIhQ8tQ.woff2) format('woff2');
  font-display: block;
}

/* Fix for social media icons */
.q-icon.fab,
.q-icon.fas,
.q-icon.far,
.q-icon.fa,
.q-icon.mdi,
.q-icon.ion {
  font-size: 20px;
}

/* Fix for button icons */
.auth-btn .q-icon {
  font-size: 20px !important;
}

/* Fix for custom icons */
.icon {
  display: inline-block;
  width: 24px;
  height: 24px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* Fix for q-tabs arrows */
.q-tabs__arrow {
  font-family: 'Material Icons';
}

.q-tabs__arrow--left:before {
  content: 'chevron_left';
}

.q-tabs__arrow--right:before {
  content: 'chevron_right';
}

/* Fix for q-stepper done icon */
.q-stepper__dot--done .q-stepper__dot-inner {
  font-family: 'Material Icons';
}

.q-stepper__dot--done .q-stepper__dot-inner:before {
  content: 'check';
}

/* Fix for material icons */
.material-icons {
  font-family: 'Material Icons', sans-serif !important;
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

/* Fix for q-btn with icon prop */
.q-btn__content .q-icon {
  font-size: 1.4em;
}

/* Fix for q-icon to ensure proper rendering */
.q-icon {
  line-height: 1;
  width: 1em;
  height: 1em;
  letter-spacing: normal;
  text-transform: none;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  text-align: center;
  position: relative;
  box-sizing: content-box;
}

/* Ensure proper alignment of icons in buttons */
.q-btn .q-icon {
  font-size: 1.4em;
  margin-right: 0.25em;
}

/* Fix for icon text fallback */
.q-icon:before {
  font-family: 'Material Icons', sans-serif !important;
}

/* Fix for icon in buttons */
.q-btn__content i.material-icons {
  font-size: 1.4em;
  margin-right: 0.25em;
}

/* Fix for unified icon component */
.unified-icon {
  vertical-align: middle;
}
