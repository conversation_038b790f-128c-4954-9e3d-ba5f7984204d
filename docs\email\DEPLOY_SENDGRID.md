# Deploying SendGrid Email Integration with Supabase

This guide will walk you through the process of deploying the SendGrid email integration with Supabase Edge Functions.

## Prerequisites

- Supabase CLI installed
- SendGrid API key: `*********************************************************************`
- Supabase project reference: `dpicnvisvxpmgjtbeicf`

## Step 1: Install Supabase CLI

If you haven't already installed the Supabase CLI, you can do so with:

```bash
npm install supabase --save-dev
```

## Step 2: Login to Supabase

```bash
npx supabase login
```

This will open a browser window where you can log in to your Supabase account.

## Step 3: Set SendGrid API Key as a Secret

```bash
npx supabase secrets set SENDGRID_API_KEY="*********************************************************************" --project-ref dpicnvisvxpmgjtbeicf
```

## Step 4: Set Additional SendGrid Configuration

```bash
npx supabase secrets set SENDGRID_FROM_EMAIL="<EMAIL>" --project-ref dpicnvisvxpmgjtbeicf
npx supabase secrets set SENDGRID_FROM_NAME="ZB Innovation Hub" --project-ref dpicnvisvxpmgjtbeicf
```

## Step 5: Deploy the Edge Function

```bash
npx supabase functions deploy send-email --project-ref dpicnvisvxpmgjtbeicf
```

## Step 6: Verify the Deployment

```bash
npx supabase functions list --project-ref dpicnvisvxpmgjtbeicf
```

## Step 7: Test the Function

You can test the function by sending a request to the Edge Function URL:

```bash
curl -X POST "https://dpicnvisvxpmgjtbeicf.supabase.co/functions/v1/send-email" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ANON_KEY" \
  -d '{
    "type": "welcome",
    "data": {
      "to": "<EMAIL>",
      "firstName": "Your Name"
    }
  }'
```

## Troubleshooting

If you encounter any issues:

1. Check the Supabase logs:
   ```bash
   npx supabase functions logs send-email --project-ref dpicnvisvxpmgjtbeicf
   ```

2. Verify the secrets are set correctly:
   ```bash
   npx supabase secrets list --project-ref dpicnvisvxpmgjtbeicf
   ```

3. Make sure the Edge Function is deployed:
   ```bash
   npx supabase functions list --project-ref dpicnvisvxpmgjtbeicf
   ```

## Using the Email Function in Your Application

To use the email function in your application, you can make a fetch request to the Edge Function URL:

```javascript
const sendEmail = async (type, data) => {
  const { data: response, error } = await supabase.functions.invoke('send-email', {
    body: {
      type,
      data
    }
  });

  if (error) {
    console.error('Error sending email:', error);
    return { success: false, error };
  }

  return { success: true, data: response };
};

// Example: Send a welcome email
const sendWelcomeEmail = async (email, firstName) => {
  return await sendEmail('welcome', {
    to: email,
    firstName
  });
};

// Example: Send a password reset email
const sendPasswordResetEmail = async (email, resetLink, firstName) => {
  return await sendEmail('password_reset', {
    to: email,
    customHtml: resetLink,
    firstName
  });
};
```

## Updating the Auth Configuration in Supabase Dashboard

For password reset functionality to work properly, you need to configure the Site URL in the Supabase dashboard:

1. Go to the Supabase dashboard: https://app.supabase.com/
2. Select your project
3. Navigate to Authentication > URL Configuration
4. Set the Site URL to your application's URL (e.g., https://zbinnovation.com or http://localhost:5179 for local development)
5. Save the changes

## Next Steps

Now that you have deployed the SendGrid email integration, you can use it to send various types of emails from your application, including:

- Welcome emails
- Password reset emails
- Email verification
- Profile completion reminders
- Custom emails

The Edge Function supports all these email types out of the box.
