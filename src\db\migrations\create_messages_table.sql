-- Create messages table
CREATE TABLE IF NOT EXISTS public.user_messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  sender_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  recipient_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  is_read BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_messages_sender_id ON public.user_messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_messages_recipient_id ON public.user_messages(recipient_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON public.user_messages(created_at);

-- Enable Row Level Security
ALTER TABLE public.user_messages ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY IF NOT EXISTS "Users can view their own messages"
  ON public.user_messages
  FOR SELECT
  USING (auth.uid() = sender_id OR auth.uid() = recipient_id);

CREATE POLICY IF NOT EXISTS "Users can insert their own messages"
  ON public.user_messages
  FOR INSERT
  WITH CHECK (auth.uid() = sender_id);

CREATE POLICY IF NOT EXISTS "Users can update their own messages"
  ON public.user_messages
  FOR UPDATE
  USING (auth.uid() = sender_id OR auth.uid() = recipient_id);

-- Create a view for conversations (latest message between two users)
CREATE OR REPLACE VIEW public.user_conversations AS
WITH latest_messages AS (
  SELECT 
    DISTINCT ON (
      LEAST(sender_id, recipient_id), 
      GREATEST(sender_id, recipient_id)
    ) 
    id,
    sender_id,
    recipient_id,
    content,
    is_read,
    created_at,
    updated_at,
    LEAST(sender_id, recipient_id) AS user1_id,
    GREATEST(sender_id, recipient_id) AS user2_id
  FROM 
    public.user_messages
  ORDER BY 
    LEAST(sender_id, recipient_id), 
    GREATEST(sender_id, recipient_id), 
    created_at DESC
)
SELECT 
  m.id,
  m.sender_id,
  m.recipient_id,
  m.content,
  m.is_read,
  m.created_at,
  m.updated_at,
  CASE 
    WHEN m.sender_id = auth.uid() THEN m.recipient_id
    ELSE m.sender_id
  END AS other_user_id
FROM 
  latest_messages m
WHERE 
  m.sender_id = auth.uid() OR m.recipient_id = auth.uid();

-- Grant permissions on the view
GRANT SELECT ON public.user_conversations TO authenticated, service_role;
