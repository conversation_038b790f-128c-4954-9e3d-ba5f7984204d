import { defineStore } from 'pinia'
import { ref } from 'vue'
import { supabase } from '../lib/supabase'
import { useNotificationStore } from './notifications'

export interface Feedback {
  id: string
  user_id: string
  feedback_type: 'GENERAL' | 'BUG_REPORT' | 'FEATURE_REQUEST' | 'OTHER'
  rating: number
  content: string
  status: 'pending' | 'reviewed' | 'resolved'
  created_at: string
  updated_at: string
}

export const useFeedbackStore = defineStore('feedback', () => {
  const notifications = useNotificationStore()
  const loading = ref(false)
  const error = ref<string | null>(null)
  const recentFeedback = ref<Feedback[]>([])

  async function submitFeedback(feedback: Omit<Feedback, 'id' | 'user_id' | 'status' | 'created_at' | 'updated_at'>) {
    loading.value = true
    error.value = null

    try {
      const { data: { user } } = await supabase.auth.getUser()

      if (!user) {
        throw new Error('User not authenticated')
      }

      // Validate the feedback data
      if (!feedback.feedback_type) {
        throw new Error('Feedback type is required')
      }
      if (!feedback.rating || feedback.rating < 1 || feedback.rating > 5) {
        throw new Error('Rating must be between 1 and 5')
      }
      if (!feedback.content || feedback.content.trim() === '') {
        throw new Error('Feedback content is required')
      }

      console.log('Submitting feedback:', { ...feedback, user_id: user.id })

      const { data, error: err } = await supabase
        .from('feedback')
        .insert([
          {
            ...feedback,
            user_id: user.id
          }
        ])
        .select()
        .single()

      if (err) throw err

      console.log('Feedback submitted successfully:', data)
      notifications.success('Feedback submitted successfully')
      return true
    } catch (err: any) {
      console.error('Error submitting feedback:', err)
      error.value = err.message
      notifications.error('Failed to submit feedback: ' + err.message)
      return false
    } finally {
      loading.value = false
    }
  }

  async function getUserFeedback() {
    loading.value = true
    error.value = null

    try {
      const { data: { user } } = await supabase.auth.getUser()

      if (!user) {
        throw new Error('User not authenticated')
      }

      const { data, error: err } = await supabase
        .from('feedback')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(5)

      if (err) throw err

      // Update the recentFeedback property
      recentFeedback.value = data || []

      return data
    } catch (err: any) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  // Convenience method to fetch user feedback and update the store
  async function fetchUserFeedback() {
    try {
      await getUserFeedback()
      return true
    } catch (err) {
      return false
    }
  }

  return {
    loading,
    error,
    recentFeedback,
    submitFeedback,
    getUserFeedback,
    fetchUserFeedback
  }
})