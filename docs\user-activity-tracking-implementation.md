# User Activity Tracking and Connections Implementation

## Overview

This document outlines the current state of user activity tracking and connections in the ZB Innovation Hub platform, along with a detailed implementation plan to enhance these features. The goal is to properly track user activities (posts, likes, comments, etc.) and connections (follows, networking) in the user dashboard.

## Current State Analysis

### Database Structure

#### Existing Tables and Views

1. **User Activity Table**
   - Table: `user_activity`
   - Columns:
     - `id` (bigint)
     - `user_id` (uuid)
     - `activity_type` (text)
     - `timestamp` (timestamp with time zone)
     - `details` (jsonb)
   - Status: Table exists but is empty (0 records)

2. **Post-Related Tables**
   - `posts`: Stores all user posts with `user_id` field
   - `likes`: Tracks post likes with `user_id` and `post_id` fields
   - `comments`: Tracks post comments with `user_id` and `post_id` fields
   - Views: `posts_with_authors`, `likes_with_authors`, `comments_with_authors`

3. **Profile Tables**
   - `personal_details`: Main profile information
   - Various specialized profile tables (e.g., `innovator_profiles`, `mentor_profiles`)

#### Missing Tables

1. **User Connections Table**
   - No `user_connections` table exists in the database
   - Referenced in documentation but not implemented
   - Proposed schema:
     ```sql
     CREATE TABLE IF NOT EXISTS public.user_connections (
       id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
       user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
       connected_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
       connection_type VARCHAR(50) NOT NULL,
       connection_strength FLOAT DEFAULT 1.0,
       created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
       updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
       UNIQUE(user_id, connected_user_id)
     );
     ```

### Frontend Implementation

1. **User Dashboard**
   - Located at: `src/views/dashboard/Dashboard.vue`
   - Currently shows basic profile information and completion status
   - Uses `TypeSpecificDashboard` component based on profile type
   - No activity feed or connections display

2. **User Profile View**
   - Located at: `src/views/public/content/UserProfileView.vue`
   - Has sections for network stats and recent activity
   - Currently uses mock data for activities:
     ```javascript
     userActivity.value = [
       {
         id: 1,
         title: 'Created a new post',
         description: 'Shared insights on sustainable business practices',
         date: '2025-07-10T14:25:00Z',
         icon: 'post_add'
       },
       // More mock activities...
     ];
     ```

3. **Connection Functionality**
   - UI has buttons for connecting with users
   - Functions are placeholders with no actual implementation:
     ```javascript
     function connectWithProfile(profileId: number): void {
       console.log('Connect with profile:', profileId);
     }
     ```

### Backend Functions

1. **Activity Tracking Functions**
   - `get_user_activity_data`: Database function to query user activities
   - No functions to insert data into the `user_activity` table

2. **Missing Implementation**
   - No code to record activities when users perform actions
   - No connection management functions
   - No real-time activity feed implementation

## Implementation Plan

### Phase 1: Database Setup

1. **Create User Connections Table**
   - Implement the missing `user_connections` table
   - Add appropriate indexes and constraints
   - Set up Row Level Security (RLS) policies

2. **Update User Activity Table**
   - Add indexes for better query performance
   - Create helper functions for activity tracking

### Phase 2: Backend Services

1. **Activity Tracking Service**
   - Create a service to record user activities
   - Implement functions for different activity types
   - Add activity tracking to existing actions (posts, likes, comments)

2. **Connection Management Service**
   - Create a service for managing user connections
   - Implement functions for connecting, disconnecting, and listing connections
   - Add connection status checking

### Phase 3: Frontend Implementation

1. **Dashboard Activity Feed**
   - Update the dashboard to display real user activities
   - Implement pagination and filtering
   - Add activity icons and formatting

2. **User Profile Connections**
   - Update profile view to show real connection data
   - Implement connection request/accept functionality
   - Add connection suggestions

3. **Activity Notifications**
   - Implement notifications for new activities
   - Add real-time updates using Supabase realtime

## Detailed Implementation Steps

### Phase 1: Database Setup

#### Step 1.1: Create User Connections Table

```sql
-- Create user connections table
CREATE TABLE IF NOT EXISTS public.user_connections (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  connected_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  connection_type VARCHAR(50) NOT NULL,
  connection_status VARCHAR(20) DEFAULT 'pending',
  connection_strength FLOAT DEFAULT 1.0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, connected_user_id)
);

-- Create indexes
CREATE INDEX idx_user_connections_user_id ON public.user_connections(user_id);
CREATE INDEX idx_user_connections_connected_user_id ON public.user_connections(connected_user_id);

-- Enable RLS
ALTER TABLE public.user_connections ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view their own connections"
  ON public.user_connections
  FOR SELECT
  USING (auth.uid() = user_id OR auth.uid() = connected_user_id);

CREATE POLICY "Users can create their own connections"
  ON public.user_connections
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own connections"
  ON public.user_connections
  FOR UPDATE
  USING (auth.uid() = user_id OR auth.uid() = connected_user_id);

CREATE POLICY "Users can delete their own connections"
  ON public.user_connections
  FOR DELETE
  USING (auth.uid() = user_id);
```

#### Step 1.2: Update User Activity Table

```sql
-- Add indexes to user_activity table
CREATE INDEX idx_user_activity_user_id ON public.user_activity(user_id);
CREATE INDEX idx_user_activity_timestamp ON public.user_activity(timestamp);
CREATE INDEX idx_user_activity_type ON public.user_activity(activity_type);

-- Enable RLS
ALTER TABLE public.user_activity ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view their own activities"
  ON public.user_activity
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own activities"
  ON public.user_activity
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);
```

### Phase 2: Backend Services

#### Step 2.1: Activity Tracking Service

Create a new file `src/services/activityTrackingService.ts`:

```typescript
import { useSupabaseClient } from '@supabase/auth-helpers-vue';
import { ref } from 'vue';

export function useActivityTrackingService() {
  const supabase = useSupabaseClient();
  const isTracking = ref(false);

  /**
   * Track a user activity
   *
   * @param activityType The type of activity
   * @param details Additional details about the activity
   * @returns Success status
   */
  async function trackActivity(activityType: string, details: any = {}): Promise<boolean> {
    if (isTracking.value) return false;

    try {
      isTracking.value = true;

      const { data: { user } } = await supabase.auth.getUser();

      if (!user) return false;

      const { error } = await supabase.from('user_activity').insert({
        user_id: user.id,
        activity_type: activityType,
        timestamp: new Date().toISOString(),
        details
      });

      if (error) throw error;

      return true;
    } catch (error) {
      console.error('Error tracking activity:', error);
      return false;
    } finally {
      isTracking.value = false;
    }
  }

  /**
   * Get user activities
   *
   * @param userId The user ID (defaults to current user)
   * @param limit Maximum number of activities to return
   * @param page Page number for pagination
   * @returns Array of user activities
   */
  async function getUserActivities(
    userId?: string,
    limit: number = 10,
    page: number = 1
  ): Promise<any[]> {
    try {
      if (!userId) {
        const { data: { user } } = await supabase.auth.getUser();
        userId = user?.id;
      }

      if (!userId) return [];

      const from = (page - 1) * limit;
      const to = from + limit - 1;

      const { data, error } = await supabase
        .from('user_activity')
        .select('*')
        .eq('user_id', userId)
        .order('timestamp', { ascending: false })
        .range(from, to);

      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('Error getting user activities:', error);
      return [];
    }
  }

  return {
    trackActivity,
    getUserActivities,
    isTracking
  };
}
```

#### Step 2.2: Connection Management Service

Create a new file `src/services/connectionService.ts`:

```typescript
import { useSupabaseClient } from '@supabase/auth-helpers-vue';
import { ref } from 'vue';
import { useActivityTrackingService } from './activityTrackingService';

export function useConnectionService() {
  const supabase = useSupabaseClient();
  const isLoading = ref(false);
  const activityService = useActivityTrackingService();

  /**
   * Send a connection request to another user
   *
   * @param userId The user ID to connect with
   * @param connectionType The type of connection
   * @returns Success status
   */
  async function connectWithUser(
    userId: string,
    connectionType: string = 'follow'
  ): Promise<boolean> {
    if (isLoading.value) return false;

    try {
      isLoading.value = true;

      const { data: { user } } = await supabase.auth.getUser();

      if (!user || user.id === userId) return false;

      // Check if connection already exists
      const { data: existingConnection, error: checkError } = await supabase
        .from('user_connections')
        .select('*')
        .eq('user_id', user.id)
        .eq('connected_user_id', userId)
        .single();

      if (checkError && checkError.code !== 'PGRST116') {
        throw checkError;
      }

      if (existingConnection) {
        // Connection already exists, update it
        const { error: updateError } = await supabase
          .from('user_connections')
          .update({
            connection_type: connectionType,
            connection_status: 'pending',
            updated_at: new Date().toISOString()
          })
          .eq('id', existingConnection.id);

        if (updateError) throw updateError;
      } else {
        // Create new connection
        const { error: insertError } = await supabase
          .from('user_connections')
          .insert({
            user_id: user.id,
            connected_user_id: userId,
            connection_type: connectionType,
            connection_status: 'pending'
          });

        if (insertError) throw insertError;
      }

      // Track the activity
      await activityService.trackActivity('connect_request', {
        connected_user_id: userId,
        connection_type: connectionType
      });

      return true;
    } catch (error) {
      console.error('Error connecting with user:', error);
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Additional functions will be implemented here

  /**
   * Accept a connection request
   *
   * @param connectionId The connection ID to accept
   * @returns Success status
   */
  async function acceptConnection(connectionId: string): Promise<boolean> {
    if (isLoading.value) return false;

    try {
      isLoading.value = true;

      const { data: { user } } = await supabase.auth.getUser();

      if (!user) return false;

      // Get the connection
      const { data: connection, error: getError } = await supabase
        .from('user_connections')
        .select('*')
        .eq('id', connectionId)
        .eq('connected_user_id', user.id)
        .eq('connection_status', 'pending')
        .single();

      if (getError) throw getError;

      if (!connection) {
        throw new Error('Connection request not found');
      }

      // Update the connection status
      const { error: updateError } = await supabase
        .from('user_connections')
        .update({
          connection_status: 'accepted',
          updated_at: new Date().toISOString()
        })
        .eq('id', connectionId);

      if (updateError) throw updateError;

      // Track the activity
      await activityService.trackActivity('connect_accept', {
        connection_id: connectionId,
        user_id: connection.user_id
      });

      return true;
    } catch (error) {
      console.error('Error accepting connection:', error);
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /**
   * Get user connections
   *
   * @param userId The user ID (defaults to current user)
   * @param status Connection status filter
   * @param limit Maximum number of connections to return
   * @param page Page number for pagination
   * @returns Array of user connections
   */
  async function getUserConnections(
    userId?: string,
    status: string = 'accepted',
    limit: number = 10,
    page: number = 1
  ): Promise<any[]> {
    try {
      if (!userId) {
        const { data: { user } } = await supabase.auth.getUser();
        userId = user?.id;
      }

      if (!userId) return [];

      const from = (page - 1) * limit;
      const to = from + limit - 1;

      const { data, error } = await supabase
        .from('user_connections')
        .select(`
          *,
          connected_user:connected_user_id(
            id,
            email,
            first_name,
            last_name,
            avatar_url
          )
        `)
        .eq('user_id', userId)
        .eq('connection_status', status)
        .order('created_at', { ascending: false })
        .range(from, to);

      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('Error getting user connections:', error);
      return [];
    }
  }

  /**
   * Get connection requests
   *
   * @param limit Maximum number of requests to return
   * @param page Page number for pagination
   * @returns Array of connection requests
   */
  async function getConnectionRequests(
    limit: number = 10,
    page: number = 1
  ): Promise<any[]> {
    try {
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) return [];

      const from = (page - 1) * limit;
      const to = from + limit - 1;

      const { data, error } = await supabase
        .from('user_connections')
        .select(`
          *,
          user:user_id(
            id,
            email,
            first_name,
            last_name,
            avatar_url
          )
        `)
        .eq('connected_user_id', user.id)
        .eq('connection_status', 'pending')
        .order('created_at', { ascending: false })
        .range(from, to);

      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('Error getting connection requests:', error);
      return [];
    }
  }

  /**
   * Check if the current user is connected to another user
   *
   * @param userId The user ID to check
   * @returns Connection status
   */
  async function getConnectionStatus(userId: string): Promise<string> {
    try {
      const { data: { user } } = await supabase.auth.getUser();

      if (!user || user.id === userId) return 'self';

      // Check outgoing connection
      const { data: outgoing, error: outgoingError } = await supabase
        .from('user_connections')
        .select('connection_status')
        .eq('user_id', user.id)
        .eq('connected_user_id', userId)
        .single();

      if (outgoing) return outgoing.connection_status;

      // Check incoming connection
      const { data: incoming, error: incomingError } = await supabase
        .from('user_connections')
        .select('connection_status')
        .eq('user_id', userId)
        .eq('connected_user_id', user.id)
        .single();

      if (incoming) return `incoming_${incoming.connection_status}`;

      return 'none';
    } catch (error) {
      console.error('Error checking connection status:', error);
      return 'error';
    }
  }

  return {
    connectWithUser,
    acceptConnection,
    getUserConnections,
    getConnectionRequests,
    getConnectionStatus,
    isLoading
  };
}
```

### Phase 3: Frontend Implementation

#### Step 3.1: Update Post Actions to Track Activities

Update `src/stores/posts/index.ts` to track activities:

```typescript
// Import the activity tracking service
import { useActivityTrackingService } from '../../services/activityTrackingService';

// Inside the store definition
export const usePostsStore = defineStore('posts', () => {
  // Existing code...

  // Initialize the activity tracking service
  const activityService = useActivityTrackingService();

  // Update the createPost function
  async function createPost(newPost: CreatePostInput): Promise<Post | null> {
    // Existing code...

    // After successfully creating the post
    if (postWithAuthor) {
      // Track the activity
      await activityService.trackActivity('post_create', {
        post_id: postWithAuthor.id,
        post_type: postWithAuthor.post_type,
        title: postWithAuthor.title || '',
        content_preview: postWithAuthor.content?.substring(0, 100) || ''
      });
    }

    // Return the post
    return mapPostFromDatabase(postWithAuthor);
  }

  // Update the likePost function
  async function likePost(postId: number): Promise<void> {
    // Existing code...

    // After successfully liking/unliking the post
    if (!existingLike) {
      // Track the like activity (only when liking, not unliking)
      await activityService.trackActivity('post_like', {
        post_id: postId,
        post_title: post.title || '',
        author_id: post.user_id,
        author_name: post.author || ''
      });
    }
  }

  // Update the commentOnPost function
  async function commentOnPost(postId: number, comment: string): Promise<void> {
    // Existing code...

    // After successfully adding the comment
    if (data) {
      // Track the comment activity
      await activityService.trackActivity('post_comment', {
        post_id: postId,
        post_title: post.title || '',
        comment: comment.substring(0, 100),
        author_id: post.user_id,
        author_name: post.author || ''
      });
    }
  }

  // Return the store
  return {
    // Existing exports...
    createPost,
    likePost,
    commentOnPost
  };
});
```

#### Step 3.2: Create Activity Feed Component

Create a new file `src/components/activity/ActivityFeed.vue`:

```vue
<template>
  <div class="activity-feed">
    <div class="text-h6 q-mb-md">{{ title }}</div>

    <div v-if="loading" class="text-center q-pa-md">
      <q-spinner color="primary" size="2em" />
      <p>Loading activities...</p>
    </div>

    <div v-else-if="activities.length === 0" class="text-center q-pa-md text-grey">
      <q-icon name="info" size="2em" />
      <p>No activities to display</p>
    </div>

    <div v-else>
      <q-timeline color="primary">
        <q-timeline-entry
          v-for="activity in activities"
          :key="activity.id"
          :title="getActivityTitle(activity)"
          :subtitle="formatDate(activity.timestamp)"
          :icon="getActivityIcon(activity)"
          :color="getActivityColor(activity)"
        >
          <div>{{ getActivityDescription(activity) }}</div>

          <div v-if="activity.details && activity.details.post_id" class="q-mt-sm">
            <q-btn
              flat
              dense
              size="sm"
              color="primary"
              :to="{ name: 'single-post', params: { id: activity.details.post_id } }"
              label="View Post"
            />
          </div>
        </q-timeline-entry>
      </q-timeline>

      <div v-if="hasMore" class="text-center q-mt-md">
        <q-btn
          outline
          color="primary"
          label="Load More"
          :loading="loadingMore"
          @click="loadMore"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { date } from 'quasar';
import { useActivityTrackingService } from '../../services/activityTrackingService';

const props = defineProps({
  userId: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: 'Recent Activity'
  },
  limit: {
    type: Number,
    default: 5
  }
});

const activityService = useActivityTrackingService();
const activities = ref([]);
const loading = ref(true);
const loadingMore = ref(false);
const currentPage = ref(1);
const hasMore = ref(false);

onMounted(async () => {
  await loadActivities();
});

async function loadActivities() {
  try {
    loading.value = true;
    const result = await activityService.getUserActivities(
      props.userId,
      props.limit,
      currentPage.value
    );

    activities.value = result;
    hasMore.value = result.length === props.limit;
  } catch (error) {
    console.error('Error loading activities:', error);
  } finally {
    loading.value = false;
  }
}

async function loadMore() {
  try {
    loadingMore.value = true;
    currentPage.value++;

    const result = await activityService.getUserActivities(
      props.userId,
      props.limit,
      currentPage.value
    );

    activities.value = [...activities.value, ...result];
    hasMore.value = result.length === props.limit;
  } catch (error) {
    console.error('Error loading more activities:', error);
    currentPage.value = Math.max(1, currentPage.value - 1);
  } finally {
    loadingMore.value = false;
  }
}

function getActivityTitle(activity) {
  const activityTitles = {
    'post_create': 'Created a post',
    'post_like': 'Liked a post',
    'post_comment': 'Commented on a post',
    'connect_request': 'Sent a connection request',
    'connect_accept': 'Accepted a connection request',
    'profile_update': 'Updated profile',
    'group_join': 'Joined a group',
    'event_register': 'Registered for an event'
  };

  return activityTitles[activity.activity_type] || 'Activity';
}

function getActivityIcon(activity) {
  const activityIcons = {
    'post_create': 'post_add',
    'post_like': 'favorite',
    'post_comment': 'comment',
    'connect_request': 'person_add',
    'connect_accept': 'handshake',
    'profile_update': 'account_circle',
    'group_join': 'group_add',
    'event_register': 'event'
  };

  return activityIcons[activity.activity_type] || 'info';
}

function getActivityColor(activity) {
  const activityColors = {
    'post_create': 'green',
    'post_like': 'pink',
    'post_comment': 'blue',
    'connect_request': 'purple',
    'connect_accept': 'deep-purple',
    'profile_update': 'teal',
    'group_join': 'indigo',
    'event_register': 'orange'
  };

  return activityColors[activity.activity_type] || 'primary';
}

function getActivityDescription(activity) {
  const details = activity.details || {};

  switch (activity.activity_type) {
    case 'post_create':
      return details.title
        ? `Created a post: "${details.title}"`
        : `Created a new post`;
    case 'post_like':
      return details.post_title
        ? `Liked a post: "${details.post_title}"`
        : `Liked a post by ${details.author_name || 'someone'}`;
    case 'post_comment':
      return details.comment
        ? `Commented: "${details.comment}"`
        : `Commented on a post`;
    case 'connect_request':
      return `Sent a connection request`;
    case 'connect_accept':
      return `Accepted a connection request`;
    case 'profile_update':
      return `Updated profile information`;
    case 'group_join':
      return details.group_name
        ? `Joined the group "${details.group_name}"`
        : `Joined a group`;
    case 'event_register':
      return details.event_title
        ? `Registered for event: "${details.event_title}"`
        : `Registered for an event`;
    default:
      return 'Performed an activity';
  }
}

function formatDate(dateString) {
  const now = new Date();
  const activityDate = new Date(dateString);
  const diffDays = Math.floor((now - activityDate) / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    return 'Today at ' + date.formatDate(dateString, 'h:mm A');
  } else if (diffDays === 1) {
    return 'Yesterday at ' + date.formatDate(dateString, 'h:mm A');
  } else if (diffDays < 7) {
    return date.formatDate(dateString, 'dddd [at] h:mm A');
  } else {
    return date.formatDate(dateString, 'MMM D, YYYY [at] h:mm A');
  }
}
</script>
```

#### Step 3.3: Create Connection Components

Create a new file `src/components/connections/ConnectionsList.vue`:

```vue
<template>
  <div class="connections-list">
    <div class="text-h6 q-mb-md">{{ title }}</div>

    <div v-if="loading" class="text-center q-pa-md">
      <q-spinner color="primary" size="2em" />
      <p>Loading connections...</p>
    </div>

    <div v-else-if="connections.length === 0" class="text-center q-pa-md text-grey">
      <q-icon name="info" size="2em" />
      <p>No connections to display</p>
    </div>

    <div v-else>
      <div class="row q-col-gutter-md">
        <div
          v-for="connection in connections"
          :key="connection.id"
          class="col-12 col-md-6"
        >
          <q-card class="connection-card">
            <q-item>
              <q-item-section avatar>
                <q-avatar>
                  <img
                    v-if="connection.connected_user?.avatar_url"
                    :src="connection.connected_user.avatar_url"
                  />
                  <div v-else class="bg-primary text-white flex flex-center full-height">
                    {{ getInitials(connection.connected_user) }}
                  </div>
                </q-avatar>
              </q-item-section>

              <q-item-section>
                <q-item-label>
                  {{ getUserName(connection.connected_user) }}
                </q-item-label>
                <q-item-label caption>
                  {{ connection.connection_type }}
                </q-item-label>
                <q-item-label caption>
                  Connected {{ formatDate(connection.created_at) }}
                </q-item-label>
              </q-item-section>

              <q-item-section side>
                <q-btn
                  flat
                  round
                  color="primary"
                  icon="person"
                  :to="{ name: 'user-profile', params: { id: connection.connected_user_id } }"
                >
                  <q-tooltip>View Profile</q-tooltip>
                </q-btn>

                <q-btn
                  flat
                  round
                  color="primary"
                  icon="message"
                  @click="handleMessage(connection.connected_user_id)"
                >
                  <q-tooltip>Message</q-tooltip>
                </q-btn>
              </q-item-section>
            </q-item>
          </q-card>
        </div>
      </div>

      <div v-if="hasMore" class="text-center q-mt-md">
        <q-btn
          outline
          color="primary"
          label="Load More"
          :loading="loadingMore"
          @click="loadMore"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { date } from 'quasar';
import { useConnectionService } from '../../services/connectionService';

const props = defineProps({
  userId: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: 'Connections'
  },
  limit: {
    type: Number,
    default: 10
  }
});

const emit = defineEmits(['message']);

const connectionService = useConnectionService();
const connections = ref([]);
const loading = ref(true);
const loadingMore = ref(false);
const currentPage = ref(1);
const hasMore = ref(false);

onMounted(async () => {
  await loadConnections();
});

async function loadConnections() {
  try {
    loading.value = true;
    const result = await connectionService.getUserConnections(
      props.userId,
      'accepted',
      props.limit,
      currentPage.value
    );

    connections.value = result;
    hasMore.value = result.length === props.limit;
  } catch (error) {
    console.error('Error loading connections:', error);
  } finally {
    loading.value = false;
  }
}

async function loadMore() {
  try {
    loadingMore.value = true;
    currentPage.value++;

    const result = await connectionService.getUserConnections(
      props.userId,
      'accepted',
      props.limit,
      currentPage.value
    );

    connections.value = [...connections.value, ...result];
    hasMore.value = result.length === props.limit;
  } catch (error) {
    console.error('Error loading more connections:', error);
    currentPage.value = Math.max(1, currentPage.value - 1);
  } finally {
    loadingMore.value = false;
  }
}

function getInitials(user) {
  if (!user) return '?';

  if (user.first_name && user.last_name) {
    return `${user.first_name[0]}${user.last_name[0]}`.toUpperCase();
  }

  if (user.email) {
    return user.email[0].toUpperCase();
  }

  return '?';
}

function getUserName(user) {
  if (!user) return 'Unknown User';

  if (user.first_name && user.last_name) {
    return `${user.first_name} ${user.last_name}`;
  }

  if (user.email) {
    return user.email.split('@')[0];
  }

  return 'Unknown User';
}

function formatDate(dateString) {
  return date.formatDate(dateString, 'MMM D, YYYY');
}

function handleMessage(userId) {
  emit('message', userId);
}
</script>
```

#### Step 3.4: Update Dashboard to Show Activities

Update `src/views/dashboard/Dashboard.vue` to include the activity feed:

```vue
<!-- Add this to the dashboard template -->
<div class="col-12">
  <activity-feed :limit="5" title="Your Recent Activity" />
</div>

<!-- Add this to the script section -->
import ActivityFeed from '../../components/activity/ActivityFeed.vue';
```

#### Step 3.5: Update User Profile View to Show Real Activities

Update `src/views/public/content/UserProfileView.vue` to use real activities:

```vue
<!-- Replace the mock activity section with this -->
<q-card-section>
  <div class="text-h6">Recent Activity</div>
  <activity-feed :user-id="profile.id" :limit="5" title="" />
</q-card-section>

<!-- Add this to the script section -->
import ActivityFeed from '../../../components/activity/ActivityFeed.vue';
```

## Implementation Timeline

1. **Week 1: Database Setup**
   - Create user_connections table
   - Update user_activity table
   - Set up indexes and RLS policies

2. **Week 2: Backend Services**
   - Implement activity tracking service
   - Implement connection management service
   - Update existing actions to track activities

3. **Week 3: Frontend Components**
   - Create activity feed component
   - Create connections list component
   - Update dashboard and profile views

4. **Week 4: Testing and Refinement**
   - Test all functionality
   - Fix bugs and edge cases
   - Optimize performance
   - Add documentation

## Conclusion

This implementation plan provides a comprehensive approach to tracking user activities and connections in the ZB Innovation Hub platform. By following these steps, we will create a robust system that enhances the user experience by providing visibility into user actions and facilitating connections between users.

The implementation leverages the existing database structure and extends it with new tables and services. The frontend components are designed to be reusable and can be integrated into various parts of the application.

Once implemented, users will be able to see their own activities, track their connections, and receive notifications about relevant events in the platform.
