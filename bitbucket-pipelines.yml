# Bitbucket Pipeline configuration for ZbInnovation
# This pipeline will build, test, and deploy the application

image: node:16

pipelines:
  default:
    - step:
        name: Build and Test
        caches:
          - node
        script:
          - npm ci
          - npm run build
        artifacts:
          - dist/**

  branches:
    develop:
      - step:
          name: Build and Test
          caches:
            - node
          script:
            - npm ci
            - npm run build
          artifacts:
            - dist/**
      - step:
          name: Deploy to Staging
          deployment: staging
          script:
            - npm ci
            - npm run build
            - pipe: atlassian/scp-deploy:1.4.0
              variables:
                USER: $STAGING_USER
                SERVER: $STAGING_HOST
                REMOTE_PATH: $STAGING_PATH
                LOCAL_PATH: 'dist'
                SSH_KEY: $STAGING_SSH_KEY

    main:
      - step:
          name: Build and Test
          caches:
            - node
          script:
            - npm ci
            - npm run lint
            - npm run test
          artifacts:
            - dist/**
      - step:
          name: Deploy to Production
          deployment: production
          trigger: manual
          script:
            - npm ci
            - npm run build
            - pipe: atlassian/scp-deploy:1.4.0
              variables:
                USER: $PRODUCTION_USER
                SERVER: $PRODUCTION_HOST
                REMOTE_PATH: $PRODUCTION_PATH
                LOCAL_PATH: 'dist'
                SSH_KEY: $PRODUCTION_SSH_KEY

  pull-requests:
    '**':
      - step:
          name: Build and Test PR
          caches:
            - node
          script:
            - npm ci
            - npm run lint
            - npm run test

definitions:
  caches:
    node: node_modules
