<template>
  <q-page padding>
    <div class="row q-col-gutter-md">
      <div class="col-12">
        <div class="text-h4 q-mb-md">Innovation Events</div>
        <div class="row items-center q-mb-lg">
          <div class="col">
            <q-input
              v-model="search"
              outlined
              dense
              placeholder="Search events..."
            >
              <template v-slot:append>
                <icons name="search" />
              </template>
            </q-input>
          </div>
          <div class="col-auto q-ml-md">
            <q-btn-group outline>
              <q-btn
                :color="view === 'grid' ? 'primary' : 'grey'"
                @click="view = 'grid'"
              >
                <icons name="grid_view" />
              </q-btn>
              <q-btn
                :color="view === 'list' ? 'primary' : 'grey'"
                @click="view = 'list'"
              >
                <icons name="view_list" />
              </q-btn>
            </q-btn-group>
          </div>
        </div>
      </div>

      <template v-if="view === 'grid'">
        <div class="col-12 col-md-4" v-for="event in filteredEvents" :key="event.id">
          <q-card class="event-card">
            <q-img
              :src="event.image"
              height="200px"
            >
              <div class="absolute-bottom text-subtitle2 text-center bg-dark q-pa-xs">
                {{ event.date }}
              </div>
            </q-img>

            <q-card-section>
              <div class="text-h6">{{ event.title }}</div>
              <div class="text-subtitle2 text-grey-7">
                <icons name="event" class="text-xs q-mr-xs" /> {{ event.location }}
              </div>
              <div class="text-body2 q-mt-sm">{{ event.description }}</div>
            </q-card-section>

            <q-card-section class="q-pt-none">
              <q-chip
                v-for="tag in event.tags"
                :key="tag"
                color="primary"
                text-color="white"
                size="sm"
              >
                {{ tag }}
              </q-chip>
            </q-card-section>

            <q-card-actions align="right">
              <q-btn flat color="primary" label="Learn More" />
              <q-btn color="primary" label="Register" />
            </q-card-actions>
          </q-card>
        </div>
      </template>

      <template v-else>
        <div class="col-12" v-for="event in filteredEvents" :key="event.id">
          <q-card>
            <q-item>
              <q-item-section avatar>
                <q-img
                  :src="event.image"
                  style="width: 100px; height: 100px"
                />
              </q-item-section>

              <q-item-section>
                <q-item-label class="text-h6">{{ event.title }}</q-item-label>
                <q-item-label caption>
                  <q-icon name="event" size="xs" /> {{ event.date }}
                  <q-icon name="location_on" size="xs" class="q-ml-sm" /> {{ event.location }}
                </q-item-label>
                <q-item-label class="q-mt-sm">{{ event.description }}</q-item-label>
                <div class="q-mt-sm">
                  <q-chip
                    v-for="tag in event.tags"
                    :key="tag"
                    color="primary"
                    text-color="white"
                    size="sm"
                  >
                    {{ tag }}
                  </q-chip>
                </div>
              </q-item-section>

              <q-item-section side>
                <div class="column q-gutter-sm">
                  <q-btn flat color="primary" label="Learn More" />
                  <q-btn color="primary" label="Register" />
                </div>
              </q-item-section>
            </q-item>
          </q-card>
        </div>
      </template>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import Icons from '../components/ui/Icons.vue'

interface Event {
  id: number
  title: string
  description: string
  date: string
  location: string
  image: string
  tags: string[]
}

const search = ref('')
const view = ref('grid')

const events: Event[] = [
  {
    id: 1,
    title: 'ZbInnovation Virtual Hub Launch Event',
    description: 'Join us for the groundbreaking launch of the ZbInnovation Virtual Hub - a one-of-a-kind virtual event connecting innovators, investors, and mentors across Zimbabwe and beyond.',
    date: 'May 14, 2025',
    location: 'Virtual Event',
    image: 'https://picsum.photos/id/3/500/300',
    tags: ['Launch', 'Virtual', 'Innovation']
  }
]

const filteredEvents = computed(() => {
  if (!search.value) return events
  const searchLower = search.value.toLowerCase()
  return events.filter(event =>
    event.title.toLowerCase().includes(searchLower) ||
    event.description.toLowerCase().includes(searchLower) ||
    event.location.toLowerCase().includes(searchLower) ||
    event.tags.some(tag => tag.toLowerCase().includes(searchLower))
  )
})
</script>

<style scoped>
.event-card {
  height: 100%;
  transition: transform 0.2s;
}

.event-card:hover {
  transform: translateY(-5px);
}
</style>
