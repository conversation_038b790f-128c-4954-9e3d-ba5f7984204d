/**
 * Connections Service
 * 
 * Centralized service for all user connection-related operations.
 * Replaces direct Supabase calls in components with proper service layer.
 */

import { supabase } from '../lib/supabase'

export interface ConnectionRequest {
  id: string
  user_id: string
  connected_user_id: string
  connection_status: 'pending' | 'accepted' | 'declined'
  created_at: string
  updated_at: string
  // Enhanced fields
  requester_name?: string
  requester_email?: string
  requester_profile_name?: string
}

export interface Connection {
  id: string
  user_id: string
  connected_user_id: string
  connection_status: 'accepted'
  created_at: string
  updated_at: string
  // Enhanced fields
  connected_user_name?: string
  connected_user_email?: string
  connected_user_profile_name?: string
}

export interface ConnectionCreateData {
  user_id: string
  connected_user_id: string
  connection_status?: 'pending'
}

/**
 * Connections Service Class
 */
export class ConnectionsService {
  private static instance: ConnectionsService

  static getInstance(): ConnectionsService {
    if (!ConnectionsService.instance) {
      ConnectionsService.instance = new ConnectionsService()
    }
    return ConnectionsService.instance
  }

  /**
   * Get connection requests for a user (incoming requests)
   */
  async getConnectionRequests(userId: string): Promise<ConnectionRequest[]> {
    try {
      // Get basic connection requests
      const { data: requests, error } = await supabase
        .from('user_connections')
        .select('*')
        .eq('connected_user_id', userId)
        .eq('connection_status', 'pending')

      if (error) {
        console.error('ConnectionsService: Error fetching connection requests:', error)
        throw error
      }

      if (!requests || requests.length === 0) {
        return []
      }

      // Enhance requests with user information
      const enhancedRequests = await Promise.all(
        requests.map(async (req) => {
          try {
            // Try to get user data from personal_details first
            const { data: personalData, error: personalError } = await supabase
              .from('personal_details')
              .select('first_name, last_name, email, profile_name')
              .eq('user_id', req.user_id)
              .single()

            if (!personalError && personalData) {
              return {
                ...req,
                requester_name: personalData.first_name && personalData.last_name 
                  ? `${personalData.first_name} ${personalData.last_name}`
                  : personalData.profile_name || 'Unknown User',
                requester_email: personalData.email,
                requester_profile_name: personalData.profile_name
              }
            }

            // Fallback to basic request data
            return {
              ...req,
              requester_name: 'Unknown User',
              requester_email: '',
              requester_profile_name: ''
            }
          } catch (error) {
            console.error(`ConnectionsService: Error enhancing request ${req.id}:`, error)
            return {
              ...req,
              requester_name: 'Unknown User',
              requester_email: '',
              requester_profile_name: ''
            }
          }
        })
      )

      return enhancedRequests
    } catch (error) {
      console.error('ConnectionsService: Failed to get connection requests:', error)
      throw error
    }
  }

  /**
   * Get accepted connections for a user
   */
  async getConnections(userId: string): Promise<Connection[]> {
    try {
      const { data: connections, error } = await supabase
        .from('user_connections')
        .select('*')
        .or(`user_id.eq.${userId},connected_user_id.eq.${userId}`)
        .eq('connection_status', 'accepted')

      if (error) {
        console.error('ConnectionsService: Error fetching connections:', error)
        throw error
      }

      return connections || []
    } catch (error) {
      console.error('ConnectionsService: Failed to get connections:', error)
      throw error
    }
  }

  /**
   * Send a connection request
   */
  async sendConnectionRequest(data: ConnectionCreateData): Promise<ConnectionRequest> {
    try {
      const { data: request, error } = await supabase
        .from('user_connections')
        .insert([{
          ...data,
          connection_status: 'pending'
        }])
        .select()
        .single()

      if (error) {
        console.error('ConnectionsService: Error sending connection request:', error)
        throw error
      }

      return request
    } catch (error) {
      console.error('ConnectionsService: Failed to send connection request:', error)
      throw error
    }
  }

  /**
   * Accept a connection request
   */
  async acceptConnectionRequest(requestId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('user_connections')
        .update({
          connection_status: 'accepted',
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId)

      if (error) {
        console.error('ConnectionsService: Error accepting connection request:', error)
        throw error
      }
    } catch (error) {
      console.error('ConnectionsService: Failed to accept connection request:', error)
      throw error
    }
  }

  /**
   * Decline/Delete a connection request
   */
  async declineConnectionRequest(requestId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('user_connections')
        .delete()
        .eq('id', requestId)

      if (error) {
        console.error('ConnectionsService: Error declining connection request:', error)
        throw error
      }
    } catch (error) {
      console.error('ConnectionsService: Failed to decline connection request:', error)
      throw error
    }
  }

  /**
   * Remove an existing connection
   */
  async removeConnection(connectionId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('user_connections')
        .delete()
        .eq('id', connectionId)

      if (error) {
        console.error('ConnectionsService: Error removing connection:', error)
        throw error
      }
    } catch (error) {
      console.error('ConnectionsService: Failed to remove connection:', error)
      throw error
    }
  }

  /**
   * Check if a connection exists between two users
   */
  async checkConnectionExists(userId1: string, userId2: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('user_connections')
        .select('id')
        .or(`and(user_id.eq.${userId1},connected_user_id.eq.${userId2}),and(user_id.eq.${userId2},connected_user_id.eq.${userId1})`)
        .limit(1)

      if (error) {
        console.error('ConnectionsService: Error checking connection:', error)
        return false
      }

      return (data && data.length > 0) || false
    } catch (error) {
      console.error('ConnectionsService: Failed to check connection:', error)
      return false
    }
  }
}

// Export singleton instance
export const connectionsService = ConnectionsService.getInstance()

// Export composable for Vue components
export function useConnectionsService() {
  return connectionsService
}
