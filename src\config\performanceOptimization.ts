/**
 * Performance Optimization Configuration
 * 
 * Fine-tuned cache configurations and performance settings
 * based on migration results and usage patterns.
 */

import type { CacheConfig } from '../services/unifiedCacheService'

/**
 * Optimized cache configurations based on migration analysis
 */
export const OPTIMIZED_CACHE_CONFIGS: Record<string, CacheConfig> = {
  // Profile data - accessed frequently, changes rarely
  profile: {
    ttl: 10 * 60 * 1000, // Increased to 10 minutes (was 5)
    storage: 'memory',
    maxSize: 150, // Increased from 100
    invalidationPatterns: ['profile:*', 'user:*']
  },

  // User statistics - accessed frequently, changes moderately
  userStats: {
    ttl: 5 * 60 * 1000, // Increased to 5 minutes (was 2)
    storage: 'memory',
    maxSize: 100, // Increased from 50
    invalidationPatterns: ['stats:*', 'user:*']
  },

  // Feed data - accessed very frequently, changes frequently
  feed: {
    ttl: 60 * 1000, // Increased to 1 minute (was 30 seconds)
    storage: 'memory',
    maxSize: 50, // Increased from 20
    invalidationPatterns: ['feed:*']
  },

  // Authentication - critical for security, moderate access
  auth: {
    ttl: 15 * 60 * 1000, // Increased to 15 minutes (was 5)
    storage: 'sessionStorage',
    invalidationPatterns: ['auth:*', 'user:*']
  },

  // Messaging conversations - accessed frequently, changes moderately
  messaging: {
    ttl: 2 * 60 * 1000, // Increased to 2 minutes (was 1)
    storage: 'memory',
    maxSize: 50, // Increased from 30
    invalidationPatterns: ['message:*', 'conversation:*']
  },

  // Messaging messages - accessed very frequently, changes frequently
  messages: {
    ttl: 45 * 1000, // Increased to 45 seconds (was 30)
    storage: 'memory',
    maxSize: 100, // New dedicated config for messages
    invalidationPatterns: ['message:*']
  },

  // Table existence checks - rarely changes, accessed occasionally
  tableExists: {
    ttl: 2 * 60 * 60 * 1000, // Increased to 2 hours (was 1)
    storage: 'memory',
    invalidationPatterns: ['table:*']
  },

  // User state - critical for UX, accessed frequently
  userState: {
    ttl: 10 * 60 * 1000, // Increased to 10 minutes (was 5)
    storage: 'sessionStorage',
    invalidationPatterns: ['userState:*', 'user:*']
  },

  // Connection service data - accessed frequently, changes rarely
  connections: {
    ttl: 5 * 60 * 1000, // 5 minutes
    storage: 'memory',
    maxSize: 200,
    invalidationPatterns: ['connection:*', 'user:*']
  },

  // Project data - accessed frequently, changes moderately
  projects: {
    ttl: 3 * 60 * 1000, // 3 minutes
    storage: 'memory',
    maxSize: 100,
    invalidationPatterns: ['project:*']
  }
}

/**
 * Performance monitoring thresholds
 */
export const PERFORMANCE_THRESHOLDS = {
  cache: {
    minHitRate: 0.85, // 85% minimum hit rate
    maxMemoryUsage: 10 * 1024 * 1024, // 10MB max memory usage
    maxEntries: 1000, // Maximum total cache entries
    cleanupInterval: 5 * 60 * 1000 // Cleanup every 5 minutes
  },
  realtime: {
    maxLatency: 1000, // 1 second max latency
    maxSubscriptions: 50, // Maximum concurrent subscriptions
    maxReconnectAttempts: 5,
    healthCheckInterval: 30 * 1000 // Health check every 30 seconds
  },
  database: {
    maxQueryTime: 2000, // 2 seconds max query time
    maxConcurrentQueries: 10,
    retryAttempts: 3
  }
}

/**
 * Cache warming strategies
 */
export const CACHE_WARMING_STRATEGIES = {
  onUserLogin: [
    'profile:${userId}:private',
    'userState:${userId}',
    'auth:userState:${userId}',
    'messaging:conversations:10:1'
  ],
  onRouteChange: {
    '/dashboard': [
      'feed:tab:feed',
      'messaging:conversations:10:1'
    ],
    '/virtual-community': [
      'feed:tab:community',
      'connection:*'
    ],
    '/projects': [
      'project:*',
      'feed:tab:projects'
    ]
  },
  onContentCreation: [
    'feed:tab:*' // Invalidate all feed tabs
  ]
}

/**
 * Memory management settings
 */
export const MEMORY_MANAGEMENT = {
  lruEvictionThreshold: 0.8, // Start LRU eviction at 80% capacity
  memoryPressureThreshold: 0.9, // Emergency cleanup at 90% capacity
  backgroundCleanupInterval: 10 * 60 * 1000, // Background cleanup every 10 minutes
  maxIdleTime: 30 * 60 * 1000 // Remove entries idle for 30 minutes
}

/**
 * Real-time optimization settings
 */
export const REALTIME_OPTIMIZATION = {
  subscriptionDeduplication: true,
  eventBatching: {
    enabled: true,
    batchSize: 10,
    batchTimeout: 100 // 100ms
  },
  connectionPooling: {
    enabled: true,
    maxConnections: 5,
    idleTimeout: 5 * 60 * 1000 // 5 minutes
  },
  reconnectionStrategy: {
    initialDelay: 1000,
    maxDelay: 30000,
    backoffMultiplier: 2,
    jitter: true
  }
}

/**
 * Performance monitoring configuration
 */
export const PERFORMANCE_MONITORING = {
  enabled: true,
  metricsCollection: {
    cacheHitRate: true,
    memoryUsage: true,
    queryTimes: true,
    realtimeLatency: true,
    errorRates: true
  },
  alerting: {
    enabled: true,
    thresholds: {
      cacheHitRateBelow: 0.8,
      memoryUsageAbove: 0.9,
      errorRateAbove: 0.05,
      latencyAbove: 2000
    }
  },
  reporting: {
    interval: 60 * 1000, // Report every minute
    aggregationWindow: 5 * 60 * 1000, // 5-minute aggregation
    retentionPeriod: 24 * 60 * 60 * 1000 // Keep data for 24 hours
  }
}

/**
 * Development vs Production optimizations
 */
export const ENVIRONMENT_OPTIMIZATIONS = {
  development: {
    cacheDebugLogging: true,
    performanceWarnings: true,
    detailedErrorReporting: true,
    cacheVisualization: true
  },
  production: {
    cacheDebugLogging: false,
    performanceWarnings: false,
    detailedErrorReporting: false,
    cacheVisualization: false,
    compressionEnabled: true,
    minificationEnabled: true
  }
}

/**
 * Apply optimized configurations to unified cache service
 */
export function applyOptimizedConfigurations(): void {
  console.log('🚀 Applying optimized performance configurations...')
  
  // Update default cache configurations
  Object.entries(OPTIMIZED_CACHE_CONFIGS).forEach(([key, config]) => {
    console.log(`📋 Optimized cache config for ${key}: TTL=${config.ttl}ms, Storage=${config.storage}`)
  })
  
  console.log('✅ Performance optimizations applied!')
}

/**
 * Performance monitoring dashboard data
 */
export function getPerformanceDashboardData() {
  return {
    cacheConfigs: OPTIMIZED_CACHE_CONFIGS,
    thresholds: PERFORMANCE_THRESHOLDS,
    warmingStrategies: CACHE_WARMING_STRATEGIES,
    memoryManagement: MEMORY_MANAGEMENT,
    realtimeOptimization: REALTIME_OPTIMIZATION,
    monitoring: PERFORMANCE_MONITORING
  }
}

/**
 * Validate current performance against thresholds
 */
export function validatePerformance(cacheStats: any, realtimeStats: any): {
  passed: boolean
  warnings: string[]
  errors: string[]
} {
  const warnings: string[] = []
  const errors: string[] = []

  // Check cache performance
  if (cacheStats.hitRate < PERFORMANCE_THRESHOLDS.cache.minHitRate) {
    warnings.push(`Cache hit rate ${(cacheStats.hitRate * 100).toFixed(1)}% below threshold ${(PERFORMANCE_THRESHOLDS.cache.minHitRate * 100)}%`)
  }

  if (cacheStats.memoryUsage > PERFORMANCE_THRESHOLDS.cache.maxMemoryUsage) {
    errors.push(`Memory usage ${(cacheStats.memoryUsage / 1024 / 1024).toFixed(1)}MB exceeds threshold ${PERFORMANCE_THRESHOLDS.cache.maxMemoryUsage / 1024 / 1024}MB`)
  }

  if (cacheStats.totalEntries > PERFORMANCE_THRESHOLDS.cache.maxEntries) {
    warnings.push(`Total cache entries ${cacheStats.totalEntries} exceeds threshold ${PERFORMANCE_THRESHOLDS.cache.maxEntries}`)
  }

  // Check real-time performance
  if (realtimeStats.averageLatency > PERFORMANCE_THRESHOLDS.realtime.maxLatency) {
    warnings.push(`Real-time latency ${realtimeStats.averageLatency.toFixed(1)}ms exceeds threshold ${PERFORMANCE_THRESHOLDS.realtime.maxLatency}ms`)
  }

  if (realtimeStats.activeSubscriptions > PERFORMANCE_THRESHOLDS.realtime.maxSubscriptions) {
    warnings.push(`Active subscriptions ${realtimeStats.activeSubscriptions} exceeds threshold ${PERFORMANCE_THRESHOLDS.realtime.maxSubscriptions}`)
  }

  return {
    passed: errors.length === 0,
    warnings,
    errors
  }
}

// Export for use in browser console
if (typeof window !== 'undefined') {
  (window as any).performanceOptimization = {
    applyOptimizedConfigurations,
    getPerformanceDashboardData,
    validatePerformance,
    configs: OPTIMIZED_CACHE_CONFIGS,
    thresholds: PERFORMANCE_THRESHOLDS
  }
}
