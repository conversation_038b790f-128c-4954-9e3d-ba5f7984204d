/**
 * Security utilities for input sanitization and XSS prevention
 */

// Basic HTML sanitization without DOMPurify (fallback)
function basicHtmlSanitize(html: string): string {
  if (!html) return '';
  
  // Remove script tags and their content
  html = html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
  
  // Remove dangerous attributes
  html = html.replace(/\s*on\w+\s*=\s*["'][^"']*["']/gi, ''); // onclick, onload, etc.
  html = html.replace(/\s*javascript\s*:/gi, ''); // javascript: URLs
  html = html.replace(/\s*vbscript\s*:/gi, ''); // vbscript: URLs
  html = html.replace(/\s*data\s*:/gi, ''); // data: URLs (can be dangerous)
  
  // Remove dangerous tags
  const dangerousTags = ['iframe', 'object', 'embed', 'form', 'input', 'textarea', 'button', 'select', 'option'];
  dangerousTags.forEach(tag => {
    const regex = new RegExp(`<${tag}\\b[^>]*>.*?<\\/${tag}>`, 'gi');
    html = html.replace(regex, '');
    // Also remove self-closing versions
    const selfClosingRegex = new RegExp(`<${tag}\\b[^>]*\\/>`, 'gi');
    html = html.replace(selfClosingRegex, '');
  });
  
  return html;
}

/**
 * Sanitize HTML content to prevent XSS attacks
 * Uses DOMPurify if available, falls back to basic sanitization
 */
export function sanitizeHtml(html: string): string {
  if (!html) return '';
  
  try {
    // Try to use DOMPurify if available
    if (typeof window !== 'undefined' && (window as any).DOMPurify) {
      return (window as any).DOMPurify.sanitize(html, {
        ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'u', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'ul', 'ol', 'li', 'a', 'img', 'blockquote', 'code', 'pre'],
        ALLOWED_ATTR: ['href', 'src', 'alt', 'title', 'target'],
        ALLOW_DATA_ATTR: false
      });
    }
  } catch (error) {
    console.warn('DOMPurify not available, using basic sanitization:', error);
  }
  
  // Fallback to basic sanitization
  return basicHtmlSanitize(html);
}

/**
 * Sanitize text input to prevent XSS in plain text contexts
 */
export function sanitizeText(text: string): string {
  if (!text) return '';
  
  return text
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
}

/**
 * Validate and sanitize URL to prevent javascript: and data: URLs
 */
export function sanitizeUrl(url: string): string {
  if (!url) return '';
  
  // Remove any whitespace
  url = url.trim();
  
  // Check for dangerous protocols
  const dangerousProtocols = ['javascript:', 'vbscript:', 'data:', 'file:'];
  const lowerUrl = url.toLowerCase();
  
  for (const protocol of dangerousProtocols) {
    if (lowerUrl.startsWith(protocol)) {
      console.warn(`Dangerous URL protocol detected: ${protocol}`);
      return '#'; // Return safe fallback
    }
  }
  
  // Only allow http, https, mailto, tel protocols
  const allowedProtocols = ['http:', 'https:', 'mailto:', 'tel:'];
  const hasProtocol = url.includes(':');
  
  if (hasProtocol) {
    const isAllowed = allowedProtocols.some(protocol => lowerUrl.startsWith(protocol));
    if (!isAllowed) {
      console.warn(`URL protocol not allowed: ${url}`);
      return '#'; // Return safe fallback
    }
  }
  
  return url;
}

/**
 * Validate email address format
 */
export function validateEmail(email: string): boolean {
  if (!email) return false;
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 254; // RFC 5321 limit
}

/**
 * Validate and sanitize form input with length limits
 */
export function sanitizeFormInput(input: string, maxLength: number = 1000): string {
  if (!input) return '';
  
  // Trim whitespace
  input = input.trim();
  
  // Enforce length limit
  if (input.length > maxLength) {
    input = input.substring(0, maxLength);
  }
  
  // Basic sanitization for form inputs
  return sanitizeText(input);
}

/**
 * Validate password strength
 */
export function validatePassword(password: string): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!password) {
    errors.push('Password is required');
    return { isValid: false, errors };
  }
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  if (password.length > 128) {
    errors.push('Password must be less than 128 characters');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/[0-9]/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }
  
  return { isValid: errors.length === 0, errors };
}

/**
 * Generate a CSRF token (basic implementation)
 */
export function generateCSRFToken(): string {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
}

/**
 * Store CSRF token in session storage
 */
export function storeCSRFToken(token: string): void {
  if (typeof window !== 'undefined' && window.sessionStorage) {
    window.sessionStorage.setItem('csrf_token', token);
  }
}

/**
 * Get CSRF token from session storage
 */
export function getCSRFToken(): string | null {
  if (typeof window !== 'undefined' && window.sessionStorage) {
    return window.sessionStorage.getItem('csrf_token');
  }
  return null;
}

/**
 * Validate CSRF token
 */
export function validateCSRFToken(token: string): boolean {
  const storedToken = getCSRFToken();
  return storedToken !== null && storedToken === token;
}

/**
 * Rate limiting helper (basic client-side implementation)
 */
export class RateLimiter {
  private attempts: Map<string, number[]> = new Map();
  
  constructor(private maxAttempts: number = 5, private windowMs: number = 60000) {}
  
  isAllowed(key: string): boolean {
    const now = Date.now();
    const attempts = this.attempts.get(key) || [];
    
    // Remove old attempts outside the window
    const validAttempts = attempts.filter(time => now - time < this.windowMs);
    
    if (validAttempts.length >= this.maxAttempts) {
      return false;
    }
    
    // Add current attempt
    validAttempts.push(now);
    this.attempts.set(key, validAttempts);
    
    return true;
  }
  
  reset(key: string): void {
    this.attempts.delete(key);
  }
}

// Export a default rate limiter instance
export const defaultRateLimiter = new RateLimiter();
