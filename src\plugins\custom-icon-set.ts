/**
 * Custom Icon Set for Quasar
 * 
 * This file defines a custom icon set for Quasar that uses SVG data URLs
 * for all icons, ensuring consistent rendering across the application.
 */

// SVG paths for Material Design icons
const iconPaths = {
  // Navigation icons
  'arrow_upward': 'M4 12l1.41 1.41L11 7.83V20h2V7.83l5.58 5.59L20 12l-8-8-8 8z',
  'arrow_forward': 'M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z',
  'arrow_downward': 'M20 12l-1.41-1.41L13 16.17V4h-2v12.17l-5.58-5.59L4 12l8 8 8-8z',
  'arrow_back': 'M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z',
  'arrow_drop_down': 'M7 10l5 5 5-5z',
  
  // Chevron icons
  'chevron_left': 'M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z',
  'chevron_right': 'M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z',
  
  // Color picker icons
  'gradient': 'M11 9h2v2h-2zm-2 2h2v2H9zm4 0h2v2h-2zm2-2h2v2h-2zM7 9h2v2H7zm12-6H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 18H7v-2h2v2zm4 0h-2v-2h2v2zm4 0h-2v-2h2v2zm2-7h-2v2h2v2h-2v-2h-2v2h-2v-2h-2v2H9v-2H7v2H5v-2h2v-2H5V5h14v6z',
  'tune': 'M3 17v2h6v-2H3zM3 5v2h10V5H3zm10 16v-2h8v-2h-8v-2h-2v6h2zM7 9v2H3v2h4v2h2V9H7zm14 4v-2H11v2h10zm-6-4h2V7h4V5h-4V3h-2v6z',
  'style': 'M2.53 19.65l1.34.56v-9.03l-2.43 5.86c-.41 1.02.08 2.19 1.09 2.61zm19.5-3.7L17.07 3.98c-.31-.75-1.04-1.21-1.81-1.23-.26 0-.53.04-.79.15L7.1 5.95c-.75.31-1.21 1.03-1.23 1.8-.01.27.04.54.15.8l4.96 11.97c.31.76 1.05 1.22 1.83 1.23.26 0 .52-.05.77-.15l7.36-3.05c1.02-.42 1.51-1.59 1.09-2.6zM7.88 8.75c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1zm-2 11c0 1.1.9 2 2 2h1.45l-3.45-8.34v6.34z',
  
  // Pull to refresh icon
  'refresh': 'M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 9h7V2l-2.35 4.35z',
  
  // Carousel icons
  'keyboard_arrow_up': 'M7.41 15.41L12 10.83l4.59 4.58L18 14l-6-6-6 6z',
  'keyboard_arrow_down': 'M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z',
  'lens': 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z',
  
  // Chip icons
  'cancel': 'M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z',
  'check': 'M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z',
  
  // Expansion item icons
  'expand_less': 'M12 8l-6 6 1.41 1.41L12 10.83l4.59 4.58L18 14z',
  'expand_more': 'M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z',
  
  // Fab icons
  'add': 'M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z',
  
  // Pagination icons
  'first_page': 'M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z',
  'last_page': 'M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z',
  
  // Rating icons
  'star': 'M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z',
  'star_border': 'M22 9.24l-7.19-.62L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21 12 17.27 18.18 21l-1.63-7.03L22 9.24zM12 15.4l-3.76 2.27 1-4.28-3.32-2.88 4.38-.38L12 6.1l1.71 4.04 4.38.38-3.32 2.88 1 4.28L12 15.4z',
  
  // Select icons
  'arrow_drop_up': 'M7 14l5-5 5 5z',
  
  // Stepper icons
  'done': 'M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z',
  'warning': 'M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z',
  
  // Table icons
  'arrow_downward': 'M20 12l-1.41-1.41L13 16.17V4h-2v12.17l-5.58-5.59L4 12l8 8 8-8z',
  'arrow_upward': 'M4 12l1.41 1.41L11 7.83V20h2V7.83l5.58 5.59L20 12l-8-8-8 8z',
  
  // Time icons
  'access_time': 'M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z',
  
  // Tree icons
  'play_arrow': 'M8 5v14l11-7z',
  
  // Uploader icons
  'cloud_upload': 'M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96zM14 13v4h-4v-4H7l5-5 5 5h-3z',
  'clear': 'M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z',
  
  // Type icons
  'check_circle': 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z',
  'error': 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z',
  'info': 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z',
  'priority_high': 'M12 19m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0M12 3l0 10'
};

// Create SVG data URL for an icon
const createSvgIcon = (path: string, color = '%230D8A3E') => {
  return `img:data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='${color}' d='${path}'/%3E%3C/svg%3E`;
};

// Create the custom icon set
export default {
  name: 'custom-material',
  type: {
    positive: createSvgIcon(iconPaths.check_circle),
    negative: createSvgIcon(iconPaths.error),
    info: createSvgIcon(iconPaths.info),
    warning: createSvgIcon(iconPaths.warning)
  },
  arrow: {
    up: createSvgIcon(iconPaths.arrow_upward),
    right: createSvgIcon(iconPaths.arrow_forward),
    down: createSvgIcon(iconPaths.arrow_downward),
    left: createSvgIcon(iconPaths.arrow_back),
    dropdown: createSvgIcon(iconPaths.arrow_drop_down)
  },
  chevron: {
    left: createSvgIcon(iconPaths.chevron_left),
    right: createSvgIcon(iconPaths.chevron_right)
  },
  colorPicker: {
    spectrum: createSvgIcon(iconPaths.gradient),
    tune: createSvgIcon(iconPaths.tune),
    palette: createSvgIcon(iconPaths.style)
  },
  pullToRefresh: {
    icon: createSvgIcon(iconPaths.refresh)
  },
  carousel: {
    left: createSvgIcon(iconPaths.chevron_left),
    right: createSvgIcon(iconPaths.chevron_right),
    up: createSvgIcon(iconPaths.keyboard_arrow_up),
    down: createSvgIcon(iconPaths.keyboard_arrow_down),
    navigationIcon: createSvgIcon(iconPaths.lens)
  },
  chip: {
    remove: createSvgIcon(iconPaths.cancel),
    selected: createSvgIcon(iconPaths.check)
  },
  datetime: {
    arrowLeft: createSvgIcon(iconPaths.chevron_left),
    arrowRight: createSvgIcon(iconPaths.chevron_right),
    now: createSvgIcon(iconPaths.access_time),
    today: createSvgIcon(iconPaths.today)
  },
  editor: {
    bold: createSvgIcon('M15.6 10.79c.97-.67 1.65-1.77 1.65-2.79 0-2.26-1.75-4-4-4H7v14h7.04c2.09 0 3.71-1.7 3.71-3.79 0-1.52-.86-2.82-2.15-3.42zM10 6.5h3c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5h-3v-3zm3.5 9H10v-3h3.5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5z'),
    italic: createSvgIcon('M10 4v3h2.21l-3.42 8H6v3h8v-3h-2.21l3.42-8H18V4z'),
    strikethrough: createSvgIcon('M10 19h4v-3h-4v3zM5 4v3h5v3h4V7h5V4H5zM3 14h18v-2H3v2z'),
    underline: createSvgIcon('M12 17c3.31 0 6-2.69 6-6V3h-2.5v8c0 1.93-1.57 3.5-3.5 3.5S8.5 12.93 8.5 11V3H6v8c0 3.31 2.69 6 6 6zm-7 2v2h14v-2H5z'),
    unorderedList: createSvgIcon('M4 10.5c-.83 0-1.5.67-1.5 1.5s.67 1.5 1.5 1.5 1.5-.67 1.5-1.5-.67-1.5-1.5-1.5zm0-6c-.83 0-1.5.67-1.5 1.5S3.17 7.5 4 7.5 5.5 6.83 5.5 6 4.83 4.5 4 4.5zm0 12c-.83 0-1.5.68-1.5 1.5s.68 1.5 1.5 1.5 1.5-.68 1.5-1.5-.67-1.5-1.5-1.5zM7 19h14v-2H7v2zm0-6h14v-2H7v2zm0-8v2h14V5H7z'),
    orderedList: createSvgIcon('M2 17h2v.5H3v1h1v.5H2v1h3v-4H2v1zm1-9h1V4H2v1h1v3zm-1 3h1.8L2 13.1v.9h3v-1H3.2L5 10.9V10H2v1zm5-6v2h14V5H7zm0 14h14v-2H7v2zm0-6h14v-2H7v2z'),
    subscript: createSvgIcon('M22 18h-2v1h-1v-1h-2v-1h2v-1h1v1h2v1zm-9-6.6c0 .2-.2.4-.4.4H5.4c-.2 0-.4-.2-.4-.4V5.4c0-.2.2-.4.4-.4h7.2c.2 0 .4.2.4.4v6zm0 7.1c0 .2-.2.4-.4.4H5.4c-.2 0-.4-.2-.4-.4V12.4c0-.2.2-.4.4-.4h7.2c.2 0 .4.2.4.4v6.1z'),
    superscript: createSvgIcon('M22 7h-2v1h-1V7h-2V6h2V5h1v1h2v1zm-9 5.6c0 .2-.2.4-.4.4H5.4c-.2 0-.4-.2-.4-.4V5.4c0-.2.2-.4.4-.4h7.2c.2 0 .4.2.4.4v7.2zm0 7.1c0 .2-.2.4-.4.4H5.4c-.2 0-.4-.2-.4-.4v-7.2c0-.2.2-.4.4-.4h7.2c.2 0 .4.2.4.4v7.2z'),
    hyperlink: createSvgIcon('M3.9 12c0-1.71 1.39-3.1 3.1-3.1h4V7H7c-2.76 0-5 2.24-5 5s2.24 5 5 5h4v-1.9H7c-1.71 0-3.1-1.39-3.1-3.1zM8 13h8v-2H8v2zm9-6h-4v1.9h4c1.71 0 3.1 1.39 3.1 3.1s-1.39 3.1-3.1 3.1h-4V17h4c2.76 0 5-2.24 5-5s-2.24-5-5-5z'),
    toggleFullscreen: createSvgIcon('M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z'),
    quote: createSvgIcon('M6 17h3l2-4V7H5v6h3zm8 0h3l2-4V7h-6v6h3z'),
    left: createSvgIcon('M15 15H3v2h12v-2zm0-8H3v2h12V7zM3 13h18v-2H3v2zm0 8h18v-2H3v2zM3 3v2h18V3H3z'),
    center: createSvgIcon('M7 15v2h10v-2H7zm-4 6h18v-2H3v2zm0-8h18v-2H3v2zm4-6v2h10V7H7zM3 3v2h18V3H3z'),
    right: createSvgIcon('M3 21h18v-2H3v2zm6-4h12v-2H9v2zm-6-4h18v-2H3v2zm6-4h12V7H9v2zM3 3v2h18V3H3z'),
    justify: createSvgIcon('M3 21h18v-2H3v2zm0-4h18v-2H3v2zm0-4h18v-2H3v2zm0-4h18V7H3v2zm0-6v2h18V3H3z')
  },
  expansionItem: {
    icon: createSvgIcon(iconPaths.expand_more),
    denseIcon: createSvgIcon(iconPaths.expand_more)
  },
  fab: {
    icon: createSvgIcon(iconPaths.add),
    activeIcon: createSvgIcon(iconPaths.close)
  },
  field: {
    clear: createSvgIcon(iconPaths.cancel),
    error: createSvgIcon(iconPaths.error)
  },
  pagination: {
    first: createSvgIcon(iconPaths.first_page),
    prev: createSvgIcon(iconPaths.chevron_left),
    next: createSvgIcon(iconPaths.chevron_right),
    last: createSvgIcon(iconPaths.last_page)
  },
  rating: {
    icon: createSvgIcon(iconPaths.star),
    halfIcon: createSvgIcon('M22 9.24l-7.19-.62L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21 12 17.27 18.18 21l-1.63-7.03L22 9.24zM12 15.4V6.1l1.71 4.04 4.38.38-3.32 2.88 1 4.28L12 15.4z'),
    emptyIcon: createSvgIcon(iconPaths.star_border)
  },
  select: {
    dropdownIcon: createSvgIcon(iconPaths.arrow_drop_down)
  },
  stepper: {
    done: createSvgIcon(iconPaths.done),
    active: createSvgIcon(iconPaths.edit),
    error: createSvgIcon(iconPaths.warning)
  },
  tabs: {
    left: createSvgIcon(iconPaths.chevron_left),
    right: createSvgIcon(iconPaths.chevron_right),
    up: createSvgIcon(iconPaths.keyboard_arrow_up),
    down: createSvgIcon(iconPaths.keyboard_arrow_down)
  },
  table: {
    arrowUp: createSvgIcon(iconPaths.arrow_upward),
    warning: createSvgIcon(iconPaths.warning),
    firstPage: createSvgIcon(iconPaths.first_page),
    prevPage: createSvgIcon(iconPaths.chevron_left),
    nextPage: createSvgIcon(iconPaths.chevron_right),
    lastPage: createSvgIcon(iconPaths.last_page)
  },
  tree: {
    icon: createSvgIcon(iconPaths.play_arrow)
  },
  uploader: {
    done: createSvgIcon(iconPaths.done),
    clear: createSvgIcon(iconPaths.clear),
    add: createSvgIcon(iconPaths.add),
    upload: createSvgIcon(iconPaths.cloud_upload),
    removeQueue: createSvgIcon(iconPaths.clear)
  }
};
