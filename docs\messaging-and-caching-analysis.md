# Messaging and Caching Analysis

## Overview

This document analyzes the current messaging implementations and caching patterns across the platform to identify consolidation opportunities and design a unified approach.

## Current Messaging Implementations

### 1. Centralized Messaging Store (`src/stores/messaging.ts`)

**Strengths:**
- Centralized state management with Pinia
- Real-time subscriptions for user_messages table
- Retry logic and error handling
- Unread count tracking
- Conversation management

**Current Features:**
- Direct messaging between users
- Real-time message notifications
- Conversation loading and caching
- Message sending with optimistic updates
- Subscription cleanup

**Limitations:**
- No context-aware messaging (project-specific, group messaging)
- No message threading or replies
- No typing indicators
- No read receipts beyond basic is_read flag
- Limited message metadata (no attachments, reactions)

### 2. MessagingView Component (`src/views/dashboard/MessagingView.vue`)

**Current Implementation:**
- Uses the centralized messaging store
- Implements additional caching with localStorage debouncing
- Background refresh intervals with visibility checks
- Optimistic UI updates for sent messages
- Retry mechanisms for failed operations

**Issues Identified:**
- Duplicate caching logic (store + component level)
- Complex refresh logic that could be simplified
- Manual subscription management
- Inconsistent loading states

### 3. Notification Service (`src/services/notificationService.ts`)

**Current Implementation:**
- Real-time subscriptions for user_notifications table
- Toast notifications for new events
- Basic notification categorization

**Integration Opportunities:**
- Could be integrated with messaging for unified communication
- Notification preferences could be centralized
- Real-time patterns could be standardized

## Current Caching Patterns

### 1. ProfileManager (`src/services/ProfileManager.ts`)
- **Storage**: In-memory Map
- **TTL**: 5 minutes (300,000ms)
- **Features**: Deduplication, force refresh, context-aware caching
- **Cache Key Pattern**: `${userId}-${context}`

### 2. FeedContainer (`src/components/feed/FeedContainer.vue`)
- **Storage**: Component-level ref
- **TTL**: 30 seconds (30,000ms)
- **Features**: Tab-based caching
- **Cache Key Pattern**: Tab name

### 3. MessagingView (`src/views/dashboard/MessagingView.vue`)
- **Storage**: localStorage
- **TTL**: 30 seconds for refresh debouncing, 5 seconds for message loading
- **Features**: Debouncing, visibility-based refresh
- **Cache Key Pattern**: `lastMessagingRefreshTime`, `lastMessageLoad_${userId}`

### 4. Route Guards (`src/router/enhancedGuards.ts`)
- **Storage**: In-memory Map
- **TTL**: 5 minutes
- **Features**: Auth state caching
- **Cache Key Pattern**: `userState_${userId}`

### 5. User State Service (`src/services/userStateService.ts`)
- **Storage**: sessionStorage
- **TTL**: 5 minutes
- **Features**: Session-based persistence
- **Cache Key Pattern**: `userState_${userId}`

## Issues Identified

### Caching Issues
1. **Inconsistent TTL Values**: 30 seconds to 5 minutes across different services
2. **Multiple Storage Backends**: Memory, localStorage, sessionStorage used inconsistently
3. **No Cache Invalidation Strategy**: No way to invalidate related cache entries
4. **Duplicate Cache Keys**: Risk of collisions between services
5. **No Memory Management**: No LRU eviction or size limits
6. **No Cache Statistics**: No monitoring of hit/miss rates

### Messaging Issues
1. **Limited Context Awareness**: No project-specific or group messaging
2. **No Message Threading**: Flat conversation structure
3. **Basic Real-time Features**: Missing typing indicators, read receipts
4. **Duplicate Subscription Patterns**: Similar patterns across different services
5. **Manual Lifecycle Management**: Components manually manage subscriptions

### Real-time Subscription Issues
1. **Duplicate Subscriptions**: Risk of multiple subscriptions to same data
2. **Inconsistent Error Handling**: Different error handling patterns
3. **No Connection Management**: No reconnection logic
4. **Manual Cleanup**: Components responsible for cleanup

## Proposed Unified Architecture

### 1. Unified Caching Service

```typescript
interface CacheConfig {
  ttl: number
  storage: 'memory' | 'localStorage' | 'sessionStorage'
  maxSize?: number
  invalidationPatterns?: string[]
}

interface CacheService {
  get<T>(key: string): T | null
  set<T>(key: string, value: T, config?: Partial<CacheConfig>): void
  invalidate(pattern: string): void
  clear(storage?: string): void
  getStats(): CacheStats
}
```

### 2. Unified Real-time Service

```typescript
interface RealtimeService {
  subscribe(table: string, filter: string, callback: Function): Subscription
  unsubscribe(subscription: Subscription): void
  getConnectionState(): ConnectionState
  reconnect(): Promise<void>
}
```

### 3. Enhanced Messaging Service

```typescript
interface MessagingService {
  // Direct messaging
  sendMessage(recipientId: string, content: string, context?: MessageContext): Promise<boolean>
  
  // Context-aware messaging
  sendProjectMessage(projectId: string, content: string): Promise<boolean>
  sendGroupMessage(groupId: string, content: string): Promise<boolean>
  
  // Threading
  replyToMessage(messageId: string, content: string): Promise<boolean>
  
  // Real-time features
  startTyping(conversationId: string): void
  stopTyping(conversationId: string): void
  markAsRead(messageId: string): Promise<boolean>
}
```

## Implementation Priority

1. **High Priority**: Unified Caching Service (foundation for all other improvements)
2. **High Priority**: Unified Real-time Service (prevents duplicate subscriptions)
3. **Medium Priority**: Enhanced Messaging Service (builds on existing functionality)
4. **Medium Priority**: Component Migration (gradual rollout)
5. **Low Priority**: Advanced Features (threading, typing indicators)

## Success Metrics

- **Cache Hit Rate**: Target 90%+ for frequently accessed data
- **Memory Usage**: Reduce overall memory footprint by 30%
- **Database Calls**: Reduce redundant database calls by 50%
- **Real-time Latency**: Maintain <1 second for message delivery
- **Code Duplication**: Eliminate duplicate caching and subscription patterns

## Next Steps

1. Implement unified caching service
2. Migrate ProfileManager to use unified caching
3. Implement unified real-time service
4. Migrate messaging store to use unified real-time service
5. Update components to use unified services
6. Performance testing and optimization
