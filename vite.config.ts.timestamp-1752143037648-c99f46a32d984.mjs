// vite.config.ts
import { defineConfig } from "file:///C:/Users/<USER>/IdeaProjects/smilefactory/node_modules/vite/dist/node/index.js";
import vue from "file:///C:/Users/<USER>/IdeaProjects/smilefactory/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import { quasar, transformAssetUrls } from "file:///C:/Users/<USER>/IdeaProjects/smilefactory/node_modules/@quasar/vite-plugin/src/index.js";
import path from "path";
var __vite_injected_original_dirname = "C:\\Users\\<USER>\\IdeaProjects\\smilefactory";
var vite_config_default = defineConfig({
  // Base URL for Apache deployment
  base: "/",
  plugins: [
    vue({
      template: { transformAssetUrls }
    }),
    quasar({
      sassVariables: "src/css/quasar.variables.scss"
    })
  ],
  resolve: {
    alias: {
      "@": path.resolve(__vite_injected_original_dirname, "./src")
    }
  },
  build: {
    // Output directory
    outDir: "dist",
    // Optimize for Apache deployment
    assetsDir: "assets",
    // Target modern browsers
    target: "es2015",
    // Chunk size warning limit
    chunkSizeWarningLimit: 1e3
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
