<template>
  <div class="news-admin-container">
    <div class="admin-header">
      <h2 class="admin-title">
        <q-icon name="campaign" size="24px" class="title-icon" />
        News Ticker Management
      </h2>
      <q-btn
        @click="showCreateDialog = true"
        color="primary"
        icon="add"
        label="Add News Item"
        class="create-btn"
      />
    </div>

    <!-- News Items List -->
    <q-card class="news-list-card">
      <q-card-section>
        <div class="list-header">
          <h3>News Items</h3>
          <q-space />
          <q-btn
            @click="refreshNews"
            :loading="isLoading"
            icon="refresh"
            flat
            round
            color="primary"
            size="sm"
          />
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section class="news-items-section">
        <div v-if="isLoading" class="loading-container">
          <q-spinner-dots size="40px" color="primary" />
          <p>Loading news items...</p>
        </div>

        <div v-else-if="newsItems.length === 0" class="empty-state">
          <q-icon name="campaign" size="48px" color="grey-5" />
          <p>No news items found</p>
          <q-btn
            @click="showCreateDialog = true"
            color="primary"
            label="Create First News Item"
            outline
          />
        </div>

        <div v-else class="news-items-grid">
          <q-card
            v-for="item in newsItems"
            :key="item.id"
            class="news-item-card"
            :class="{ 'inactive': !item.is_active }"
          >
            <q-card-section>
              <div class="item-header">
                <div class="item-status">
                  <q-badge
                    :color="item.is_active ? 'positive' : 'grey'"
                    :label="item.is_active ? 'Active' : 'Inactive'"
                  />
                  <q-badge
                    color="info"
                    :label="`Priority: ${item.priority}`"
                    class="priority-badge"
                  />
                </div>
                <div class="item-actions">
                  <q-btn
                    @click="editItem(item)"
                    icon="edit"
                    size="sm"
                    flat
                    round
                    color="primary"
                  />
                  <q-btn
                    @click="toggleItemStatus(item)"
                    :icon="item.is_active ? 'visibility_off' : 'visibility'"
                    size="sm"
                    flat
                    round
                    :color="item.is_active ? 'orange' : 'positive'"
                  />
                  <q-btn
                    @click="deleteItem(item)"
                    icon="delete"
                    size="sm"
                    flat
                    round
                    color="negative"
                  />
                </div>
              </div>

              <h4 class="item-title">{{ item.title }}</h4>
              <p class="item-content">{{ truncateContent(item.content) }}</p>

              <div class="item-meta">
                <div class="meta-item">
                  <q-icon name="category" size="16px" />
                  <span>{{ item.category }}</span>
                </div>
                <div class="meta-item">
                  <q-icon name="schedule" size="16px" />
                  <span>{{ formatDate(item.start_date) }}</span>
                </div>
                <div v-if="item.end_date" class="meta-item">
                  <q-icon name="event_busy" size="16px" />
                  <span>{{ formatDate(item.end_date) }}</span>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>
      </q-card-section>
    </q-card>

    <!-- Create/Edit Dialog -->
    <q-dialog v-model="showCreateDialog" persistent>
      <q-card class="create-dialog">
        <q-card-section class="dialog-header">
          <h3>{{ editingItem ? 'Edit News Item' : 'Create News Item' }}</h3>
        </q-card-section>

        <q-separator />

        <q-card-section class="dialog-content">
          <q-form @submit="saveItem" class="news-form">
            <div class="form-row">
              <q-input
                v-model="formData.title"
                label="Title *"
                :rules="[val => !!val || 'Title is required']"
                outlined
                class="full-width"
              />
            </div>

            <div class="form-row">
              <q-input
                v-model="formData.content"
                label="Content *"
                type="textarea"
                rows="4"
                :rules="[val => !!val || 'Content is required']"
                outlined
                class="full-width"
              />
            </div>

            <div class="form-row">
              <q-select
                v-model="formData.category"
                :options="categoryOptions"
                label="Category"
                outlined
                class="category-select"
              />
              <q-input
                v-model.number="formData.priority"
                label="Priority"
                type="number"
                min="1"
                max="10"
                outlined
                class="priority-input"
              />
            </div>

            <div class="form-row">
              <q-input
                v-model="formData.start_date"
                label="Start Date"
                type="datetime-local"
                outlined
                class="date-input"
              />
              <q-input
                v-model="formData.end_date"
                label="End Date (Optional)"
                type="datetime-local"
                outlined
                class="date-input"
              />
            </div>

            <div class="form-row">
              <q-toggle
                v-model="formData.is_active"
                label="Active"
                color="positive"
              />
            </div>
          </q-form>
        </q-card-section>

        <q-separator />

        <q-card-actions align="right" class="dialog-actions">
          <q-btn
            @click="cancelEdit"
            label="Cancel"
            flat
            color="grey"
          />
          <q-btn
            @click="saveItem"
            :label="editingItem ? 'Update' : 'Create'"
            color="primary"
            :loading="isSaving"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import { supabase } from '../../lib/supabase';

// Types
interface NewsItem {
  id: string;
  title: string;
  content: string;
  category: string;
  priority: number;
  is_active: boolean;
  start_date: string;
  end_date?: string;
  created_at: string;
  created_by?: string;
}

interface FormData {
  title: string;
  content: string;
  category: string;
  priority: number;
  is_active: boolean;
  start_date: string;
  end_date: string;
}

// Composables
const $q = useQuasar();

// Reactive state
const newsItems = ref<NewsItem[]>([]);
const isLoading = ref(true);
const isSaving = ref(false);
const showCreateDialog = ref(false);
const editingItem = ref<NewsItem | null>(null);

// Form data
const formData = ref<FormData>({
  title: '',
  content: '',
  category: 'general',
  priority: 1,
  is_active: true,
  start_date: '',
  end_date: ''
});

// Category options
const categoryOptions = [
  'general',
  'announcement',
  'event',
  'partnership',
  'update',
  'maintenance',
  'feature',
  'promotion'
];

// Methods
const refreshNews = async () => {
  try {
    isLoading.value = true;

    const { data, error } = await supabase
      .from('news_items')
      .select('*')
      .order('priority', { ascending: false })
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    newsItems.value = data || [];
  } catch (err: any) {
    console.error('Error fetching news items:', err);
    $q.notify({
      type: 'negative',
      message: 'Failed to load news items',
      position: 'top'
    });
  } finally {
    isLoading.value = false;
  }
};

const editItem = (item: NewsItem) => {
  editingItem.value = item;
  formData.value = {
    title: item.title,
    content: item.content,
    category: item.category,
    priority: item.priority,
    is_active: item.is_active,
    start_date: formatDateForInput(item.start_date),
    end_date: item.end_date ? formatDateForInput(item.end_date) : ''
  };
  showCreateDialog.value = true;
};

const cancelEdit = () => {
  editingItem.value = null;
  resetForm();
  showCreateDialog.value = false;
};

const resetForm = () => {
  formData.value = {
    title: '',
    content: '',
    category: 'general',
    priority: 1,
    is_active: true,
    start_date: '',
    end_date: ''
  };
};

const saveItem = async () => {
  try {
    isSaving.value = true;

    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Prepare data for database
    const itemData = {
      title: formData.value.title,
      content: formData.value.content,
      category: formData.value.category,
      priority: formData.value.priority,
      is_active: formData.value.is_active,
      start_date: formData.value.start_date || new Date().toISOString(),
      end_date: formData.value.end_date || null,
      created_by: user.id
    };

    let result;
    if (editingItem.value) {
      // Update existing item
      result = await supabase
        .from('news_items')
        .update(itemData)
        .eq('id', editingItem.value.id)
        .select()
        .single();
    } else {
      // Create new item
      result = await supabase
        .from('news_items')
        .insert(itemData)
        .select()
        .single();
    }

    if (result.error) {
      throw result.error;
    }

    $q.notify({
      type: 'positive',
      message: `News item ${editingItem.value ? 'updated' : 'created'} successfully`,
      position: 'top'
    });

    cancelEdit();
    await refreshNews();
  } catch (err: any) {
    console.error('Error saving news item:', err);
    $q.notify({
      type: 'negative',
      message: `Failed to ${editingItem.value ? 'update' : 'create'} news item`,
      position: 'top'
    });
  } finally {
    isSaving.value = false;
  }
};

const toggleItemStatus = async (item: NewsItem) => {
  try {
    const { error } = await supabase
      .from('news_items')
      .update({ is_active: !item.is_active })
      .eq('id', item.id);

    if (error) {
      throw error;
    }

    $q.notify({
      type: 'positive',
      message: `News item ${!item.is_active ? 'activated' : 'deactivated'}`,
      position: 'top'
    });

    await refreshNews();
  } catch (err: any) {
    console.error('Error toggling item status:', err);
    $q.notify({
      type: 'negative',
      message: 'Failed to update item status',
      position: 'top'
    });
  }
};

const deleteItem = async (item: NewsItem) => {
  $q.dialog({
    title: 'Confirm Delete',
    message: `Are you sure you want to delete "${item.title}"? This action cannot be undone.`,
    cancel: true,
    persistent: true
  }).onOk(async () => {
    try {
      const { error } = await supabase
        .from('news_items')
        .delete()
        .eq('id', item.id);

      if (error) {
        throw error;
      }

      $q.notify({
        type: 'positive',
        message: 'News item deleted successfully',
        position: 'top'
      });

      await refreshNews();
    } catch (err: any) {
      console.error('Error deleting news item:', err);
      $q.notify({
        type: 'negative',
        message: 'Failed to delete news item',
        position: 'top'
      });
    }
  });
};

// Utility functions
const truncateContent = (content: string, maxLength: number = 100): string => {
  if (content.length <= maxLength) return content;
  return content.substring(0, maxLength) + '...';
};

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleString();
};

const formatDateForInput = (dateString: string): string => {
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  return `${year}-${month}-${day}T${hours}:${minutes}`;
};

// Lifecycle
onMounted(() => {
  refreshNews();
});
</script>

<style scoped>
.news-admin-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.admin-title {
  display: flex;
  align-items: center;
  margin: 0;
  color: #0D8A3E;
  font-size: 24px;
  font-weight: 600;
}

.title-icon {
  margin-right: 12px;
}

.news-list-card {
  margin-bottom: 20px;
}

.list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.list-header h3 {
  margin: 0;
  color: #333;
}

.news-items-section {
  min-height: 200px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #666;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #666;
  text-align: center;
}

.empty-state p {
  margin: 16px 0;
  font-size: 16px;
}

.news-items-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 16px;
}

.news-item-card {
  border: 1px solid #e0e0e0;
  transition: all 0.2s ease;
}

.news-item-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.news-item-card.inactive {
  opacity: 0.6;
  background-color: #f5f5f5;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.item-status {
  display: flex;
  gap: 8px;
}

.priority-badge {
  margin-left: 4px;
}

.item-actions {
  display: flex;
  gap: 4px;
}

.item-title {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.4;
}

.item-content {
  margin: 0 0 12px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.item-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  font-size: 12px;
  color: #888;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.create-dialog {
  width: 100%;
  max-width: 600px;
}

.dialog-header h3 {
  margin: 0;
  color: #333;
}

.dialog-content {
  padding: 20px;
}

.news-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-row {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.form-row .full-width {
  flex: 1;
}

.category-select {
  flex: 2;
}

.priority-input {
  flex: 1;
  min-width: 120px;
}

.date-input {
  flex: 1;
}

.dialog-actions {
  padding: 16px 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .news-admin-container {
    padding: 16px;
  }

  .admin-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .news-items-grid {
    grid-template-columns: 1fr;
  }

  .form-row {
    flex-direction: column;
    gap: 12px;
  }

  .create-dialog {
    margin: 16px;
    max-width: calc(100vw - 32px);
  }
}

@media (max-width: 480px) {
  .admin-title {
    font-size: 20px;
  }

  .item-header {
    flex-direction: column;
    gap: 8px;
  }

  .item-actions {
    align-self: flex-end;
  }
}
</style>