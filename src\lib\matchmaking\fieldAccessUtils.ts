/**
 * Field Access Utilities
 * 
 * This file contains utility functions for safely accessing fields in profile objects.
 * These functions handle missing or null fields, support nested field access,
 * and provide type conversion.
 */

/**
 * Safely access a field from a profile
 * @param profile The profile object
 * @param path Path to the field (can be nested using dot notation)
 * @param defaultValue Default value if field doesn't exist
 * @returns The field value or default value
 */
export function getProfileField(profile: any, path: string, defaultValue: any = null): any {
  if (!profile) return defaultValue;
  
  const parts = path.split('.');
  let current = profile;
  
  for (const part of parts) {
    if (current === null || current === undefined || typeof current !== 'object') {
      return defaultValue;
    }
    
    current = current[part];
  }
  
  return current !== undefined ? current : defaultValue;
}

/**
 * Safely access an array field from a profile
 * @param profile The profile object
 * @param path Path to the array field
 * @returns The array or an empty array
 */
export function getProfileArrayField(profile: any, path: string): any[] {
  const value = getProfileField(profile, path, []);
  
  if (Array.isArray(value)) {
    return value;
  }
  
  // Handle single string value by converting to array
  if (typeof value === 'string') {
    return [value];
  }
  
  return [];
}

/**
 * Safely access a string field from a profile
 * @param profile The profile object
 * @param path Path to the string field
 * @returns The string or an empty string
 */
export function getProfileStringField(profile: any, path: string): string {
  const value = getProfileField(profile, path, '');
  
  if (typeof value === 'string') {
    return value;
  }
  
  // Handle array by joining
  if (Array.isArray(value)) {
    return value.join(', ');
  }
  
  // Convert to string
  return String(value || '');
}

/**
 * Safely access a numeric field from a profile
 * @param profile The profile object
 * @param path Path to the numeric field
 * @returns The number or 0
 */
export function getProfileNumericField(profile: any, path: string): number {
  const value = getProfileField(profile, path, 0);
  
  if (typeof value === 'number') {
    return value;
  }
  
  // Try to parse as number
  const parsed = parseFloat(value);
  return isNaN(parsed) ? 0 : parsed;
}

/**
 * Safely access a boolean field from a profile
 * @param profile The profile object
 * @param path Path to the boolean field
 * @returns The boolean or false
 */
export function getProfileBooleanField(profile: any, path: string): boolean {
  const value = getProfileField(profile, path, false);
  
  if (typeof value === 'boolean') {
    return value;
  }
  
  // Handle string values
  if (typeof value === 'string') {
    return value.toLowerCase() === 'true' || value === '1';
  }
  
  // Handle numeric values
  if (typeof value === 'number') {
    return value !== 0;
  }
  
  return false;
}

/**
 * Safely access a date field from a profile
 * @param profile The profile object
 * @param path Path to the date field
 * @returns The date or null
 */
export function getProfileDateField(profile: any, path: string): Date | null {
  const value = getProfileField(profile, path, null);
  
  if (value instanceof Date) {
    return value;
  }
  
  if (typeof value === 'string' || typeof value === 'number') {
    const date = new Date(value);
    return isNaN(date.getTime()) ? null : date;
  }
  
  return null;
}
