# Critical Fixes Implementation Plan

## Overview

This document provides step-by-step implementation plans for the 4 most critical platform inconsistencies that need immediate attention.

## Fix #1: Profile Loading Consolidation (Priority: Critical)

### Current Problem
- `profileService.ts`: 4 different profile loading functions
- `profileStore.ts`: Duplicate profile loading with different caching
- `publicProfiles.ts`: Separate public profile loading
- Result: 3-5x redundant API calls for same profile data

### Implementation Steps

#### Step 1: Create ProfileManager Service
**File**: `src/services/ProfileManager.ts`

```typescript
import { supabase } from '../lib/supabase'
import type { BaseProfile } from './profileService'

export class ProfileManager {
  private static instance: ProfileManager
  private cache = new Map<string, { data: any; timestamp: number }>()
  private loading = new Set<string>()
  
  static getInstance(): ProfileManager {
    if (!ProfileManager.instance) {
      ProfileManager.instance = new ProfileManager()
    }
    return ProfileManager.instance
  }
  
  async getProfile(userId: string, options: {
    context?: 'public' | 'private'
    forceRefresh?: boolean
    includeSpecialized?: boolean
  } = {}): Promise<BaseProfile | null> {
    const cacheKey = `${userId}-${options.context || 'private'}`
    
    // Check cache first (5 minute TTL)
    if (!options.forceRefresh && this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey)!
      if (Date.now() - cached.timestamp < 300000) {
        return cached.data
      }
    }
    
    // Prevent duplicate requests
    if (this.loading.has(cacheKey)) {
      while (this.loading.has(cacheKey)) {
        await new Promise(resolve => setTimeout(resolve, 50))
      }
      return this.cache.get(cacheKey)?.data || null
    }
    
    this.loading.add(cacheKey)
    try {
      const profile = await this.loadFromDatabase(userId, options)
      if (profile) {
        this.cache.set(cacheKey, { data: profile, timestamp: Date.now() })
      }
      return profile
    } finally {
      this.loading.delete(cacheKey)
    }
  }
  
  private async loadFromDatabase(userId: string, options: any): Promise<BaseProfile | null> {
    const { data, error } = await supabase
      .from('personal_details')
      .select('*')
      .eq('user_id', userId)
      .single()
    
    if (error || !data) return null
    
    // Convert to BaseProfile format
    return {
      user_id: data.user_id,
      email: data.email,
      profile_name: data.profile_name,
      profile_type: data.profile_type,
      profile_state: data.profile_state,
      profile_completion: data.profile_completion,
      // ... other fields
    } as BaseProfile
  }
  
  invalidateCache(userId?: string): void {
    if (userId) {
      for (const key of this.cache.keys()) {
        if (key.startsWith(userId)) {
          this.cache.delete(key)
        }
      }
    } else {
      this.cache.clear()
    }
  }
  
  getCacheStats() {
    return {
      size: this.cache.size,
      loading: this.loading.size
    }
  }
}

// Export singleton instance
export const profileManager = ProfileManager.getInstance()
```

#### Step 2: Update ProfileStore to Use ProfileManager
**File**: `src/stores/profileStore.ts`

```typescript
// Replace existing loadUserProfiles function
async function loadUserProfiles(specificUserId?: string): Promise<void> {
  const userId = specificUserId || authStore.currentUser?.id
  if (!userId) return
  
  loading.value = true
  try {
    // Use ProfileManager instead of direct service calls
    const profile = await profileManager.getProfile(userId, { 
      context: 'private',
      includeSpecialized: true 
    })
    
    if (profile) {
      if (!specificUserId) {
        currentProfile.value = profile
      }
      
      // Update userProfiles array
      const existingIndex = userProfiles.value.findIndex(p => p.user_id === userId)
      if (existingIndex >= 0) {
        userProfiles.value[existingIndex] = profile
      } else {
        userProfiles.value.push(profile)
      }
    }
  } catch (err) {
    console.error('Error loading user profiles:', err)
    error.value = err instanceof Error ? err.message : 'Failed to load profiles'
  } finally {
    loading.value = false
  }
}
```

#### Step 3: Migration Plan
1. **Week 1**: Create ProfileManager, test in isolation
2. **Week 1**: Update profileStore to use ProfileManager
3. **Week 2**: Update all components to use unified API
4. **Week 2**: Deprecate old profile loading functions
5. **Week 3**: Remove deprecated functions after verification

### Expected Impact
- **Performance**: 60-70% reduction in profile-related API calls
- **Consistency**: Single source of truth for profile data
- **Maintenance**: Easier to debug and modify profile logic

## Fix #2: Route Guard Consolidation (Priority: Critical)

### Current Problem
- Two route guard files: `guards.ts` and `enhancedGuards.ts`
- Different authentication logic causing inconsistent behavior
- No caching of authentication checks

### Implementation Steps

#### Step 1: Remove Duplicate Guards
**Action**: Delete `src/router/guards.ts`

#### Step 2: Enhance Existing Guards
**File**: `src/router/enhancedGuards.ts`

```typescript
import { Router } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { useUserState } from '../services/userStateService'

// Cache for authentication checks
const authCache = new Map<string, { result: boolean; timestamp: number }>()
const AUTH_CACHE_TTL = 60000 // 1 minute

export function setupEnhancedRouteGuards(router: Router) {
  router.beforeEach(async (to, from) => {
    const authStore = useAuthStore()
    const { checkUserState } = useUserState()
    
    // Skip auth for public routes
    if (to.path === '/auth/callback' || to.path === '/auth/verify') {
      return true
    }
    
    // Ensure auth is initialized
    if (!authStore.isInitialized) {
      await authStore.checkSession()
    }
    
    // Check authentication for protected routes
    if (to.matched.some(record => record.meta.requiresAuth)) {
      if (!authStore.isAuthenticated) {
        return '/sign-in'
      }
      
      // Cached user state check (5 minutes)
      const cacheKey = `userState_${authStore.currentUser?.id}`
      const cached = authCache.get(cacheKey)
      const now = Date.now()
      
      if (!cached || (now - cached.timestamp) > 300000) {
        try {
          await checkUserState()
          authCache.set(cacheKey, { result: true, timestamp: now })
        } catch (error) {
          console.error('Error checking user state:', error)
        }
      }
    }
    
    // Redirect authenticated users from guest-only pages
    if (to.matched.some(record => record.meta.guestOnly)) {
      if (authStore.isAuthenticated) {
        return '/dashboard'
      }
    }
    
    return true
  })
}
```

#### Step 3: Update Router Configuration
**File**: `src/router/index.ts`

```typescript
// Remove old guards import
// import { setupRouteGuards } from './guards'

// Use only enhanced guards
import { setupEnhancedRouteGuards } from './enhancedGuards'

// In router setup
setupEnhancedRouteGuards(router)
```

### Expected Impact
- **Consistency**: Single authentication flow
- **Performance**: Cached authentication checks
- **Maintenance**: Single file to maintain

## Fix #3: Service Initialization Coordination (Priority: Critical)

### Current Problem
- `DashboardLayout.vue` and `Dashboard.vue` both initialize same services
- Race conditions in service startup
- 2x slower dashboard loading

### Implementation Steps

#### Step 1: Create ServiceCoordinator
**File**: `src/services/ServiceCoordinator.ts`

```typescript
export class ServiceCoordinator {
  private static instance: ServiceCoordinator
  private initialized = new Set<string>()
  private initializing = new Set<string>()
  private initPromises = new Map<string, Promise<void>>()
  
  static getInstance(): ServiceCoordinator {
    if (!ServiceCoordinator.instance) {
      ServiceCoordinator.instance = new ServiceCoordinator()
    }
    return ServiceCoordinator.instance
  }
  
  async initializeService(serviceName: string, initializer: () => Promise<void>): Promise<void> {
    // Return immediately if already initialized
    if (this.initialized.has(serviceName)) {
      return
    }
    
    // Return existing promise if currently initializing
    if (this.initPromises.has(serviceName)) {
      return this.initPromises.get(serviceName)!
    }
    
    // Start initialization
    const promise = this.doInitialize(serviceName, initializer)
    this.initPromises.set(serviceName, promise)
    
    return promise
  }
  
  private async doInitialize(serviceName: string, initializer: () => Promise<void>): Promise<void> {
    this.initializing.add(serviceName)
    
    try {
      await initializer()
      this.initialized.add(serviceName)
      console.log(`✅ Service initialized: ${serviceName}`)
    } catch (error) {
      console.error(`❌ Service initialization failed: ${serviceName}`, error)
      throw error
    } finally {
      this.initializing.delete(serviceName)
      this.initPromises.delete(serviceName)
    }
  }
  
  isInitialized(serviceName: string): boolean {
    return this.initialized.has(serviceName)
  }
  
  reset(): void {
    this.initialized.clear()
    this.initializing.clear()
    this.initPromises.clear()
  }
  
  getStatus() {
    return {
      initialized: Array.from(this.initialized),
      initializing: Array.from(this.initializing)
    }
  }
}

export const serviceCoordinator = ServiceCoordinator.getInstance()
```

#### Step 2: Update DashboardLayout
**File**: `src/layouts/DashboardLayout.vue`

```typescript
import { serviceCoordinator } from '../services/ServiceCoordinator'

// In onMounted
onMounted(async () => {
  try {
    // Coordinate service initialization
    await Promise.all([
      serviceCoordinator.initializeService('messaging', async () => {
        await messagingStore.initializeMessaging()
      }),
      serviceCoordinator.initializeService('activityNotifications', async () => {
        await activityNotificationsStore.initialize()
      }),
      serviceCoordinator.initializeService('userNotifications', async () => {
        await userNotificationsStore.initialize()
      })
    ])
    
    console.log('✅ All dashboard services initialized')
  } catch (error) {
    console.error('❌ Service initialization error:', error)
  }
})
```

#### Step 3: Update Dashboard Component
**File**: `src/views/dashboard/Dashboard.vue`

```typescript
// Remove duplicate initialization, rely on ServiceCoordinator
onMounted(async () => {
  isLoading.value = true
  try {
    await checkUserState()
    
    if (hasAnyProfileData.value && !profileStore.currentProfile) {
      await profileStore.loadUserProfiles()
    }
    
    // Services already initialized by DashboardLayout via ServiceCoordinator
    console.log('Dashboard: Using pre-initialized services')
    console.log('Service status:', serviceCoordinator.getStatus())
    
  } catch (error) {
    console.error('Dashboard initialization error:', error)
  } finally {
    isLoading.value = false
  }
})
```

### Expected Impact
- **Performance**: 50% faster dashboard loading
- **Reliability**: No more duplicate service initialization
- **Debugging**: Clear service initialization tracking

## Fix #4: Eliminate Duplicate Components (Priority: Critical)

### Current Problem
- Two identical `SignIn.vue` components in different locations
- Routing confusion and maintenance burden

### Implementation Steps

#### Step 1: Remove Duplicate Component
**Action**: Delete `src/views/public/auth/SignIn.vue`

#### Step 2: Update Route References
**File**: `src/router/index.ts`

```typescript
// Ensure all sign-in routes point to the main component
{
  path: '/sign-in',
  name: 'sign-in',
  component: () => import('../views/SignIn.vue'),
  meta: { guestOnly: true }
}
```

#### Step 3: Update Navigation References
Search and update any components that reference the deleted path.

### Expected Impact
- **Maintenance**: 50% reduction in auth component maintenance
- **Consistency**: Single sign-in experience
- **Clarity**: Clear component organization

## Implementation Timeline

### Week 1
- [ ] Create ProfileManager service
- [ ] Remove duplicate route guards
- [ ] Create ServiceCoordinator
- [ ] Remove duplicate SignIn component

### Week 2  
- [ ] Update profileStore to use ProfileManager
- [ ] Update DashboardLayout and Dashboard components
- [ ] Test service coordination
- [ ] Update route references

### Week 3
- [ ] Migrate all components to use ProfileManager
- [ ] Remove deprecated profile functions
- [ ] Performance testing and optimization
- [ ] Documentation updates

## Success Metrics
- Dashboard load time: Target 50% improvement
- API call reduction: Target 60% for profile operations
- Code duplication: Target 70% reduction in affected areas
- Bug reports: Target 80% reduction in state-related issues
