<template>
  <div class="connections-list">
    <div v-if="title" class="text-h6 q-mb-md">{{ title }}</div>

    <div v-if="loading" class="text-center q-pa-md">
      <q-spinner color="primary" size="2em" />
      <p>Loading connections...</p>
    </div>

    <div v-else-if="connections.length === 0" class="text-center q-pa-md text-grey">
      <q-icon name="info" size="2em" />
      <p>No connections to display</p>
    </div>

    <div v-else>
      <div class="row q-col-gutter-md">
        <div
          v-for="connection in connections"
          :key="connection.id"
          class="col-12 col-md-6"
        >
          <q-card class="connection-card">
            <q-item>
              <q-item-section avatar>
                <q-avatar>
                  <img
                    v-if="connection.connected_user?.avatar_url"
                    :src="connection.connected_user.avatar_url"
                  />
                  <div v-else class="bg-primary text-white flex flex-center full-height">
                    {{ getInitials(connection.connected_user) }}
                  </div>
                </q-avatar>
              </q-item-section>

              <q-item-section>
                <q-item-label>
                  {{ getUserName(connection.connected_user) }}
                </q-item-label>
                <q-item-label caption>
                  {{ connection.connection_type }}
                </q-item-label>
                <q-item-label caption>
                  Connected {{ formatDate(connection.created_at) }}
                </q-item-label>
              </q-item-section>

              <q-item-section side>
                <q-btn
                  flat
                  round
                  color="primary"
                  icon="person"
                  :to="{ name: 'user-profile', params: { id: connection.connected_user_id } }"
                >
                  <q-tooltip>View Profile</q-tooltip>
                </q-btn>

                <q-btn
                  flat
                  round
                  color="primary"
                  icon="message"
                  @click="handleMessage(connection.connected_user_id)"
                >
                  <q-tooltip>Message</q-tooltip>
                </q-btn>
              </q-item-section>
            </q-item>
          </q-card>
        </div>
      </div>

      <div v-if="hasMore" class="text-center q-mt-md">
        <q-btn
          outline
          color="primary"
          label="Load More"
          :loading="loadingMore"
          @click="loadMore"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { date } from 'quasar';
import { useConnectionService } from '../../services/connectionService';
import { getUniversalUsername } from '../../utils/userUtils';

const props = defineProps({
  userId: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: 'Connections'
  },
  limit: {
    type: Number,
    default: 10
  }
});

const emit = defineEmits(['message']);

const connectionService = useConnectionService();
const connections = ref([]);
const loading = ref(true);
const loadingMore = ref(false);
const currentPage = ref(1);
const hasMore = ref(false);

onMounted(async () => {
  await loadConnections();
});

async function loadConnections() {
  try {
    loading.value = true;
    const result = await connectionService.getUserConnections(
      props.userId,
      'accepted',
      props.limit,
      currentPage.value
    );

    connections.value = result;
    hasMore.value = result.length === props.limit;
  } catch (error) {
    console.error('Error loading connections:', error);
  } finally {
    loading.value = false;
  }
}

async function loadMore() {
  try {
    loadingMore.value = true;
    currentPage.value++;

    const result = await connectionService.getUserConnections(
      props.userId,
      'accepted',
      props.limit,
      currentPage.value
    );

    connections.value = [...connections.value, ...result];
    hasMore.value = result.length === props.limit;
  } catch (error) {
    console.error('Error loading more connections:', error);
    currentPage.value = Math.max(1, currentPage.value - 1);
  } finally {
    loadingMore.value = false;
  }
}

function getInitials(user) {
  if (!user) return '?';

  if (user.first_name && user.last_name) {
    return `${user.first_name[0]}${user.last_name[0]}`.toUpperCase();
  }

  if (user.email) {
    return user.email[0].toUpperCase();
  }

  return '?';
}

function getUserName(user) {
  return getUniversalUsername(user);
}

function formatDate(dateString) {
  return date.formatDate(dateString, 'MMM D, YYYY');
}

function handleMessage(userId) {
  emit('message', userId);
}

// Expose the loadConnections function and connection count to parent components
defineExpose({
  loadConnections,
  connectionsCount: computed(() => connections.value.length)
});
</script>

<style scoped>
.connections-list {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
}

.connection-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.connection-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

@media (max-width: 599px) {
  .connections-list {
    padding: 12px;
  }
}
</style>
