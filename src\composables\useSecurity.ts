/**
 * Composable for security-related functionality
 */

import { ref, computed } from 'vue'
import { 
  sanitizeHtml, 
  sanitizeText, 
  sanitizeUrl, 
  validateEmail, 
  sanitizeFormInput,
  validatePassword,
  generateCSRFToken,
  storeCSRFToken,
  getCSRFToken,
  validate<PERSON><PERSON><PERSON>oken,
  defaultRateLimiter
} from '@/lib/security'

export function useSecurity() {
  const csrfToken = ref<string | null>(null)

  /**
   * Initialize CSRF protection
   */
  const initCSRF = () => {
    let token = getCSRFToken()
    if (!token) {
      token = generateCSRFToken()
      storeCSRFToken(token)
    }
    csrfToken.value = token
    return token
  }

  /**
   * Sanitize HTML content for safe display
   */
  const sanitizeContent = (content: string) => {
    return sanitizeHtml(content)
  }

  /**
   * Sanitize plain text content
   */
  const sanitizeTextContent = (content: string) => {
    return sanitizeText(content)
  }

  /**
   * Sanitize and validate URL
   */
  const sanitizeLink = (url: string) => {
    return sanitizeUrl(url)
  }

  /**
   * Validate email format
   */
  const isValidEmail = (email: string) => {
    return validateEmail(email)
  }

  /**
   * Sanitize form input with length limits
   */
  const sanitizeInput = (input: string, maxLength?: number) => {
    return sanitizeFormInput(input, maxLength)
  }

  /**
   * Validate password strength
   */
  const checkPasswordStrength = (password: string) => {
    return validatePassword(password)
  }

  /**
   * Check if action is rate limited
   */
  const isRateLimited = (key: string) => {
    return !defaultRateLimiter.isAllowed(key)
  }

  /**
   * Reset rate limit for a key
   */
  const resetRateLimit = (key: string) => {
    defaultRateLimiter.reset(key)
  }

  /**
   * Validate CSRF token for forms
   */
  const validateCSRF = (token: string) => {
    return validateCSRFToken(token)
  }

  /**
   * Get current CSRF token
   */
  const getCurrentCSRFToken = () => {
    return csrfToken.value || getCSRFToken()
  }

  /**
   * Computed property for CSRF token validity
   */
  const isCSRFValid = computed(() => {
    const token = getCurrentCSRFToken()
    return token !== null && token.length > 0
  })

  return {
    // CSRF protection
    csrfToken,
    initCSRF,
    validateCSRF,
    getCurrentCSRFToken,
    isCSRFValid,
    
    // Content sanitization
    sanitizeContent,
    sanitizeTextContent,
    sanitizeLink,
    sanitizeInput,
    
    // Validation
    isValidEmail,
    checkPasswordStrength,
    
    // Rate limiting
    isRateLimited,
    resetRateLimit
  }
}

/**
 * Directive for automatic HTML sanitization
 */
export const vSanitize = {
  mounted(el: HTMLElement, binding: any) {
    if (binding.value && typeof binding.value === 'string') {
      el.innerHTML = sanitizeHtml(binding.value)
    }
  },
  updated(el: HTMLElement, binding: any) {
    if (binding.value && typeof binding.value === 'string') {
      el.innerHTML = sanitizeHtml(binding.value)
    }
  }
}

/**
 * Directive for automatic text sanitization
 */
export const vSanitizeText = {
  mounted(el: HTMLElement, binding: any) {
    if (binding.value && typeof binding.value === 'string') {
      el.textContent = sanitizeText(binding.value)
    }
  },
  updated(el: HTMLElement, binding: any) {
    if (binding.value && typeof binding.value === 'string') {
      el.textContent = sanitizeText(binding.value)
    }
  }
}
