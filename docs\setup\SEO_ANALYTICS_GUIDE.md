# SEO and Google Analytics Guide

This guide explains how to configure and use the SEO and Google Analytics features in the ZB Innovation Hub platform.

## Google Analytics Setup

### 1. Create a Google Analytics 4 Property

1. Go to [Google Analytics](https://analytics.google.com/) and sign in with your Google account
2. Create a new property for the ZB Innovation Hub website
3. Set up a Google Analytics 4 property
4. Get your Measurement ID (it starts with "G-")

### 2. Configure the Environment Variables

Add your Google Analytics Measurement ID to the `.env` file:

```
VITE_GA_MEASUREMENT_ID=G-XXXXXXXXXX
```

Replace `G-XXXXXXXXXX` with your actual Measurement ID.

### 3. Verify Installation

1. Start the application
2. Navigate to `/analytics-test` to see the Analytics Example page
3. Click the buttons to generate test events
4. Check your Google Analytics dashboard to verify events are being tracked

## Using Analytics in Components

You can track events from any component using the global `$analytics` object:

```javascript
// In your Vue component:
const { $analytics } = getCurrentInstance()?.appContext.config.globalProperties || {}

// Track a simple event
$analytics?.trackEvent({
  action: 'button_click',
  category: 'engagement',
  label: 'homepage_cta',
  value: 1
})

// Track a more complex event with custom parameters
$analytics?.trackEvent({
  action: 'form_submit',
  category: 'conversion',
  label: 'contact_form',
  form_id: 'contact_us',
  user_type: 'visitor'
})
```

## SEO Features

The platform includes several SEO features:

1. **Meta Tags**: All pages have proper meta tags for title, description, keywords, etc.
2. **Open Graph Tags**: For better social media sharing
3. **Twitter Card Tags**: For Twitter sharing
4. **Canonical URLs**: To prevent duplicate content issues
5. **Structured Data**: JSON-LD for better search engine understanding
6. **Robots.txt**: Configured to allow search engines to index public pages while keeping private areas protected
7. **Sitemap**: XML sitemap for search engines

### Protected Routes

The following routes are protected from search engine indexing:

- `/dashboard/*` - All dashboard pages (contains sensitive user information)
- `/admin/*` - Admin pages
- `/login/*` - Authentication pages
- `/private/*` - Private content
- `/auth/*` - Authentication flow pages
- `/reset-password/*` - Password reset pages
- `/verify-email/*` - Email verification pages
- `/onboarding/*` - User onboarding pages

These routes are excluded from the sitemap and blocked in robots.txt to ensure user privacy and security.

### Updating SEO Meta Tags for a Page

In your Vue component, use the `useSEO` hook:

```javascript
import { useSEO } from '@/utils/seo'

// In your setup function
const { updateMeta } = useSEO()

// Update meta tags for this page
updateMeta({
  title: 'Page Title | ZB Innovation Hub',
  description: 'Page description goes here (150-160 characters recommended)',
  keywords: ['keyword1', 'keyword2', 'keyword3'],
  ogTitle: 'Page Title for Social Media',
  ogDescription: 'Description for social media sharing',
  ogImage: 'https://example.com/image.jpg'
})
```

## Best Practices

### SEO Best Practices

1. **Page Titles**: Keep titles under 60 characters
2. **Meta Descriptions**: Keep descriptions between 150-160 characters
3. **Keywords**: Use relevant keywords naturally in content
4. **Images**: Always include alt text for images
5. **URLs**: Use descriptive, SEO-friendly URLs
6. **Content**: Create high-quality, original content
7. **Mobile Friendly**: Ensure the site is responsive

### Analytics Best Practices

1. **Event Naming**: Use consistent naming conventions for events
2. **Event Categories**: Organize events into logical categories
3. **Custom Dimensions**: Use custom dimensions for additional context
4. **User Privacy**: Respect user privacy and comply with regulations
5. **Regular Analysis**: Regularly review analytics data to improve the platform

## Troubleshooting

### Google Analytics Not Working

1. Check that your Measurement ID is correctly set in the `.env` file
2. Verify that the analytics plugin is properly initialized in `main.ts`
3. Check for any JavaScript errors in the browser console
4. Ensure that ad blockers are not blocking the Google Analytics scripts

### SEO Issues

1. Use tools like [Google Search Console](https://search.google.com/search-console) to identify SEO issues
2. Check that all pages have proper meta tags
3. Verify that the sitemap.xml file is accessible
4. Ensure that robots.txt is not blocking important pages
