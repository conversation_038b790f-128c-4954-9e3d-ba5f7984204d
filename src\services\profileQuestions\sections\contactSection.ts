// Common Contact Information Section
// This section can be included in all profile types

import { ProfileSection } from '../types';

export const contactSection: ProfileSection = {
  title: 'Contact Information',
  icon: 'contact_mail',
  description: 'Share your contact information',
  questions: [
    {
      id: 'contact_email',
      name: 'contact_email',
      label: 'Contact Email',
      type: 'text',
      required: true,
      hint: 'Your preferred email for contacts (if different from account email)'
    },
    {
      id: 'contact_phone',
      name: 'contact_phone',
      label: 'Contact Phone',
      type: 'text',
      hint: 'Your phone number with country code'
    },
    {
      id: 'whatsapp',
      name: 'whatsapp',
      label: 'WhatsApp',
      type: 'text',
      hint: 'Your WhatsApp number (if different from phone)'
    },
    {
      id: 'telegram',
      name: 'telegram',
      label: 'Telegram',
      type: 'text',
      hint: 'Your Telegram username'
    },
    {
      id: 'skype',
      name: 'skype',
      label: 'Skype',
      type: 'text',
      hint: 'Your Skype ID'
    },
    {
      id: 'preferred_contact_method',
      name: 'preferred_contact_method',
      label: 'Preferred Contact Method',
      type: 'select',
      options: 'contactMethodOptions',
      hint: 'How would you prefer to be contacted?'
    },
    {
      id: 'availability_for_contact',
      name: 'availability_for_contact',
      label: 'Availability for Contact',
      type: 'text',
      hint: 'When are you typically available for contact? (e.g., weekdays 9-5 CAT)'
    }
  ]
};
