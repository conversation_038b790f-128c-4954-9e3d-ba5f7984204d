// Profile Questions Service
// This service provides a single source of truth for all profile questions

import { ref, computed } from 'vue'
import type { Ref } from 'vue'
import { ProfileType, ProfileSection, ProfileQuestion } from './types'
import { commonOptions } from './common'

// Import profile types
import { innovatorProfile } from './profiles/innovator'
import { investorProfile } from './profiles/investor'
import { mentorProfile } from './profiles/mentor'
import { professionalProfile } from './profiles/professional'
import { industryExpertProfile } from './profiles/industry_expert'
import { academicStudentProfile } from './profiles/academic_student'
import { academicInstitutionProfile } from './profiles/academic_institution'
import { organisationProfile } from './profiles/organisation'

// Map of all profile types
export const profileQuestionsMap: Record<string, ProfileType> = {
  'innovator': innovatorProfile,
  'investor': investorProfile,
  'mentor': mentorProfile,
  'professional': professionalProfile,
  'industry_expert': industryExpertProfile,
  'academic_student': academicStudentProfile,
  'academic_institution': academicInstitutionProfile,
  'organisation': organisationProfile
}

// Helper function to get profile questions by type
export function getProfileQuestions(profileType: string): ProfileType | null {
  return profileQuestionsMap[profileType] || null
}

// Helper function to get profile sections by type
export function getProfileSections(profileType: string): ProfileSection[] {
  const profileQuestions = getProfileQuestions(profileType)
  return profileQuestions ? profileQuestions.sections : []
}

// Helper function to get profile options by type
export function getProfileOptions(profileType: string): Record<string, string[]> {
  const profileQuestions = getProfileQuestions(profileType)
  return profileQuestions ? profileQuestions.options : {}
}

// Helper function to get options for a specific field
export function getOptionsForField(profileType: string, fieldName: string): string[] {
  const options = getProfileOptions(profileType)

  // Check if the options exist in the profile type
  if (options[fieldName]) {
    return options[fieldName]
  }

  // Check if the options exist in common options
  if (commonOptions[fieldName as keyof typeof commonOptions]) {
    return commonOptions[fieldName as keyof typeof commonOptions]
  }

  return []
}

// Composable function to use profile questions
export function useProfileQuestions() {
  const currentProfileType: Ref<string | null> = ref(null)

  // Get questions for the current profile type
  const currentQuestions = computed(() => {
    if (!currentProfileType.value) {
      return null
    }
    return getProfileQuestions(currentProfileType.value)
  })

  // Get sections for the current profile type
  const currentSections = computed(() => {
    if (!currentQuestions.value) {
      return []
    }
    return currentQuestions.value.sections
  })

  // Get options for the current profile type
  const currentOptions = computed(() => {
    if (!currentQuestions.value) {
      return {}
    }
    return currentQuestions.value.options
  })

  // Set the current profile type
  function setProfileType(profileType: string) {
    currentProfileType.value = profileType
  }

  // Get options for a specific field
  function getFieldOptions(fieldName: string): string[] {
    if (!currentProfileType.value) {
      return []
    }
    return getOptionsForField(currentProfileType.value, fieldName)
  }

  // Convert database field names to display names
  function getFieldDisplayName(fieldName: string): string {
    if (!currentProfileType.value || !currentQuestions.value) {
      return fieldName
    }

    // Search through all sections and questions to find the matching field
    for (const section of currentQuestions.value.sections) {
      for (const question of section.questions) {
        if (question.name === fieldName) {
          return question.label
        }
      }
    }

    return fieldName
  }

  // Get all fields for the current profile type
  function getAllFields(): ProfileQuestion[] {
    if (!currentQuestions.value) {
      return []
    }

    const fields: ProfileQuestion[] = []
    for (const section of currentQuestions.value.sections) {
      fields.push(...section.questions)
    }

    return fields
  }

  // Get questions directly for a specific profile type
  function getQuestionsForProfileType(profileType: string): ProfileType | null {
    return getProfileQuestions(profileType)
  }

  return {
    currentProfileType,
    currentQuestions,
    currentSections,
    currentOptions,
    setProfileType,
    getFieldOptions,
    getFieldDisplayName,
    getAllFields,
    profileQuestionsMap,
    getQuestionsForProfileType
  }
}

// Re-export types and common options
export * from './types'
export * from './common'

// Export profile types for direct access
export { innovatorProfile, investorProfile, mentorProfile, professionalProfile,
  industryExpertProfile, academicStudentProfile, academicInstitutionProfile, organisationProfile }

// For backward compatibility with unifiedProfileQuestions
export const getUnifiedProfileQuestions = getProfileQuestions
export const getUnifiedProfileSections = getProfileSections
export const getUnifiedProfileOptions = getProfileOptions
export const useUnifiedProfileQuestions = useProfileQuestions
