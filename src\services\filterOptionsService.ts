export const filterOptions = {
  // Post type options for the feed tab
  postTypeOptions: [
    { label: 'General Posts', value: 'general', icon: 'post_add' },
    { label: 'Opportunities', value: 'opportunity', icon: 'emoji_objects' },
    { label: 'Success Stories', value: 'success_story', icon: 'emoji_events' },
    { label: 'Questions & Help', value: 'question_help', icon: 'help' },
    { label: 'Resources', value: 'resource', icon: 'description' },
    { label: 'Job Listings', value: 'job_talent', icon: 'work' },
    { label: 'Innovation Challenges', value: 'innovation_challenge', icon: 'extension' },
    { label: 'Announcements', value: 'admin_announcement', icon: 'campaign' },
    { label: 'Events', value: 'event', icon: 'event' },
    { label: 'Funding Opportunities', value: 'funding_opportunity', icon: 'attach_money' },
    { label: 'Collaboration Requests', value: 'collaboration_opportunity', icon: 'handshake' },
    { label: 'Mentorship Offers', value: 'mentorship_opportunity', icon: 'school' }
  ],

  // Category options for the feed tab
  categoryOptions: [
    { label: 'General', value: 'general', icon: 'feed' },
    { label: 'Funding', value: 'funding', icon: 'attach_money' },
    { label: 'Collaboration', value: 'collaboration', icon: 'handshake' },
    { label: 'Mentorship', value: 'mentorship', icon: 'school' },
    { label: 'Innovation', value: 'innovation', icon: 'lightbulb' },
    { label: 'Research', value: 'research', icon: 'science' },
    { label: 'Training', value: 'training', icon: 'menu_book' },
    { label: 'Technology', value: 'technology', icon: 'devices' },
    { label: 'Healthcare', value: 'healthcare', icon: 'health_and_safety' },
    { label: 'Education', value: 'education', icon: 'school' },
    { label: 'Agriculture', value: 'agriculture', icon: 'grass' },
    { label: 'Energy', value: 'energy', icon: 'bolt' },
    { label: 'Finance', value: 'finance', icon: 'payments' },
    { label: 'Social Impact', value: 'social_impact', icon: 'public' }
  ],

  // Opportunity type options
  opportunityTypeOptions: [
    { label: 'Funding', value: 'funding_opportunity', icon: 'attach_money' },
    { label: 'Collaboration', value: 'collaboration_opportunity', icon: 'handshake' },
    { label: 'Mentorship', value: 'mentorship_opportunity', icon: 'school' },
    { label: 'Research Partnership', value: 'research_opportunity', icon: 'science' },
    { label: 'Incubation', value: 'incubation_opportunity', icon: 'egg' },
    { label: 'Acceleration', value: 'acceleration_opportunity', icon: 'speed' }
  ],

  // Date range options (common across tabs)
  dateRangeOptions: [
    { label: 'All Time', value: 'all', icon: 'all_inclusive' },
    { label: 'Today Only', value: 'today', icon: 'today' },
    { label: 'This Week', value: 'week', icon: 'date_range' },
    { label: 'This Month', value: 'month', icon: 'calendar_month' },
    { label: 'Last 3 Months', value: 'quarter', icon: 'calendar_view_month' },
    { label: 'Last Year', value: 'year', icon: 'calendar_today' }
  ],

  // Profile type options for the profiles tab - using actual platform profile types
  profileTypeOptions: [
    { label: 'Innovator', value: 'innovator', icon: 'lightbulb' },
    { label: 'Business Investor', value: 'investor', icon: 'attach_money' },
    { label: 'Mentor', value: 'mentor', icon: 'school' },
    { label: 'Professional', value: 'professional', icon: 'work' },
    { label: 'Industry Expert', value: 'industry_expert', icon: 'engineering' },
    { label: 'Academic Student', value: 'academic_student', icon: 'menu_book' },
    { label: 'Academic Institution', value: 'academic_institution', icon: 'account_balance' },
    { label: 'Organisation', value: 'organisation', icon: 'business' }
  ],

  // Expertise area options for the profiles tab
  expertiseAreaOptions: [
    { label: 'Software Development', value: 'software_development', icon: 'code' },
    { label: 'Artificial Intelligence', value: 'ai_ml', icon: 'psychology' },
    { label: 'Fintech', value: 'fintech', icon: 'payments' },
    { label: 'Healthcare & Biotech', value: 'healthcare', icon: 'health_and_safety' },
    { label: 'Education Technology', value: 'edtech', icon: 'school' },
    { label: 'Sustainable Agriculture', value: 'agritech', icon: 'grass' },
    { label: 'Clean Energy', value: 'cleantech', icon: 'bolt' },
    { label: 'E-commerce', value: 'ecommerce', icon: 'shopping_cart' },
    { label: 'Manufacturing', value: 'manufacturing', icon: 'precision_manufacturing' },
    { label: 'Blockchain', value: 'blockchain', icon: 'link' },
    { label: 'IoT & Hardware', value: 'iot_hardware', icon: 'devices' },
    { label: 'Social Impact', value: 'social_impact', icon: 'public' }
  ],

  // Blog category options for the blog tab
  blogCategoryOptions: [
    { label: 'Innovation Insights', value: 'innovation', icon: 'lightbulb' },
    { label: 'Entrepreneurship', value: 'entrepreneurship', icon: 'rocket_launch' },
    { label: 'Technology Trends', value: 'technology', icon: 'trending_up' },
    { label: 'Funding & Investment', value: 'funding', icon: 'attach_money' },
    { label: 'Success Stories', value: 'success_stories', icon: 'emoji_events' },
    { label: 'Research & Development', value: 'research', icon: 'science' },
    { label: 'Industry News', value: 'industry_news', icon: 'feed' },
    { label: 'Interviews', value: 'interviews', icon: 'record_voice_over' },
    { label: 'How-to Guides', value: 'guides', icon: 'menu_book' },
    { label: 'Events & Conferences', value: 'events', icon: 'event' }
  ],

  // Read time options for the blog tab
  readTimeOptions: [
    { label: 'Any Length', value: 'any' },
    { label: 'Under 5 minutes', value: 'under_5' },
    { label: '5-10 minutes', value: '5_10' },
    { label: 'Over 10 minutes', value: 'over_10' }
  ],

  // Event type options for the events tab
  eventTypeOptions: [
    { label: 'Workshop', value: 'workshop', icon: 'build' },
    { label: 'Conference', value: 'conference', icon: 'groups' },
    { label: 'Networking', value: 'networking', icon: 'handshake' },
    { label: 'Hackathon', value: 'hackathon', icon: 'code' },
    { label: 'Pitch Competition', value: 'pitch_competition', icon: 'campaign' },
    { label: 'Training Session', value: 'training', icon: 'school' },
    { label: 'Webinar', value: 'webinar', icon: 'video_camera_front' },
    { label: 'Meetup', value: 'meetup', icon: 'people' },
    { label: 'Exhibition', value: 'exhibition', icon: 'view_quilt' },
    { label: 'Accelerator Program', value: 'accelerator', icon: 'speed' },
    { label: 'Incubator Program', value: 'incubator', icon: 'egg' },
    { label: 'Funding Round', value: 'funding_round', icon: 'attach_money' }
  ],

  // Event format options for the events tab
  eventFormatOptions: [
    { label: 'In-Person', value: 'physical', icon: 'location_on' },
    { label: 'Virtual', value: 'virtual', icon: 'computer' },
    { label: 'Hybrid', value: 'hybrid', icon: 'sync_alt' },
    { label: 'On-Demand', value: 'on_demand', icon: 'schedule' }
  ],

  // Group category options for the groups tab
  groupCategoryOptions: [
    { label: 'Tech Startups', value: 'tech_startups', icon: 'rocket_launch' },
    { label: 'Business Development', value: 'business_development', icon: 'trending_up' },
    { label: 'Innovation Hub', value: 'innovation_hub', icon: 'lightbulb' },
    { label: 'Research Collaboration', value: 'research', icon: 'science' },
    { label: 'Networking', value: 'networking', icon: 'people' },
    { label: 'Mentorship', value: 'mentorship', icon: 'school' },
    { label: 'Funding & Investment', value: 'funding', icon: 'attach_money' },
    { label: 'Industry-Specific', value: 'industry', icon: 'category' },
    { label: 'Social Impact', value: 'social_impact', icon: 'public' },
    { label: 'Women in Tech', value: 'women_in_tech', icon: 'diversity_3' },
    { label: 'Youth Innovation', value: 'youth_innovation', icon: 'child_care' },
    { label: 'Regional Focus', value: 'regional', icon: 'location_on' }
  ],

  // Listing type options for the marketplace tab
  listingTypeOptions: [
    { label: 'Product', value: 'product', icon: 'inventory_2' },
    { label: 'Service', value: 'service', icon: 'miscellaneous_services' },
    { label: 'Equipment', value: 'equipment', icon: 'construction' },
    { label: 'Space', value: 'space', icon: 'meeting_room' },
    { label: 'Job', value: 'job', icon: 'work' },
    { label: 'Free', value: 'free', icon: 'card_giftcard' }
  ]
};
