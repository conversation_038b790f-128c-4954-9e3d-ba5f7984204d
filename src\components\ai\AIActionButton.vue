<template>
  <q-btn
    :color="action.color || 'primary'"
    :icon="action.icon"
    :label="action.label"
    :loading="loading"
    :disable="disabled"
    @click="handleClick"
    class="ai-action-button"
    :class="buttonClass"
    :size="size"
    :outline="outline"
    :flat="flat"
    :round="round"
    :push="push"
    :glossy="glossy"
    :data-testid="`action-button-${action.label.toLowerCase().replace(/\\s+/g, '-')}`"
  >
    <q-tooltip v-if="tooltip" class="bg-primary">
      {{ tooltip }}
    </q-tooltip>
  </q-btn>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useQuasar } from 'quasar';
import { executeAction, validateAction, trackActionExecution, type ActionButton } from '../../services/aiEnhancedService';

interface Props {
  action: ActionButton;
  size?: string;
  outline?: boolean;
  flat?: boolean;
  round?: boolean;
  push?: boolean;
  glossy?: boolean;
  disabled?: boolean;
  tooltip?: string;
  variant?: 'default' | 'compact' | 'minimal';
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  outline: false,
  flat: false,
  round: false,
  push: false,
  glossy: false,
  disabled: false,
  variant: 'default'
});

const emit = defineEmits<{
  click: [action: ActionButton];
  success: [action: ActionButton];
  error: [action: ActionButton, error: Error];
}>();

const $q = useQuasar();
const loading = ref(false);

const buttonClass = computed(() => {
  const classes = [`ai-action-button--${props.variant}`];
  
  if (props.action.type) {
    classes.push(`ai-action-button--${props.action.type}`);
  }
  
  return classes.join(' ');
});

const handleClick = async () => {
  if (loading.value || props.disabled) return;

  // Validate action before execution
  if (!validateAction(props.action)) {
    console.error('Invalid action button:', props.action);
    $q.notify({
      type: 'negative',
      message: 'Invalid action configuration',
      position: 'top'
    });
    return;
  }

  loading.value = true;
  emit('click', props.action);

  try {
    await executeAction(props.action);
    
    // Track successful execution
    trackActionExecution(props.action, true);
    
    // Show success feedback
    showSuccessFeedback();
    
    emit('success', props.action);
  } catch (error: any) {
    console.error('Action execution failed:', error);
    
    // Track failed execution
    trackActionExecution(props.action, false);
    
    // Show error feedback
    showErrorFeedback(error.message);
    
    emit('error', props.action, error);
  } finally {
    loading.value = false;
  }
};

const showSuccessFeedback = () => {
  const messages = {
    navigation: 'Navigating...',
    action: 'Action completed successfully',
    external: 'Opening external link...'
  };

  $q.notify({
    type: 'positive',
    message: messages[props.action.type] || 'Action completed',
    position: 'top',
    timeout: 2000,
    icon: 'check_circle'
  });
};

const showErrorFeedback = (errorMessage: string) => {
  $q.notify({
    type: 'negative',
    message: `Action failed: ${errorMessage}`,
    position: 'top',
    timeout: 3000,
    icon: 'error'
  });
};
</script>

<style scoped>
.ai-action-button {
  margin: 2px 4px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.ai-action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Variant styles */
.ai-action-button--default {
  min-width: 120px;
  height: 36px;
}

.ai-action-button--compact {
  min-width: 100px;
  height: 32px;
  font-size: 0.875rem;
}

.ai-action-button--minimal {
  min-width: 80px;
  height: 28px;
  font-size: 0.75rem;
}

/* Action type styles */
.ai-action-button--navigation {
  border-left: 3px solid currentColor;
}

.ai-action-button--action {
  border-left: 3px solid #ff9800;
}

.ai-action-button--external {
  border-left: 3px solid #4caf50;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .ai-action-button {
    min-width: 100px;
    height: 32px;
    font-size: 0.875rem;
  }
  
  .ai-action-button--compact {
    min-width: 80px;
    height: 28px;
    font-size: 0.75rem;
  }
  
  .ai-action-button--minimal {
    min-width: 60px;
    height: 24px;
    font-size: 0.7rem;
  }
}

/* Loading state */
.ai-action-button .q-btn__content {
  transition: opacity 0.3s ease;
}

.ai-action-button.q-btn--loading .q-btn__content {
  opacity: 0.6;
}

/* Accessibility improvements */
.ai-action-button:focus {
  outline: 2px solid var(--q-primary);
  outline-offset: 2px;
}

.ai-action-button:focus:not(:focus-visible) {
  outline: none;
}

/* Animation for successful actions */
@keyframes success-pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.ai-action-button.success-animation {
  animation: success-pulse 0.3s ease-in-out;
}
</style>
