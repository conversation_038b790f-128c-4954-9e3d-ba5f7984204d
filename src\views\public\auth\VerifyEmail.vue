<template>
  <q-page class="flex flex-center bg-grey-2">
    <q-card class="verification-card q-pa-lg">
      <q-card-section class="text-center">
        <div class="text-h4 q-mb-md">Email Verification</div>
      </q-card-section>

      <q-card-section>
        <div v-if="loading" class="text-center q-pa-md">
          <q-spinner color="primary" size="3em" />
          <p class="text-body1 q-mt-md">Verifying your email...</p>
        </div>

        <div v-else-if="verified" class="text-center q-pa-md">
          <q-icon name="check_circle" color="positive" size="48px" />
          <p class="text-body1 q-mb-md">
            Your email has been successfully verified. You can now sign in to your account.
          </p>
          <q-btn
            flat
            color="primary"
            label="Go to Sign In"
            class="q-mt-md"
            to="/sign-in"
          />
        </div>

        <div v-else-if="error" class="text-center q-pa-md">
          <q-icon name="error" color="negative" size="48px" />
          <p class="text-body1 q-mb-md">
            {{ errorMessage }}
          </p>
          <q-btn
            flat
            color="primary"
            label="Go to Sign In"
            class="q-mt-md"
            to="/sign-in"
          />
        </div>

        <div v-else class="text-center q-pa-md">
          <q-icon name="info" color="info" size="48px" />
          <p class="text-body1 q-mb-md">
            Invalid or missing verification parameters.
          </p>
          <q-btn
            flat
            color="primary"
            label="Go to Sign In"
            class="q-mt-md"
            to="/sign-in"
          />
        </div>
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { supabase } from '../../../lib/supabase'
import { useNotificationStore } from '../../../stores/notifications'

const route = useRoute()
const notificationStore = useNotificationStore()
const loading = ref(true)
const verified = ref(false)
const error = ref(false)
const errorMessage = ref('An error occurred during verification. Please try again or contact support.')

onMounted(async () => {
  try {
    // Get parameters from URL
    const token = route.query.token as string
    const type = route.query.type as string || 'signup'
    const email = route.query.email as string

    // Log the verification attempt
    console.log('Verification attempt with params:', { token, type, email })

    // Validate parameters
    if (!token && !email) {
      error.value = true
      errorMessage.value = 'Invalid verification link. Missing required parameters.'
      loading.value = false
      return
    }

    let verificationSuccessful = false
    let userId = null

    // Try Supabase's built-in verification if we have a token
    if (token) {
      try {
        console.log('Attempting Supabase OTP verification with token')
        const { data, error: verifyError } = await supabase.auth.verifyOtp({
          token_hash: token,
          type: 'email'
        })

        if (verifyError) {
          console.warn('Supabase OTP verification failed:', verifyError)
          // Don't return here, we'll try the email-based verification next
        } else if (data?.user) {
          console.log('Supabase OTP verification successful')
          verificationSuccessful = true
          userId = data.user.id
        }
      } catch (err) {
        console.warn('Error during Supabase OTP verification:', err)
        // Continue to try other methods
      }
    }

    // If Supabase verification failed and we have an email, try to verify by email
    if (!verificationSuccessful && email) {
      try {
        console.log('Attempting email-based verification')
        // Find the user by email
        const { data: userData, error: userError } = await supabase
          .from('personal_details')
          .select('user_id')
          .eq('email', decodeURIComponent(email))
          .maybeSingle()

        if (userError) {
          console.warn('Error finding user by email:', userError)
        } else if (userData) {
          console.log('Found user by email:', userData.user_id)
          userId = userData.user_id
          verificationSuccessful = true
        }
      } catch (err) {
        console.warn('Error during email-based verification:', err)
      }
    }

    // If verification failed through both methods
    if (!verificationSuccessful) {
      error.value = true
      errorMessage.value = 'Error verifying your email. Please try signing in or contact support.'
      loading.value = false
      return
    }

    // If we have a user ID, mark them as verified in the database
    if (userId) {
      try {
        console.log('Updating verification status for user:', userId)
        const { error: updateError } = await supabase
          .from('personal_details')
          .update({ email_verified: true })
          .eq('user_id', userId)

        if (updateError) {
          console.warn('Error updating verification status:', updateError)
          // Continue anyway, as the verification was successful
        }
      } catch (err) {
        console.warn('Error updating verification status:', err)
      }
    }

    // Success
    verified.value = true
    notificationStore.success('Email verified successfully! You can now sign in to your account.')
  } catch (err) {
    console.error('Error during verification:', err)
    error.value = true
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
.verification-card {
  width: 100%;
  max-width: 400px;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

@media (max-width: 599px) {
  .verification-card {
    width: 90%;
  }
}
</style>
