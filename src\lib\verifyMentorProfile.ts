/**
 * Utility function to verify and fix mentor profile structure
 *
 * This function checks if a mentor profile exists for a given user_id,
 * and creates one if it doesn't exist.
 */

import { supabase } from './supabase'

/**
 * Verify that a mentor profile exists for a given user_id
 *
 * @param userId The user_id to check
 * @param profileName The profile name to use if creating a new profile
 * @returns A promise that resolves to the mentor profile data
 */
export async function verifyMentorProfile(userId: string, profileName: string = 'My Profile'): Promise<any> {
  try {
    console.log('Verifying mentor profile for user:', userId)

    // Log that we're verifying the mentor profile
    console.log('Checking mentor_profiles table for user:', userId)

    // Check if a mentor profile exists for this user
    const { data: existingProfile, error: checkError } = await supabase
      .from('mentor_profiles')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (checkError && checkError.code !== 'PGRST116') {
      // PGRST116 is "No rows returned" - this is expected if no profile exists
      console.error('Error checking mentor profile:', checkError)
      throw checkError
    }

    if (existingProfile) {
      console.log('Existing mentor profile found:', existingProfile)
      return { data: existingProfile, error: null }
    }

    // No profile exists, create one
    console.log('No mentor profile found, creating one')
    const newProfile = {
      user_id: userId,
      profile_name: profileName,
      is_public: false,
      completion_percentage: 0,
      // Initialize with empty array for required field
      expertise_areas: [], // Primary field name in the database
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    console.log('Creating new mentor profile with data:', newProfile)

    const { data: insertedProfile, error: insertError } = await supabase
      .from('mentor_profiles')
      .insert(newProfile)
      .select()

    if (insertError) {
      console.error('Error creating mentor profile:', insertError)
      throw insertError
    }

    console.log('Created new mentor profile:', insertedProfile)
    return { data: insertedProfile[0], error: null }
  } catch (err: any) {
    console.error('Error in verifyMentorProfile:', err)
    return { data: null, error: err }
  }
}

/**
 * Update a mentor profile with new data
 *
 * @param userId The user_id of the profile to update
 * @param data The data to update
 * @returns A promise that resolves to the updated profile data
 */
export async function updateMentorProfile(userId: string, data: any): Promise<any> {
  try {
    // First verify that the profile exists
    const { data: profile, error: verifyError } = await verifyMentorProfile(userId)
    if (verifyError) throw verifyError

    // Fix field name discrepancies
    const fixedData = { ...data }

    // Ensure expertise_areas is always an array if it exists
    if (fixedData.expertise_areas !== undefined) {
      console.log('Processing expertise_areas:', fixedData.expertise_areas)
      if (!Array.isArray(fixedData.expertise_areas)) {
        if (typeof fixedData.expertise_areas === 'string') {
          fixedData.expertise_areas = [fixedData.expertise_areas]
        } else if (fixedData.expertise_areas === null) {
          fixedData.expertise_areas = []
        }
      }
    }



    // Log the final data being sent to the database
    console.log('Final mentor profile data being sent to database:', fixedData)

    // Update the profile
    const { data: updatedProfile, error: updateError } = await supabase
      .from('mentor_profiles')
      .update({
        ...fixedData,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .select()

    if (updateError) {
      console.error('Error updating mentor profile:', updateError)

      // Check if it's a column error and provide more helpful message
      if (updateError.message && updateError.message.includes('column')) {
        console.log('Column error detected, providing more details')
        // Extract column name from error message if possible
        const columnMatch = updateError.message.match(/column "([^"]+)"/)
        const columnName = columnMatch ? columnMatch[1] : 'unknown'

        // Create a more helpful error message
        const enhancedError = new Error(
          `Failed to update mentor profile: The field '${columnName}' doesn't exist in the database. ` +
          `Please check field names in your form and database schema.`
        )
        throw enhancedError
      }

      throw updateError
    }

    console.log('Updated mentor profile:', updatedProfile)
    return { data: updatedProfile[0], error: null }
  } catch (err: any) {
    console.error('Error in updateMentorProfile:', err)
    return { data: null, error: err }
  }
}
