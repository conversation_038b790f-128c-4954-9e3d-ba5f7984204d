-- This SQL script should be executed in the Supabase SQL Editor
-- It creates a function that allows executing arbitrary SQL commands if it doesn't exist

-- Check if the function exists
DO $do_block$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_proc
        WHERE proname = 'exec_sql'
        AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
    ) THEN
        -- Create the exec_sql function
        CREATE OR REPLACE FUNCTION public.exec_sql(sql text)
        RETURNS void
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $function$
        BEGIN
          EXECUTE sql;
        END;
        $function$;

        -- Grant execute permission to authenticated users and service role
        GRANT EXECUTE ON FUNCTION public.exec_sql TO authenticated;
        GRANT EXECUTE ON FUNCTION public.exec_sql TO service_role;
    END IF;
END
$do_block$;
