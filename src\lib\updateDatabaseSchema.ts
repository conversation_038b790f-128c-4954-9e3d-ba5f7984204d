import { supabase } from './supabase';

/**
 * Updates the innovator_profiles table with the enhanced schema
 */
export async function updateInnovatorProfilesTable(): Promise<{ success: boolean; message: string }> {
  try {
    console.log('Updating innovator_profiles table with enhanced schema...');

    // Execute the SQL to update the table
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        -- Add new columns to innovator_profiles table if they don't exist
        ALTER TABLE public.innovator_profiles ADD COLUMN IF NOT EXISTS innovation_description TEXT;
        ALTER TABLE public.innovator_profiles ADD COLUMN IF NOT EXISTS team_description TEXT;
        ALTER TABLE public.innovator_profiles ADD COLUMN IF NOT EXISTS funding_stage TEXT;
        ALTER TABLE public.innovator_profiles ADD COLUMN IF NOT EXISTS contact_email TEXT;
        ALTER TABLE public.innovator_profiles ADD COLUMN IF NOT EXISTS contact_phone TEXT;
        ALTER TABLE public.innovator_profiles ADD COLUMN IF NOT EXISTS contact_address TEXT;
        ALTER TABLE public.innovator_profiles ADD COLUMN IF NOT EXISTS achievements TEXT[];
        ALTER TABLE public.innovator_profiles ADD COLUMN IF NOT EXISTS awards TEXT[];
        ALTER TABLE public.innovator_profiles ADD COLUMN IF NOT EXISTS product_catalog_url TEXT;
        ALTER TABLE public.innovator_profiles ADD COLUMN IF NOT EXISTS company_profile_url TEXT;
        ALTER TABLE public.innovator_profiles ADD COLUMN IF NOT EXISTS logo_url TEXT;
        ALTER TABLE public.innovator_profiles ADD COLUMN IF NOT EXISTS target_market TEXT;
        ALTER TABLE public.innovator_profiles ADD COLUMN IF NOT EXISTS business_model TEXT;
        ALTER TABLE public.innovator_profiles ADD COLUMN IF NOT EXISTS competitive_advantage TEXT;
        ALTER TABLE public.innovator_profiles ADD COLUMN IF NOT EXISTS intellectual_property TEXT;
        ALTER TABLE public.innovator_profiles ADD COLUMN IF NOT EXISTS sustainability_impact TEXT;
        
        -- Make sure all columns allow NULL values (this is the default in PostgreSQL)
        -- No need to explicitly alter columns for this
      `
    });

    if (error) {
      console.error('Error updating innovator_profiles table:', error.message);
      return { success: false, message: error.message };
    }

    return { success: true, message: 'Innovator profiles table updated successfully with enhanced schema' };
  } catch (err: any) {
    console.error('Error updating innovator_profiles table:', err.message);
    return { success: false, message: err.message };
  }
}

/**
 * Creates the innovator_profiles table if it doesn't exist
 */
export async function createInnovatorProfilesTable(): Promise<{ success: boolean; message: string }> {
  try {
    console.log('Creating innovator_profiles table if it doesn\'t exist...');

    // Execute the SQL to create the table
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        -- Create the innovator_profiles table if it doesn't exist
        CREATE TABLE IF NOT EXISTS public.innovator_profiles (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            profile_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
            
            -- Basic Innovation Information
            innovation_area TEXT,
            innovation_stage TEXT,
            innovation_description TEXT,
            
            -- Team Information
            team_size INTEGER,
            team_description TEXT,
            
            -- Funding Information
            funding_needs BOOLEAN DEFAULT false,
            funding_amount NUMERIC,
            funding_stage TEXT,
            
            -- Prototype Information
            has_prototype BOOLEAN DEFAULT false,
            prototype_description TEXT,
            
            -- Goals and Challenges
            goals TEXT[],
            challenges TEXT[],
            
            -- Online Presence
            website TEXT,
            social_links JSONB DEFAULT '{}'::jsonb,
            
            -- Contact Information
            contact_email TEXT,
            contact_phone TEXT,
            contact_address TEXT,
            
            -- Bio and Achievements
            bio TEXT,
            achievements TEXT[],
            awards TEXT[],
            
            -- File Storage
            product_catalog_url TEXT,
            company_profile_url TEXT,
            logo_url TEXT,
            
            -- Additional Innovator-specific Fields
            target_market TEXT,
            business_model TEXT,
            competitive_advantage TEXT,
            intellectual_property TEXT,
            sustainability_impact TEXT,
            
            -- Timestamps
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Enable Row Level Security on the table
        ALTER TABLE public.innovator_profiles ENABLE ROW LEVEL SECURITY;

        -- Drop existing policies first to avoid conflicts
        DROP POLICY IF EXISTS "Temporary permissive policy for innovator profiles" ON public.innovator_profiles;

        -- Create a temporary permissive policy for testing
        -- This allows all operations for authenticated users
        CREATE POLICY "Temporary permissive policy for innovator profiles"
            ON public.innovator_profiles
            USING (auth.role() = 'authenticated');

        -- Grant permissions to authenticated users
        GRANT ALL ON public.innovator_profiles TO authenticated;
        GRANT ALL ON public.innovator_profiles TO service_role;
      `
    });

    if (error) {
      console.error('Error creating innovator_profiles table:', error.message);
      return { success: false, message: error.message };
    }

    return { success: true, message: 'Innovator profiles table created successfully' };
  } catch (err: any) {
    console.error('Error creating innovator_profiles table:', err.message);
    return { success: false, message: err.message };
  }
}

/**
 * Updates the profiles table to allow NULL values for all fields
 */
export async function updateProfilesTable(): Promise<{ success: boolean; message: string }> {
  try {
    console.log('Updating profiles table to allow NULL values...');

    // In PostgreSQL, columns allow NULL values by default unless explicitly set to NOT NULL
    // So we don't need to explicitly alter columns for this
    // This function is more for documentation purposes

    return { success: true, message: 'Profiles table already allows NULL values for all fields' };
  } catch (err: any) {
    console.error('Error updating profiles table:', err.message);
    return { success: false, message: err.message };
  }
}

/**
 * Comprehensive database update function
 */
export async function updateDatabase(): Promise<{ success: boolean; message: string }> {
  try {
    // Step 1: Create or update the innovator_profiles table
    const createResult = await createInnovatorProfilesTable();
    if (!createResult.success) {
      return createResult;
    }

    // Step 2: Update the innovator_profiles table with enhanced schema
    const updateResult = await updateInnovatorProfilesTable();
    if (!updateResult.success) {
      return updateResult;
    }

    // Step 3: Refresh the schema cache
    const { error } = await supabase.rpc('exec_sql', {
      sql: 'SELECT 1;'
    });

    if (error) {
      console.error('Error refreshing schema cache:', error.message);
      return { success: false, message: error.message };
    }

    return { success: true, message: 'Database updated successfully with enhanced schema' };
  } catch (err: any) {
    console.error('Error updating database:', err.message);
    return { success: false, message: err.message };
  }
}
