<template>
  <div class="post-creation-fab">
    <q-fab
      v-model="fabOpen"
      color="primary"
      icon="add"
      direction="up"
      :disable="loading"
      @click="handleFabClick"
      size="lg"
      class="pulse-animation"
    >
      <!-- We'll only show sub-actions for the feed tab -->
      <template v-if="activeTab === 'feed' && isAuthenticated">
        <q-fab-action
          color="primary"
          icon="post_add"
          label="General Post"
          label-position="left"
          @click="openPostCreation('general')"
        />
        <q-fab-action
          color="amber"
          icon="emoji_objects"
          label="Opportunity"
          label-position="left"
          @click="openPostCreation('opportunity')"
        />
        <q-fab-action
          color="deep-orange"
          icon="article"
          label="Blog Article"
          label-position="left"
          @click="openPostCreation('blog')"
        />
        <q-fab-action
          color="green"
          icon="event"
          label="Event"
          label-position="left"
          @click="openPostCreation('event')"
        />
      </template>
    </q-fab>

    <!-- Post Creation Dialog -->
    <post-creation-dialog
      ref="dialogRef"
      v-model="showDialog"
      :post-type="selectedPostType"
      :active-tab="activeTab"
      @post-created="handlePostCreated"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, watch } from 'vue';
import { useAuthStore } from '../../stores/auth';
import PostCreationDialog from './PostCreationDialog.vue';

const props = defineProps({
  activeTab: {
    type: String,
    required: true
  }
});

const emit = defineEmits(['post-created']);

// State
const fabOpen = ref(false);
const showDialog = ref(false);
const selectedPostType = ref('');
const loading = ref(false);
const dialogRef = ref(null);

// Get auth store
const authStore = useAuthStore();
const isAuthenticated = computed(() => authStore.isAuthenticated);

// Watch for dialog opening/closing to update loading state
watch(showDialog, (newVal) => {
  if (!newVal) {
    // Reset loading when dialog closes
    loading.value = false;
  }
});

// Handle FAB click
function handleFabClick() {
  // If it's not the feed tab, we'll directly open the appropriate form
  if (props.activeTab !== 'feed') {
    openPostCreation(getPostTypeForTab(props.activeTab));
  } else {
    // For feed tab, toggle the FAB open state
    fabOpen.value = !fabOpen.value;
  }

  console.log('FAB clicked, activeTab:', props.activeTab, 'fabOpen:', fabOpen.value);
}

// Open post creation dialog
function openPostCreation(postType: string) {
  selectedPostType.value = postType;
  showDialog.value = true;
  fabOpen.value = false; // Close the FAB
}

// Handle post created event
function handlePostCreated(post: any) {
  emit('post-created', post);
  showDialog.value = false;
}

// Get post type based on active tab
function getPostTypeForTab(tab: string): string {
  switch (tab) {
    case 'feed':
      return 'general';
    case 'blog':
      return 'blog';
    case 'events':
      return 'event';
    case 'groups':
      // Group creation is disabled, return general instead
      return 'general';
    case 'marketplace':
      return 'marketplace';
    default:
      return 'general';
  }
}
</script>

<style scoped>
.post-creation-fab {
  position: fixed !important;
  right: 20px !important;
  bottom: 20px !important;
  z-index: 9999 !important;
}

/* Make the FAB more prominent */
:deep(.q-fab) {
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3) !important;
  transform: scale(1.1) !important;
}

:deep(.q-fab__icon-container) {
  background-color: var(--q-primary) !important;
}

:deep(.q-fab__active-icon) {
  font-size: 24px !important;
}

/* Pulse animation for the FAB */
.pulse-animation {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(13, 138, 62, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(13, 138, 62, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(13, 138, 62, 0);
  }
}
</style>
