# Database Migrations

This directory contains SQL migration files for the Supabase database. These files are executed in order based on their filenames when you run `supabase db reset` or when you deploy to Supabase.

## Migration Files

1. `20240501000001_create_base_tables.sql` - Creates the initial tables for the application
2. `20240501000002_create_innovator_profiles.sql` - Creates the innovator_profiles table with enhanced fields
3. `20240501000003_create_profile_visibility_function.sql` - Creates a function to check if a profile is visible to the current user
4. `20240501000004_create_triggers.sql` - Creates triggers for automatic timestamp updates
5. `20240501000005_create_profile_completion_function.sql` - Creates a function to calculate profile completion percentage
6. `20240501000006_create_user_management_functions.sql` - Creates functions for user management

## How to Apply Migrations

### Option 1: Using the Supabase SQL Editor

1. Go to your Supabase project dashboard
2. Navigate to the SQL Editor
3. Copy and paste the contents of each migration file
4. Execute them in the order listed above

### Option 2: Using the Supabase CLI

If you have the Supabase CLI installed, you can run:

```bash
supabase db reset
```

### Option 3: Using the Application

You can also apply migrations directly from the application:

1. Go to the Dashboard page
2. Click the "Apply Migrations" button

## Troubleshooting

If you encounter errors during migration:

1. Check the error message carefully
2. Make sure you're executing the migrations in the correct order
3. Verify that all referenced tables and functions exist
4. If a migration fails, you may need to manually clean up any partially created objects before retrying

## Database Schema Overview

### Profiles

- `profiles`: Base table with core profile information
  - Contains personal information, visibility settings, and state
  - Referenced by specialized profile tables

### Specialized Profiles

- `innovator_profiles`: Type-specific profile table for innovators
  - Contains fields relevant to innovators
  - References back to the base `profiles` table

### Functions and Triggers

- Profile visibility function determines who can see a profile
- Profile completion calculation helps users track their progress
- Timestamp updates keep track of when profiles are modified
- User management functions handle new user signups and deletions
