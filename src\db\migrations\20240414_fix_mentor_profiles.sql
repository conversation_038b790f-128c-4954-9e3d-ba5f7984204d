-- Fix mentor_profiles table structure
-- Add completion_percentage column if it doesn't exist

-- First check if the mentor_profiles table exists
DO $$
BEGIN
  IF EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'mentor_profiles'
  ) THEN
    -- Add completion_percentage column if it doesn't exist
    IF NOT EXISTS (
      SELECT FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = 'mentor_profiles' 
      AND column_name = 'completion_percentage'
    ) THEN
      ALTER TABLE public.mentor_profiles 
      ADD COLUMN completion_percentage INTEGER DEFAULT 0;
    END IF;
  END IF;
END
$$;
