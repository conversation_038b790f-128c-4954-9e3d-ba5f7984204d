import { onBeforeRouteLeave, onBeforeRouteUpdate } from 'vue-router';
import { ref, onMounted, Ref } from 'vue';

/**
 * Composable to manage scroll position in components with scrollable content
 * @param scrollContainerRef - Reference to the scrollable container element
 */
export function useScrollReset(scrollContainerRef?: Ref<HTMLElement | null>) {
  // If no container ref is provided, create one
  const containerRef = scrollContainerRef || ref<HTMLElement | null>(null);

  // Reset scroll position when the component is mounted
  onMounted(() => {
    resetScroll();
  });

  // Reset scroll position when route is updated but component is reused
  onBeforeRouteUpdate(() => {
    resetScroll();
  });

  // Reset scroll position when leaving the route
  onBeforeRouteLeave(() => {
    resetScroll();
  });

  // Function to reset scroll position
  const resetScroll = () => {
    // Reset window scroll
    window.scrollTo(0, 0);

    // Reset container scroll if available
    if (containerRef.value) {
      containerRef.value.scrollTop = 0;
    }

    // Find all scrollable elements in the component and reset them
    const scrollableElements = document.querySelectorAll('.q-scrollarea-container, .scroll-container');
    scrollableElements.forEach(el => {
      if (el instanceof HTMLElement) {
        el.scrollTop = 0;
      }
    });
  };

  return {
    containerRef,
    resetScroll
  };
}
