/**
 * Enhanced Messaging Service
 * 
 * Builds on the existing messaging store to provide context-aware messaging,
 * threading, typing indicators, and improved real-time features.
 */

import { ref, computed } from 'vue'
import { supabase } from '../lib/supabase'
import { useUnifiedCache } from './unifiedCacheService'
import { useUnifiedRealtime } from './unifiedRealtimeService'
import { useNotificationStore } from '../stores/notification'

export type MessageContext = 'direct' | 'project' | 'group' | 'support'
export type MessageType = 'text' | 'file' | 'image' | 'system'
export type MessageStatus = 'sending' | 'sent' | 'delivered' | 'read' | 'failed'

export interface EnhancedMessage {
  id: string
  sender_id: string
  recipient_id?: string
  content: string
  message_type: MessageType
  context: MessageContext
  context_id?: string // project_id, group_id, etc.
  thread_id?: string
  parent_message_id?: string
  status: MessageStatus
  metadata?: Record<string, any>
  created_at: string
  updated_at: string
  is_read: boolean
  read_at?: string
  sender?: {
    id: string
    email: string
    first_name?: string
    last_name?: string
    profile_name?: string
    avatar_url?: string
  }
}

export interface MessageThread {
  id: string
  root_message_id: string
  context: MessageContext
  context_id?: string
  participant_ids: string[]
  last_message_at: string
  message_count: number
  unread_count: number
}

export interface TypingIndicator {
  user_id: string
  conversation_id: string
  context: MessageContext
  context_id?: string
  started_at: string
}

export interface MessagingStats {
  totalMessages: number
  unreadCount: number
  activeConversations: number
  typingUsers: number
  averageResponseTime: number
}

class EnhancedMessagingService {
  private cache = useUnifiedCache()
  private realtime = useUnifiedRealtime()
  private notifications = useNotificationStore()
  
  private messages = ref<Map<string, EnhancedMessage[]>>(new Map())
  private threads = ref<Map<string, MessageThread>>(new Map())
  private typingIndicators = ref<Map<string, TypingIndicator>>(new Map())
  private isLoading = ref(false)
  private error = ref<string | null>(null)
  
  private typingTimeouts = new Map<string, number>()
  private subscriptions = new Map<string, any>()

  constructor() {
    this.initializeRealtimeSubscriptions()
  }

  /**
   * Send a direct message
   */
  async sendDirectMessage(
    recipientId: string,
    content: string,
    metadata?: Record<string, any>
  ): Promise<EnhancedMessage | null> {
    return this.sendMessage({
      recipient_id: recipientId,
      content,
      context: 'direct',
      metadata
    })
  }

  /**
   * Send a project-specific message
   */
  async sendProjectMessage(
    projectId: string,
    content: string,
    recipientId?: string,
    metadata?: Record<string, any>
  ): Promise<EnhancedMessage | null> {
    return this.sendMessage({
      recipient_id: recipientId,
      content,
      context: 'project',
      context_id: projectId,
      metadata
    })
  }

  /**
   * Send a group message
   */
  async sendGroupMessage(
    groupId: string,
    content: string,
    metadata?: Record<string, any>
  ): Promise<EnhancedMessage | null> {
    return this.sendMessage({
      content,
      context: 'group',
      context_id: groupId,
      metadata
    })
  }

  /**
   * Reply to a message (threading)
   */
  async replyToMessage(
    parentMessageId: string,
    content: string,
    metadata?: Record<string, any>
  ): Promise<EnhancedMessage | null> {
    // Get parent message to determine context
    const parentMessage = await this.getMessageById(parentMessageId)
    if (!parentMessage) {
      throw new Error('Parent message not found')
    }

    return this.sendMessage({
      recipient_id: parentMessage.recipient_id,
      content,
      context: parentMessage.context,
      context_id: parentMessage.context_id,
      parent_message_id: parentMessageId,
      thread_id: parentMessage.thread_id || parentMessage.id,
      metadata
    })
  }

  /**
   * Get messages for a conversation
   */
  async getMessages(
    conversationId: string,
    context: MessageContext = 'direct',
    options: {
      limit?: number
      offset?: number
      includeThreads?: boolean
    } = {}
  ): Promise<EnhancedMessage[]> {
    const cacheKey = `messages:${conversationId}:${context}`
    
    // Try cache first
    const cached = this.cache.get<EnhancedMessage[]>(cacheKey)
    if (cached) {
      return cached
    }

    try {
      this.isLoading.value = true
      this.error.value = null

      let query = supabase
        .from('user_messages')
        .select(`
          *,
          sender:sender_id (
            id,
            email,
            first_name,
            last_name,
            profile_name
          )
        `)
        .order('created_at', { ascending: true })

      // Add context-specific filters
      if (context === 'direct') {
        query = query.or(`and(sender_id.eq.${conversationId}),and(recipient_id.eq.${conversationId})`)
      } else if (context === 'project' || context === 'group') {
        query = query.eq('context_id', conversationId)
      }

      if (options.limit) {
        query = query.limit(options.limit)
      }

      if (options.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 50) - 1)
      }

      const { data, error } = await query

      if (error) throw error

      const messages = data || []
      
      // Cache the results
      this.cache.set(cacheKey, messages, {
        ttl: 60 * 1000, // 1 minute
        storage: 'memory'
      })

      // Update local state
      this.messages.value.set(conversationId, messages)

      return messages
    } catch (err: any) {
      this.error.value = err.message
      console.error('Error loading messages:', err)
      return []
    } finally {
      this.isLoading.value = false
    }
  }

  /**
   * Get message threads
   */
  async getThreads(
    context: MessageContext,
    contextId?: string
  ): Promise<MessageThread[]> {
    const cacheKey = `threads:${context}:${contextId || 'all'}`
    
    const cached = this.cache.get<MessageThread[]>(cacheKey)
    if (cached) {
      return cached
    }

    try {
      let query = supabase
        .from('message_threads')
        .select('*')
        .eq('context', context)
        .order('last_message_at', { ascending: false })

      if (contextId) {
        query = query.eq('context_id', contextId)
      }

      const { data, error } = await query

      if (error) throw error

      const threads = data || []
      
      this.cache.set(cacheKey, threads, {
        ttl: 2 * 60 * 1000, // 2 minutes
        storage: 'memory'
      })

      return threads
    } catch (err: any) {
      console.error('Error loading threads:', err)
      return []
    }
  }

  /**
   * Start typing indicator
   */
  startTyping(conversationId: string, context: MessageContext = 'direct', contextId?: string): void {
    const key = `${conversationId}:${context}:${contextId || ''}`
    
    // Clear existing timeout
    if (this.typingTimeouts.has(key)) {
      clearTimeout(this.typingTimeouts.get(key))
    }

    // Send typing indicator
    this.sendTypingIndicator(conversationId, context, contextId, true)

    // Auto-stop typing after 3 seconds
    const timeout = setTimeout(() => {
      this.stopTyping(conversationId, context, contextId)
    }, 3000)

    this.typingTimeouts.set(key, timeout)
  }

  /**
   * Stop typing indicator
   */
  stopTyping(conversationId: string, context: MessageContext = 'direct', contextId?: string): void {
    const key = `${conversationId}:${context}:${contextId || ''}`
    
    if (this.typingTimeouts.has(key)) {
      clearTimeout(this.typingTimeouts.get(key))
      this.typingTimeouts.delete(key)
    }

    this.sendTypingIndicator(conversationId, context, contextId, false)
  }

  /**
   * Mark message as read
   */
  async markAsRead(messageId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('enhanced_messages')
        .update({
          is_read: true,
          read_at: new Date().toISOString(),
          status: 'read'
        })
        .eq('id', messageId)

      if (error) throw error

      // Invalidate related cache
      this.cache.invalidate('messages:*')
      
      return true
    } catch (err: any) {
      console.error('Error marking message as read:', err)
      return false
    }
  }

  /**
   * Get messaging statistics
   */
  getStats(): MessagingStats {
    const allMessages = Array.from(this.messages.value.values()).flat()
    const unreadCount = allMessages.filter(m => !m.is_read).length
    const typingUsers = this.typingIndicators.value.size

    return {
      totalMessages: allMessages.length,
      unreadCount,
      activeConversations: this.messages.value.size,
      typingUsers,
      averageResponseTime: 0 // TODO: Calculate from message timestamps
    }
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    // Clear all typing timeouts
    for (const timeout of this.typingTimeouts.values()) {
      clearTimeout(timeout)
    }
    this.typingTimeouts.clear()

    // Unsubscribe from all real-time subscriptions
    for (const subscription of this.subscriptions.values()) {
      this.realtime.unsubscribe(subscription)
    }
    this.subscriptions.clear()
  }

  // Private methods

  private async sendMessage(params: {
    recipient_id?: string
    content: string
    context: MessageContext
    context_id?: string
    parent_message_id?: string
    thread_id?: string
    metadata?: Record<string, any>
  }): Promise<EnhancedMessage | null> {
    try {
      this.isLoading.value = true
      this.error.value = null

      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('User not authenticated')

      const messageData = {
        sender_id: user.id,
        recipient_id: params.recipient_id,
        content: params.content,
        message_type: 'text' as MessageType,
        context: params.context,
        context_id: params.context_id,
        parent_message_id: params.parent_message_id,
        thread_id: params.thread_id,
        status: 'sending' as MessageStatus,
        metadata: params.metadata,
        is_read: false
      }

      const { data, error } = await supabase
        .from('enhanced_messages')
        .insert(messageData)
        .select(`
          *,
          sender:sender_id (
            id,
            email,
            first_name,
            last_name,
            profile_name,
            avatar_url
          )
        `)
        .single()

      if (error) throw error

      // Invalidate related cache
      this.cache.invalidate('messages:*')
      this.cache.invalidate('threads:*')

      return data
    } catch (err: any) {
      this.error.value = err.message
      console.error('Error sending message:', err)
      return null
    } finally {
      this.isLoading.value = false
    }
  }

  private async getMessageById(messageId: string): Promise<EnhancedMessage | null> {
    const cacheKey = `message:${messageId}`
    
    const cached = this.cache.get<EnhancedMessage>(cacheKey)
    if (cached) {
      return cached
    }

    try {
      const { data, error } = await supabase
        .from('enhanced_messages')
        .select('*')
        .eq('id', messageId)
        .single()

      if (error) throw error

      this.cache.set(cacheKey, data, {
        ttl: 5 * 60 * 1000, // 5 minutes
        storage: 'memory'
      })

      return data
    } catch (err: any) {
      console.error('Error getting message by ID:', err)
      return null
    }
  }

  private async sendTypingIndicator(
    conversationId: string,
    context: MessageContext,
    contextId: string | undefined,
    isTyping: boolean
  ): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return

      if (isTyping) {
        // Insert typing indicator
        await supabase
          .from('typing_indicators')
          .upsert({
            user_id: user.id,
            conversation_id: conversationId,
            context,
            context_id: contextId,
            started_at: new Date().toISOString()
          })
      } else {
        // Remove typing indicator
        await supabase
          .from('typing_indicators')
          .delete()
          .eq('user_id', user.id)
          .eq('conversation_id', conversationId)
          .eq('context', context)
      }
    } catch (err: any) {
      console.error('Error sending typing indicator:', err)
    }
  }

  private initializeRealtimeSubscriptions(): void {
    // Subscribe to new messages
    const messageSubscription = this.realtime.subscribe(
      {
        table: 'enhanced_messages',
        event: '*'
      },
      (payload) => this.handleMessageEvent(payload),
      { deduplicate: true }
    )
    this.subscriptions.set('messages', messageSubscription)

    // Subscribe to typing indicators
    const typingSubscription = this.realtime.subscribe(
      {
        table: 'typing_indicators',
        event: '*'
      },
      (payload) => this.handleTypingEvent(payload),
      { deduplicate: true }
    )
    this.subscriptions.set('typing', typingSubscription)
  }

  private handleMessageEvent(payload: any): void {
    console.log('Enhanced messaging: Message event received', payload)
    
    // Invalidate relevant cache
    this.cache.invalidate('messages:*')
    this.cache.invalidate('threads:*')
    
    // Show notification for new messages
    if (payload.eventType === 'INSERT') {
      const message = payload.new as EnhancedMessage
      this.notifications.info({
        message: 'New message received',
        caption: message.content.substring(0, 50) + (message.content.length > 50 ? '...' : ''),
        timeout: 3000
      })
    }
  }

  private handleTypingEvent(payload: any): void {
    const indicator = payload.new as TypingIndicator
    const key = `${indicator.conversation_id}:${indicator.context}:${indicator.context_id || ''}`
    
    if (payload.eventType === 'INSERT') {
      this.typingIndicators.value.set(key, indicator)
    } else if (payload.eventType === 'DELETE') {
      this.typingIndicators.value.delete(key)
    }
  }
}

// Create singleton instance
const enhancedMessagingService = new EnhancedMessagingService()

// Export composable function
export function useEnhancedMessaging() {
  return {
    sendDirectMessage: enhancedMessagingService.sendDirectMessage.bind(enhancedMessagingService),
    sendProjectMessage: enhancedMessagingService.sendProjectMessage.bind(enhancedMessagingService),
    sendGroupMessage: enhancedMessagingService.sendGroupMessage.bind(enhancedMessagingService),
    replyToMessage: enhancedMessagingService.replyToMessage.bind(enhancedMessagingService),
    getMessages: enhancedMessagingService.getMessages.bind(enhancedMessagingService),
    getThreads: enhancedMessagingService.getThreads.bind(enhancedMessagingService),
    startTyping: enhancedMessagingService.startTyping.bind(enhancedMessagingService),
    stopTyping: enhancedMessagingService.stopTyping.bind(enhancedMessagingService),
    markAsRead: enhancedMessagingService.markAsRead.bind(enhancedMessagingService),
    getStats: enhancedMessagingService.getStats.bind(enhancedMessagingService),
    isLoading: computed(() => enhancedMessagingService.isLoading.value),
    error: computed(() => enhancedMessagingService.error.value)
  }
}

// Export service instance
export { enhancedMessagingService }

// Export types
export type { EnhancedMessage, MessageThread, TypingIndicator, MessagingStats, MessageContext, MessageType, MessageStatus }
