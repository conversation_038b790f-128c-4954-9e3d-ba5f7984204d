<template>
  <q-page class="q-pa-md">
    <div class="row q-col-gutter-md">
      <div class="col-12">
        <q-card class="content-header-card bg-primary text-white">
          <q-card-section>
            <div class="text-h4">Recent Activity</div>
            <p class="text-body1 q-mt-md">
              Stay updated with the latest activities and interactions on the platform.
            </p>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-12">
        <activity-feed title="All Activity" />
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { useAuthStore } from '../../stores/auth';
import { useActivityNotificationsStore } from '../../stores/activityNotifications';
import ActivityFeed from '../../components/activity/ActivityFeed.vue';

const router = useRouter();
const $q = useQuasar();
const authStore = useAuthStore();
const activityNotificationsStore = useActivityNotificationsStore();

// Check if user is authenticated and mark activities as read
onMounted(() => {
  if (!authStore.isAuthenticated) {
    router.push('/login');
    $q.notify({
      color: 'negative',
      message: 'You must be logged in to view this page',
      icon: 'error'
    });
    return;
  }

  // Mark all activities as read when this page is viewed
  activityNotificationsStore.markActivitiesAsRead();
});
</script>

<style scoped>
.content-header-card {
  border-radius: 8px;
  overflow: hidden;
}
</style>
