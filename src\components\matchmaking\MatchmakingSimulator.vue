<template>
  <div class="matchmaking-simulator q-pa-md">
    <h2 class="text-h5 q-mb-md">Matchmaking Simulator</h2>

    <q-card class="q-mb-md">
      <q-card-section>
        <div class="row q-col-gutter-md">
          <div class="col-12 col-md-4">
            <q-select
              v-model="sourceProfileType"
              :options="profileTypeOptions"
              label="Source Profile Type"
              outlined
              dense
            />
          </div>

          <div class="col-12 col-md-4">
            <q-select
              v-model="sourceProfileId"
              :options="sourceProfileOptions"
              label="Source Profile"
              outlined
              dense
              :disable="!sourceProfileType"
            />
          </div>

          <div class="col-12 col-md-4">
            <q-select
              v-model="targetProfileType"
              :options="profileTypeOptions"
              label="Target Profile Type"
              outlined
              dense
            />
          </div>
        </div>

        <div class="row q-mt-md">
          <div class="col-12">
            <q-btn
              color="primary"
              label="Generate Matches"
              :disable="!canGenerateMatches"
              @click="generateMatches"
              :loading="loading"
            />
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- Results section -->
    <q-card v-if="matches.length > 0">
      <q-card-section>
        <div class="text-h6">Match Results</div>
      </q-card-section>

      <q-separator />

      <q-list>
        <q-item v-for="match in matches" :key="match.id" class="q-py-md">
          <q-item-section>
            <q-item-label>{{ getProfileName(match) }}</q-item-label>
            <q-item-label caption>{{ match.entityType }}</q-item-label>
          </q-item-section>

          <q-item-section side>
            <div class="row items-center">
              <q-circular-progress
                :value="match.score * 100"
                size="60px"
                :thickness="0.2"
                color="primary"
                center-color="white"
                track-color="grey-3"
                class="q-mr-md"
              >
                <div class="text-caption">{{ (match.score * 100).toFixed(0) }}%</div>
              </q-circular-progress>

              <q-btn flat round color="primary" icon="info">
                <q-tooltip>
                  <div v-for="(score, reason) in match.reasons" :key="reason">
                    {{ formatReason(reason) }}: {{ (score * 100).toFixed(0) }}%
                  </div>
                </q-tooltip>
              </q-btn>
            </div>
          </q-item-section>
        </q-item>
      </q-list>
    </q-card>

    <q-card v-else-if="hasGeneratedMatches" class="q-mt-md">
      <q-card-section>
        <div class="text-subtitle1">No matches found. Try different profile types or profiles.</div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { ProfileType, EntityType, MatchResult } from '@/lib/matchmaking/matchmakingService';
import { getSimulatedProfiles } from '@/lib/matchmaking/simulator';
import { useMatchmakingStore } from '@/stores/matchmaking';

// Store
const matchmakingStore = useMatchmakingStore();

// Enable simulation mode
matchmakingStore.setSimulationMode(true);

// State
const sourceProfileType = ref<ProfileType>('innovator');
const sourceProfileId = ref<string>('');
const targetProfileType = ref<ProfileType>('investor');
const matches = ref<MatchResult[]>([]);
const hasGeneratedMatches = ref(false);

// Computed
const loading = computed(() => matchmakingStore.isLoading);

// Profile type options
const profileTypeOptions = [
  { label: 'Innovator', value: 'innovator' },
  { label: 'Investor', value: 'investor' },
  { label: 'Mentor', value: 'mentor' },
  { label: 'Professional', value: 'professional' },
  { label: 'Industry Expert', value: 'industry_expert' },
  { label: 'Academic Student', value: 'academic_student' },
  { label: 'Academic Institution', value: 'academic_institution' },
  { label: 'Organisation', value: 'organisation' }
];

// Source profile options based on selected type
const sourceProfileOptions = computed(() => {
  if (!sourceProfileType.value) return [];

  const profiles = getSimulatedProfiles(sourceProfileType.value);
  return profiles.map(profile => ({
    label: getProfileDisplayName(profile, sourceProfileType.value),
    value: profile.user_id
  }));
});

// Reset source profile when type changes
watch(sourceProfileType, () => {
  if (sourceProfileOptions.value.length > 0) {
    sourceProfileId.value = sourceProfileOptions.value[0].value;
  } else {
    sourceProfileId.value = '';
  }
});

// Can generate matches
const canGenerateMatches = computed(() =>
  sourceProfileType.value &&
  sourceProfileId.value &&
  targetProfileType.value
);

// Generate matches
async function generateMatches() {
  if (!canGenerateMatches.value) return;

  try {
    // Generate matches using the matchmaking store
    matches.value = await matchmakingStore.generateSimulatedMatches(
      sourceProfileId.value,
      sourceProfileType.value,
      targetProfileType.value
    );

    hasGeneratedMatches.value = true;
  } catch (error) {
    console.error('Error generating matches:', error);
  }
}

// Helper functions
function getProfileName(match: MatchResult): string {
  const profiles = getSimulatedProfiles(match.entityType as ProfileType);
  const profile = profiles.find(p => p.user_id === match.entityId);

  if (!profile) return match.entityId;

  return getProfileDisplayName(profile, match.entityType as ProfileType);
}

function getProfileDisplayName(profile: any, profileType: ProfileType): string {
  switch (profileType) {
    case 'innovator':
      return profile.innovation_area || profile.user_id;
    case 'investor':
      return profile.investment_focus?.join(', ') || profile.user_id;
    case 'mentor':
      return profile.expertise_areas?.join(', ') || profile.user_id;
    case 'professional':
      return profile.job_title || profile.user_id;
    case 'industry_expert':
      return profile.expertise_areas?.join(', ') || profile.user_id;
    case 'academic_student':
      return `${profile.field_of_study} Student` || profile.user_id;
    case 'academic_institution':
      return profile.institution_name || profile.user_id;
    case 'organisation':
      return profile.organisation_name || profile.user_id;
    default:
      return profile.user_id;
  }
}

function formatReason(reason: string): string {
  return reason
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// Initialize with first profile
onMounted(() => {
  if (sourceProfileOptions.value.length > 0) {
    sourceProfileId.value = sourceProfileOptions.value[0].value;
  }
});
</script>

<style scoped>
.matchmaking-simulator {
  max-width: 1200px;
  margin: 0 auto;
}
</style>
