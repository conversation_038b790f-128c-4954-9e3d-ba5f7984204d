<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useNewsStore } from '../../stores/news';
import MainLayout from '../../layouts/MainLayout.vue';
// Icons component is registered globally

const route = useRoute();
const router = useRouter();
const newsStore = useNewsStore();
const loading = ref(true);
const error = ref(null);

// Fetch news data on component mount
onMounted(async () => {
  try {
    loading.value = true;
    error.value = null;
    await newsStore.fetchNews();
  } catch (err) {
    console.error('Error loading news:', err);
    error.value = 'Failed to load news data';
  } finally {
    loading.value = false;
  }
});

const currentStory = computed(() => {
  const id = Number(route.params.id);
  return newsStore.getNewsById(id);
});

const relatedStories = computed(() => {
  return newsStore.getRelatedNews(Number(route.params.id));
});

const navigateToStory = (id: number) => {
  router.push(`/news/${id}`);
};

const goBack = () => {
  router.push('/news');
};
</script>

<template>
  <main-layout>
    <q-page class="story-page q-py-xl">
      <div class="container q-mx-auto q-px-md">
        <div class="row">
          <div class="col-12">
            <!-- Back Button -->
            <q-btn
              flat
              color="primary"
              no-caps
              class="q-mb-lg"
              @click="goBack"
            >
              <icons name="arrow_back" class="q-mr-xs" />
              Back to News
            </q-btn>
          </div>

          <!-- Loading State -->
          <div v-if="loading" class="col-12 text-center q-py-xl">
            <q-spinner color="primary" size="3em" />
            <div class="q-mt-md text-body1">Loading story...</div>
          </div>

          <!-- Error State -->
          <div v-else-if="error" class="col-12 text-center q-py-xl">
            <div class="text-negative text-h6">{{ error }}</div>
            <q-btn color="primary" label="Retry" class="q-mt-md" @click="newsStore.fetchNews()" />
          </div>

          <!-- No Story Found -->
          <div v-else-if="!currentStory" class="col-12 text-center q-py-xl">
            <div class="text-h6">Story not found</div>
            <q-btn color="primary" label="Back to News" class="q-mt-md" @click="goBack" />
          </div>

          <!-- Main Story Content -->
          <div v-else class="col-md-8 col-sm-12">
            <article class="story-content q-pa-lg bg-white rounded-borders">
              <header>
                <q-chip
                  :color="currentStory.categoryColor"
                  :text-color="currentStory.textColor"
                  class="q-mb-sm"
                  size="sm"
                >
                  {{ currentStory.category }}
                </q-chip>
                <h1 class="text-h4 q-mb-md">{{ currentStory.title }}</h1>
                <div class="row items-center q-mb-lg">
                  <div class="author-info">
                    <div class="text-subtitle1 text-weight-medium">{{ currentStory.author }}</div>
                    <div class="text-caption text-grey">{{ currentStory.authorRole }}</div>
                  </div>
                  <q-space />
                  <time :datetime="currentStory.date" class="text-caption text-grey">
                    {{ new Date(currentStory.date).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    }) }}
                  </time>
                </div>
              </header>
              <div class="story-body text-body1" v-html="currentStory.content"></div>
            </article>
          </div>

          <!-- Related Stories Sidebar -->
          <div v-if="!loading && !error && currentStory" class="col-md-4 col-sm-12">
            <aside class="related-stories q-pl-md">
              <h2 class="text-h6 q-mb-md">Related Stories</h2>
              <div v-if="relatedStories && relatedStories.length > 0" class="row q-col-gutter-md">
                <div v-for="story in relatedStories" :key="story.id" class="col-12">
                  <q-card
                    flat
                    bordered
                    class="related-story-card cursor-pointer"
                    @click="navigateToStory(story.id)"
                  >
                    <q-card-section>
                      <q-chip
                        :color="story.categoryColor"
                        :text-color="story.textColor"
                        class="q-mb-sm"
                        size="sm"
                      >
                        {{ story.category }}
                      </q-chip>
                      <h3 class="text-subtitle1 q-mb-sm">{{ story.title }}</h3>
                      <p class="text-caption text-grey-8 q-mb-none">{{ story.excerpt }}</p>
                    </q-card-section>
                  </q-card>
                </div>
              </div>
              <div v-else class="text-center q-pa-md">
                <p class="text-grey">No related stories found</p>
              </div>
            </aside>
          </div>
        </div>
      </div>
    </q-page>
  </main-layout>
</template>

<style scoped>
.container {
  max-width: 1400px;
  margin: 0 auto;
}

.story-content {
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.story-body {
  line-height: 1.8;
}

.story-body p {
  margin-bottom: 1.5rem;
}

.related-story-card {
  transition: all 0.3s ease;
}

.related-story-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

@media (max-width: 767px) {
  .related-stories {
    padding-left: 0;
    margin-top: 2rem;
  }

  .story-page {
    padding: 40px 0;
  }
}
</style>