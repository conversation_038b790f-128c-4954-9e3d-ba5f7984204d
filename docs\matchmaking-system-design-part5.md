# Matchmaking System Design Document - Part 5

## 18. Enhanced Profile-to-Profile Matching

### 18.1 Additional Profile Type Combinations

The following section details additional profile type combinations that extend the existing matchmaking system:

#### 18.1.1 Academic Student Matching

- **Academic Student → Mentor**
  - **Matching Criteria**:
    - Academic expertise (student.field_of_study ↔ mentor.expertise_areas)
    - Career guidance (student.career_interests ↔ mentor.industry_experience)
    - Research compatibility (student.research_interests ↔ mentor.research_interests)
    - Learning preferences (student.learning_preferences ↔ mentor.mentoring_approach)
  - **Weights**:
    - Academic expertise: 0.3
    - Career guidance: 0.3
    - Research: 0.2
    - Learning preferences: 0.2

- **Academic Student → Academic Institution**
  - **Matching Criteria**:
    - Field of study (student.field_of_study ↔ institution.programs_offered)
    - Research interests (student.research_interests ↔ institution.research_areas)
    - Program compatibility (student.degree_interests ↔ institution.degree_programs)
    - Geographic considerations (student.preferred_locations ↔ institution.location)
  - **Weights**:
    - Field of study: 0.4
    - Research: 0.3
    - Program: 0.2
    - Location: 0.1

#### 18.1.2 Professional Matching

- **Professional → Innovator**
  - **Matching Criteria**:
    - Skills alignment (professional.skills ↔ innovator.current_challenges)
    - Industry match (professional.industry ↔ innovator.industry)
    - Service compatibility (professional.services_offered ↔ innovator.looking_for)
  - **Weights**:
    - Skills: 0.4
    - Industry: 0.3
    - Service: 0.3

- **Professional → Organisation**
  - **Matching Criteria**:
    - Skills needs (professional.skills ↔ organisation.skill_requirements)
    - Industry alignment (professional.industry ↔ organisation.industry_focus)
    - Role compatibility (professional.job_interests ↔ organisation.available_roles)
  - **Weights**:
    - Skills: 0.4
    - Industry: 0.3
    - Role: 0.3

#### 18.1.3 Industry Expert Matching

- **Industry Expert → Innovator**
  - **Matching Criteria**:
    - Domain expertise (expert.expertise_areas ↔ innovator.industry)
    - Knowledge needs (expert.specialized_knowledge ↔ innovator.current_challenges)
    - Advisory fit (expert.advisory_style ↔ innovator.looking_for)
  - **Weights**:
    - Domain: 0.4
    - Knowledge: 0.3
    - Advisory: 0.3

- **Industry Expert → Organisation**
  - **Matching Criteria**:
    - Industry alignment (expert.industry_experience ↔ organisation.industry_focus)
    - Expertise needs (expert.expertise_areas ↔ organisation.expertise_requirements)
    - Consulting compatibility (expert.consulting_interests ↔ organisation.consulting_needs)
  - **Weights**:
    - Industry: 0.4
    - Expertise: 0.3
    - Consulting: 0.3

### 18.2 Enhanced Content Types for Profile-to-Content Matching

In addition to the content types already documented (Events, Opportunities, Resources), the following content types should be considered for profile-to-content matching:

#### 18.2.1 Group Matching

- **Match Groups to Profiles**:
  - Topic alignment
  - Industry relevance
  - Activity level
  - Member compatibility
  - Geographic proximity

- **Group Matching Weights by Profile Type**:
  - Innovator: Topic (0.3), Industry (0.3), Activity (0.2), Members (0.2)
  - Investor: Industry (0.4), Dealflow (0.3), Members (0.2), Activity (0.1)
  - Professional: Industry (0.3), Networking (0.3), Topic (0.2), Activity (0.2)
  - Academic: Research (0.4), Collaboration (0.3), Topic (0.2), Members (0.1)

#### 18.2.2 Marketplace Listing Matching

- **Match Marketplace Listings to Profiles**:
  - Service/product relevance
  - Price compatibility
  - Quality rating
  - Provider reputation
  - Geographic availability

- **Marketplace Matching Weights by Profile Type**:
  - Innovator: Relevance (0.4), Price (0.3), Quality (0.2), Location (0.1)
  - Professional: Relevance (0.3), Price (0.3), Quality (0.2), Provider (0.2)
  - Organisation: Relevance (0.4), Price (0.3), Provider (0.2), Location (0.1)

#### 18.2.3 Blog Post Matching

- **Match Blog Posts to Profiles**:
  - Topic relevance
  - Industry alignment
  - Recency
  - Reading level
  - Author credibility

- **Blog Post Matching Weights by Profile Type**:
  - Innovator: Topic (0.4), Industry (0.3), Recency (0.2), Author (0.1)
  - Investor: Industry (0.4), Topic (0.3), Author (0.2), Recency (0.1)
  - Professional: Topic (0.4), Industry (0.3), Recency (0.2), Reading Level (0.1)
  - Academic: Topic (0.5), Research (0.3), Author (0.1), Recency (0.1)

## 19. Matchmaking Simulation Environment

### 19.1 Purpose

The matchmaking simulation environment serves several key purposes:

1. **Testing Environment**: Provides a safe environment to test matching algorithms without affecting production data
2. **Algorithm Refinement**: Allows rapid iteration and refinement of matching algorithms
3. **Visualization**: Offers visual feedback on match quality and scoring
4. **Comprehensive Testing**: Enables testing of all profile type combinations, including those not yet implemented
5. **Development Tool**: Serves as a development tool for implementing new profile matchers

### 19.2 Architecture

The simulation architecture consists of several components:

#### 19.2.1 Simulator Module (`src/lib/matchmaking/simulator.ts`)

This core module provides:
- Mock profile data for all profile types
- Simulated matching rules
- Functions to simulate database operations
- Helper functions for profile display and formatting

```typescript
// Key functions
getSimulatedProfiles(profileType): any[]
getSimulatedMatchingRules(sourceType, targetType): MatchmakingRule[]
getSimulatedMatchableProfileTypes(profileType): ProfileType[]
```

#### 19.2.2 Field Access Utilities (`src/lib/matchmaking/fieldAccessUtils.ts`)

Provides safe methods for accessing profile fields:
- Handles missing or null fields
- Supports nested field access with dot notation
- Converts between different data types

```typescript
// Key functions
getProfileField(profile, path, defaultValue): any
getProfileArrayField(profile, path): any[]
getProfileStringField(profile, path): string
getProfileNumericField(profile, path): number
```

#### 19.2.3 Matchmaking Service with Simulation Mode

The existing matchmaking service is enhanced with simulation capabilities:
- Toggle between simulation and production modes
- Use mock data when in simulation mode
- Maintain the same API for both modes

#### 19.2.4 Simulator UI Component (`src/components/matchmaking/MatchmakingSimulator.vue`)

A Vue component that provides:
- Profile type and profile selection
- Match generation controls
- Visualization of match results and scores
- Detailed breakdown of match reasons

### 19.3 Mock Data Structure

The simulation includes mock data for all profile types, with realistic attributes that match the production database schema. Each profile type has multiple mock profiles with different characteristics to test various matching scenarios.

### 19.4 Implementation Steps

1. **Create the Simulator Module**
   - Define mock profiles for all profile types
   - Implement the simulation function
   - Add helper functions for profile name formatting, etc.

2. **Create Field Access Utilities**
   - Implement safe field access functions
   - Test with various profile structures

3. **Implement Additional Profile Matchers**
   - Start with the most important profile combinations
   - Use the field access utilities
   - Follow the pattern of existing matchers

4. **Create the Simulator Component**
   - Build the UI for profile selection
   - Implement the match generation logic
   - Create visualizations for match scores and reasons

5. **Test and Refine**
   - Test all profile combinations
   - Adjust matching algorithms based on results
   - Fine-tune weights and scoring methods

### 19.5 Usage Guide

1. Navigate to `/matchmaking-simulator` in the application
2. Select a source profile type (e.g., "Innovator")
3. Select a specific source profile
4. Select a target profile type (e.g., "Investor")
5. Click "Generate Matches"
6. View the resulting matches sorted by score
7. Hover over the score to see the breakdown of match reasons

### 19.6 Benefits

1. **Risk Reduction**: Test changes without affecting production data
2. **Rapid Iteration**: Quickly test and refine matching algorithms
3. **Comprehensive Testing**: Test all profile type combinations
4. **Visual Feedback**: See the impact of algorithm changes immediately
5. **Development Efficiency**: Develop new matchers without database dependencies

## 20. Implementation Status

### 20.1 Matchmaking Simulation Environment Implementation

The matchmaking simulation environment has been successfully implemented with the following components:

#### 20.1.1 Field Access Utilities (`src/lib/matchmaking/fieldAccessUtils.ts`)

- Implemented safe methods for accessing profile fields
- Added support for handling missing or null fields
- Added support for nested field access with dot notation
- Implemented type conversion utilities for arrays, strings, numbers, booleans, and dates

#### 20.1.2 Simulator Module (`src/lib/matchmaking/simulator.ts`)

- Created mock profiles for all profile types with realistic attributes
- Implemented simulated matching rules for innovator-investor and innovator-mentor combinations
- Added functions to simulate database operations:
  - `getSimulatedProfiles(profileType)`
  - `getSimulatedMatchingRules(sourceType, targetType)`
  - `getSimulatedMatchableProfileTypes(profileType)`
  - `simulateMatching(sourceType, sourceProfileId, targetType)`

#### 20.1.3 Enhanced Matchmaking Service (`src/lib/matchmaking/matchmakingService.ts`)

- Added simulation mode toggle with `setSimulationMode(enabled)` and `isSimulationMode()`
- Modified core methods to support simulation mode:
  - `getMatchingRules(sourceType, targetType)`
  - `getMatchableProfileTypes(profileType)`
  - `generateMatches(userId, profileType)`
  - `matchProfileToProfileType(sourceProfile, sourceType, targetType)`
- Maintained the same API for both real and simulated operations

#### 20.1.4 Matchmaking Store Integration (`src/stores/matchmaking.ts`)

- Added simulation mode support to the store
- Added methods for generating simulated matches
- Implemented `setSimulationMode(enabled)` and `generateSimulatedMatches(userId, sourceType, targetType)`
- Added `isSimulationMode` computed property

#### 20.1.5 Simulator UI Component (`src/components/matchmaking/MatchmakingSimulator.vue`)

- Created a dedicated Vue component for the simulation interface
- Implemented profile type and profile selection
- Added match generation controls
- Created visualizations for match results and scores
- Added detailed breakdown of match reasons with tooltips

#### 20.1.6 Routing and Navigation

- Added a route for the matchmaking simulator at `/dashboard/matchmaking-simulator`
- Added a link to the simulator from the main matchmaking page
- Configured proper authentication requirements

### 20.2 Current Limitations

- Only innovator-investor and innovator-mentor matching algorithms are fully implemented
- The simulation environment does not yet support profile-to-content matching
- Match reason generation is basic and could be enhanced with more detailed explanations
- The UI could be improved with more interactive visualizations and filtering options

## 21. Next Steps

1. Complete implementation of all profile-to-profile matching combinations
2. Implement profile-to-content matching algorithms for all content types
3. Enhance the simulation environment to support content matching
4. Develop dashboard component population logic
5. Create UI components for displaying matches
6. Implement match feedback and refinement mechanisms
7. Integrate with recommendation system
8. Implement performance optimizations for large-scale matching
