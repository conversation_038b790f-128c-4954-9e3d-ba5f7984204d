// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

const SUPABASE_URL = Deno.env.get('ZB_SUPABASE_URL') || 'https://dpicnvisvxpmgjtbeicf.supabase.co'
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get('ZB_SUPABASE_SERVICE_ROLE_KEY') || ''
const FUNCTION_URL = `${SUPABASE_URL}/functions/v1/send-profile-reminder`

serve(async (req) => {
  // This function is meant to be triggered by a cron job
  // It will call the send-profile-reminder function to send emails

  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create a Supabase client with the service role key
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

    // Get the authorization header from the current request
    const authHeader = req.headers.get('Authorization') || ''

    // Call the send-profile-reminder function
    const response = await fetch(FUNCTION_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader
      },
      body: JSON.stringify({
        // We can pass additional parameters here if needed
        scheduled: true,
        timestamp: new Date().toISOString()
      })
    })

    // Get the response data
    const data = await response.json()

    // Log the execution
    await supabase
      .from('cron_logs')
      .insert({
        function_name: 'profile-reminder-cron',
        status: response.ok ? 'success' : 'error',
        result: data,
        executed_at: new Date().toISOString()
      })

    // Return the response
    return new Response(
      JSON.stringify(data),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: response.status
      }
    )

  } catch (error) {
    console.error('Error executing profile reminder cron:', error)

    // Create a Supabase client with the service role key
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

    // Log the error
    await supabase
      .from('cron_logs')
      .insert({
        function_name: 'profile-reminder-cron',
        status: 'error',
        result: { error: error.message || 'Unknown error' },
        executed_at: new Date().toISOString()
      })

    return new Response(
      JSON.stringify({ error: error.message || 'Internal server error' }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
    )
  }
})
