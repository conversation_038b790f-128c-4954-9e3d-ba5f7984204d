import { supabase } from './supabase';
import { useNotificationStore } from '../stores/notifications';

/**
 * Applies a migration file to the database
 * @param migrationName The name of the migration file
 * @param sql The SQL content of the migration file
 * @returns A promise that resolves to a success/error message
 */
export async function applyMigration(migrationName: string, sql: string): Promise<{ success: boolean; message: string }> {
  try {
    console.log(`Applying migration: ${migrationName}`);

    const { error } = await supabase.rpc('exec_sql', { sql });

    if (error) {
      console.error(`Error applying migration ${migrationName}:`, error.message);
      return { success: false, message: error.message };
    }

    return { success: true, message: `Migration ${migrationName} applied successfully` };
  } catch (err: any) {
    console.error(`Error applying migration ${migrationName}:`, err.message);
    return { success: false, message: err.message };
  }
}

/**
 * Applies all migrations in order
 * @returns A promise that resolves to a success/error message
 */
export async function applyAllMigrations(): Promise<{ success: boolean; message: string }> {
  const notifications = useNotificationStore();

  try {
    // Migration 1: Create base tables
    const migration1 = await applyMigration('create_base_tables', `
      -- Enable UUID extension
      CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

      -- Create profiles table if it doesn't exist
      CREATE TABLE IF NOT EXISTS public.profiles (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
          first_name TEXT,
          last_name TEXT,
          email TEXT,
          phone TEXT,
          bio TEXT,
          avatar_url TEXT,
          profile_type TEXT,
          profile_state TEXT DEFAULT 'IN_PROGRESS',
          profile_visibility TEXT DEFAULT 'public',
          profile_completion INTEGER DEFAULT 0,
          hear_about_us TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      -- Enable Row Level Security on profiles
      ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

      -- Create RLS policies for profiles
      CREATE POLICY "Users can view their own profiles"
          ON public.profiles FOR SELECT
          USING (user_id = auth.uid());

      CREATE POLICY "Users can update their own profiles"
          ON public.profiles FOR UPDATE
          USING (user_id = auth.uid());

      CREATE POLICY "Users can insert their own profiles"
          ON public.profiles FOR INSERT
          WITH CHECK (user_id = auth.uid());

      CREATE POLICY "Users can delete their own profiles"
          ON public.profiles FOR DELETE
          USING (user_id = auth.uid());

      -- Grant permissions to authenticated users
      GRANT ALL ON public.profiles TO authenticated;
      GRANT ALL ON public.profiles TO service_role;
    `);

    if (!migration1.success) {
      return migration1;
    }

    // Migration 2: Create innovator profiles table
    const migration2 = await applyMigration('create_innovator_profiles', `
      -- Create the innovator_profiles table if it doesn't exist
      CREATE TABLE IF NOT EXISTS public.innovator_profiles (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          profile_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,

          -- Basic Innovation Information
          innovation_area TEXT,
          innovation_stage TEXT,
          innovation_description TEXT,

          -- Team Information
          team_size INTEGER,
          team_description TEXT,

          -- Funding Information
          funding_needs BOOLEAN DEFAULT false,
          funding_amount NUMERIC,
          funding_stage TEXT,

          -- Prototype Information
          has_prototype BOOLEAN DEFAULT false,
          prototype_description TEXT,

          -- Goals and Challenges
          goals TEXT[],
          challenges TEXT[],

          -- Online Presence
          website TEXT,
          social_links JSONB DEFAULT '{}'::jsonb,

          -- Contact Information
          contact_email TEXT,
          contact_phone TEXT,
          contact_address TEXT,

          -- Bio and Achievements
          bio TEXT,
          achievements TEXT[],
          awards TEXT[],

          -- Additional Information
          target_market TEXT,
          business_model TEXT,
          competitive_advantage TEXT,
          intellectual_property TEXT,
          sustainability_impact TEXT,

          -- Timestamps
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      -- Enable Row Level Security on the table
      ALTER TABLE public.innovator_profiles ENABLE ROW LEVEL SECURITY;

      -- Create RLS policies for the innovator_profiles table
      CREATE POLICY "Users can view their own innovator profiles"
          ON public.innovator_profiles FOR SELECT
          USING ((SELECT user_id FROM profiles WHERE id = profile_id) = auth.uid());

      CREATE POLICY "Users can update their own innovator profiles"
          ON public.innovator_profiles FOR UPDATE
          USING ((SELECT user_id FROM profiles WHERE id = profile_id) = auth.uid());

      CREATE POLICY "Users can insert their own innovator profiles"
          ON public.innovator_profiles FOR INSERT
          WITH CHECK ((SELECT user_id FROM profiles WHERE id = profile_id) = auth.uid());

      CREATE POLICY "Users can delete their own innovator profiles"
          ON public.innovator_profiles FOR DELETE
          USING ((SELECT user_id FROM profiles WHERE id = profile_id) = auth.uid());

      -- Grant permissions to authenticated users
      GRANT ALL ON public.innovator_profiles TO authenticated;
      GRANT ALL ON public.innovator_profiles TO service_role;
    `);

    if (!migration2.success) {
      return migration2;
    }

    // Migration 3: Create profile visibility function
    const migration3 = await applyMigration('create_profile_visibility_function', `
      -- Function to check if a profile is visible to the current user
      CREATE OR REPLACE FUNCTION public.is_profile_visible(profile_id UUID)
      RETURNS BOOLEAN AS $$
      DECLARE
          visibility TEXT;
          profile_user_id UUID;
      BEGIN
          -- Get the visibility and user_id from the profiles table
          SELECT profile_visibility, user_id INTO visibility, profile_user_id
          FROM profiles
          WHERE id = profile_id;

          -- If the profile belongs to the current user, it's always visible
          IF profile_user_id = auth.uid() THEN
              RETURN TRUE;
          END IF;

          -- Check visibility settings
          CASE visibility
              WHEN 'public' THEN
                  RETURN TRUE;
              WHEN 'private' THEN
                  RETURN FALSE;
              WHEN 'connections_only' THEN
                  -- For now, return false - in the future, check connections
                  RETURN FALSE;
              ELSE
                  RETURN FALSE;
          END CASE;
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;

      -- Create a policy to allow users to view profiles based on visibility
      CREATE POLICY "Users can view visible profiles"
          ON public.profiles FOR SELECT
          USING (
              user_id = auth.uid() OR
              (profile_visibility = 'public') OR
              (profile_visibility = 'connections_only' AND
              -- This is a placeholder for future connection checking logic
              false)
          );

      -- Create a policy to allow users to view visible innovator profiles
      CREATE POLICY "Users can view visible innovator profiles"
          ON public.innovator_profiles FOR SELECT
          USING (is_profile_visible(profile_id));
    `);

    if (!migration3.success) {
      return migration3;
    }

    // Migration 4: Create triggers
    const migration4 = await applyMigration('create_triggers', `
      -- Function to update the updated_at timestamp
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
          NEW.updated_at = NOW();
          RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      -- Create trigger for profiles table
      DROP TRIGGER IF EXISTS update_profiles_updated_at ON public.profiles;
      CREATE TRIGGER update_profiles_updated_at
      BEFORE UPDATE ON public.profiles
      FOR EACH ROW
      EXECUTE FUNCTION update_updated_at_column();

      -- Create trigger for innovator_profiles table
      DROP TRIGGER IF EXISTS update_innovator_profiles_updated_at ON public.innovator_profiles;
      CREATE TRIGGER update_innovator_profiles_updated_at
      BEFORE UPDATE ON public.innovator_profiles
      FOR EACH ROW
      EXECUTE FUNCTION update_updated_at_column();
    `);

    if (!migration4.success) {
      return migration4;
    }

    // Migration 5: Create profile types and additional profile tables
    const migration5 = await applyMigration('create_profile_types_tables', `
      -- Create profile_types table if it doesn't exist
      CREATE TABLE IF NOT EXISTS public.profile_types (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name VARCHAR(50) NOT NULL UNIQUE,
        display_name VARCHAR(100) NOT NULL,
        description TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      -- Insert profile types if they don't exist
      INSERT INTO public.profile_types (name, display_name, description)
      VALUES
        ('innovator', 'Innovator', 'Entrepreneurs and startups with innovative ideas')
      ON CONFLICT (name) DO NOTHING;

      INSERT INTO public.profile_types (name, display_name, description)
      VALUES
        ('investor', 'Business Investor', 'Investors looking to fund innovative projects')
      ON CONFLICT (name) DO NOTHING;

      INSERT INTO public.profile_types (name, display_name, description)
      VALUES
        ('mentor', 'Mentor', 'Experienced professionals offering guidance')
      ON CONFLICT (name) DO NOTHING;

      INSERT INTO public.profile_types (name, display_name, description)
      VALUES
        ('professional', 'Professional', 'Industry professionals seeking opportunities')
      ON CONFLICT (name) DO NOTHING;

      INSERT INTO public.profile_types (name, display_name, description)
      VALUES
        ('industry_expert', 'Industry Expert', 'Specialists with deep domain knowledge')
      ON CONFLICT (name) DO NOTHING;

      INSERT INTO public.profile_types (name, display_name, description)
      VALUES
        ('academic_student', 'Academic Student', 'Students looking for research or career opportunities')
      ON CONFLICT (name) DO NOTHING;

      INSERT INTO public.profile_types (name, display_name, description)
      VALUES
        ('academic_institution', 'Academic Institution', 'Universities and research institutions')
      ON CONFLICT (name) DO NOTHING;

      INSERT INTO public.profile_types (name, display_name, description)
      VALUES
        ('organisation', 'Organisation', 'Companies and organizations seeking innovation')
      ON CONFLICT (name) DO NOTHING;

      -- Create investor_profiles table if it doesn't exist
      CREATE TABLE IF NOT EXISTS public.investor_profiles (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
        profile_type_id UUID NOT NULL REFERENCES public.profile_types(id),
        firm_name VARCHAR(255),
        investment_focus TEXT[],
        investment_stages TEXT[],
        ticket_size VARCHAR(50),
        portfolio TEXT[],
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(profile_id)
      );

      -- Create mentor_profiles table if it doesn't exist
      CREATE TABLE IF NOT EXISTS public.mentor_profiles (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
        profile_type_id UUID NOT NULL REFERENCES public.profile_types(id),
        areas_of_expertise TEXT[],
        industry_experience TEXT[],
        years_of_experience INTEGER,
        mentoring_experience TEXT,
        mentoring_approach TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(profile_id)
      );

      -- Create professional_profiles table if it doesn't exist
      CREATE TABLE IF NOT EXISTS public.professional_profiles (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
        profile_type_id UUID NOT NULL REFERENCES public.profile_types(id),
        company VARCHAR(255),
        job_title VARCHAR(255),
        industry TEXT[],
        skills TEXT[],
        experience_years INTEGER,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(profile_id)
      );

      -- Create industry_expert_profiles table if it doesn't exist
      CREATE TABLE IF NOT EXISTS public.industry_expert_profiles (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
        profile_type_id UUID NOT NULL REFERENCES public.profile_types(id),
        areas_of_expertise TEXT[],
        industry TEXT[],
        experience_years INTEGER,
        publications TEXT[],
        speaking_engagements TEXT[],
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(profile_id)
      );

      -- Create academic_student_profiles table if it doesn't exist
      CREATE TABLE IF NOT EXISTS public.academic_student_profiles (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
        profile_type_id UUID NOT NULL REFERENCES public.profile_types(id),
        institution VARCHAR(255),
        degree_program VARCHAR(255),
        field_of_study VARCHAR(255),
        graduation_year INTEGER,
        research_interests TEXT[],
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(profile_id)
      );

      -- Create academic_institution_profiles table if it doesn't exist
      CREATE TABLE IF NOT EXISTS public.academic_institution_profiles (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
        profile_type_id UUID NOT NULL REFERENCES public.profile_types(id),
        institution_name VARCHAR(255),
        institution_type VARCHAR(50),
        departments TEXT[],
        research_areas TEXT[],
        website VARCHAR(255),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(profile_id)
      );

      -- Create organisation_profiles table if it doesn't exist
      CREATE TABLE IF NOT EXISTS public.organisation_profiles (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
        profile_type_id UUID NOT NULL REFERENCES public.profile_types(id),
        organisation_name VARCHAR(255),
        organisation_type VARCHAR(50),
        industry TEXT[],
        size VARCHAR(50),
        website VARCHAR(255),
        mission_statement TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(profile_id)
      );

      -- Add RLS policies for each profile type table
      ALTER TABLE public.investor_profiles ENABLE ROW LEVEL SECURITY;
      ALTER TABLE public.mentor_profiles ENABLE ROW LEVEL SECURITY;
      ALTER TABLE public.professional_profiles ENABLE ROW LEVEL SECURITY;
      ALTER TABLE public.industry_expert_profiles ENABLE ROW LEVEL SECURITY;
      ALTER TABLE public.academic_student_profiles ENABLE ROW LEVEL SECURITY;
      ALTER TABLE public.academic_institution_profiles ENABLE ROW LEVEL SECURITY;
      ALTER TABLE public.organisation_profiles ENABLE ROW LEVEL SECURITY;

      -- Create policies for investor_profiles
      CREATE POLICY "Users can view their own investor profile"
      ON public.investor_profiles FOR SELECT
      USING (
        profile_id IN (
          SELECT id FROM public.profiles
          WHERE user_id = auth.uid()
        )
      );

      CREATE POLICY "Users can update their own investor profile"
      ON public.investor_profiles FOR UPDATE
      USING (
        profile_id IN (
          SELECT id FROM public.profiles
          WHERE user_id = auth.uid()
        )
      );

      CREATE POLICY "Users can insert their own investor profile"
      ON public.investor_profiles FOR INSERT
      WITH CHECK (
        profile_id IN (
          SELECT id FROM public.profiles
          WHERE user_id = auth.uid()
        )
      );

      -- Create policies for mentor_profiles
      CREATE POLICY "Users can view their own mentor profile"
      ON public.mentor_profiles FOR SELECT
      USING (
        profile_id IN (
          SELECT id FROM public.profiles
          WHERE user_id = auth.uid()
        )
      );

      CREATE POLICY "Users can update their own mentor profile"
      ON public.mentor_profiles FOR UPDATE
      USING (
        profile_id IN (
          SELECT id FROM public.profiles
          WHERE user_id = auth.uid()
        )
      );

      CREATE POLICY "Users can insert their own mentor profile"
      ON public.mentor_profiles FOR INSERT
      WITH CHECK (
        profile_id IN (
          SELECT id FROM public.profiles
          WHERE user_id = auth.uid()
        )
      );

      -- Create policies for professional_profiles
      CREATE POLICY "Users can view their own professional profile"
      ON public.professional_profiles FOR SELECT
      USING (
        profile_id IN (
          SELECT id FROM public.profiles
          WHERE user_id = auth.uid()
        )
      );

      CREATE POLICY "Users can update their own professional profile"
      ON public.professional_profiles FOR UPDATE
      USING (
        profile_id IN (
          SELECT id FROM public.profiles
          WHERE user_id = auth.uid()
        )
      );

      CREATE POLICY "Users can insert their own professional profile"
      ON public.professional_profiles FOR INSERT
      WITH CHECK (
        profile_id IN (
          SELECT id FROM public.profiles
          WHERE user_id = auth.uid()
        )
      );

      -- Create policies for industry_expert_profiles
      CREATE POLICY "Users can view their own industry expert profile"
      ON public.industry_expert_profiles FOR SELECT
      USING (
        profile_id IN (
          SELECT id FROM public.profiles
          WHERE user_id = auth.uid()
        )
      );

      CREATE POLICY "Users can update their own industry expert profile"
      ON public.industry_expert_profiles FOR UPDATE
      USING (
        profile_id IN (
          SELECT id FROM public.profiles
          WHERE user_id = auth.uid()
        )
      );

      CREATE POLICY "Users can insert their own industry expert profile"
      ON public.industry_expert_profiles FOR INSERT
      WITH CHECK (
        profile_id IN (
          SELECT id FROM public.profiles
          WHERE user_id = auth.uid()
        )
      );

      -- Create policies for academic_student_profiles
      CREATE POLICY "Users can view their own academic student profile"
      ON public.academic_student_profiles FOR SELECT
      USING (
        profile_id IN (
          SELECT id FROM public.profiles
          WHERE user_id = auth.uid()
        )
      );

      CREATE POLICY "Users can update their own academic student profile"
      ON public.academic_student_profiles FOR UPDATE
      USING (
        profile_id IN (
          SELECT id FROM public.profiles
          WHERE user_id = auth.uid()
        )
      );

      CREATE POLICY "Users can insert their own academic student profile"
      ON public.academic_student_profiles FOR INSERT
      WITH CHECK (
        profile_id IN (
          SELECT id FROM public.profiles
          WHERE user_id = auth.uid()
        )
      );

      -- Create policies for academic_institution_profiles
      CREATE POLICY "Users can view their own academic institution profile"
      ON public.academic_institution_profiles FOR SELECT
      USING (
        profile_id IN (
          SELECT id FROM public.profiles
          WHERE user_id = auth.uid()
        )
      );

      CREATE POLICY "Users can update their own academic institution profile"
      ON public.academic_institution_profiles FOR UPDATE
      USING (
        profile_id IN (
          SELECT id FROM public.profiles
          WHERE user_id = auth.uid()
        )
      );

      CREATE POLICY "Users can insert their own academic institution profile"
      ON public.academic_institution_profiles FOR INSERT
      WITH CHECK (
        profile_id IN (
          SELECT id FROM public.profiles
          WHERE user_id = auth.uid()
        )
      );

      -- Create policies for organisation_profiles
      CREATE POLICY "Users can view their own organisation profile"
      ON public.organisation_profiles FOR SELECT
      USING (
        profile_id IN (
          SELECT id FROM public.profiles
          WHERE user_id = auth.uid()
        )
      );

      CREATE POLICY "Users can update their own organisation profile"
      ON public.organisation_profiles FOR UPDATE
      USING (
        profile_id IN (
          SELECT id FROM public.profiles
          WHERE user_id = auth.uid()
        )
      );

      CREATE POLICY "Users can insert their own organisation profile"
      ON public.organisation_profiles FOR INSERT
      WITH CHECK (
        profile_id IN (
          SELECT id FROM public.profiles
          WHERE user_id = auth.uid()
        )
      );

      -- Add profile_state enum type if it doesn't exist
      DO $$
      BEGIN
          IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'profile_state_enum') THEN
              CREATE TYPE profile_state_enum AS ENUM ('in_progress', 'active', 'disabled', 'banned');
          END IF;
      END$$;

      -- Add profile_state column to profiles table if it doesn't exist
      DO $$
      BEGIN
          IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'profile_state') THEN
              ALTER TABLE public.profiles ADD COLUMN profile_state profile_state_enum DEFAULT 'in_progress';
          END IF;
      END$$;

      -- Create triggers for all profile tables
      DO $$
      DECLARE
          tables TEXT[] := ARRAY['investor_profiles', 'mentor_profiles', 'professional_profiles',
                                'industry_expert_profiles', 'academic_student_profiles',
                                'academic_institution_profiles', 'organisation_profiles'];
          t TEXT;
      BEGIN
          FOREACH t IN ARRAY tables
          LOOP
              EXECUTE format('
                  DROP TRIGGER IF EXISTS update_%s_updated_at ON public.%s;
                  CREATE TRIGGER update_%s_updated_at
                  BEFORE UPDATE ON public.%s
                  FOR EACH ROW
                  EXECUTE FUNCTION public.update_updated_at_column();
              ', t, t, t, t);
          END LOOP;
      END$$;
    `);

    if (!migration5.success) {
      return migration5;
    }

    // Refresh schema cache
    const refreshResult = await supabase.rpc('exec_sql', {
      sql: 'SELECT pg_catalog.set_config(\'search_path\', \'public\', false);'
    });

    if (refreshResult.error) {
      console.error('Error refreshing schema cache:', refreshResult.error.message);
      return { success: false, message: refreshResult.error.message };
    }

    notifications.success('All migrations applied successfully');
    return { success: true, message: 'All migrations applied successfully' };
  } catch (err: any) {
    console.error('Error applying migrations:', err.message);
    notifications.error(`Error applying migrations: ${err.message}`);
    return { success: false, message: err.message };
  }
}
