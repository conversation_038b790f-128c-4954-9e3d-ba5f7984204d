// Supabase Edge Function for sending emails using Resend
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { corsHeaders } from '../_shared/cors.ts'

// Types for email data
interface EmailData {
  to: string
  subject: string
  html: string
  text?: string
  from?: {
    email: string
    name: string
  }
}

// Types for email request
interface EmailRequest {
  type: string
  data: {
    to: string
    firstName?: string
    lastName?: string
    subject?: string
    customHtml?: string
    customText?: string
    verificationLink?: string
    profileLink?: string
    profileCompletion?: number
  }
}

// Resend API configuration
const RESEND_API_KEY = Deno.env.get('RESEND_KEY')
const DEFAULT_FROM_EMAIL = Deno.env.get('SENDGRID_FROM_EMAIL') || '<EMAIL>'
const DEFAULT_FROM_NAME = Deno.env.get('SENDGRID_FROM_NAME') || 'ZB Innovation Hub'

// Function to send email via Resend
async function sendEmail(emailData: EmailData): Promise<Response> {
  if (!RESEND_API_KEY) {
    console.error('Resend API key is not configured')
    throw new Error('Resend API key is not configured')
  }

  console.log('Sending email with Resend:', {
    to: emailData.to,
    subject: emailData.subject,
    from: emailData.from || {
      email: DEFAULT_FROM_EMAIL,
      name: DEFAULT_FROM_NAME
    }
  })

  const url = 'https://api.resend.com/emails'
  
  // Prepare the payload for Resend
  const payload = {
    from: `${emailData.from?.name || DEFAULT_FROM_NAME} <${emailData.from?.email || DEFAULT_FROM_EMAIL}>`,
    to: [emailData.to],
    subject: emailData.subject,
    html: emailData.html,
    text: emailData.text || stripHtml(emailData.html)
  }

  console.log('Resend request payload:', JSON.stringify({
    ...payload,
    html: payload.html.length > 100 ? `${payload.html.substring(0, 100)}... (truncated)` : payload.html,
    text: payload.text.length > 100 ? `${payload.text.substring(0, 100)}... (truncated)` : payload.text
  }))

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${RESEND_API_KEY}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(payload)
  })

  if (!response.ok) {
    let errorMessage = `Failed to send email: ${response.status} ${response.statusText}`
    try {
      const errorData = await response.json()
      errorMessage = `${errorMessage} - ${JSON.stringify(errorData)}`
    } catch (e) {
      // Ignore JSON parsing error
    }
    console.error('Resend API error:', errorMessage)
    throw new Error(errorMessage)
  }

  console.log('Email sent successfully')
  return response
}

// Function to extract name from email
function extractNameFromEmail(email: string): string | undefined {
  if (!email) return undefined

  // Get the part before the @ symbol
  const localPart = email.split('@')[0]

  // Remove numbers and special characters
  const cleanedName = localPart.replace(/[0-9_.-]/g, ' ')

  // Capitalize first letter of each word
  const words = cleanedName.split(' ')
    .filter(word => word.length > 0)
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())

  // Return the first word if it exists
  return words.length > 0 ? words[0] : undefined
}

// Function to strip HTML tags for plain text version
function stripHtml(html: string): string {
  return html
    .replace(/<[^>]*>/g, '')
    .replace(/\s+/g, ' ')
    .trim()
}

// Function to generate welcome email HTML
function generateWelcomeEmail(email: string, firstName?: string): { html: string, subject: string } {
  // Try to extract name from email if not provided
  const extractedName = !firstName ? extractNameFromEmail(email) : undefined
  const greeting = firstName ? `Hi ${firstName},` : extractedName ? `Hi ${extractedName},` : 'Hi there,'

  const subject = 'Welcome to ZB Innovation Hub!'

  const html = `
    <div style="max-width: 600px; margin: 0 auto; padding: 24px; font-family: Arial, sans-serif;">
      <div style="text-align: center; margin-bottom: 24px;">
        <img src="https://zbinnovation.com/logo.png" alt="ZB Innovation Hub" style="max-width: 200px;">
      </div>

      <h1 style="color: #0D8A3E; margin-bottom: 16px;">${greeting}</h1>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        Welcome to the ZB Innovation Hub! We're excited to have you join our community of innovators,
        investors, mentors, and industry experts.
      </p>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        To get started, please complete your profile to help us connect you with the right opportunities
        and resources. The more information you provide, the better we can serve you.
      </p>

      <div style="text-align: center; margin: 32px 0;">
        <a href="https://zbinnovation.com/dashboard"
           style="background-color: #0D8A3E; color: white; padding: 12px 24px; text-decoration: none;
                  border-radius: 4px; font-weight: bold;">
          Complete Your Profile
        </a>
      </div>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        If you have any questions or need assistance, please don't hesitate to contact our support team
        at <a href="mailto:<EMAIL>" style="color: #0D8A3E;"><EMAIL></a>.
      </p>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        Thank you for joining us on this journey to transform Zimbabwe's innovation ecosystem!
      </p>

      <p style="margin-bottom: 8px; line-height: 1.5;">
        Best regards,
      </p>

      <p style="line-height: 1.5;">
        The ZB Innovation Hub Team
      </p>

      <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666;">
        <p>
          This email was sent to ${email}. If you did not sign up for an account, please ignore this email.
        </p>
      </div>
    </div>
  `

  return { html, subject }
}

// Function to generate password reset email HTML
function generatePasswordResetEmail(email: string, resetLink: string, firstName?: string): { html: string, subject: string } {
  // Try to extract name from email if not provided
  const extractedName = !firstName ? extractNameFromEmail(email) : undefined
  const greeting = firstName ? `Hi ${firstName},` : extractedName ? `Hi ${extractedName},` : 'Hi there,'

  const subject = 'Reset Your ZB Innovation Hub Password'

  const html = `
    <div style="max-width: 600px; margin: 0 auto; padding: 24px; font-family: Arial, sans-serif;">
      <div style="text-align: center; margin-bottom: 24px;">
        <img src="https://zbinnovation.com/logo.png" alt="ZB Innovation Hub" style="max-width: 200px;">
      </div>

      <h1 style="color: #0D8A3E; margin-bottom: 16px;">${greeting}</h1>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        We received a request to reset your password for your ZB Innovation Hub account.
        If you didn't make this request, you can safely ignore this email.
      </p>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        To reset your password, click the button below:
      </p>

      <div style="text-align: center; margin: 32px 0;">
        <a href="${resetLink}"
           style="background-color: #0D8A3E; color: white; padding: 12px 24px; text-decoration: none;
                  border-radius: 4px; font-weight: bold;">
          Reset Password
        </a>
      </div>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        This link will expire in 24 hours. If you need assistance, please contact our support team
        at <a href="mailto:<EMAIL>" style="color: #0D8A3E;"><EMAIL></a>.
      </p>

      <p style="margin-bottom: 8px; line-height: 1.5;">
        Best regards,
      </p>

      <p style="line-height: 1.5;">
        The ZB Innovation Hub Team
      </p>

      <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666;">
        <p>
          This email was sent to ${email}. If you did not request a password reset, please ignore this email.
        </p>
      </div>
    </div>
  `

  return { html, subject }
}

// Main handler function
serve(async (req) => {
  // Handle CORS for preflight requests
  if (req.method === 'OPTIONS') {
    console.log('Handling OPTIONS request with CORS headers')
    return new Response('ok', {
      headers: {
        ...corsHeaders,
        'Content-Type': 'text/plain'
      }
    })
  }

  try {
    console.log('Received request:', {
      method: req.method,
      url: req.url,
      headers: Object.fromEntries(req.headers.entries())
    })

    // Parse request body
    const requestData: EmailRequest = await req.json()
    console.log('Request data:', JSON.stringify(requestData))

    // Validate request
    if (!requestData.type || !requestData.data || !requestData.data.to) {
      console.error('Invalid request - missing required fields:', JSON.stringify(requestData))
      return new Response(
        JSON.stringify({ error: 'Invalid request. Missing required fields.' }),
        {
          status: 400,
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          }
        }
      )
    }

    // Process based on email type
    let emailContent: { html: string, subject: string }

    switch (requestData.type) {
      case 'welcome':
        emailContent = generateWelcomeEmail(
          requestData.data.to,
          requestData.data.firstName
        )
        break

      case 'password_reset':
        if (!requestData.data.customHtml) {
          return new Response(
            JSON.stringify({ error: 'Password reset requires a reset link.' }),
            {
              status: 400,
              headers: {
                ...corsHeaders,
                'Content-Type': 'application/json'
              }
            }
          )
        }
        emailContent = generatePasswordResetEmail(
          requestData.data.to,
          requestData.data.customHtml,
          requestData.data.firstName
        )
        break

      case 'custom':
        if (!requestData.data.customHtml || !requestData.data.subject) {
          return new Response(
            JSON.stringify({ error: 'Custom email requires HTML content and subject.' }),
            {
              status: 400,
              headers: {
                ...corsHeaders,
                'Content-Type': 'application/json'
              }
            }
          )
        }
        emailContent = {
          html: requestData.data.customHtml,
          subject: requestData.data.subject
        }
        break

      default:
        return new Response(
          JSON.stringify({ error: `Unknown email type: ${requestData.type}` }),
          {
            status: 400,
            headers: {
              ...corsHeaders,
              'Content-Type': 'application/json'
            }
          }
        )
    }

    // Send the email
    await sendEmail({
      to: requestData.data.to,
      subject: emailContent.subject,
      html: emailContent.html,
      text: requestData.data.customText || stripHtml(emailContent.html)
    })

    // Return success response
    return new Response(
      JSON.stringify({ success: true, message: `Email sent to ${requestData.data.to}` }),
      {
        status: 200,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      }
    )
  } catch (error) {
    // Handle errors
    console.error('Error sending email:', error)

    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'Failed to send email',
        stack: error.stack
      }),
      {
        status: 500,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      }
    )
  }
})
