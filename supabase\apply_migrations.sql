-- Apply all migrations in order
-- This file can be executed in the Supabase SQL Editor to apply all migrations at once

-- Include all migration files
\i migrations/20240501000001_create_base_tables.sql
\i migrations/20240501000002_create_innovator_profiles.sql
\i migrations/20240501000003_create_profile_visibility_function.sql
\i migrations/20240501000004_create_triggers.sql
\i migrations/20240501000005_create_profile_completion_function.sql
\i migrations/20240501000006_create_user_management_functions.sql
\i migrations/20240601_fix_profile_rls_and_create_public_view.sql
\i migrations/20240701_consolidate_migrations.sql

-- Refresh schema cache
SELECT pg_catalog.set_config('search_path', 'public', false);
