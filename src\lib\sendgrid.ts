/**
 * SendGrid Email Service
 *
 * This service handles sending emails via SendGrid API
 */

// Types for email data
export interface EmailData {
  to: string;
  subject: string;
  html: string;
  text?: string;
  from?: {
    email: string;
    name: string;
  };
}

/**
 * SECURITY: Direct SendGrid API calls disabled for client-side security
 * Use Supabase Edge Functions instead for email sending
 * @param emailData The email data to send
 * @returns A promise that resolves to the API response
 */
export async function sendEmail(emailData: EmailData): Promise<Response> {
  // SECURITY: Client-side SendGrid API calls are disabled
  // All email sending should go through Supabase Edge Functions
  console.warn('Direct SendGrid API calls are disabled for security. Use Supabase Edge Functions instead.');

  throw new Error('Direct SendGrid API calls are disabled for security. Use emailService.ts instead.');
}

/**
 * Extracts a potential first name from an email address
 * @param email The email address to extract from
 * @returns The potential first name or undefined if not found
 */
export function extractNameFromEmail(email: string): string | undefined {
  if (!email) return undefined;

  // Get the part before the @ symbol
  const localPart = email.split('@')[0];

  // Remove numbers and special characters
  const cleanedName = localPart.replace(/[0-9_.-]/g, ' ');

  // Capitalize first letter of each word
  const words = cleanedName.split(' ')
    .filter(word => word.length > 0)
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase());

  // Return the first word if it exists
  return words.length > 0 ? words[0] : undefined;
}

/**
 * Sends a welcome email to a new user
 * @param email The user's email address
 * @param firstName Optional first name of the user
 * @returns A promise that resolves to the API response
 */
export async function sendWelcomeEmail(email: string, firstName?: string): Promise<Response> {
  // SECURITY: Direct SendGrid calls disabled - use emailService.ts instead
  console.warn('Direct SendGrid welcome email is disabled for security. Use emailService.ts instead.');
  throw new Error('Direct SendGrid API calls are disabled for security. Use emailService.ts instead.');
}

/**
 * Simple function to strip HTML tags for plain text version
 * @param html HTML string to strip
 * @returns Plain text version
 */
function stripHtml(html: string): string {
  return html
    .replace(/<[^>]*>/g, '')
    .replace(/\s+/g, ' ')
    .trim();
}
