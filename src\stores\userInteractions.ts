/**
 * User Interactions Store
 * 
 * Centralized store for managing all user interactions from virtual-community
 * including saved posts, event registrations, group memberships, and saved profiles.
 * This ensures DRY principles and provides dashboard representation of all interactions.
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { supabase } from '@/lib/supabase'
import { useAuthStore } from './auth'
import { useNotificationStore } from './notifications'

export interface SavedPost {
  id: number
  user_id: string
  post_id: number
  created_at: string
  post?: {
    id: number
    title?: string
    content: string
    post_type: string
    sub_type: string
    featured_image?: string
    created_at: string
  }
}

export interface EventRegistration {
  id: number
  user_id: string
  post_id: number
  registration_status: string
  registration_data: any
  created_at: string
  updated_at: string
  event?: {
    id: number
    event_title?: string
    event_start_datetime?: string
    event_location?: string
    event_type?: string
    featured_image?: string
  }
}

export interface GroupMembership {
  id: number
  user_id: string
  group_post_id: number
  membership_status: string
  role: string
  joined_at: string
  updated_at: string
  group?: {
    id: number
    title?: string
    content: string
    featured_image?: string
    created_at: string
  }
}

export interface SavedProfile {
  id: number
  user_id: string
  profile_id: string
  created_at: string
  profile?: {
    id: string
    first_name?: string
    last_name?: string
    email: string
    profile_type?: string
    avatar_url?: string
  }
}

export const useUserInteractionsStore = defineStore('userInteractions', () => {
  const authStore = useAuthStore()
  const notificationStore = useNotificationStore()

  // State
  const savedPosts = ref<SavedPost[]>([])
  const eventRegistrations = ref<EventRegistration[]>([])
  const groupMemberships = ref<GroupMembership[]>([])
  const savedProfiles = ref<SavedProfile[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Computed
  const totalSavedItems = computed(() => savedPosts.value.length + savedProfiles.value.length)
  const totalRegistrations = computed(() => eventRegistrations.value.length)
  const totalMemberships = computed(() => groupMemberships.value.length)
  const totalInteractions = computed(() =>
    totalSavedItems.value + totalRegistrations.value + totalMemberships.value
  )

  // Categorized saved posts for dashboard
  const savedMarketplaceItems = computed(() =>
    savedPosts.value.filter(saved => saved.post?.sub_type === 'marketplace')
  )

  const savedEvents = computed(() =>
    savedPosts.value.filter(saved => saved.post?.sub_type === 'event')
  )

  const savedRegularPosts = computed(() =>
    savedPosts.value.filter(saved =>
      saved.post?.sub_type !== 'marketplace' &&
      saved.post?.sub_type !== 'event' &&
      saved.post?.sub_type !== 'group'
    )
  )

  // Actions
  async function fetchAllUserInteractions() {
    // Check authentication first
    if (!authStore.currentUser?.id) {
      console.log('User not authenticated, skipping user interactions fetch')
      return
    }

    // Prevent duplicate loading
    if (loading.value) {
      console.log('User interactions already loading, skipping duplicate request')
      return
    }

    loading.value = true
    error.value = null

    try {
      // Fetch interactions with proper error handling for each
      // Temporarily disable saved profiles due to database relationship issue
      const results = await Promise.allSettled([
        fetchSavedPosts(),
        fetchEventRegistrations(),
        fetchGroupMemberships()
        // fetchSavedProfiles() // Disabled due to database relationship error
      ])

      // Log any individual failures but don't fail the entire operation
      results.forEach((result, index) => {
        if (result.status === 'rejected') {
          const operations = ['saved posts', 'event registrations', 'group memberships', 'saved profiles']
          console.warn(`Failed to fetch ${operations[index]}:`, result.reason)
        }
      })

    } catch (err: any) {
      console.error('Error fetching user interactions:', err)
      error.value = err.message || 'Failed to fetch user interactions'
    } finally {
      loading.value = false
    }
  }

  async function fetchSavedPosts() {
    if (!authStore.currentUser?.id) return

    try {
      const { data, error: fetchError } = await supabase
        .from('saved_posts')
        .select(`
          *,
          post:posts(
            id,
            title,
            content,
            post_type,
            sub_type,
            featured_image,
            created_at
          )
        `)
        .eq('user_id', authStore.currentUser.id)
        .order('created_at', { ascending: false })

      if (fetchError) throw fetchError
      savedPosts.value = data || []
    } catch (err: any) {
      console.error('Error fetching saved posts:', err)
      throw err
    }
  }

  async function fetchEventRegistrations() {
    if (!authStore.currentUser?.id) return

    try {
      const { data, error: fetchError } = await supabase
        .from('event_registrations')
        .select(`
          *,
          event:posts(
            id,
            event_title,
            event_start_datetime,
            event_location,
            event_type,
            featured_image
          )
        `)
        .eq('user_id', authStore.currentUser.id)
        .order('created_at', { ascending: false })

      if (fetchError) throw fetchError
      eventRegistrations.value = data || []
    } catch (err: any) {
      console.error('Error fetching event registrations:', err)
      throw err
    }
  }

  async function fetchGroupMemberships() {
    if (!authStore.currentUser?.id) return

    try {
      const { data, error: fetchError } = await supabase
        .from('group_memberships')
        .select(`
          *,
          group:posts(
            id,
            title,
            content,
            featured_image,
            created_at
          )
        `)
        .eq('user_id', authStore.currentUser.id)
        .order('joined_at', { ascending: false })

      if (fetchError) throw fetchError
      groupMemberships.value = data || []
    } catch (err: any) {
      console.error('Error fetching group memberships:', err)
      throw err
    }
  }

  async function fetchSavedProfiles() {
    // Check authentication
    if (!authStore.currentUser?.id) {
      console.log('User not authenticated, skipping saved profiles fetch')
      return
    }

    try {
      const { data, error: fetchError } = await supabase
        .from('saved_profiles')
        .select(`
          *,
          profile:profiles(
            id,
            first_name,
            last_name,
            email,
            profile_type,
            avatar_url
          )
        `)
        .eq('user_id', authStore.currentUser.id)
        .order('created_at', { ascending: false })

      if (fetchError) {
        // Handle specific error cases
        if (fetchError.code === 'PGRST116') {
          // No rows found - this is normal, not an error
          console.log('No saved profiles found for user')
          savedProfiles.value = []
          return
        }

        // Log the error but don't throw to prevent breaking the entire flow
        console.error('Error fetching saved profiles:', fetchError)
        savedProfiles.value = []
        return
      }

      savedProfiles.value = data || []
      console.log(`Loaded ${savedProfiles.value.length} saved profiles`)
    } catch (err: any) {
      console.error('Error fetching saved profiles:', err)
      savedProfiles.value = []
      // Don't throw the error to prevent breaking the parent operation
    }
  }

  // Helper functions to check if content is saved/registered/joined
  function isPostSaved(postId: number): boolean {
    return savedPosts.value.some(saved => saved.post_id === postId)
  }

  function isEventRegistered(eventId: number): boolean {
    return eventRegistrations.value.some(reg => reg.post_id === eventId)
  }

  function isGroupMember(groupId: number): boolean {
    return groupMemberships.value.some(membership => membership.group_post_id === groupId)
  }

  function isProfileSaved(profileId: string): boolean {
    return savedProfiles.value.some(saved => saved.profile_id === profileId)
  }

  // Remove functions for dashboard management
  async function removeSavedPost(postId: number) {
    if (!authStore.currentUser?.id) return

    try {
      const { error } = await supabase
        .from('saved_posts')
        .delete()
        .eq('user_id', authStore.currentUser.id)
        .eq('post_id', postId)

      if (error) throw error

      savedPosts.value = savedPosts.value.filter(saved => saved.post_id !== postId)
      notificationStore.success('Post removed from saved items')
    } catch (err: any) {
      console.error('Error removing saved post:', err)
      notificationStore.error('Failed to remove saved post')
    }
  }

  async function cancelEventRegistration(eventId: number) {
    if (!authStore.currentUser?.id) return

    try {
      const { error } = await supabase
        .from('event_registrations')
        .delete()
        .eq('user_id', authStore.currentUser.id)
        .eq('post_id', eventId)

      if (error) throw error

      eventRegistrations.value = eventRegistrations.value.filter(reg => reg.post_id !== eventId)
      notificationStore.success('Event registration cancelled')
    } catch (err: any) {
      console.error('Error cancelling event registration:', err)
      notificationStore.error('Failed to cancel event registration')
    }
  }

  async function leaveGroup(groupId: number) {
    if (!authStore.currentUser?.id) return

    try {
      const { error } = await supabase
        .from('group_memberships')
        .delete()
        .eq('user_id', authStore.currentUser.id)
        .eq('group_post_id', groupId)

      if (error) throw error

      groupMemberships.value = groupMemberships.value.filter(membership => membership.group_post_id !== groupId)
      notificationStore.success('Left group successfully')
    } catch (err: any) {
      console.error('Error leaving group:', err)
      notificationStore.error('Failed to leave group')
    }
  }

  async function removeSavedProfile(profileId: string) {
    if (!authStore.currentUser?.id) return

    try {
      const { error } = await supabase
        .from('saved_profiles')
        .delete()
        .eq('user_id', authStore.currentUser.id)
        .eq('profile_id', profileId)

      if (error) throw error

      savedProfiles.value = savedProfiles.value.filter(saved => saved.profile_id !== profileId)
      notificationStore.success('Profile removed from saved items')
    } catch (err: any) {
      console.error('Error removing saved profile:', err)
      notificationStore.error('Failed to remove saved profile')
    }
  }

  return {
    // State
    savedPosts,
    eventRegistrations,
    groupMemberships,
    savedProfiles,
    loading,
    error,

    // Computed
    totalSavedItems,
    totalRegistrations,
    totalMemberships,
    totalInteractions,
    savedMarketplaceItems,
    savedEvents,
    savedRegularPosts,

    // Actions
    fetchAllUserInteractions,
    fetchSavedPosts,
    fetchEventRegistrations,
    fetchGroupMemberships,
    fetchSavedProfiles,

    // Helper functions
    isPostSaved,
    isEventRegistered,
    isGroupMember,
    isProfileSaved,

    // Remove functions
    removeSavedPost,
    cancelEventRegistration,
    leaveGroup,
    removeSavedProfile
  }
})
