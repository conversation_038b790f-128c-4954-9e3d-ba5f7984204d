import { supabase } from '../../lib/supabase'

/**
 * Submit user feedback
 * 
 * @param feedback The feedback data to submit
 * @returns The submitted feedback
 */
export async function submitFeedback(feedback: {
  user_id: string;
  subject: string;
  message: string;
  category?: string;
}) {
  const { data, error } = await supabase
    .from('feedback')
    .insert(feedback)
    .select()
    .single()
    
  if (error) {
    throw error
  }
  
  return data
}

/**
 * Fetch feedback for a user
 * 
 * @param userId The user ID to fetch feedback for
 * @returns The user's feedback
 */
export async function fetchUserFeedback(userId: string) {
  const { data, error } = await supabase
    .from('feedback')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
    
  if (error) {
    throw error
  }
  
  return data || []
}
