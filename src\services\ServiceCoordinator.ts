/**
 * Service Coordination Manager
 * 
 * This service prevents duplicate service initialization and coordinates
 * the startup of various application services to improve performance
 * and prevent race conditions.
 */

export interface ServiceInitializer {
  name: string
  initializer: () => Promise<void>
  priority?: number // Lower numbers = higher priority
  dependencies?: string[] // Services that must be initialized first
}

export interface ServiceStatus {
  name: string
  status: 'pending' | 'initializing' | 'initialized' | 'failed'
  startTime?: number
  endTime?: number
  error?: Error
}

export class ServiceCoordinator {
  private static instance: ServiceCoordinator
  private services = new Map<string, ServiceStatus>()
  private initializers = new Map<string, ServiceInitializer>()
  private initPromises = new Map<string, Promise<void>>()
  private readonly maxRetries = 3
  private readonly retryDelay = 1000 // 1 second
  
  static getInstance(): ServiceCoordinator {
    if (!ServiceCoordinator.instance) {
      ServiceCoordinator.instance = new ServiceCoordinator()
    }
    return ServiceCoordinator.instance
  }
  
  /**
   * Register a service for coordinated initialization
   */
  registerService(service: ServiceInitializer): void {
    this.initializers.set(service.name, service)
    this.services.set(service.name, {
      name: service.name,
      status: 'pending'
    })
    console.log(`ServiceCoordinator: Registered service '${service.name}'`)
  }
  
  /**
   * Initialize a specific service (with dependency resolution)
   */
  async initializeService(serviceName: string, initializer?: () => Promise<void>): Promise<void> {
    // If initializer is provided, register it first
    if (initializer) {
      this.registerService({
        name: serviceName,
        initializer
      })
    }
    
    // Return immediately if already initialized
    const status = this.services.get(serviceName)
    if (status?.status === 'initialized') {
      console.log(`ServiceCoordinator: Service '${serviceName}' already initialized`)
      return
    }
    
    // Return existing promise if currently initializing
    if (this.initPromises.has(serviceName)) {
      console.log(`ServiceCoordinator: Waiting for existing initialization of '${serviceName}'`)
      return this.initPromises.get(serviceName)!
    }
    
    // Start initialization
    const promise = this.doInitialize(serviceName)
    this.initPromises.set(serviceName, promise)
    
    return promise
  }
  
  /**
   * Initialize multiple services in parallel (respecting dependencies)
   */
  async initializeServices(serviceNames: string[]): Promise<void> {
    const promises = serviceNames.map(name => this.initializeService(name))
    await Promise.allSettled(promises)
    
    // Check for any failed services
    const failed = serviceNames.filter(name => 
      this.services.get(name)?.status === 'failed'
    )
    
    if (failed.length > 0) {
      console.warn(`ServiceCoordinator: Some services failed to initialize: ${failed.join(', ')}`)
    }
  }
  
  /**
   * Initialize all registered services
   */
  async initializeAll(): Promise<void> {
    const serviceNames = Array.from(this.initializers.keys())
    
    // Sort by priority (lower number = higher priority)
    serviceNames.sort((a, b) => {
      const priorityA = this.initializers.get(a)?.priority || 100
      const priorityB = this.initializers.get(b)?.priority || 100
      return priorityA - priorityB
    })
    
    console.log(`ServiceCoordinator: Initializing ${serviceNames.length} services in order:`, serviceNames)
    
    await this.initializeServices(serviceNames)
  }
  
  /**
   * Perform the actual service initialization
   */
  private async doInitialize(serviceName: string): Promise<void> {
    const service = this.initializers.get(serviceName)
    const status = this.services.get(serviceName)
    
    if (!service || !status) {
      throw new Error(`Service '${serviceName}' not registered`)
    }
    
    // Initialize dependencies first
    if (service.dependencies && service.dependencies.length > 0) {
      console.log(`ServiceCoordinator: Initializing dependencies for '${serviceName}':`, service.dependencies)
      await this.initializeServices(service.dependencies)
    }
    
    // Update status
    status.status = 'initializing'
    status.startTime = Date.now()
    
    let retries = 0
    while (retries <= this.maxRetries) {
      try {
        console.log(`ServiceCoordinator: Initializing '${serviceName}' (attempt ${retries + 1})`)
        
        await service.initializer()
        
        // Success
        status.status = 'initialized'
        status.endTime = Date.now()
        
        const duration = status.endTime - (status.startTime || 0)
        console.log(`✅ ServiceCoordinator: '${serviceName}' initialized successfully (${duration}ms)`)
        
        return
        
      } catch (error) {
        retries++
        status.error = error as Error
        
        console.error(`❌ ServiceCoordinator: '${serviceName}' initialization failed (attempt ${retries}):`, error)
        
        if (retries <= this.maxRetries) {
          console.log(`ServiceCoordinator: Retrying '${serviceName}' in ${this.retryDelay}ms...`)
          await new Promise(resolve => setTimeout(resolve, this.retryDelay))
        }
      }
    }
    
    // All retries failed
    status.status = 'failed'
    status.endTime = Date.now()
    
    throw new Error(`Service '${serviceName}' failed to initialize after ${this.maxRetries + 1} attempts`)
  }
  
  /**
   * Check if a service is initialized
   */
  isInitialized(serviceName: string): boolean {
    return this.services.get(serviceName)?.status === 'initialized'
  }
  
  /**
   * Check if a service is currently initializing
   */
  isInitializing(serviceName: string): boolean {
    return this.services.get(serviceName)?.status === 'initializing'
  }
  
  /**
   * Get the status of a specific service
   */
  getServiceStatus(serviceName: string): ServiceStatus | undefined {
    return this.services.get(serviceName)
  }
  
  /**
   * Get the status of all services
   */
  getAllServiceStatus(): ServiceStatus[] {
    return Array.from(this.services.values())
  }
  
  /**
   * Get initialization statistics
   */
  getStats() {
    const statuses = Array.from(this.services.values())
    const stats = {
      total: statuses.length,
      pending: 0,
      initializing: 0,
      initialized: 0,
      failed: 0,
      totalInitTime: 0,
      averageInitTime: 0
    }
    
    let totalTime = 0
    let completedServices = 0
    
    for (const status of statuses) {
      stats[status.status]++
      
      if (status.startTime && status.endTime) {
        const duration = status.endTime - status.startTime
        totalTime += duration
        completedServices++
      }
    }
    
    stats.totalInitTime = totalTime
    stats.averageInitTime = completedServices > 0 ? totalTime / completedServices : 0
    
    return stats
  }
  
  /**
   * Reset all services (useful for testing or re-initialization)
   */
  reset(): void {
    this.services.clear()
    this.initializers.clear()
    this.initPromises.clear()
    console.log('ServiceCoordinator: Reset all services')
  }
  
  /**
   * Wait for a specific service to be initialized
   */
  async waitForService(serviceName: string, timeout: number = 30000): Promise<boolean> {
    const startTime = Date.now()
    
    while (Date.now() - startTime < timeout) {
      const status = this.services.get(serviceName)
      
      if (status?.status === 'initialized') {
        return true
      }
      
      if (status?.status === 'failed') {
        return false
      }
      
      // Wait a bit before checking again
      await new Promise(resolve => setTimeout(resolve, 100))
    }
    
    console.warn(`ServiceCoordinator: Timeout waiting for service '${serviceName}'`)
    return false
  }
  
  /**
   * Wait for all services to be initialized
   */
  async waitForAllServices(timeout: number = 60000): Promise<boolean> {
    const startTime = Date.now()
    
    while (Date.now() - startTime < timeout) {
      const stats = this.getStats()
      
      if (stats.pending === 0 && stats.initializing === 0) {
        return stats.failed === 0 // Success if no failures
      }
      
      // Wait a bit before checking again
      await new Promise(resolve => setTimeout(resolve, 200))
    }
    
    console.warn('ServiceCoordinator: Timeout waiting for all services')
    return false
  }
}

// Export singleton instance
export const serviceCoordinator = ServiceCoordinator.getInstance()

// Common service initialization helpers
export const ServiceNames = {
  MESSAGING: 'messaging',
  ACTIVITY_NOTIFICATIONS: 'activityNotifications',
  USER_NOTIFICATIONS: 'userNotifications',
  PROFILE_MANAGER: 'profileManager',
  AUTH: 'auth'
} as const

export type ServiceName = typeof ServiceNames[keyof typeof ServiceNames]
