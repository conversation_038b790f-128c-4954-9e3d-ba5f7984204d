/**
 * Matchmaking Store
 *
 * This store manages the state for the matchmaking system.
 * It provides actions to generate and retrieve matches for users.
 */

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import {
  useMatchmakingService,
  ProfileType,
  EntityType,
  MatchResult,
  EnrichedMatch
} from '@/services/matchmakingService';
import { MatchmakingService } from '@/lib/matchmaking/matchmakingService';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from './auth';
import { useNotificationStore } from './notifications';

export const useMatchmakingStore = defineStore('matchmaking', () => {
  // Services
  const matchmakingService = useMatchmakingService();
  const authStore = useAuthStore();
  const notificationStore = useNotificationStore();

  // Create a direct instance of the matchmaking service for simulation
  const directMatchmakingService = new MatchmakingService(supabase);

  // State
  const matches = ref<EnrichedMatch[]>([]);
  const loading = ref(false);
  const error = ref<string | null>(null);
  const initialized = ref(false);
  const currentProfileType = ref<ProfileType | null>(null);

  // Initialize with empty arrays to prevent undefined errors
  const emptyMatches: EnrichedMatch[] = [];

  // Getters
  const isLoading = computed(() => loading.value);
  const getError = computed(() => error.value);
  const getMatches = computed(() => matches.value || emptyMatches);
  const getSavedMatches = computed(() => (matches.value || emptyMatches).filter(match => match.isSaved));
  const getNewMatches = computed(() => (matches.value || emptyMatches).filter(match => !match.isViewed));
  const getMatchesByType = computed(() => (type: EntityType) =>
    (matches.value || emptyMatches).filter(match => match.entityType === type)
  );

  /**
   * Initialize the matchmaking system
   */
  async function initialize(): Promise<boolean> {
    if (initialized.value) return true;

    loading.value = true;
    error.value = null;

    try {
      // Check if matchmaking_rules table exists and has data
      const { data, error: checkError } = await supabase
        .from('matchmaking_rules')
        .select('id')
        .limit(1);

      if (checkError) {
        // If there's an error, it might be because the table doesn't exist yet
        // In a real implementation, we would create the table and rules here
        console.log('Matchmaking rules table may not exist yet, but we can proceed');
      }

      // For the purpose of this UI, we'll just mark as initialized
      // In a real implementation, we would ensure the rules are properly set up
      initialized.value = true;
      return true;
    } catch (err: any) {
      error.value = err.message;
      console.error('Error initializing matchmaking:', err);
      // Even if there's an error, we'll still mark as initialized to allow the UI to function
      initialized.value = true;
      return true;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Generate matches for the current user
   */
  async function generateMatches(profileType: ProfileType): Promise<boolean> {
    if (!authStore.isAuthenticated) {
      error.value = 'User must be authenticated to generate matches';
      return false;
    }

    loading.value = true;
    error.value = null;

    try {
      // Initialize if not already initialized
      if (!initialized.value) {
        await initialize();
      }

      // Set current profile type
      currentProfileType.value = profileType;

      // Generate matches using the matchmaking service
      const userId = authStore.user?.id;
      if (!userId) {
        throw new Error('User ID not available');
      }

      const generatedMatches = await directMatchmakingService.generateMatches(userId, profileType);

      // Convert MatchResult[] to EnrichedMatch[]
      const enrichedMatches: EnrichedMatch[] = generatedMatches.map(match => ({
        id: match.id,
        userId: userId,
        matchedEntityId: match.entityId,
        entityType: match.entityType,
        matchScore: match.score,
        matchReasons: match.reasons,
        isViewed: false,
        isSaved: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        profile: {
          name: `${match.entityType} Profile`,
          description: `Matched ${match.entityType} profile`
        },
        user: {
          firstName: 'Unknown',
          lastName: 'User',
          avatarUrl: null
        }
      }));

      // Set the matches
      matches.value = enrichedMatches;

      notificationStore.showNotification({
        type: 'success',
        message: `Generated ${enrichedMatches.length} matches for your ${profileType} profile`
      });

      return true;
    } catch (err: any) {
      error.value = err.message;
      console.error('Error generating matches:', err);

      notificationStore.showNotification({
        type: 'error',
        message: 'Failed to generate matches. Please try again later.'
      });

      return false;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Load matches for the current user
   */
  async function loadMatches(
    entityType?: EntityType,
    limit: number = 20,
    offset: number = 0,
    minScore: number = 0.3
  ): Promise<boolean> {
    if (!authStore.isAuthenticated) {
      error.value = 'User must be authenticated to load matches';
      return false;
    }

    loading.value = true;
    error.value = null;

    try {
      // In a real implementation, we would call the matchmaking service
      // For now, we'll just return the matches we already have in the store
      // This avoids the need for the database tables

      // Filter matches by entity type and min score if needed
      let filteredMatches = [...matches.value];

      if (entityType) {
        filteredMatches = filteredMatches.filter(match => match.entityType === entityType);
      }

      if (minScore > 0) {
        filteredMatches = filteredMatches.filter(match => match.matchScore >= minScore);
      }

      // Sort by match score (highest first)
      filteredMatches.sort((a, b) => b.matchScore - a.matchScore);

      // Apply pagination
      const paginatedMatches = filteredMatches.slice(offset, offset + limit);

      // If this is a fresh load (offset is 0), replace the matches
      // Otherwise, append the new matches
      if (offset === 0 && entityType) {
        // Only replace if we're filtering by entity type and it's a fresh load
        matches.value = paginatedMatches;
      } else if (offset > 0) {
        // Append for pagination
        matches.value = [...matches.value, ...paginatedMatches];
      }

      return true;
    } catch (err: any) {
      error.value = err.message;
      console.error('Error loading matches:', err);
      return false;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Save or dismiss a match
   */
  async function updateMatch(matchId: string, isSaved: boolean): Promise<boolean> {
    loading.value = true;
    error.value = null;

    try {
      // In a real implementation, we would call the matchmaking service
      // For now, we'll just update the match in the local state
      // This avoids the need for the database tables

      // Update the match in the local state
      const matchIndex = matches.value.findIndex(match => match.id === matchId);

      if (matchIndex !== -1) {
        matches.value[matchIndex].isSaved = isSaved;

        // Show a notification
        notificationStore.showNotification({
          type: 'success',
          message: isSaved ? 'Match saved successfully' : 'Match dismissed'
        });
      } else {
        throw new Error('Match not found');
      }

      return true;
    } catch (err: any) {
      error.value = err.message;
      console.error('Error updating match:', err);
      return false;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Clear all matches from the store
   */
  function clearMatches(): void {
    matches.value = [];
    currentProfileType.value = null;
  }



  return {
    // State
    matches,
    loading,
    error,
    initialized,
    currentProfileType,

    // Getters
    isLoading,
    getError,
    getMatches,
    getSavedMatches,
    getNewMatches,
    getMatchesByType,

    // Actions
    initialize,
    generateMatches,
    loadMatches,
    updateMatch,
    clearMatches
  };
});
