<template>
  <q-page class="flex flex-center">
    <div class="text-center">
      <q-spinner
        color="primary"
        size="3em"
      />
      <div class="text-h6 q-mt-md">
        Processing authentication...
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../../../stores/auth'
import { useProfileStore } from '../../../stores/profile'
import { useNotificationStore } from '../../../stores/notifications'

const router = useRouter()
const authStore = useAuthStore()
const profileStore = useProfileStore()
const notifications = useNotificationStore()

onMounted(async () => {
  try {
    // Check session to handle the OAuth callback
    await authStore.checkSession()

    // If we have a user after the session check
    if (authStore.user) {
      // Load user profiles
      await profileStore.loadUserProfiles()

      // Always redirect to dashboard, with appropriate welcome message
      if (profileStore.userProfiles.length === 0) {
        notifications.info('Welcome! You can create your profile from the dashboard.')
      } else {
        notifications.success('Successfully signed in!')
      }
      router.push('/dashboard')
    } else {
      // If no user is found after the callback, redirect to sign-in
      notifications.error('Authentication failed. Please try again.')
      router.push('/sign-in')
    }
  } catch (error: any) {
    console.error('Auth callback error:', error)
    notifications.error('Authentication error: ' + error.message)
    router.push('/sign-in')
  }
})
</script>
