import type { Issue, IssueType } from './types/jira'

// SECURITY: JIRA integration disabled for client-side security
// These credentials should be moved to server-side/Edge Functions
const JIRA_HOST = import.meta.env.VITE_JIRA_HOST
const JIRA_API_TOKEN = import.meta.env.VITE_JIRA_API_TOKEN
const JIRA_EMAIL = import.meta.env.VITE_JIRA_EMAIL
const JIRA_PROJECT_KEY = import.meta.env.VITE_JIRA_PROJECT_KEY

// Check if JIRA is properly configured (optional for security)
const isJiraConfigured = JIRA_HOST && JIRA_API_TOKEN && JIRA_EMAIL && JIRA_PROJECT_KEY

if (!isJiraConfigured) {
  console.warn('JIRA integration is disabled - missing environment variables. This is expected for security.')
}

const headers = {
  'Authorization': `Basic ${btoa(`${JIRA_EMAIL}:${JIRA_API_TOKEN}`)}`,
  'Accept': 'application/json',
  'Content-Type': 'application/json'
}

const baseUrl = `https://${JIRA_HOST}/rest/api/3`

export async function testJiraConnection(): Promise<boolean> {
  if (!isJiraConfigured) {
    console.warn('JIRA integration is disabled for security reasons')
    return false
  }

  try {
    const response = await fetch(`${baseUrl}/myself`, {
      method: 'GET',
      headers
    })

    if (!response.ok) {
      throw new Error('Failed to connect to JIRA')
    }

    console.log('✅ JIRA connection successful!')
    return true
  } catch (error) {
    console.error('❌ JIRA connection error:', error)
    return false
  }
}

export async function createIssue(data: {
  summary: string
  description: string
  issueType: IssueType
}): Promise<Issue> {
  if (!isJiraConfigured) {
    throw new Error('JIRA integration is disabled for security reasons')
  }

  const response = await fetch(`${baseUrl}/issue`, {
    method: 'POST',
    headers,
    body: JSON.stringify({
      fields: {
        project: {
          key: JIRA_PROJECT_KEY
        },
        summary: data.summary,
        description: {
          type: 'doc',
          version: 1,
          content: [
            {
              type: 'paragraph',
              content: [
                {
                  type: 'text',
                  text: data.description
                }
              ]
            }
          ]
        },
        issuetype: {
          name: data.issueType
        }
      }
    })
  })

  if (!response.ok) {
    throw new Error('Failed to create JIRA issue')
  }

  return response.json()
}

export async function getIssue(issueKey: string): Promise<Issue> {
  if (!isJiraConfigured) {
    throw new Error('JIRA integration is disabled for security reasons')
  }

  const response = await fetch(`${baseUrl}/issue/${issueKey}`, {
    method: 'GET',
    headers
  })

  if (!response.ok) {
    throw new Error('Failed to fetch JIRA issue')
  }

  return response.json()
}

export async function updateIssue(
  issueKey: string,
  data: Partial<{
    summary: string
    description: string
    status: string
  }>
): Promise<void> {
  if (!isJiraConfigured) {
    throw new Error('JIRA integration is disabled for security reasons')
  }

  const response = await fetch(`${baseUrl}/issue/${issueKey}`, {
    method: 'PUT',
    headers,
    body: JSON.stringify({
      fields: {
        ...(data.summary && { summary: data.summary }),
        ...(data.description && {
          description: {
            type: 'doc',
            version: 1,
            content: [
              {
                type: 'paragraph',
                content: [
                  {
                    type: 'text',
                    text: data.description
                  }
                ]
              }
            ]
          }
        }),
        ...(data.status && { status: { name: data.status } })
      }
    })
  })

  if (!response.ok) {
    throw new Error('Failed to update JIRA issue')
  }
}

export async function searchIssues(jql: string): Promise<Issue[]> {
  if (!isJiraConfigured) {
    throw new Error('JIRA integration is disabled for security reasons')
  }

  const response = await fetch(`${baseUrl}/search?jql=${encodeURIComponent(jql)}`, {
    method: 'GET',
    headers
  })

  if (!response.ok) {
    throw new Error('Failed to search JIRA issues')
  }

  const data = await response.json()
  return data.issues
}

export async function getProjectIssues(): Promise<Issue[]> {
  if (!isJiraConfigured) {
    throw new Error('JIRA integration is disabled for security reasons')
  }
  return searchIssues(`project = ${JIRA_PROJECT_KEY} ORDER BY created DESC`)
}

export async function getMyIssues(): Promise<Issue[]> {
  if (!isJiraConfigured) {
    throw new Error('JIRA integration is disabled for security reasons')
  }
  return searchIssues(`project = ${JIRA_PROJECT_KEY} AND assignee = currentUser() ORDER BY created DESC`)
}