<template>
  <q-stepper
    :model-value="modelValue"
    vertical
    color="primary"
    animated
    alternative-labels
    header-nav
    :contracted="false"
    @update:model-value="handleStepChange"
  >
    <!-- Step 1: Profile Category Selection -->
    <q-step
      :name="1"
      title="Select Profile Category"
      :done="modelValue > 1"
      :header-nav="modelValue > 1"
      prefix="1"
    >

      <div class="q-pa-md">
        <div class="text-subtitle1 q-mb-md">Choose your profile category</div>
        <div class="text-caption q-mb-lg text-grey-8">
          <q-icon name="warning" color="warning" class="q-mr-xs" />
          <strong>Important:</strong> You cannot change your profile category after selection.
        </div>

        <slot name="category-selection"></slot>
      </div>

      <q-stepper-navigation>
        <q-btn
          color="primary"
          :disable="!canContinueFromStep1"
          @click="handleContinueFromStep1"
          label="Continue"
        />
      </q-stepper-navigation>
    </q-step>

    <!-- Step 2: Personal Details -->
    <q-step
      :name="2"
      title="Personal Details"
      :done="modelValue > 2"
      :header-nav="modelValue > 2"
      prefix="2"
    >

      <div class="q-pa-md">
        <div class="text-subtitle1 q-mb-md">Enter your personal details</div>
        <div class="text-caption q-mb-lg text-grey-8">
          All fields in this section are required to proceed.
        </div>

        <slot name="personal-details"></slot>
      </div>

      <q-stepper-navigation>
        <q-btn
          color="primary"
          flat
          @click="$emit('update:modelValue', 1)"
          label="Back"
          class="q-mr-sm"
        />
        <q-btn
          color="primary"
          :disable="!canContinueFromStep2"
          @click="handleContinueFromStep2"
          label="Continue"
        />
      </q-stepper-navigation>
    </q-step>

    <!-- Step 3: Profile-Specific Details -->
    <q-step
      :name="3"
      title="Profile Details"
      prefix="3"
    >

      <div class="q-pa-md">
        <div class="text-subtitle1 q-mb-md">Complete your {{ profileTypeDisplay }} profile</div>
        <div class="text-caption q-mb-lg text-grey-8">
          Fill out as many fields as possible to increase your profile completion.
        </div>

        <slot name="profile-details"></slot>
      </div>

      <q-stepper-navigation>
        <q-btn
          color="primary"
          flat
          @click="$emit('update:modelValue', 2)"
          label="Back"
          class="q-mr-sm"
        />
        <q-btn
          color="positive"
          @click="$emit('complete')"
          label="Complete Profile"
        />
      </q-stepper-navigation>
    </q-step>
  </q-stepper>

  <!-- Category Selection Warning Dialog -->
  <q-dialog v-model="showCategoryWarning" persistent>
    <q-card>
      <q-card-section class="row items-center">
        <q-avatar icon="warning" color="warning" text-color="white" />
        <span class="q-ml-sm text-h6">Warning</span>
      </q-card-section>

      <q-card-section>
        <p>Once you select a profile category, you <strong>cannot change it later</strong>.</p>
        <p>Are you sure you want to continue with <strong>{{ selectedCategory }}</strong> as your profile category?</p>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn flat label="Cancel" color="primary" v-close-popup />
        <q-btn flat label="Confirm" color="primary" @click="confirmCategorySelection" v-close-popup />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import UnifiedIcon from '../ui/UnifiedIcon.vue';

const props = defineProps({
  modelValue: {
    type: Number,
    default: 1
  },
  selectedCategory: {
    type: String,
    default: ''
  },
  canContinueFromStep1: {
    type: Boolean,
    default: false
  },
  canContinueFromStep2: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits([
  'update:modelValue',
  'confirm-category',
  'save-personal-details',
  'complete'
]);

// Format profile type for display
const profileTypeDisplay = computed(() => {
  if (!props.selectedCategory) return '';
  return props.selectedCategory.charAt(0).toUpperCase() + props.selectedCategory.slice(1);
});

// Category selection warning dialog
const showCategoryWarning = ref(false);

// Handle step change
function handleStepChange(step: number) {
  // Don't allow going back to step 1 if already past it (category selection is final)
  if (props.modelValue > 1 && step === 1) {
    return;
  }

  emit('update:modelValue', step);
}

// Handle continue from step 1 (category selection)
function handleContinueFromStep1() {
  if (!props.canContinueFromStep1) return;

  // Show warning before proceeding
  showCategoryWarning.value = true;
}

// Confirm category selection and proceed to step 2
function confirmCategorySelection() {
  emit('confirm-category');
  emit('update:modelValue', 2);
}

// Handle continue from step 2 (personal details)
function handleContinueFromStep2() {
  if (!props.canContinueFromStep2) return;

  emit('save-personal-details');
  emit('update:modelValue', 3);
}
</script>

<style scoped>
/* Add any custom styles here */
</style>
