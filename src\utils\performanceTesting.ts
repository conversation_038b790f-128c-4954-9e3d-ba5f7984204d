/**
 * Performance Testing Utilities
 * 
 * Tools for testing and validating the performance improvements
 * from unified caching and real-time services.
 */

import { useUnifiedCache } from '../services/unifiedCacheService'
import { useUnifiedRealtime } from '../services/unifiedRealtimeService'
import { ProfileManager } from '../services/ProfileManager'

export interface PerformanceTestResult {
  testName: string
  iterations: number
  totalTime: number
  averageTime: number
  minTime: number
  maxTime: number
  cacheHitRate?: number
  memoryUsage?: number
  errors: number
}

export interface PerformanceTestSuite {
  suiteName: string
  results: PerformanceTestResult[]
  summary: {
    totalTests: number
    totalTime: number
    averageTime: number
    overallCacheHitRate: number
    memoryEfficiency: number
  }
}

export class PerformanceTestRunner {
  private cache = useUnifiedCache()
  private realtime = useUnifiedRealtime()
  private profileManager = ProfileManager.getInstance()

  /**
   * Run cache performance tests
   */
  async runCachePerformanceTests(): Promise<PerformanceTestResult[]> {
    const results: PerformanceTestResult[] = []

    // Test 1: Cache write performance
    results.push(await this.testCacheWrites())

    // Test 2: Cache read performance
    results.push(await this.testCacheReads())

    // Test 3: Cache invalidation performance
    results.push(await this.testCacheInvalidation())

    // Test 4: Memory usage under load
    results.push(await this.testMemoryUsage())

    return results
  }

  /**
   * Run profile loading performance tests
   */
  async runProfilePerformanceTests(userIds: string[]): Promise<PerformanceTestResult[]> {
    const results: PerformanceTestResult[] = []

    // Test 1: Cold profile loading (no cache)
    results.push(await this.testColdProfileLoading(userIds))

    // Test 2: Warm profile loading (with cache)
    results.push(await this.testWarmProfileLoading(userIds))

    // Test 3: Concurrent profile loading
    results.push(await this.testConcurrentProfileLoading(userIds))

    return results
  }

  /**
   * Run real-time performance tests
   */
  async runRealtimePerformanceTests(): Promise<PerformanceTestResult[]> {
    const results: PerformanceTestResult[] = []

    // Test 1: Subscription creation performance
    results.push(await this.testSubscriptionCreation())

    // Test 2: Event handling latency
    results.push(await this.testEventLatency())

    // Test 3: Connection stability
    results.push(await this.testConnectionStability())

    return results
  }

  /**
   * Run complete performance test suite
   */
  async runCompleteTestSuite(userIds: string[]): Promise<PerformanceTestSuite> {
    console.log('🚀 Starting complete performance test suite...')

    const startTime = Date.now()
    const allResults: PerformanceTestResult[] = []

    // Run all test categories
    const cacheResults = await this.runCachePerformanceTests()
    const profileResults = await this.runProfilePerformanceTests(userIds)
    const realtimeResults = await this.runRealtimePerformanceTests()

    allResults.push(...cacheResults, ...profileResults, ...realtimeResults)

    const totalTime = Date.now() - startTime
    const averageTime = allResults.reduce((sum, r) => sum + r.averageTime, 0) / allResults.length
    const overallCacheHitRate = this.calculateOverallCacheHitRate()
    const memoryEfficiency = this.calculateMemoryEfficiency()

    return {
      suiteName: 'Unified Services Performance Test',
      results: allResults,
      summary: {
        totalTests: allResults.length,
        totalTime,
        averageTime,
        overallCacheHitRate,
        memoryEfficiency
      }
    }
  }

  // Private test methods

  private async testCacheWrites(): Promise<PerformanceTestResult> {
    const iterations = 1000
    const times: number[] = []
    let errors = 0

    console.log('Testing cache write performance...')

    for (let i = 0; i < iterations; i++) {
      try {
        const startTime = performance.now()
        
        this.cache.set(`test:write:${i}`, {
          id: i,
          data: `Test data ${i}`,
          timestamp: Date.now()
        })
        
        const endTime = performance.now()
        times.push(endTime - startTime)
      } catch (error) {
        errors++
      }
    }

    return this.calculateTestResult('Cache Writes', iterations, times, errors)
  }

  private async testCacheReads(): Promise<PerformanceTestResult> {
    const iterations = 1000
    const times: number[] = []
    let errors = 0

    // Pre-populate cache
    for (let i = 0; i < iterations; i++) {
      this.cache.set(`test:read:${i}`, { id: i, data: `Test data ${i}` })
    }

    console.log('Testing cache read performance...')

    for (let i = 0; i < iterations; i++) {
      try {
        const startTime = performance.now()
        
        const result = this.cache.get(`test:read:${i}`)
        
        const endTime = performance.now()
        times.push(endTime - startTime)

        if (!result) {
          errors++
        }
      } catch (error) {
        errors++
      }
    }

    const cacheStats = this.cache.getStats()
    const result = this.calculateTestResult('Cache Reads', iterations, times, errors)
    result.cacheHitRate = cacheStats.hitRate

    return result
  }

  private async testCacheInvalidation(): Promise<PerformanceTestResult> {
    const iterations = 100
    const times: number[] = []
    let errors = 0

    // Pre-populate cache with pattern-based keys
    for (let i = 0; i < iterations; i++) {
      for (let j = 0; j < 10; j++) {
        this.cache.set(`test:invalidate:${i}:${j}`, { id: j, data: `Test data ${j}` })
      }
    }

    console.log('Testing cache invalidation performance...')

    for (let i = 0; i < iterations; i++) {
      try {
        const startTime = performance.now()
        
        this.cache.invalidate(`test:invalidate:${i}:*`)
        
        const endTime = performance.now()
        times.push(endTime - startTime)
      } catch (error) {
        errors++
      }
    }

    return this.calculateTestResult('Cache Invalidation', iterations, times, errors)
  }

  private async testMemoryUsage(): Promise<PerformanceTestResult> {
    const iterations = 5000
    const times: number[] = []
    let errors = 0

    console.log('Testing memory usage under load...')

    const initialMemory = this.cache.getStats().memoryUsage

    for (let i = 0; i < iterations; i++) {
      try {
        const startTime = performance.now()
        
        // Create larger objects to test memory management
        this.cache.set(`test:memory:${i}`, {
          id: i,
          data: new Array(100).fill(`Large data chunk ${i}`),
          metadata: {
            created: Date.now(),
            size: 100,
            type: 'test'
          }
        })
        
        const endTime = performance.now()
        times.push(endTime - startTime)
      } catch (error) {
        errors++
      }
    }

    const finalMemory = this.cache.getStats().memoryUsage
    const result = this.calculateTestResult('Memory Usage', iterations, times, errors)
    result.memoryUsage = finalMemory - initialMemory

    return result
  }

  private async testColdProfileLoading(userIds: string[]): Promise<PerformanceTestResult> {
    const times: number[] = []
    let errors = 0

    console.log('Testing cold profile loading...')

    // Clear cache first
    this.cache.clear()

    for (const userId of userIds) {
      try {
        const startTime = performance.now()
        
        await this.profileManager.getProfile(userId, { forceRefresh: true })
        
        const endTime = performance.now()
        times.push(endTime - startTime)
      } catch (error) {
        errors++
      }
    }

    return this.calculateTestResult('Cold Profile Loading', userIds.length, times, errors)
  }

  private async testWarmProfileLoading(userIds: string[]): Promise<PerformanceTestResult> {
    const times: number[] = []
    let errors = 0

    console.log('Testing warm profile loading...')

    // Load profiles once to populate cache
    for (const userId of userIds) {
      await this.profileManager.getProfile(userId)
    }

    // Now test cached loading
    for (const userId of userIds) {
      try {
        const startTime = performance.now()
        
        await this.profileManager.getProfile(userId)
        
        const endTime = performance.now()
        times.push(endTime - startTime)
      } catch (error) {
        errors++
      }
    }

    const cacheStats = this.cache.getStats()
    const result = this.calculateTestResult('Warm Profile Loading', userIds.length, times, errors)
    result.cacheHitRate = cacheStats.hitRate

    return result
  }

  private async testConcurrentProfileLoading(userIds: string[]): Promise<PerformanceTestResult> {
    console.log('Testing concurrent profile loading...')

    // Clear cache first
    this.cache.clear()

    const startTime = performance.now()
    
    try {
      // Load all profiles concurrently
      const promises = userIds.map(userId => 
        this.profileManager.getProfile(userId, { forceRefresh: true })
      )
      
      await Promise.all(promises)
      
      const endTime = performance.now()
      const totalTime = endTime - startTime

      return {
        testName: 'Concurrent Profile Loading',
        iterations: userIds.length,
        totalTime,
        averageTime: totalTime / userIds.length,
        minTime: totalTime / userIds.length,
        maxTime: totalTime / userIds.length,
        errors: 0
      }
    } catch (error) {
      return {
        testName: 'Concurrent Profile Loading',
        iterations: userIds.length,
        totalTime: 0,
        averageTime: 0,
        minTime: 0,
        maxTime: 0,
        errors: userIds.length
      }
    }
  }

  private async testSubscriptionCreation(): Promise<PerformanceTestResult> {
    const iterations = 50
    const times: number[] = []
    let errors = 0
    const subscriptions: any[] = []

    console.log('Testing subscription creation performance...')

    for (let i = 0; i < iterations; i++) {
      try {
        const startTime = performance.now()
        
        const subscription = this.realtime.subscribe(
          {
            table: 'test_table',
            event: '*',
            filter: `id=eq.${i}`
          },
          () => {},
          { deduplicate: false }
        )
        
        subscriptions.push(subscription)
        
        const endTime = performance.now()
        times.push(endTime - startTime)
      } catch (error) {
        errors++
      }
    }

    // Cleanup subscriptions
    subscriptions.forEach(sub => this.realtime.unsubscribe(sub))

    return this.calculateTestResult('Subscription Creation', iterations, times, errors)
  }

  private async testEventLatency(): Promise<PerformanceTestResult> {
    // This would require actual database events to test properly
    // For now, return a placeholder result
    return {
      testName: 'Event Latency',
      iterations: 0,
      totalTime: 0,
      averageTime: 0,
      minTime: 0,
      maxTime: 0,
      errors: 0
    }
  }

  private async testConnectionStability(): Promise<PerformanceTestResult> {
    console.log('Testing connection stability...')

    const startTime = performance.now()
    
    // Test reconnection
    try {
      await this.realtime.reconnect()
      const endTime = performance.now()
      
      return {
        testName: 'Connection Stability',
        iterations: 1,
        totalTime: endTime - startTime,
        averageTime: endTime - startTime,
        minTime: endTime - startTime,
        maxTime: endTime - startTime,
        errors: 0
      }
    } catch (error) {
      return {
        testName: 'Connection Stability',
        iterations: 1,
        totalTime: 0,
        averageTime: 0,
        minTime: 0,
        maxTime: 0,
        errors: 1
      }
    }
  }

  private calculateTestResult(
    testName: string,
    iterations: number,
    times: number[],
    errors: number
  ): PerformanceTestResult {
    const totalTime = times.reduce((sum, time) => sum + time, 0)
    const averageTime = times.length > 0 ? totalTime / times.length : 0
    const minTime = times.length > 0 ? Math.min(...times) : 0
    const maxTime = times.length > 0 ? Math.max(...times) : 0

    return {
      testName,
      iterations,
      totalTime,
      averageTime,
      minTime,
      maxTime,
      errors
    }
  }

  private calculateOverallCacheHitRate(): number {
    const stats = this.cache.getStats()
    return stats.hitRate
  }

  private calculateMemoryEfficiency(): number {
    const stats = this.cache.getStats()
    // Calculate efficiency as entries per KB
    return stats.totalEntries / (stats.memoryUsage / 1024)
  }
}

// Export utility functions
export function formatPerformanceResults(results: PerformanceTestResult[]): string {
  let output = '\n📊 Performance Test Results\n'
  output += '=' .repeat(50) + '\n\n'

  results.forEach(result => {
    output += `🔍 ${result.testName}\n`
    output += `   Iterations: ${result.iterations}\n`
    output += `   Total Time: ${result.totalTime.toFixed(2)}ms\n`
    output += `   Average Time: ${result.averageTime.toFixed(2)}ms\n`
    output += `   Min/Max: ${result.minTime.toFixed(2)}ms / ${result.maxTime.toFixed(2)}ms\n`
    
    if (result.cacheHitRate !== undefined) {
      output += `   Cache Hit Rate: ${(result.cacheHitRate * 100).toFixed(1)}%\n`
    }
    
    if (result.memoryUsage !== undefined) {
      output += `   Memory Usage: ${(result.memoryUsage / 1024).toFixed(1)} KB\n`
    }
    
    output += `   Errors: ${result.errors}\n\n`
  })

  return output
}

export function formatTestSuite(suite: PerformanceTestSuite): string {
  let output = `\n🎯 ${suite.suiteName}\n`
  output += '=' .repeat(50) + '\n'
  output += `Total Tests: ${suite.summary.totalTests}\n`
  output += `Total Time: ${suite.summary.totalTime.toFixed(2)}ms\n`
  output += `Average Time: ${suite.summary.averageTime.toFixed(2)}ms\n`
  output += `Overall Cache Hit Rate: ${(suite.summary.overallCacheHitRate * 100).toFixed(1)}%\n`
  output += `Memory Efficiency: ${suite.summary.memoryEfficiency.toFixed(1)} entries/KB\n\n`
  
  output += formatPerformanceResults(suite.results)
  
  return output
}
