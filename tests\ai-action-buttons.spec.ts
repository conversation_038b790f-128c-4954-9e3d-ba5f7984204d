import { test, expect } from '@playwright/test';

test.describe('AI Chat Action Buttons', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('http://localhost:5173');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
  });

  test('should show unauthenticated user actions when not logged in', async ({ page }) => {
    // Open AI chat
    await page.click('[data-testid="ai-chat-toggle"]');
    
    // Wait for chat to open
    await expect(page.locator('[data-testid="ai-chat-container"]')).toBeVisible();
    
    // Send a message to trigger AI response
    await page.fill('[data-testid="ai-chat-input"]', 'Hello, I need help getting started');
    await page.click('[data-testid="ai-chat-send"]');
    
    // Wait for AI response
    await page.waitForSelector('[data-testid="ai-message"]', { timeout: 10000 });
    
    // Check that unauthenticated actions are present
    await expect(page.locator('text=Sign Up')).toBeVisible();
    await expect(page.locator('text=Sign In')).toBeVisible();
    await expect(page.locator('text=Explore Community')).toBeVisible();
    
    // Check that authenticated-only actions are NOT present
    await expect(page.locator('text=Create Post')).not.toBeVisible();
    await expect(page.locator('text=Complete Profile')).not.toBeVisible();
    await expect(page.locator('text=Find Mentors')).not.toBeVisible();
  });

  test('should navigate correctly when Sign Up button is clicked', async ({ page }) => {
    // Open AI chat
    await page.click('[data-testid="ai-chat-toggle"]');
    
    // Send a message to get action buttons
    await page.fill('[data-testid="ai-chat-input"]', 'I want to join the platform');
    await page.click('[data-testid="ai-chat-send"]');
    
    // Wait for AI response with action buttons
    await page.waitForSelector('text=Sign Up', { timeout: 10000 });
    
    // Click Sign Up button
    await page.click('text=Sign Up');
    
    // Check that navigation occurred (URL should change to auth/signup)
    await expect(page).toHaveURL(/.*auth\/signup.*/);
  });

  test('should navigate correctly when Explore Community button is clicked', async ({ page }) => {
    // Open AI chat
    await page.click('[data-testid="ai-chat-toggle"]');
    
    // Send a message to get action buttons
    await page.fill('[data-testid="ai-chat-input"]', 'Show me the community');
    await page.click('[data-testid="ai-chat-send"]');
    
    // Wait for AI response with action buttons
    await page.waitForSelector('text=Explore Community', { timeout: 10000 });
    
    // Click Explore Community button
    await page.click('text=Explore Community');
    
    // Check that navigation occurred
    await expect(page).toHaveURL(/.*virtual-community.*/);
  });

  test('should show authenticated user actions when logged in', async ({ page }) => {
    // First, simulate login by setting auth state
    await page.addInitScript(() => {
      // Mock authenticated state
      window.localStorage.setItem('supabase.auth.token', JSON.stringify({
        access_token: 'mock-token',
        user: {
          id: 'test-user-id',
          email: '<EMAIL>'
        }
      }));
    });
    
    // Reload page to apply auth state
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    // Open AI chat
    await page.click('[data-testid="ai-chat-toggle"]');
    
    // Send a message to trigger AI response
    await page.fill('[data-testid="ai-chat-input"]', 'What can I do on the platform?');
    await page.click('[data-testid="ai-chat-send"]');
    
    // Wait for AI response
    await page.waitForSelector('[data-testid="ai-message"]', { timeout: 10000 });
    
    // Check that authenticated actions are present
    await expect(page.locator('text=Create Post')).toBeVisible();
    await expect(page.locator('text=Complete Profile')).toBeVisible();
    await expect(page.locator('text=Find Mentors')).toBeVisible();
    await expect(page.locator('text=View Dashboard')).toBeVisible();
    
    // Check that unauthenticated-only actions are NOT present
    await expect(page.locator('text=Sign Up')).not.toBeVisible();
    await expect(page.locator('text=Sign In')).not.toBeVisible();
  });

  test('should handle Find Mentors action correctly', async ({ page }) => {
    // Mock authenticated state
    await page.addInitScript(() => {
      window.localStorage.setItem('supabase.auth.token', JSON.stringify({
        access_token: 'mock-token',
        user: {
          id: 'test-user-id',
          email: '<EMAIL>'
        }
      }));
    });
    
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    // Open AI chat
    await page.click('[data-testid="ai-chat-toggle"]');
    
    // Send a message to get Find Mentors action
    await page.fill('[data-testid="ai-chat-input"]', 'I need to find mentors');
    await page.click('[data-testid="ai-chat-send"]');
    
    // Wait for AI response with action buttons
    await page.waitForSelector('text=Find Mentors', { timeout: 10000 });
    
    // Click Find Mentors button
    await page.click('text=Find Mentors');
    
    // Check that navigation occurred to profiles with mentor filter
    await expect(page).toHaveURL(/.*virtual-community.*tab=profiles.*filter=mentor.*/);
  });

  test('should handle action button validation errors gracefully', async ({ page }) => {
    // Listen for console errors
    const consoleErrors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    // Open AI chat
    await page.click('[data-testid="ai-chat-toggle"]');
    
    // Send a message
    await page.fill('[data-testid="ai-chat-input"]', 'Test message');
    await page.click('[data-testid="ai-chat-send"]');
    
    // Wait for response
    await page.waitForSelector('[data-testid="ai-message"]', { timeout: 10000 });
    
    // Check that no "Invalid action button" errors occurred
    const invalidActionErrors = consoleErrors.filter(error => 
      error.includes('Invalid action button')
    );
    
    expect(invalidActionErrors.length).toBe(0);
  });

  test('should display streaming responses correctly', async ({ page }) => {
    // Open AI chat
    await page.click('[data-testid="ai-chat-toggle"]');
    
    // Send a message
    await page.fill('[data-testid="ai-chat-input"]', 'Tell me about the platform');
    await page.click('[data-testid="ai-chat-send"]');
    
    // Wait for the message to appear
    const aiMessage = page.locator('[data-testid="ai-message"]').last();
    await expect(aiMessage).toBeVisible({ timeout: 10000 });
    
    // Check that the message content is not empty
    const messageContent = await aiMessage.textContent();
    expect(messageContent?.length).toBeGreaterThan(0);
    
    // Check that action buttons appear after the response
    await expect(page.locator('.ai-action-button')).toHaveCount({ min: 1 });
  });
});
