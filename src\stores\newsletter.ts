import { defineS<PERSON> } from 'pinia'
import { supabase } from '@/lib/supabase'
import type { Tables } from '@/lib/supabase'

type NewsletterSubscriber = Tables['newsletter_subscribers']['Row']
type NewsletterSubscriberInsert = Tables['newsletter_subscribers']['Insert']

interface NewsletterState {
  loading: boolean
  error: string | null
  subscriptionStatus: 'active' | 'unsubscribed' | null
}

export const useNewsletterStore = defineStore('newsletter', {
  state: (): NewsletterState => ({
    loading: false,
    error: null,
    subscriptionStatus: null
  }),

  actions: {
    async subscribe(data: NewsletterSubscriberInsert) {
      this.loading = true
      this.error = null

      try {
        const { data: subscriber, error } = await supabase
          .from('newsletter_subscribers')
          .insert(data)
          .select()
          .single()

        if (error) throw error

        this.subscriptionStatus = 'active'
        return subscriber as NewsletterSubscriber
      } catch (error: any) {
        this.error = error.message
        throw error
      } finally {
        this.loading = false
      }
    },

    async unsubscribe(email: string) {
      this.loading = true
      this.error = null

      try {
        const { error } = await supabase
          .from('newsletter_subscribers')
          .update({ status: 'unsubscribed' })
          .eq('email', email)

        if (error) throw error

        this.subscriptionStatus = 'unsubscribed'
      } catch (error: any) {
        this.error = error.message
        throw error
      } finally {
        this.loading = false
      }
    },

    async checkSubscriptionStatus(email: string) {
      this.loading = true
      this.error = null

      try {
        const { data, error } = await supabase
          .from('newsletter_subscribers')
          .select('status')
          .eq('email', email)
          .single()

        if (error) {
          if (error.code === 'PGRST116') {
            // No subscription found
            this.subscriptionStatus = null
            return null
          }
          throw error
        }

        this.subscriptionStatus = data.status
        return data.status
      } catch (error: any) {
        this.error = error.message
        throw error
      } finally {
        this.loading = false
      }
    }
  }
}) 