/**
 * Utility functions for text manipulation
 */

/**
 * Truncates text to a specified maximum length and adds ellipsis if needed
 * @param text The text to truncate
 * @param maxLength Maximum length of the truncated text (default: 250)
 * @returns Truncated text with ellipsis if needed
 */
export function truncateText(text: string, maxLength: number = 250): string {
  if (!text) return '';

  // If text is already shorter than the max length, return it as is
  if (text.length <= maxLength) return text;

  // Find the last space before the maxLength to avoid cutting words
  const lastSpace = text.substring(0, maxLength).lastIndexOf(' ');

  // If no space found or it's too close to the beginning, just cut at maxLength
  const cutoff = lastSpace > maxLength * 0.8 ? lastSpace : maxLength;

  // Return truncated text with ellipsis
  return text.substring(0, cutoff) + '...';
}

/**
 * Strips HTML tags from a string
 * @param html HTML string to strip
 * @returns Plain text without HTML tags
 */
export function stripHtml(html: string): string {
  if (!html) return '';
  return html.replace(/<[^>]*>/g, '');
}
