<template>
  <q-page>
    <div class="events-hero">
      <div class="container q-mx-auto q-px-md">
        <div class="row justify-center">
          <div class="col-10 col-sm-10 col-md-8">
            <div class="text-h2 text-weight-light q-mb-md text-center" style="color: #0D8A3E">Hub Events & Programs</div>
            <p class="text-body1 text-center q-mb-lg" style="max-width: 800px; margin: 0 auto;">
              Discover our upcoming events, workshops, and programs designed to foster innovation and collaboration.
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Featured Event Section -->
    <div class="featured-event-section q-py-xl">
      <div class="container q-mx-auto q-px-md">
        <div class="row justify-center">
          <div class="col-10 col-sm-10 col-md-8">
            <h2 class="text-h4 text-primary text-weight-light q-mb-lg text-center">Featured Event</h2>

            <q-card class="featured-event-card">
              <div class="row q-col-gutter-none">
                <div class="col-12 col-md-6">
                  <q-img
                    src="https://images.unsplash.com/photo-1540575467063-178a50c2df87?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80"
                    style="height: 100%; min-height: 300px;"
                  >
                    <div class="absolute-top-left q-ma-md">
                      <q-badge color="primary" class="text-bold q-pa-sm text-subtitle1">FEATURED</q-badge>
                    </div>
                  </q-img>
                </div>
                <div class="col-12 col-md-6">
                  <q-card-section class="q-pa-lg">
                    <div class="text-overline text-primary">INNOVATION SUMMIT</div>
                    <div class="text-h4 q-mb-md">Annual Innovation Summit 2025</div>
                    <div class="row items-center q-mb-md">
                      <q-icon name="event" color="primary" size="sm" class="q-mr-sm" />
                      <span class="text-body2">November 15-17, 2025</span>
                      <q-space />
                      <q-icon name="place" color="primary" size="sm" class="q-mr-sm" />
                      <span class="text-body2">ZbInnovation, Harare</span>
                    </div>
                    <p class="text-body1 q-mb-md">
                      Join us for our flagship annual event bringing together innovators, entrepreneurs, investors, and industry leaders for three days of inspiring talks, workshops, and networking opportunities.
                    </p>
                    <div class="row justify-between items-center">
                      <q-badge color="secondary" text-color="primary" class="q-pa-xs">
                        <q-icon name="people" size="xs" class="q-mr-xs" />
                        <span>500+ Attendees</span>
                      </q-badge>
                      <q-btn color="primary" label="Register Now" class="q-px-md" />
                    </div>
                  </q-card-section>
                </div>
              </div>
            </q-card>
          </div>
        </div>
      </div>
    </div>

    <!-- Upcoming Events Section -->
    <div class="upcoming-events-section q-py-xl bg-grey-2">
      <div class="container q-mx-auto q-px-md">
        <div class="row justify-center">
          <div class="col-10 col-sm-10 col-md-8">
            <div class="row items-center q-mb-lg">
              <div class="col">
                <h2 class="text-h4 text-primary text-weight-light">Upcoming Events</h2>
              </div>
              <div class="col-auto">
                <q-btn-toggle
                  v-model="eventFilter"
                  toggle-color="primary"
                  color="white"
                  text-color="primary"
                  :options="[
                    {label: 'All', value: 'all'},
                    {label: 'Physical', value: 'physical'},
                    {label: 'Virtual', value: 'virtual'}
                  ]"
                  class="q-mb-md"
                />
              </div>
            </div>

            <div class="row q-col-gutter-md">
              <div class="col-12 col-md-6" v-for="(event, index) in filteredEvents" :key="index">
                <q-card class="event-card" :class="{ 'animate-fade-in': isVisible }" :style="`animation-delay: ${index * 0.1}s`">
                  <q-img
                    :src="event.image"
                    :ratio="16/9"
                  >
                    <div class="absolute-bottom text-subtitle2 text-white q-pa-xs" :class="event.type === 'virtual' ? 'bg-blue-8' : 'bg-primary'">
                      {{ event.type === 'virtual' ? 'Virtual Event' : 'Physical Event' }}
                    </div>
                  </q-img>
                  <q-card-section>
                    <div class="text-subtitle2 text-primary">{{ event.category }}</div>
                    <div class="text-h6">{{ event.title }}</div>
                    <div class="row items-center q-mt-sm">
                      <q-icon name="event" color="grey" size="xs" class="q-mr-xs" />
                      <span class="text-caption text-grey">{{ event.date }}</span>
                      <q-space />
                      <q-chip size="sm" :color="event.type === 'virtual' ? 'blue-1' : 'green-1'" :text-color="event.type === 'virtual' ? 'blue-8' : 'primary'" dense>
                        {{ event.type === 'virtual' ? 'Online' : 'In-Person' }}
                      </q-chip>
                    </div>
                  </q-card-section>
                  <q-separator />
                  <q-card-actions>
                    <q-btn flat color="primary" label="Learn More" />
                    <q-space />
                    <q-btn flat color="primary" icon="event_available" label="Register" />
                  </q-card-actions>
                </q-card>
              </div>
            </div>

            <div class="text-center q-mt-lg">
              <q-btn outline color="primary" label="View All Events" icon-right="arrow_forward" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Programs Section -->
    <div class="programs-section q-py-xl">
      <div class="container q-mx-auto q-px-md">
        <div class="row justify-center">
          <div class="col-10 col-sm-10 col-md-8">
            <h2 class="text-h4 text-primary text-weight-light q-mb-lg text-center">Our Programs</h2>

            <div class="row q-col-gutter-lg">
              <div class="col-12 col-md-4" v-for="(program, index) in programs" :key="index">
                <q-card class="program-card" :class="{ 'animate-fade-in': isVisible }" :style="`animation-delay: ${index * 0.2}s`">
                  <q-img
                    :src="program.image"
                    :ratio="1"
                  >
                    <div class="absolute-full flex flex-center program-overlay">
                      <div class="text-h5 text-white text-center">{{ program.title }}</div>
                    </div>
                  </q-img>
                  <q-card-section>
                    <p class="text-body2 q-mb-md">{{ program.description }}</p>
                    <q-list dense>
                      <q-item v-for="(feature, fIndex) in program.features" :key="fIndex" dense class="q-pa-none q-my-xs">
                        <q-item-section avatar class="q-ml-none">
                          <q-icon name="check_circle" color="primary" size="xs" />
                        </q-item-section>
                        <q-item-section>
                          <q-item-label class="text-caption">{{ feature }}</q-item-label>
                        </q-item-section>
                      </q-item>
                    </q-list>
                  </q-card-section>
                  <q-card-actions align="right">
                    <q-btn flat color="primary" label="Apply Now" />
                  </q-card-actions>
                </q-card>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';

// Animation for cards
const isVisible = ref(false);
onMounted(() => {
  setTimeout(() => {
    isVisible.value = true;
  }, 500);
});

// Event filter
const eventFilter = ref('all');

// Events data
const events = ref([
  {
    title: 'Startup Pitch Competition',
    category: 'ENTREPRENEURSHIP',
    date: 'October 25, 2025',
    type: 'physical',
    image: 'https://images.unsplash.com/photo-1475721027785-f74eccf877e2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'
  },
  {
    title: 'Digital Marketing Masterclass',
    category: 'WORKSHOP',
    date: 'November 5, 2025',
    type: 'virtual',
    image: 'https://images.unsplash.com/photo-1432888498266-38ffec3eaf0a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1474&q=80'
  },
  {
    title: 'Blockchain Technology Seminar',
    category: 'TECHNOLOGY',
    date: 'November 12, 2025',
    type: 'physical',
    image: 'https://images.unsplash.com/photo-1639322537228-f710d846310a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1632&q=80'
  },
  {
    title: 'Women in Tech Networking',
    category: 'NETWORKING',
    date: 'December 20, 2025',
    type: 'virtual',
    image: 'https://images.unsplash.com/photo-1573164713988-8665fc963095?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1469&q=80'
  }
]);

// Filter events based on selected type
const filteredEvents = computed(() => {
  if (eventFilter.value === 'all') {
    return events.value;
  }
  return events.value.filter(event => event.type === eventFilter.value);
});

// Programs data
const programs = ref([
  {
    title: 'Startup Incubator',
    description: 'A 6-month program designed to help early-stage startups develop their business models and prepare for investment.',
    features: ['Mentorship from industry experts', 'Workspace access', 'Funding opportunities', 'Networking events'],
    image: 'https://images.unsplash.com/photo-1559136555-9303baea8ebd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'
  },
  {
    title: 'Innovation Accelerator',
    description: 'An intensive 3-month program for established startups looking to scale their operations and reach new markets.',
    features: ['Business strategy development', 'Market access support', 'Investor connections', 'Growth workshops'],
    image: 'https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'
  },
  {
    title: 'Youth Innovation Program',
    description: 'A program designed to nurture innovation and entrepreneurship skills among young people aged 16-24.',
    features: ['Skills development workshops', 'Mentorship', 'Project funding', 'Networking opportunities'],
    image: 'https://images.unsplash.com/photo-1529390079861-591de354faf5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'
  }
]);
</script>

<style scoped>
.container {
  width: 100%;
  max-width: 1400px;
}

.events-hero {
  padding: 80px 0;
  background-color: #f5f5f5;
  position: relative;
  overflow: hidden;
}

/* Add animated elements to hero section */
.events-hero::before,
.events-hero::after {
  content: '';
  position: absolute;
  width: 300px;
  height: 300px;
  border-radius: 50%;
  background: rgba(13, 138, 62, 0.05);
  z-index: 0;
}

.events-hero::before {
  top: -100px;
  right: -100px;
  animation: pulse 15s infinite alternate;
}

.events-hero::after {
  bottom: -100px;
  left: -100px;
  animation: pulse 12s infinite alternate-reverse;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.05;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.08;
  }
  100% {
    transform: scale(1);
    opacity: 0.05;
  }
}

.featured-event-section, .upcoming-events-section, .programs-section {
  padding: 60px 0;
}

.featured-event-card {
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.featured-event-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.1);
}

.event-card, .program-card {
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.event-card:hover, .program-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.1);
}

.program-overlay {
  background: linear-gradient(to bottom, rgba(13, 138, 62, 0.5), rgba(13, 138, 62, 0.8));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.program-card:hover .program-overlay {
  opacity: 1;
}

/* Animation classes */
.animate-fade-in {
  animation: fadeIn 0.8s ease forwards;
  opacity: 0;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.text-primary {
  color: #0D8A3E !important;
}

.bg-primary {
  background-color: #0D8A3E !important;
}
</style>
