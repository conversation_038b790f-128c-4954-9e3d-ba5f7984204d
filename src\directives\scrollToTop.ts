import type { Directive } from 'vue';

/**
 * Vue directive to scroll to the top of the page when a component is mounted
 * Usage: v-scroll-to-top
 */
export const scrollToTop: Directive = {
  mounted() {
    window.scrollTo({
      top: 0,
      behavior: 'auto'
    });
  }
};

/**
 * Vue directive to scroll to the top of the page when a component is updated
 * Usage: v-scroll-to-top-on-update
 */
export const scrollToTopOnUpdate: Directive = {
  mounted() {
    window.scrollTo({
      top: 0,
      behavior: 'auto'
    });
  },
  updated() {
    window.scrollTo({
      top: 0,
      behavior: 'auto'
    });
  }
};
