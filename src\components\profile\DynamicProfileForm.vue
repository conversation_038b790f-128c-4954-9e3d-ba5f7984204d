<template>
  <div class="dynamic-profile-form">
    <div v-if="loading" class="q-pa-md flex flex-center">
      <q-spinner color="primary" size="3em" />
      <div class="q-ml-md">Loading profile questions...</div>
    </div>

    <div v-else>
      <div v-for="(section, sectionIndex) in profileQuestions?.sections" :key="sectionIndex" class="q-mb-lg">
        <div class="text-h6 q-mb-sm">{{ section.title }}</div>
        <div v-if="section.description" class="text-subtitle2 q-mb-md">{{ section.description }}</div>

        <div class="row q-col-gutter-md">
          <template v-for="(field, fieldIndex) in section.questions" :key="fieldIndex">
            <!-- Only show fields if they don't have a condition or if the condition is met -->
            <template v-if="!field.condition || formData[field.condition.field] === field.condition.value">

              <!-- Text input -->
              <div v-if="field.type === 'text'" class="col-12" :class="{ 'col-md-6': !field.fullWidth }">
                <q-input
                  v-model="formData[field.name]"
                  :label="field.label + (field.required ? ' *' : '')"
                  outlined
                  :placeholder="field.placeholder"
                  :hint="field.hint || field.helpText"
                  :rules="field.required ? [(val: any) => !!val || `${field.label} is required`] : []"
                  @update:model-value="updateModelValue"
                >
                  <template v-slot:prepend>
                    <unified-icon name="edit" />
                  </template>
                </q-input>
              </div>

              <!-- Number input -->
              <div v-else-if="field.type === 'number'" class="col-12" :class="{ 'col-md-6': !field.fullWidth }">
                <q-input
                  v-model.number="formData[field.name]"
                  :label="field.label + (field.required ? ' *' : '')"
                  outlined
                  type="number"
                  :placeholder="field.placeholder"
                  :hint="field.hint || field.helpText"
                  :rules="field.required ? [(val: any) => val !== null && val !== undefined || `${field.label} is required`] : []"
                  @update:model-value="updateModelValue"
                >
                  <template v-slot:prepend>
                    <unified-icon name="edit" />
                  </template>
                </q-input>
              </div>

              <!-- Select input -->
              <div v-else-if="field.type === 'select'" class="col-12" :class="{ 'col-md-6': !field.fullWidth }">
                <enhanced-select
                  v-model="formData[field.name]"
                  :options="getOptions(field.options)"
                  :label="field.label + (field.required ? ' *' : '')"
                  outlined
                  :placeholder="field.placeholder"
                  :hint="field.hint || field.helpText"
                  :rules="field.required ? [(val: any) => !!val || `${field.label} is required`] : []"
                  @update:model-value="updateModelValue"
                />
              </div>

              <!-- Multi-select input -->
              <div v-else-if="field.type === 'multi-select'" class="col-12">
                <enhanced-select
                  v-model="formData[field.name]"
                  :options="getOptions(field.options)"
                  :label="field.label + (field.required ? ' *' : '')"
                  outlined
                  multiple
                  use-chips
                  :placeholder="field.placeholder"
                  :hint="field.hint || field.helpText"
                  :rules="field.required ? [(val: any) => val && val.length > 0 || `Please select at least one ${field.label.toLowerCase()}`] : []"
                  @update:model-value="(val) => handleMultiSelectChange(field.name, val)"
                />
                <div v-if="field.name === 'research_areas'" class="text-caption q-mt-xs">
                  Selected research areas: {{ formData[field.name] ? formData[field.name].length : 0 }}
                </div>
              </div>

              <!-- Boolean input -->
              <div v-else-if="field.type === 'boolean'" class="col-12">
                <q-toggle
                  v-model="formData[field.name]"
                  :label="field.label"
                  color="primary"
                  :hint="field.hint || field.helpText"
                  @update:model-value="updateModelValue"
                />
              </div>

              <!-- Textarea input -->
              <div v-else-if="field.type === 'textarea'" class="col-12" :class="{ 'col-md-6': !field.fullWidth }">
                <q-input
                  v-model="formData[field.name]"
                  :label="field.label + (field.required ? ' *' : '')"
                  outlined
                  type="textarea"
                  :placeholder="field.placeholder"
                  :hint="field.hint || field.helpText"
                  :rules="field.required ? [(val: any) => !!val || `${field.label} is required`] : []"
                  @update:model-value="updateModelValue"
                  rows="4"
                  autogrow
                >
                  <template v-slot:prepend>
                    <unified-icon name="edit" />
                  </template>
                </q-input>
              </div>

              <!-- Multi-select-free input -->
              <div v-else-if="field.type === 'multi-select-free'" class="col-12">
                <q-select
                  v-model="formData[field.name]"
                  :label="field.label + (field.required ? ' *' : '')"
                  outlined
                  multiple
                  use-chips
                  use-input
                  new-value-mode="add-unique"
                  :placeholder="field.placeholder"
                  :hint="field.hint || field.helpText"
                  :rules="field.required ? [(val: any) => val && val.length > 0 || `Please select at least one ${field.label.toLowerCase()}`] : []"
                  @update:model-value="(val) => handleMultiSelectChange(field.name, val)"
                  hide-dropdown-icon
                >
                  <template v-slot:append>
                    <unified-icon name="arrow_drop_down" />
                  </template>
                </q-select>
                <div v-if="['other_research_areas', 'research_centers'].includes(field.name)" class="text-caption q-mt-xs">
                  Selected items: {{ formData[field.name] ? formData[field.name].length : 0 }}
                </div>
              </div>

              <!-- Toggle input -->
              <div v-else-if="field.type === 'toggle'" class="col-12">
                <q-toggle
                  v-model="formData[field.name]"
                  :label="field.label"
                  color="primary"
                  :hint="field.hint || field.helpText"
                  @update:model-value="updateModelValue"
                />
              </div>
            </template>
          </template>
        </div>
      </div>

      <!-- Progress indicator -->
      <div class="q-mt-lg">
        <div class="text-subtitle1 q-mb-sm">Profile Completion</div>
        <q-linear-progress
          :value="completionPercentage / 100"
          color="primary"
          class="q-mb-xs"
        />
        <div class="text-caption text-grey">{{ completionPercentage }}% complete</div>
      </div>

      <!-- Save button -->
      <div class="q-mt-lg">
        <q-btn
          label="Save Profile"
          color="primary"
          :loading="saving"
          @click="saveProfile"
        >
          <template v-slot:append>
            <unified-icon name="check_circle" />
          </template>
        </q-btn>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useProfileQuestions } from '@/services/profileQuestions'
import UnifiedIcon from '../ui/UnifiedIcon.vue'
import EnhancedSelect from '../ui/EnhancedSelect.vue'

const props = defineProps<{
  profileType: string
  modelValue: any
  loading?: boolean
  isNewProfile?: boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: any): void
  (e: 'save'): void
}>()

const { setProfileType, currentQuestions, getFieldOptions } = useProfileQuestions()
const formData = ref<Record<string, any>>({})
const saving = ref(false)
const profileQuestions = computed(() => currentQuestions.value)

// Calculate completion percentage
const completionPercentage = computed(() => {
  if (!profileQuestions.value) return 0

  let totalFields = 0
  let filledFields = 0

  // Count required fields
  profileQuestions.value.sections.forEach(section => {
    section.questions.forEach(field => {
      if (field.required) {
        totalFields++

        // Check if field is filled
        const value = formData.value[field.name]
        if (value !== undefined && value !== null && value !== '') {
          if (Array.isArray(value)) {
            if (value.length > 0) filledFields++
          } else {
            filledFields++
          }
        }
      }
    })
  })

  if (totalFields === 0) return 100
  return Math.round((filledFields / totalFields) * 100)
})

// Get options for a field
function getOptions(optionsKey?: string) {
  if (!optionsKey) return []
  return getFieldOptions(optionsKey)
}

// Handle multi-select field changes
function handleMultiSelectChange(fieldName: string, value: any) {
  console.log(`DynamicProfileForm: handleMultiSelectChange called for ${fieldName}`, value);

  // Ensure the value is an array
  if (value === null || value === undefined) {
    formData.value[fieldName] = [];
  } else if (!Array.isArray(value)) {
    formData.value[fieldName] = [value];
  } else {
    formData.value[fieldName] = value;
  }

  // For database compatibility, convert arrays to JSON strings for certain fields
  const jsonFields = [
    'research_areas', 'other_research_areas', 'research_centers',
    'academic_programs', 'industry_partnerships', 'collaboration_types'
  ];

  if (jsonFields.includes(fieldName)) {
    // Store the original array value
    const originalArray = [...formData.value[fieldName]];

    // For database compatibility, we'll store the stringified version
    // but keep the array for UI display
    formData.value[`${fieldName}_original`] = originalArray;

    // For academic institution profile, directly convert to JSON string
    if (props.profileType === 'academic_institution' &&
        ['research_areas', 'other_research_areas', 'research_centers'].includes(fieldName)) {
      // Convert to JSON string for database compatibility
      formData.value[fieldName] = JSON.stringify(originalArray);
      console.log(`DynamicProfileForm: Converted ${fieldName} to JSON string:`, formData.value[fieldName]);
    }

    // Log the conversion
    console.log(`DynamicProfileForm: Converting ${fieldName} to JSON string for database compatibility`);
  }

  // Log the updated value
  console.log(`DynamicProfileForm: ${fieldName} updated to:`, formData.value[fieldName]);

  // Call updateModelValue to emit the updated form data
  updateModelValue();
}

// Update model value when form data changes
function updateModelValue() {
  console.log('DynamicProfileForm: updateModelValue called', {
    profileType: props.profileType,
    isNewProfile: props.isNewProfile,
    formDataKeys: Object.keys(formData.value)
  })

  // Check for contact fields and ensure they're properly set
  const contactFields = ['contact_email', 'contact_phone_country_code', 'contact_phone_number',
                         'whatsapp_country_code', 'whatsapp_number', 'telegram', 'skype',
                         'preferred_contact_method']

  // Log the current contact field values
  console.log('DynamicProfileForm: Contact field values before update:',
    contactFields.reduce((acc, field) => {
      acc[field] = formData.value[field]
      return acc
    }, {}))

  // Check for location, goals, and interest fields
  const jsonFields = [
    'preferred_locations',
    'short_term_goals', 'long_term_goals', 'current_challenges',
    'looking_for', 'collaboration_interests', 'sdg_alignment',
    // Academic institution specific JSON fields
    'research_areas', 'other_research_areas', 'research_centers',
    'academic_programs', 'industry_partnerships', 'collaboration_types'
  ]

  // Log the current JSON field values
  console.log('DynamicProfileForm: JSON field values before update:',
    jsonFields.reduce((acc, field) => {
      acc[field] = formData.value[field]
      return acc
    }, {}))

  // Ensure contact fields are properly set before updating model value
  // This is a safety check to make sure contact fields are included
  const personalDetailsFields = {
    'contact_email': props.modelValue?.contact_email,
    'contact_phone_country_code': props.modelValue?.contact_phone_country_code,
    'contact_phone_number': props.modelValue?.contact_phone_number,
    'bio': props.modelValue?.bio
  }

  // Only add fields that aren't already in formData
  for (const [key, value] of Object.entries(personalDetailsFields)) {
    if (value && !formData.value[key]) {
      console.log(`DynamicProfileForm: Adding missing field ${key} from modelValue:`, value)
      formData.value[key] = value
    }
  }

  // Ensure JSON fields are properly formatted
  jsonFields.forEach(field => {
    if (formData.value[field] !== undefined) {
      // If it's a string, try to parse it as JSON
      if (typeof formData.value[field] === 'string') {
        try {
          formData.value[field] = JSON.parse(formData.value[field])
        } catch (e) {
          // If parsing fails, treat it as a single-item array
          formData.value[field] = [formData.value[field]]
        }
      }

      // Ensure it's an array if null
      if (formData.value[field] === null) {
        formData.value[field] = []
      }

      // If it's still not an array or object, convert it to an array
      if (!Array.isArray(formData.value[field]) && typeof formData.value[field] !== 'object') {
        formData.value[field] = [formData.value[field]]
      }

      // For academic institution profile, ensure research fields are properly formatted for database
      if (props.profileType === 'academic_institution' &&
          ['research_areas', 'other_research_areas', 'research_centers'].includes(field)) {
        // Store both the array and string representation
        const originalArray = [...formData.value[field]];

        // Convert to JSON string for database compatibility
        if (Array.isArray(formData.value[field])) {
          formData.value[field] = JSON.stringify(formData.value[field]);
        }

        console.log(`DynamicProfileForm: Converted ${field} for database:`, {
          original: originalArray,
          stringified: formData.value[field]
        });
      }

      console.log(`DynamicProfileForm: Processed JSON field ${field}:`, formData.value[field])
    }
  })

  // Create a copy of the form data for emitting
  const emitData = {};

  // Process each field to ensure proper types
  for (const [key, value] of Object.entries(formData.value)) {
    // For academic institution profile, ensure research fields are properly formatted
    if (props.profileType === 'academic_institution' &&
        ['research_areas', 'other_research_areas', 'research_centers'].includes(key) &&
        Array.isArray(value)) {
      // Store as string for database compatibility
      emitData[key] = JSON.stringify(value);
      console.log(`DynamicProfileForm: Converted array to string for ${key}:`, emitData[key]);
    } else {
      // Keep other values as is
      emitData[key] = value;
    }
  }

  // Update the model value
  console.log('DynamicProfileForm: Emitting update:modelValue with processed data');
  emit('update:modelValue', emitData)

  // No longer emitting save event on every input change
  console.log('DynamicProfileForm: Model value updated, but not triggering autosave')
}

// Save profile
function saveProfile() {
  saving.value = true

  // Check for contact fields and ensure they're properly set
  const contactFields = ['contact_email', 'contact_phone_country_code', 'contact_phone_number',
                         'whatsapp_country_code', 'whatsapp_number', 'telegram', 'skype',
                         'preferred_contact_method']

  // Log the current contact field values
  console.log('DynamicProfileForm: Contact field values before save:',
    contactFields.reduce((acc, field) => {
      acc[field] = formData.value[field]
      return acc
    }, {}))

  // Check for location, goals, and interest fields
  const jsonFields = [
    'preferred_locations',
    'short_term_goals', 'long_term_goals', 'current_challenges',
    'looking_for', 'collaboration_interests', 'sdg_alignment',
    // Academic institution specific JSON fields
    'research_areas', 'other_research_areas', 'research_centers',
    'academic_programs', 'industry_partnerships', 'collaboration_types'
  ]

  // Log academic institution specific fields if this is an academic institution profile
  if (props.profileType === 'academic_institution') {
    console.log('DynamicProfileForm: Academic Institution Research Fields before save:', {
      research_areas: formData.value.research_areas,
      other_research_areas: formData.value.other_research_areas,
      research_centers: formData.value.research_centers,
      research_achievements: formData.value.research_achievements,
      has_tto: formData.value.has_tto,
      innovation_programs: formData.value.innovation_programs,
      research_funding: formData.value.research_funding
    })
  }

  // Log the current JSON field values
  console.log('DynamicProfileForm: JSON field values before save:',
    jsonFields.reduce((acc, field) => {
      acc[field] = formData.value[field]
      return acc
    }, {}))

  // Ensure all form data is included in the emit
  console.log('DynamicProfileForm: All form data before save:', formData.value)
  console.log('DynamicProfileForm: Form data keys:', Object.keys(formData.value))

  // Ensure contact fields are properly set before saving
  // This is a safety check to make sure contact fields are included
  const personalDetailsFields = {
    'contact_email': props.modelValue?.contact_email,
    'contact_phone_country_code': props.modelValue?.contact_phone_country_code,
    'contact_phone_number': props.modelValue?.contact_phone_number,
    'bio': props.modelValue?.bio
  }

  // Only add fields that aren't already in formData
  for (const [key, value] of Object.entries(personalDetailsFields)) {
    if (value && !formData.value[key]) {
      console.log(`DynamicProfileForm: Adding missing field ${key} from modelValue:`, value)
      formData.value[key] = value
    }
  }

  // Ensure JSON fields are properly formatted
  jsonFields.forEach(field => {
    if (formData.value[field] !== undefined) {
      // If it's a string, try to parse it as JSON
      if (typeof formData.value[field] === 'string') {
        try {
          formData.value[field] = JSON.parse(formData.value[field])
        } catch (e) {
          // If parsing fails, treat it as a single-item array
          formData.value[field] = [formData.value[field]]
        }
      }

      // Ensure it's an array if null
      if (formData.value[field] === null) {
        formData.value[field] = []
      }

      // If it's still not an array or object, convert it to an array
      if (!Array.isArray(formData.value[field]) && typeof formData.value[field] !== 'object') {
        formData.value[field] = [formData.value[field]]
      }

      // For academic institution profile, ensure research fields are properly formatted for database
      if (props.profileType === 'academic_institution' &&
          ['research_areas', 'other_research_areas', 'research_centers'].includes(field)) {
        // Store both the array and string representation
        const originalArray = [...formData.value[field]];

        // Convert to JSON string for database compatibility
        if (Array.isArray(formData.value[field])) {
          formData.value[field] = JSON.stringify(formData.value[field]);
        }

        console.log(`DynamicProfileForm: Converted ${field} for database:`, {
          original: originalArray,
          stringified: formData.value[field]
        });
      }

      console.log(`DynamicProfileForm: Processed JSON field ${field}:`, formData.value[field])
    }
  })

  // Create a copy of the form data for emitting
  const emitData = {};

  // Process each field to ensure proper types
  for (const [key, value] of Object.entries(formData.value)) {
    // For academic institution profile, ensure research fields are properly formatted
    if (props.profileType === 'academic_institution' &&
        ['research_areas', 'other_research_areas', 'research_centers'].includes(key) &&
        Array.isArray(value)) {
      // Store as string for database compatibility
      emitData[key] = JSON.stringify(value);
      console.log(`DynamicProfileForm: Converted array to string for ${key} before save:`, emitData[key]);
    } else {
      // Keep other values as is
      emitData[key] = value;
    }
  }

  // Update the form data with the processed data
  formData.value = emitData;

  // Emit update:modelValue first to ensure parent has latest data
  console.log('DynamicProfileForm: Emitting update:modelValue with processed data before save');
  emit('update:modelValue', formData.value);

  // Emit save event
  console.log('DynamicProfileForm: Emitting save with processed data');
  emit('save')

  // Simulate API call
  setTimeout(() => {
    saving.value = false
  }, 1000)
}

// Initialize form data when props change
if (props.modelValue) {
  console.log('DynamicProfileForm: Initial modelValue:', props.modelValue)
  console.log('DynamicProfileForm: Initial modelValue keys:', Object.keys(props.modelValue))

  // Check for expertise fields if this is a mentor profile
  if (props.profileType === 'mentor') {
    console.log('DynamicProfileForm: Mentor profile expertise fields in modelValue:', {
      expertise_areas: props.modelValue.expertise_areas,
      areas_of_expertise: props.modelValue.areas_of_expertise
    })
  }

  // Make sure expertise_areas is available for mentor profiles
  if (props.profileType === 'mentor' && props.modelValue.areas_of_expertise && !props.modelValue.expertise_areas) {
    console.log('DynamicProfileForm: Copying areas_of_expertise to expertise_areas')
    const updatedModelValue = { ...props.modelValue, expertise_areas: props.modelValue.areas_of_expertise }
    formData.value = updatedModelValue
  } else {
    formData.value = { ...props.modelValue }
  }
  console.log('DynamicProfileForm: Initial formData:', formData.value)
}

// Set initial profile type
if (props.profileType) {
  setProfileType(props.profileType)
}

// Update form data when props change
onMounted(() => {
  console.log('DynamicProfileForm: Component mounted')
  if (props.modelValue) {
    console.log('DynamicProfileForm: Initial model value:', props.modelValue)
    console.log('DynamicProfileForm: Initial model value keys:', Object.keys(props.modelValue))

    // Check for expertise fields if this is a mentor profile
    if (props.profileType === 'mentor') {
      console.log('DynamicProfileForm: Mentor profile expertise fields in onMounted:', {
        expertise_areas: props.modelValue.expertise_areas,
        areas_of_expertise: props.modelValue.areas_of_expertise
      })

      // Make sure expertise_areas is available for mentor profiles
      if (props.modelValue.areas_of_expertise && !props.modelValue.expertise_areas) {
        console.log('DynamicProfileForm: Copying areas_of_expertise to expertise_areas in onMounted')
        const updatedModelValue = { ...props.modelValue, expertise_areas: props.modelValue.areas_of_expertise }
        formData.value = updatedModelValue
      } else {
        formData.value = { ...props.modelValue }
      }
    } else {
      formData.value = { ...props.modelValue }
    }

    console.log('DynamicProfileForm: formData after assignment:', formData.value)
  } else {
    console.log('DynamicProfileForm: No initial model value provided')
  }

  if (props.profileType) {
    console.log('DynamicProfileForm: Initial profile type:', props.profileType)
    setProfileType(props.profileType)
    console.log('DynamicProfileForm: Profile questions after setting type:', profileQuestions.value)
  } else {
    console.log('DynamicProfileForm: No profile type provided')
  }
})

// Watch for changes in the model value
watch(() => props.modelValue, (newValue) => {
  console.log('DynamicProfileForm: Model value watch triggered')
  if (newValue && Object.keys(newValue).length > 0) {
    console.log('DynamicProfileForm: Model value changed:', newValue)
    console.log('DynamicProfileForm: Model value keys:', Object.keys(newValue))
    // Only update if the new value is different to avoid loops
    const currentKeys = Object.keys(formData.value)
    const newKeys = Object.keys(newValue)

    // Check if we have new keys or different values
    const hasNewKeys = newKeys.some(key => !currentKeys.includes(key))
    const hasDifferentValues = newKeys.some(key => formData.value[key] !== newValue[key])

    console.log('DynamicProfileForm: Current keys:', currentKeys)
    console.log('DynamicProfileForm: New keys:', newKeys)
    console.log('DynamicProfileForm: Has new keys:', hasNewKeys)
    console.log('DynamicProfileForm: Has different values:', hasDifferentValues)

    // Check for contact fields in the new value
    const contactFields = ['contact_email', 'contact_phone_country_code', 'contact_phone_number',
                           'whatsapp_country_code', 'whatsapp_number', 'telegram', 'skype',
                           'preferred_contact_method']

    const hasContactFields = contactFields.some(field => newValue[field] !== undefined && newValue[field] !== null)
    console.log('DynamicProfileForm: Has contact fields:', hasContactFields)

    if (hasContactFields) {
      console.log('DynamicProfileForm: Contact fields in new value:',
        contactFields.reduce((acc, field) => {
          acc[field] = newValue[field]
          return acc
        }, {}))
    }

    if (hasNewKeys || hasDifferentValues) {
      console.log('DynamicProfileForm: Updating form data with new model value')

      // Make sure expertise_areas is available for mentor profiles
      if (props.profileType === 'mentor' && newValue.areas_of_expertise && !newValue.expertise_areas) {
        console.log('DynamicProfileForm: Copying areas_of_expertise to expertise_areas in watch')
        const updatedModelValue = { ...newValue, expertise_areas: newValue.areas_of_expertise }
        formData.value = updatedModelValue
      } else {
        formData.value = { ...newValue }
      }

      // Ensure contact fields are preserved
      if (hasContactFields) {
        console.log('DynamicProfileForm: Ensuring contact fields are preserved')
        contactFields.forEach(field => {
          if (newValue[field] !== undefined && newValue[field] !== null) {
            formData.value[field] = newValue[field]
            console.log(`DynamicProfileForm: Preserved contact field ${field}:`, formData.value[field])
          }
        })
      }

      console.log('DynamicProfileForm: Updated formData:', formData.value)
    } else {
      console.log('DynamicProfileForm: No changes detected, not updating formData')
    }
  } else {
    console.log('DynamicProfileForm: Empty or null model value received')
  }
})

// Watch for changes in the profile type
watch(() => props.profileType, (newType) => {
  if (newType) {
    console.log('DynamicProfileForm: Profile type changed:', newType)
    setProfileType(newType)
  }
})
</script>

<style scoped>
.dynamic-profile-form {
  max-width: 1200px;
  margin: 0 auto;
}
</style>
