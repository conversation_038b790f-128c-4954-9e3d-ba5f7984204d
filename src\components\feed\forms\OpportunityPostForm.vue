<template>
  <div class="opportunity-post-form">
    <q-form @submit="handleSubmit" class="q-gutter-md">
      <!-- Title -->
      <q-input
        v-model="formData.title"
        label="Title"
        outlined
        :rules="[val => !!val || 'Title is required', val => val.length <= 100 || 'Maximum 100 characters']"
        counter
        maxlength="100"
      />

      <!-- Opportunity Type -->
      <q-select
        v-model="formData.opportunityType"
        :options="opportunityTypeOptions"
        label="Opportunity Type"
        outlined
        emit-value
        map-options
        :rules="[val => !!val || 'Opportunity type is required']"
      >
        <template v-slot:prepend>
          <q-icon name="emoji_objects" />
        </template>
      </q-select>

      <!-- Category -->
      <q-select
        v-model="formData.category"
        :options="categoryOptions"
        label="Category"
        outlined
        emit-value
        map-options
        :rules="[val => !!val || 'Category is required']"
      >
        <template v-slot:prepend>
          <q-icon name="category" />
        </template>
      </q-select>

      <!-- Description -->
      <q-input
        v-model="formData.content"
        type="textarea"
        label="Description"
        outlined
        autogrow
        :rules="[val => !!val || 'Description is required', val => val.length <= 2000 || 'Maximum 2000 characters']"
        counter
        maxlength="2000"
      />

      <!-- Deadline -->
      <q-input
        v-model="formData.opportunityDeadline"
        label="Application Deadline (optional)"
        outlined
        mask="date"
        :rules="['date']"
      >
        <template v-slot:append>
          <q-icon name="event" class="cursor-pointer">
            <q-popup-proxy cover transition-show="scale" transition-hide="scale">
              <q-date v-model="formData.opportunityDeadline">
                <div class="row items-center justify-end">
                  <q-btn v-close-popup label="Close" color="primary" flat />
                </div>
              </q-date>
            </q-popup-proxy>
          </q-icon>
        </template>
      </q-input>

      <!-- Application URL -->
      <q-input
        v-model="formData.applicationUrl"
        label="Application URL (optional)"
        outlined
        type="url"
        :rules="[val => !val || isValidUrl(val) || 'Please enter a valid URL']"
      >
        <template v-slot:prepend>
          <q-icon name="link" />
        </template>
      </q-input>

      <!-- Image Upload -->
      <div class="q-mb-md">
        <q-file
          v-model="imageFile"
          label="Add Cover Image (optional)"
          outlined
          accept=".jpg, .jpeg, .png, .gif"
          @update:model-value="handleImageUpload"
          max-file-size="5242880"
          @rejected="onRejected"
        >
          <template v-slot:prepend>
            <q-icon name="attach_file" />
          </template>
        </q-file>

        <!-- Image Preview -->
        <div v-if="formData.image" class="image-preview q-mt-sm">
          <q-img :src="formData.image" style="max-height: 200px; max-width: 100%;" />
          <q-btn
            round
            color="negative"
            icon="delete"
            size="sm"
            class="absolute-top-right"
            @click="removeImage"
          />
        </div>
      </div>

      <!-- Predefined Tags -->
      <div class="q-mb-md">
        <div class="text-subtitle2 q-mb-xs">Tags (select multiple)</div>
        <q-select
          v-model="selectedPredefinedTag"
          :options="predefinedTagsFiltered"
          label="Select from predefined tags"
          outlined
          clearable
          use-input
          hide-selected
          fill-input
          @filter="filterTags"
          @update:model-value="addPredefinedTag"
        >
          <template v-slot:prepend>
            <q-icon name="local_offer" />
          </template>
          <template v-slot:no-option>
            <q-item>
              <q-item-section class="text-grey">
                No results
              </q-item-section>
            </q-item>
          </template>
          <template v-slot:after>
            <q-badge color="primary" floating>
              {{ formData.tags.length }}
            </q-badge>
          </template>
        </q-select>
        <div class="text-caption text-grey q-mt-xs">
          Select a tag and click to add it. You can add multiple tags.
        </div>
      </div>

      <!-- Custom Tags -->
      <q-input
        v-model="tagsInput"
        label="Or add custom tags (comma-separated)"
        outlined
        hint="Enter tags separated by commas"
        @blur="processTags"
      >
        <template v-slot:prepend>
          <q-icon name="add_circle" />
        </template>
      </q-input>

      <!-- Tags Display -->
      <div v-if="formData.tags && formData.tags.length > 0" class="q-mb-md q-mt-sm">
        <div class="text-subtitle2 q-mb-xs">Selected Tags:</div>
        <div class="row q-gutter-xs">
          <q-chip
            v-for="(tag, index) in formData.tags"
            :key="index"
            removable
            @remove="removeTag(index)"
            color="primary"
            text-color="white"
            size="md"
          >
            {{ tag }}
          </q-chip>
        </div>
      </div>

      <!-- Visibility -->
      <div class="q-mb-md">
        <q-select
          v-model="formData.visibility"
          :options="visibilityOptions"
          label="Visibility"
          outlined
          emit-value
          map-options
        >
          <template v-slot:prepend>
            <q-icon name="visibility" />
          </template>
        </q-select>
      </div>

      <!-- Action Buttons -->
      <div class="row justify-end q-gutter-sm">
        <q-btn
          label="Cancel"
          color="grey"
          flat
          @click="$emit('cancel')"
        />
        <q-btn
          label="Post"
          type="submit"
          color="primary"
          :loading="loading"
          :disable="loading"
        >
          <template v-slot:loading>
            <q-spinner-dots size="24px" />
          </template>
        </q-btn>
      </div>
    </q-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useQuasar } from 'quasar';

const emit = defineEmits(['submit', 'cancel']);
const $q = useQuasar();

// State
const loading = ref(false);
const imageFile = ref(null);
const tagsInput = ref('');
const selectedPredefinedTag = ref(null);
const formData = ref({
  title: '',
  content: '',
  image: null,
  category: '',
  opportunityType: '',
  opportunityDeadline: '',
  applicationUrl: '',
  tags: [],
  visibility: 'public',
  postType: 'opportunity'
});

// Options
const opportunityTypeOptions = [
  { label: 'Funding Opportunity', value: 'funding_opportunity', icon: 'attach_money' },
  { label: 'Collaboration Opportunity', value: 'collaboration_opportunity', icon: 'handshake' },
  { label: 'Mentorship Opportunity', value: 'mentorship_opportunity', icon: 'school' },
  { label: 'Research Partnership', value: 'research_opportunity', icon: 'science' },
  { label: 'Incubation', value: 'incubation_opportunity', icon: 'egg' },
  { label: 'Acceleration', value: 'acceleration_opportunity', icon: 'speed' }
];

const categoryOptions = [
  { label: 'General', value: 'general', icon: 'feed' },
  { label: 'Funding', value: 'funding', icon: 'attach_money' },
  { label: 'Collaboration', value: 'collaboration', icon: 'handshake' },
  { label: 'Mentorship', value: 'mentorship', icon: 'school' },
  { label: 'Innovation', value: 'innovation', icon: 'lightbulb' },
  { label: 'Research', value: 'research', icon: 'science' },
  { label: 'Training', value: 'training', icon: 'menu_book' },
  { label: 'Technology', value: 'technology', icon: 'devices' },
  { label: 'Healthcare', value: 'healthcare', icon: 'health_and_safety' },
  { label: 'Education', value: 'education', icon: 'school' },
  { label: 'Agriculture', value: 'agriculture', icon: 'grass' },
  { label: 'Energy', value: 'energy', icon: 'bolt' },
  { label: 'Finance', value: 'finance', icon: 'payments' },
  { label: 'Social Impact', value: 'social_impact', icon: 'public' }
];

const visibilityOptions = [
  { label: 'Public', value: 'public', icon: 'public' },
  { label: 'Connections Only', value: 'connections', icon: 'people' },
  { label: 'Private', value: 'private', icon: 'lock' }
];

// Predefined tags based on opportunity types and categories
const predefinedTagOptions = [
  // Funding related tags
  { label: 'funding', value: 'funding' },
  { label: 'grant', value: 'grant' },
  { label: 'investment', value: 'investment' },
  { label: 'seed-funding', value: 'seed-funding' },
  { label: 'venture-capital', value: 'venture-capital' },
  { label: 'angel-investor', value: 'angel-investor' },
  { label: 'startup-funding', value: 'startup-funding' },

  // Collaboration related tags
  { label: 'collaboration', value: 'collaboration' },
  { label: 'partnership', value: 'partnership' },
  { label: 'joint-venture', value: 'joint-venture' },
  { label: 'co-creation', value: 'co-creation' },
  { label: 'research-collaboration', value: 'research-collaboration' },

  // Mentorship related tags
  { label: 'mentorship', value: 'mentorship' },
  { label: 'coaching', value: 'coaching' },
  { label: 'advisory', value: 'advisory' },
  { label: 'guidance', value: 'guidance' },
  { label: 'expertise', value: 'expertise' },

  // Research related tags
  { label: 'research', value: 'research' },
  { label: 'development', value: 'development' },
  { label: 'innovation', value: 'innovation' },
  { label: 'technology', value: 'technology' },
  { label: 'science', value: 'science' },

  // Incubation/Acceleration related tags
  { label: 'incubation', value: 'incubation' },
  { label: 'acceleration', value: 'acceleration' },
  { label: 'startup', value: 'startup' },
  { label: 'scale-up', value: 'scale-up' },
  { label: 'business-development', value: 'business-development' },

  // Industry specific tags
  { label: 'healthcare', value: 'healthcare' },
  { label: 'education', value: 'education' },
  { label: 'agriculture', value: 'agriculture' },
  { label: 'energy', value: 'energy' },
  { label: 'finance', value: 'finance' },
  { label: 'technology', value: 'technology' },
  { label: 'social-impact', value: 'social-impact' },

  // General tags
  { label: 'opportunity', value: 'opportunity' },
  { label: 'deadline', value: 'deadline' },
  { label: 'application', value: 'application' },
  { label: 'networking', value: 'networking' }
];

// Filtered predefined tags
const predefinedTagsFiltered = ref(predefinedTagOptions);

// Methods
function handleImageUpload(file) {
  if (!file) return;

  // In a real implementation, this would upload the file to a server
  // For now, we'll just create a data URL
  const reader = new FileReader();
  reader.onload = (e) => {
    formData.value.image = e.target.result;
  };
  reader.readAsDataURL(file);
}

function removeImage() {
  formData.value.image = null;
  imageFile.value = null;
}

function onRejected(rejectedEntries) {
  // Display notification for rejected files
  rejectedEntries.forEach(entry => {
    let message = '';
    if (entry.failedPropValidation === 'accept') {
      message = 'Please upload only image files (JPG, PNG, GIF)';
    } else if (entry.failedPropValidation === 'max-file-size') {
      message = 'File is too large. Maximum size is 5MB';
    } else {
      message = 'File upload failed';
    }

    $q.notify({
      type: 'negative',
      message
    });
  });
}

// Filter predefined tags based on user input
function filterTags(val, update) {
  if (val === '') {
    update(() => {
      // Show all options when no search term
      predefinedTagsFiltered.value = predefinedTagOptions;
    });
    return;
  }

  update(() => {
    const needle = val.toLowerCase();
    // Filter options based on search term
    predefinedTagsFiltered.value = predefinedTagOptions.filter(
      v => v.label.toLowerCase().indexOf(needle) > -1
    );
  });
}

// Add a predefined tag to the list
function addPredefinedTag(tag) {
  if (!tag) return;

  // Extract the tag value if it's an object
  let tagValue;
  if (typeof tag === 'object' && tag !== null) {
    // Use the value property if available, otherwise use label
    tagValue = tag.value || tag.label;
  } else {
    tagValue = tag;
  }

  // Add the tag if it's not already in the list
  if (tagValue && !formData.value.tags.includes(tagValue)) {
    formData.value.tags.push(tagValue);
  }

  // Clear the selection for next use
  selectedPredefinedTag.value = null;
}

// Process manually entered tags
function processTags() {
  if (!tagsInput.value) return;

  // Split by comma and trim whitespace
  const newTags = tagsInput.value.split(',')
    .map(tag => tag.trim())
    .filter(tag => tag && !formData.value.tags.includes(tag)); // Only add tags that don't already exist

  // Add new tags to the existing tags
  if (newTags.length > 0) {
    formData.value.tags = [...formData.value.tags, ...newTags];
  }

  // Clear the input
  tagsInput.value = '';
}

// Remove a tag from the list
function removeTag(index) {
  formData.value.tags.splice(index, 1);
}

function isValidUrl(url) {
  try {
    new URL(url);
    return true;
  } catch (e) {
    return false;
  }
}

async function handleSubmit() {
  loading.value = true;

  try {
    // Process tags one more time before submission
    processTags();

    // Automatically add main category and subcategory as tags
    // For opportunities, the main category is always 'opportunity'
    if (!formData.value.tags.includes('opportunity')) {
      formData.value.tags.push('opportunity');
    }

    // Add opportunity type as a tag if it exists and is not already included
    if (formData.value.opportunityType) {
      const typeTag = formData.value.opportunityType.replace('_opportunity', '').toLowerCase();
      if (!formData.value.tags.includes(typeTag)) {
        formData.value.tags.push(typeTag);
      }
    }

    // Add category as a tag if it exists and is not already included
    if (formData.value.category) {
      const categoryTag = formData.value.category.toLowerCase();
      if (!formData.value.tags.includes(categoryTag)) {
        formData.value.tags.push(categoryTag);
      }
    }

    // If still no tags were added, add a default 'opportunity' tag
    if (!formData.value.tags || formData.value.tags.length === 0) {
      formData.value.tags = ['opportunity'];
    }

    // Create post object with postType
    const post = {
      ...formData.value,
      postType: 'opportunity',
      subType: formData.value.opportunityType || 'general',
      author: 'Current User', // This would come from the auth store
      avatar: 'https://cdn.quasar.dev/img/avatar.png', // This would come from the auth store
      date: new Date().toLocaleString(),
      likes: 0,
      comments: 0
    };

    // Emit the post data to parent component
    emit('submit', post);
  } catch (error) {
    console.error('Error creating opportunity post:', error);
    $q.notify({
      type: 'negative',
      message: 'Failed to create opportunity post. Please try again.'
    });
  } finally {
    // Add a small delay to show the loading state
    setTimeout(() => {
      loading.value = false;
    }, 500);
  }
}
</script>

<style scoped>
.image-preview {
  position: relative;
  display: inline-block;
}
</style>
