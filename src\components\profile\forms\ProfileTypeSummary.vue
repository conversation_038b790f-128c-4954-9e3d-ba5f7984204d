<template>
  <div class="profile-type-summary">
    <div v-if="!profileData || Object.keys(profileData).length === 0" class="text-center q-pa-md">
      <q-icon name="info" color="grey-7" size="48px" />
      <div class="text-subtitle1 q-mt-sm">No profile data available</div>
    </div>
    
    <div v-else>
      <div class="row q-col-gutter-md">
        <template v-for="(value, key) in formattedData" :key="key">
          <div class="col-12 col-md-6" v-if="value !== null && value !== undefined && value !== ''">
            <div class="text-caption text-grey-8">{{ formatKey(key) }}</div>
            <div class="q-mt-xs">
              <!-- Arrays (display as chips) -->
              <template v-if="Array.isArray(value)">
                <div class="row q-col-gutter-xs">
                  <div v-for="(item, index) in value" :key="index" class="col-auto">
                    <q-chip dense color="primary" text-color="white" size="sm">{{ item }}</q-chip>
                  </div>
                </div>
              </template>
              
              <!-- URLs (display as links) -->
              <template v-else-if="isUrl(value)">
                <a :href="value" target="_blank" rel="noopener noreferrer" class="text-primary">
                  {{ value }}
                  <q-icon name="open_in_new" size="xs" class="q-ml-xs" />
                </a>
              </template>
              
              <!-- Regular text -->
              <template v-else>
                {{ value }}
              </template>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps({
  profileData: {
    type: Object,
    default: () => ({})
  }
});

// Format the data for display
const formattedData = computed(() => {
  const data = { ...props.profileData };
  
  // Remove internal fields
  delete data.id;
  delete data.profile_id;
  delete data.profile_type_id;
  delete data.created_at;
  delete data.updated_at;
  
  return data;
});

// Format key names for display
function formatKey(key: string): string {
  // Convert snake_case to Title Case
  return key
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// Check if a value is a URL
function isUrl(value: string): boolean {
  try {
    new URL(value);
    return true;
  } catch {
    return false;
  }
}
</script>

<style scoped>
.profile-type-summary {
  width: 100%;
}
</style>
