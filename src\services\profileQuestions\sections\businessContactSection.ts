// Business Contact Information Section
// This section is for business/organization contact information
// Used for innovator, investor, and organisation profiles

import { ProfileSection } from '../types';

export const businessContactSection: ProfileSection = {
  title: 'Business Contact Information',
  icon: 'business',
  description: 'Share your business or organization contact information (separate from your personal contact details)',
  questions: [
    {
      id: 'contact_email',
      name: 'contact_email',
      label: 'Business Email',
      type: 'text',
      required: true,
      hint: 'Your business email for contacts (if different from personal email)'
    },
    {
      id: 'contact_phone',
      name: 'contact_phone',
      label: 'Business Phone',
      type: 'text',
      hint: 'Your business phone number with country code (if different from personal phone)'
    },
    {
      id: 'whatsapp',
      name: 'whatsapp',
      label: 'Business WhatsApp',
      type: 'text',
      hint: 'Your business WhatsApp number (if different from personal WhatsApp)'
    },
    {
      id: 'telegram',
      name: 'telegram',
      label: 'Business Telegram',
      type: 'text',
      hint: 'Your business Telegram username'
    },
    {
      id: 'skype',
      name: 'skype',
      label: 'Business Skype',
      type: 'text',
      hint: 'Your business Skype ID'
    },
    {
      id: 'preferred_contact_method',
      name: 'preferred_contact_method',
      label: 'Preferred Business Contact Method',
      type: 'select',
      options: 'contactMethodOptions',
      hint: 'How would you prefer to be contacted for business matters?'
    },
    {
      id: 'availability_for_contact',
      name: 'availability_for_contact',
      label: 'Business Availability for Contact',
      type: 'text',
      hint: 'When are you typically available for business contact? (e.g., weekdays 9-5 CAT)'
    }
  ]
};
