// Common Location Section
// This section can be included in all profile types

import { ProfileSection } from '../types';

export const locationSection: ProfileSection = {
  title: 'Location',
  icon: 'location_on',
  description: 'Share your location information',
  questions: [
    {
      id: 'address',
      name: 'address',
      label: 'Address',
      type: 'text',
      fullWidth: true,
      hint: 'Your physical address'
    },
    {
      id: 'city',
      name: 'city',
      label: 'City',
      type: 'text',
      required: true,
      hint: 'City where you are located'
    },
    {
      id: 'state_province',
      name: 'state_province',
      label: 'State/Province',
      type: 'text',
      hint: 'State or province where you are located'
    },
    {
      id: 'country',
      name: 'country',
      label: 'Country',
      type: 'select',
      required: true,
      options: 'countryOptions',
      hint: 'Country where you are located'
    },
    {
      id: 'postal_code',
      name: 'postal_code',
      label: 'Postal Code',
      type: 'text',
      hint: 'Your postal or ZIP code'
    },
    {
      id: 'willing_to_relocate',
      name: 'willing_to_relocate',
      label: 'Operate in Other Markets',
      type: 'boolean',
      hint: 'Are you willing to operate in markets outside your current location?'
    },
    {
      id: 'preferred_locations',
      name: 'preferred_locations',
      label: 'Other Markets of Interest',
      type: 'multi-select',
      options: 'preferredLocationsOptions',
      fullWidth: true,
      hint: 'Will to operate in other markets, if which other markets would you be interested in',
      condition: {
        field: 'willing_to_relocate',
        value: true
      }
    }
  ]
};
