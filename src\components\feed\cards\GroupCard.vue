<template>
  <q-card class="group-card">
    <q-img :src="group.image" height="120px" />
    <q-card-section>
      <div class="text-h6">{{ group.name }}</div>
      <div class="text-caption q-mb-sm">{{ group.members }} members</div>

      <div v-if="group.category" class="row items-center q-mb-sm">
        <q-icon name="category" size="xs" class="q-mr-xs" />
        <span class="text-caption">{{ group.category }}</span>
      </div>

      <p class="text-body2">{{ truncatedDescription }}</p>

      <!-- Topics/Tags -->
      <div v-if="group.topics && group.topics.length" class="q-mt-sm">
        <div class="text-caption text-weight-bold q-mb-xs">Topics</div>
        <div class="row q-gutter-xs">
          <q-chip
            v-for="(topic, index) in group.topics.slice(0, 3)"
            :key="index"
            dense
            size="sm"
            color="grey-3"
            text-color="grey-8"
          >
            {{ topic }}
          </q-chip>
          <q-chip
            v-if="group.topics.length > 3"
            dense
            size="sm"
            color="grey-3"
            text-color="grey-8"
          >
            +{{ group.topics.length - 3 }} more
          </q-chip>
        </div>
      </div>

      <!-- Recent Activity -->
      <div v-if="group.lastActivity" class="q-mt-sm">
        <div class="text-caption text-weight-bold">Recent Activity</div>
        <div class="text-caption">{{ group.lastActivity }}</div>
      </div>
    </q-card-section>

    <q-card-actions align="right">
      <q-btn flat color="grey" icon="info" @click="handleViewDetails">
        <q-tooltip>View Details</q-tooltip>
      </q-btn>
      <q-btn flat color="grey" icon="share" @click="handleShare">
        <q-tooltip>Share</q-tooltip>
      </q-btn>
      <q-btn
        flat
        :color="isJoined ? 'negative' : 'primary'"
        :label="isJoined ? 'Leave Group' : 'Join Group'"
        @click="handleJoinLeave"
      />
    </q-card-actions>
  </q-card>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { truncateText } from '../../../utils/textUtils';

const props = defineProps({
  group: {
    type: Object,
    required: true
  },
  joined: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['view', 'share', 'join', 'leave']);
const router = useRouter();
const isJoined = ref(props.joined);

// Truncate group description for feed view
const truncatedDescription = computed(() => {
  let description = props.group.description || '';

  // Extract content from JSON if needed
  if (typeof description === 'string' && (description.startsWith('{') || description.startsWith('['))) {
    try {
      const parsedContent = JSON.parse(description);

      // Handle different JSON structures
      if (parsedContent.description) {
        description = parsedContent.description;
      } else if (parsedContent.groupDetails && parsedContent.groupDetails.description) {
        description = parsedContent.groupDetails.description;
      } else if (parsedContent.content) {
        description = parsedContent.content;
      } else if (typeof parsedContent === 'object') {
        // If it's an object but doesn't have expected fields, stringify it
        description = JSON.stringify(parsedContent);
      }
    } catch (e) {
      // If parsing fails, it's not valid JSON, keep the original content
      console.log('Failed to parse content as JSON:', e);
    }
  }

  return truncateText(description, 250);
});

// Methods
function handleViewDetails() {
  emit('view', props.group.id);

  // Navigate to group page
  router.push({ name: 'group', params: { id: props.group.id } });
}

function handleShare() {
  emit('share', props.group.id);
}

function handleJoinLeave() {
  if (isJoined.value) {
    isJoined.value = false;
    emit('leave', props.group.id);
  } else {
    isJoined.value = true;
    emit('join', props.group.id);
  }
}
</script>

<style scoped>
.group-card {
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.group-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

@media (max-width: 599px) {
  .group-card {
    margin: 0 8px 16px 8px;
    width: calc(100% - 16px);
  }

  /* Fix row gutter issues */
  .row.q-gutter-xs {
    margin-left: 0;
  }
}
</style>
