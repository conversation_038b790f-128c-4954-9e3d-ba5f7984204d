<template>
  <q-page class="q-pa-md">
    <div class="row q-col-gutter-md">
      <div class="col-12">
        <q-card class="marketplace-header-card bg-primary text-white">
          <q-card-section>
            <div class="text-h4">My Marketplace Listings</div>
            <p class="text-body1 q-mt-md">
              Manage your products and services listed on the ZbInnovation Marketplace.
              View, edit, or create new listings to showcase your offerings to the community.
            </p>
          </q-card-section>
          <q-card-actions align="right" class="bg-primary">
            <q-btn
              color="white"
              text-color="primary"
              label="Browse Marketplace"
              @click="goToMarketplace"
              class="q-mr-sm"
            />
            <q-btn
              color="secondary"
              label="Create New Listing"
              @click="createNewListing"
            />
          </q-card-actions>
        </q-card>
      </div>

      <!-- Loading state -->
      <div v-if="loading" class="col-12 text-center q-pa-lg">
        <q-spinner color="primary" size="3em" />
        <p class="text-subtitle1 q-mt-md">Loading your marketplace listings...</p>
      </div>

      <!-- Error state -->
      <div v-else-if="error" class="col-12">
        <q-card class="text-center q-pa-md">
          <q-icon name="error" size="3em" color="negative" />
          <p class="text-subtitle1 q-mt-md">{{ error }}</p>
          <q-btn color="primary" label="Retry" @click="fetchUserListings" class="q-mt-md" />
        </q-card>
      </div>

      <!-- No listings state -->
      <div v-else-if="userListings.length === 0" class="col-12">
        <q-card class="text-center q-pa-xl">
          <q-icon name="inventory_2" size="4em" color="grey-5" />
          <p class="text-h6 q-mt-md">You don't have any marketplace listings yet</p>
          <p class="text-body1 q-mb-lg">Create your first listing to showcase your products or services to the community.</p>
          <q-btn color="primary" label="Create Your First Listing" size="lg" @click="createNewListing" />
        </q-card>
      </div>

      <!-- Listings display -->
      <template v-else>
        <div v-for="listing in userListings" :key="listing.id" class="col-12 col-md-6 col-lg-4">
          <q-card class="listing-card">
            <q-img :src="listing.image || 'https://placehold.co/600x400?text=No+Image'" :ratio="16/9">
              <div class="absolute-bottom text-subtitle2 text-center bg-transparent">
                <q-badge :color="getListingTypeColor(listing.type)">{{ listing.type }}</q-badge>
              </div>
            </q-img>
            <q-card-section>
              <div class="text-h6">{{ listing.title }}</div>
              <div class="text-caption text-grey q-mb-sm">{{ listing.category || 'Uncategorized' }}</div>

              <div v-if="listing.price" class="row items-center q-mb-sm">
                <q-icon name="attach_money" size="xs" class="q-mr-xs" />
                <span class="text-subtitle2 text-weight-bold">{{ listing.price }}</span>
              </div>

              <div v-if="listing.location" class="row items-center q-mb-sm">
                <q-icon name="location_on" size="xs" class="q-mr-xs" />
                <span class="text-caption">{{ listing.location }}</span>
              </div>

              <p class="text-body2 listing-description">{{ listing.description }}</p>

              <div class="row items-center q-mt-sm">
                <q-icon name="schedule" size="xs" class="q-mr-xs" />
                <span class="text-caption">Posted {{ formatDate(listing.createdAt) }}</span>
              </div>
            </q-card-section>

            <q-card-actions align="right">
              <q-btn flat color="grey" icon="visibility" @click="viewListing(listing.id)">
                <q-tooltip>View Listing</q-tooltip>
              </q-btn>
              <q-btn flat color="primary" icon="edit" @click="editListing(listing.id)">
                <q-tooltip>Edit Listing</q-tooltip>
              </q-btn>
              <q-btn flat color="negative" icon="delete" @click="confirmDelete(listing.id)">
                <q-tooltip>Delete Listing</q-tooltip>
              </q-btn>
            </q-card-actions>
          </q-card>
        </div>
      </template>

      <!-- Upcoming Features Section -->
      <div class="col-12 q-mt-lg">
        <q-separator class="q-my-md" />
        <div class="text-h6 q-mb-md">Upcoming Marketplace Features</div>

        <div class="row q-col-gutter-md">
          <div class="col-12 col-md-4">
            <q-card class="feature-card bg-blue-1">
              <q-card-section class="text-center">
                <q-icon name="insights" size="56px" color="blue-8" />
                <div class="text-h6 q-mt-sm">Analytics Dashboard</div>
                <p class="text-body2">
                  Track views, inquiries, and engagement with your marketplace listings.
                </p>
              </q-card-section>
            </q-card>
          </div>

          <div class="col-12 col-md-4">
            <q-card class="feature-card bg-green-1">
              <q-card-section class="text-center">
                <q-icon name="handshake" size="56px" color="green-8" />
                <div class="text-h6 q-mt-sm">Smart Matching</div>
                <p class="text-body2">
                  AI-powered matching to connect your offerings with potential customers.
                </p>
              </q-card-section>
            </q-card>
          </div>

          <div class="col-12 col-md-4">
            <q-card class="feature-card bg-orange-1">
              <q-card-section class="text-center">
                <q-icon name="payments" size="56px" color="orange-8" />
                <div class="text-h6 q-mt-sm">Secure Transactions</div>
                <p class="text-body2">
                  Built-in payment processing and transaction management.
                </p>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Dialog -->
    <q-dialog v-model="showDeleteDialog" persistent>
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar icon="warning" color="negative" text-color="white" />
          <span class="q-ml-sm text-h6">Delete Listing</span>
        </q-card-section>

        <q-card-section>
          Are you sure you want to delete this listing? This action cannot be undone.
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="Cancel" color="primary" v-close-popup />
          <q-btn flat label="Delete" color="negative" @click="deleteListing" :loading="deleting" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useQuasar, date } from 'quasar';
import { useAuthStore } from '../../stores/auth';
import { usePostsStore } from '../../stores/posts';

const router = useRouter();
const $q = useQuasar();
const authStore = useAuthStore();
const postsStore = usePostsStore();

// State
const loading = ref(true);
const error = ref<string | null>(null);
const userListings = ref<any[]>([]);
const showDeleteDialog = ref(false);
const listingToDelete = ref<string | null>(null);
const deleting = ref(false);

// Fetch user's marketplace listings
onMounted(async () => {
  await fetchUserListings();
});

async function fetchUserListings() {
  loading.value = true;
  error.value = null;

  try {
    // Get the current user ID
    const userId = authStore.currentUser?.id;

    if (!userId) {
      throw new Error('User not authenticated');
    }

    console.log('Fetching marketplace listings for user dashboard');

    // Fetch posts with sub_type 'marketplace' that belong to the current user
    await postsStore.fetchUserPosts({
      userId,
      postType: 'MARKETPLACE' // This will be translated to sub_type='marketplace' in the store
    });

    console.log('Fetched user posts for marketplace:', postsStore.userPosts);

    // Map the posts to the marketplace format - no need to filter as the query already did that
    userListings.value = postsStore.userPosts.map((post: any) => ({
      id: post.id,
      title: post.title || '',
      description: post.content || '',
      image: post.imageUrl || post.featuredImage || '',
      price: formatPrice(post.price),
      type: post.subType || post.listingType || 'Product',
      category: post.category || '',
      location: post.location || '',
      createdAt: post.createdAt || new Date().toISOString(),
      updatedAt: post.updatedAt || new Date().toISOString()
    }));

    console.log('Mapped marketplace listings for dashboard:', userListings.value);
  } catch (err: any) {
    console.error('Error fetching user listings:', err);
    error.value = err.message || 'Failed to load your marketplace listings';

    // For development, use empty array
    userListings.value = [];
  } finally {
    loading.value = false;
  }
}

// Navigation functions
function goToMarketplace() {
  router.push('/virtual-community?tab=marketplace');
}

function createNewListing() {
  // Navigate to the marketplace tab with create dialog open
  router.push('/virtual-community?tab=marketplace&action=create');
}

function viewListing(id: string) {
  router.push({ name: 'marketplace-listing', params: { id } });
}

function editListing(id: string) {
  router.push({ name: 'edit-marketplace-listing', params: { id } });
}

function confirmDelete(id: string) {
  listingToDelete.value = id;
  showDeleteDialog.value = true;
}

async function deleteListing() {
  if (!listingToDelete.value) return;

  deleting.value = true;

  try {
    // Call the posts store to delete the listing
    await postsStore.deletePost(listingToDelete.value);

    // Remove from local array
    userListings.value = userListings.value.filter(listing => listing.id !== listingToDelete.value);

    // Show success notification
    $q.notify({
      color: 'positive',
      message: 'Listing deleted successfully',
      icon: 'check_circle',
      position: 'top',
      timeout: 2000
    });

    // Close dialog
    showDeleteDialog.value = false;
  } catch (err: any) {
    console.error('Error deleting listing:', err);

    // Show error notification
    $q.notify({
      color: 'negative',
      message: err.message || 'Failed to delete listing',
      icon: 'error',
      position: 'top',
      timeout: 3000
    });
  } finally {
    deleting.value = false;
  }
}

// Helper functions
function formatDate(dateString: string): string {
  return date.formatDate(dateString, 'MMM D, YYYY');
}

function formatPrice(price: string | number | null | undefined): string {
  if (!price && price !== 0) return '';

  // If it's already a formatted string with currency symbol, return as is
  if (typeof price === 'string' && (price.includes('$') || price.includes('USD') || price.includes('ZWL'))) {
    return price;
  }

  // Convert to number if it's a string
  const numPrice = typeof price === 'string' ? parseFloat(price) : price;

  // If it's 0 or free, return "Free"
  if (numPrice === 0) return 'Free';

  // If it's not a valid number, return the original value as string
  if (isNaN(numPrice)) return String(price);

  // Format as currency (assuming USD for now, could be made configurable)
  return `$${numPrice.toLocaleString()}`;
}

function getListingTypeColor(type: string): string {
  const typeColors: Record<string, string> = {
    'Product': 'blue',
    'Service': 'green',
    'Project': 'purple',
    'Job': 'orange',
    'Equipment': 'teal',
    'Space': 'deep-orange',
    'Investment': 'indigo'
  };

  return typeColors[type] || 'primary';
}
</script>

<style scoped>
.marketplace-header-card {
  border-radius: 8px;
}

.listing-card {
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
}

.listing-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.listing-description {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  min-height: 4.5em;
}

.feature-card {
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

@media (max-width: 599px) {
  .listing-card {
    margin-bottom: 16px;
  }
}
</style>
