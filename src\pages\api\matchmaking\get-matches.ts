/**
 * API endpoint to retrieve matches for a user
 * 
 * This endpoint retrieves matches for a user from the matchmaking_results table
 * and enriches them with profile data.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { EntityType } from '@/lib/matchmaking/matchmakingService';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  
  // Create Supabase server client
  const supabase = createServerSupabaseClient({ req, res });
  
  // Check if user is authenticated
  const {
    data: { session },
  } = await supabase.auth.getSession();
  
  if (!session) {
    return res.status(401).json({ error: 'Unauthorized' });
  }
  
  try {
    // Get query parameters
    const entityType = req.query.entityType as EntityType | undefined;
    const limit = parseInt(req.query.limit as string || '10', 10);
    const offset = parseInt(req.query.offset as string || '0', 10);
    const minScore = parseFloat(req.query.minScore as string || '0');
    
    // Build the query
    let query = supabase
      .from('matchmaking_results')
      .select('*')
      .eq('user_id', session.user.id)
      .gte('match_score', minScore)
      .order('match_score', { ascending: false })
      .limit(limit)
      .range(offset, offset + limit - 1);
      
    // Add entity type filter if provided
    if (entityType) {
      query = query.eq('entity_type', entityType);
    }
    
    // Execute the query
    const { data: matches, error } = await query;
    
    if (error) {
      console.error('Error fetching matches:', error);
      return res.status(500).json({ error: 'Failed to fetch matches' });
    }
    
    // Enrich matches with profile data
    const enrichedMatches = await Promise.all(
      matches.map(async match => {
        // Get profile data based on entity type
        const { data: profileData, error: profileError } = await supabase
          .from(`${match.entity_type}_profiles`)
          .select('*')
          .eq('user_id', match.matched_entity_id)
          .single();
          
        if (profileError) {
          console.error(`Error fetching ${match.entity_type} profile:`, profileError);
          return {
            ...match,
            profile: null
          };
        }
        
        // Get basic user data
        const { data: userData, error: userError } = await supabase
          .from('profiles')
          .select('first_name, last_name, avatar_url')
          .eq('user_id', match.matched_entity_id)
          .single();
          
        if (userError) {
          console.error('Error fetching user data:', userError);
          return {
            ...match,
            profile: profileData,
            user: null
          };
        }
        
        // Mark match as viewed
        if (!match.is_viewed) {
          await supabase
            .from('matchmaking_results')
            .update({ is_viewed: true })
            .eq('id', match.id);
        }
        
        return {
          ...match,
          profile: profileData,
          user: userData
        };
      })
    );
    
    return res.status(200).json({ 
      success: true, 
      matches: enrichedMatches,
      pagination: {
        limit,
        offset,
        total: enrichedMatches.length
      }
    });
  } catch (error) {
    console.error('Error retrieving matches:', error);
    return res.status(500).json({ error: 'Failed to retrieve matches' });
  }
}
