<template>
  <q-page padding>
    <!-- Featured Event Banner -->
    <q-card class="featured-event q-mb-lg">
      <q-card-section class="bg-primary text-white">
        <div class="text-overline">Featured Event</div>
        <div class="text-h4">ZbInnovation Virtual Hub Launch</div>
        <div class="row items-center q-mt-md">
          <div class="col-12 col-md-8">
            <div class="text-h6 q-mb-sm">Join us for this one-of-a-kind virtual event!</div>
            <div class="row items-center q-gutter-x-md q-mb-md">
              <div>
                <icons name="today" />
                May 14, 2025
              </div>
              <div>
                <icons name="schedule" />
                9:00 AM
              </div>
              <div>
                <icons name="event" />
                Virtual Event
              </div>
            </div>
            <!-- Countdown Timer -->
            <div class="row q-col-gutter-md">
              <div class="col text-center">
                <div class="text-h4">{{ countdown.days }}</div>
                <div class="text-caption">Days</div>
              </div>
              <div class="col text-center">
                <div class="text-h4">{{ countdown.hours }}</div>
                <div class="text-caption">Hours</div>
              </div>
              <div class="col text-center">
                <div class="text-h4">{{ countdown.minutes }}</div>
                <div class="text-caption">Minutes</div>
              </div>
              <div class="col text-center">
                <div class="text-h4">{{ countdown.seconds }}</div>
                <div class="text-caption">Seconds</div>
              </div>
            </div>
          </div>
          <div class="col-12 col-md-4 text-right q-mt-md q-mt-md-none">
            <q-badge color="secondary" class="q-pa-sm">Coming Soon</q-badge>
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- Events Sections -->
    <div class="row q-col-gutter-md">
      <!-- Your Registered Events -->
      <div class="col-12 col-md-6">
        <div class="text-h6 q-mb-md">Your Registered Events</div>
        <div class="row q-col-gutter-md">
          <div v-for="event in registeredEvents" :key="event.id" class="col-12">
            <q-card flat bordered>
              <q-card-section>
                <div class="row items-center">
                  <!-- Date Box -->
                  <div class="date-box text-center q-mr-md">
                    <div class="text-h6 text-primary">{{ formatDay(event.date) }}</div>
                    <div class="text-caption">{{ formatMonth(event.date) }}</div>
                  </div>
                  <!-- Event Details -->
                  <div class="col">
                    <div class="text-subtitle1 text-weight-medium">{{ event.title }}</div>
                    <div class="text-caption text-grey-7">
                      <icons name="clock" class="q-mr-xs" style="width: 16px; height: 16px;" />
                      {{ formatTime(event.date) }} •
                      <icons name="location" class="q-mr-xs" style="width: 16px; height: 16px;" />
                      {{ event.location }}
                    </div>
                  </div>
                  <div>
                    <q-btn flat round color="primary" class="calendar-btn">
                      <icons name="today" />
                      <q-tooltip>Add to Calendar</q-tooltip>
                    </q-btn>
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </div>

      <!-- Upcoming Events -->
      <div class="col-12 col-md-6">
        <div class="text-h6 q-mb-md">Upcoming Events</div>
        <div class="row q-col-gutter-md">
          <div v-for="event in upcomingEvents" :key="event.id" class="col-12">
            <q-card flat bordered>
              <q-card-section>
                <div class="row items-center">
                  <!-- Date Box -->
                  <div class="date-box text-center q-mr-md">
                    <div class="text-h6 text-primary">{{ formatDay(event.date) }}</div>
                    <div class="text-caption">{{ formatMonth(event.date) }}</div>
                  </div>
                  <!-- Event Details -->
                  <div class="col">
                    <div class="text-subtitle1 text-weight-medium">{{ event.title }}</div>
                    <div class="text-caption text-grey-7">
                      <icons name="clock" class="q-mr-xs" style="width: 16px; height: 16px;" />
                      {{ formatTime(event.date) }} •
                      <icons name="location" class="q-mr-xs" style="width: 16px; height: 16px;" />
                      {{ event.location }}
                    </div>
                  </div>
                  <div>
                    <q-btn flat color="primary" class="register-btn">
                      Register
                      <icons name="arrow-right" class="q-ml-xs" />
                    </q-btn>
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import Icons from '../../components/ui/Icons.vue'

const registeredEvents = [
  {
    id: 1,
    title: 'ZbInnovation Virtual Hub Launch',
    date: '2025-05-14T09:00:00',
    location: 'Virtual Event'
  }
]

const upcomingEvents = [
  {
    id: 2,
    title: 'ZbInnovation Virtual Hub Launch',
    date: '2025-05-14T09:00:00',
    location: 'Virtual Event'
  }
]

const countdown = ref({
  days: '00',
  hours: '00',
  minutes: '00',
  seconds: '00'
})

const targetDate = new Date('2025-05-14T09:00:00') // ZbInnovation Virtual Hub Launch
let timer: NodeJS.Timeout | null = null

const updateCountdown = () => {
  const now = new Date().getTime()
  const distance = targetDate.getTime() - now

  if (distance < 0) {
    if (timer) clearInterval(timer)
    countdown.value = {
      days: '00',
      hours: '00',
      minutes: '00',
      seconds: '00'
    }
    return
  }

  countdown.value = {
    days: Math.floor(distance / (1000 * 60 * 60 * 24)).toString().padStart(2, '0'),
    hours: Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)).toString().padStart(2, '0'),
    minutes: Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)).toString().padStart(2, '0'),
    seconds: Math.floor((distance % (1000 * 60)) / 1000).toString().padStart(2, '0')
  }
}

onMounted(() => {
  updateCountdown()
  timer = setInterval(updateCountdown, 1000)
})

onUnmounted(() => {
  if (timer) clearInterval(timer)
})

const formatDay = (date: string) => {
  return new Date(date).getDate()
}

const formatMonth = (date: string) => {
  return new Date(date).toLocaleString('default', { month: 'short' })
}

const formatTime = (date: string) => {
  return new Date(date).toLocaleString('default', { hour: '2-digit', minute: '2-digit' })
}
</script>

<style scoped>
.featured-event {
  background: linear-gradient(45deg, var(--q-primary) 0%, #2E7D32 100%);
}

.date-box {
  background: rgba(var(--q-primary), 0.1);
  border-radius: 8px;
  padding: 8px 16px;
  min-width: 70px;
}

.text-h4 {
  font-weight: 500;
}

.icon {
  width: 18px;
  height: 18px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  margin-right: 4px;
}

.calendar-btn .icon,
.register-btn .icon {
  width: 20px;
  height: 20px;
  margin: 0;
}

.register-btn .icon {
  margin-left: 4px;
}
</style>
