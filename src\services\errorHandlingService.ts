import { useNotificationStore } from '../stores/notifications'

// Error categories
export enum ErrorCategory {
  AUTHENTICATION = 'authentication',
  DATABASE = 'database',
  NETWORK = 'network',
  VALIDATION = 'validation',
  UNKNOWN = 'unknown'
}

// Error handler interface
export interface ErrorHandler {
  handle(error: any): void
}

// Base error handler
export class BaseErrorHandler implements ErrorHandler {
  protected notifications = useNotificationStore()

  handle(error: any): void {
    console.error('Unhandled error:', error)
    this.notifications.error('An unexpected error occurred')
  }
}

// Authentication error handler
export class AuthErrorHandler extends BaseErrorHandler {
  handle(error: any): void {
    console.error('Authentication error:', error)

    // Handle specific auth errors
    if (error.code === 'auth/invalid-email') {
      this.notifications.error('Invalid email address')
    } else if (error.code === 'auth/wrong-password') {
      this.notifications.error('Incorrect password')
    } else if (error.code === 'auth/user-not-found') {
      this.notifications.error('User not found')
    } else if (error.code === 'auth/email-already-in-use') {
      this.notifications.error('Email already in use')
    } else if (error.message && error.message.includes('Email not confirmed')) {
      this.notifications.error('Please confirm your email address before signing in')
    } else if (error.message && error.message.includes('Invalid login credentials')) {
      this.notifications.error('Invalid email or password')
    } else {
      this.notifications.error(`Authentication error: ${error.message}`)
    }
  }
}

// Database error handler
export class DatabaseErrorHandler extends BaseErrorHandler {
  handle(error: any): void {
    console.error('Database error:', error)

    // Handle specific database errors
    if (error.code === 'PGRST301') {
      this.notifications.error('Database connection error')
    } else if (error.code === 'PGRST302') {
      this.notifications.error('Database query error')
    } else if (error.code === 'PGRST116') {
      // No rows returned - this is often not an error, so just log it
      console.log('No data found for the query')
      return
    } else if (error.code === 'PGRST109' || error.code === '42P01') {
      this.notifications.warning('Database table structure issue. The system will attempt to fix this automatically.')
    } else if (error.code === '23505') {
      this.notifications.error('Duplicate entry error')
    } else if (error.code === '23503') {
      this.notifications.error('This operation cannot be completed because it references data that does not exist')
    } else if (error.code === '23502') {
      this.notifications.error('Required information is missing. Please fill in all required fields.')
    } else if (error.message && error.message.includes('column')) {
      this.notifications.warning('Database schema issue detected. The system will attempt to fix this automatically.')
    } else if (error.message && error.message.includes('timeout')) {
      this.notifications.error('The database request timed out. Please try again.')
    } else if (error.message && error.message.includes('permission denied')) {
      this.notifications.error('You do not have permission to perform this operation.')
    } else {
      this.notifications.error(`Database error: ${error.message || 'Unknown database error'}`)
    }
  }
}

// Network error handler
export class NetworkErrorHandler extends BaseErrorHandler {
  handle(error: any): void {
    console.error('Network error:', error)
    this.notifications.error('Network error: Please check your internet connection')
  }
}

// Validation error handler
export class ValidationErrorHandler extends BaseErrorHandler {
  handle(error: any): void {
    console.error('Validation error:', error)

    if (error.errors && Array.isArray(error.errors)) {
      // Handle validation errors array
      const messages = error.errors.map((err: any) => err.message).join(', ')
      this.notifications.error(`Validation error: ${messages}`)
    } else {
      this.notifications.error(`Validation error: ${error.message}`)
    }
  }
}

// Error handler factory
export class ErrorHandlerFactory {
  static getHandler(category: ErrorCategory): ErrorHandler {
    switch (category) {
      case ErrorCategory.AUTHENTICATION:
        return new AuthErrorHandler()
      case ErrorCategory.DATABASE:
        return new DatabaseErrorHandler()
      case ErrorCategory.NETWORK:
        return new NetworkErrorHandler()
      case ErrorCategory.VALIDATION:
        return new ValidationErrorHandler()
      default:
        return new BaseErrorHandler()
    }
  }

  static handleError(error: any, category: ErrorCategory = ErrorCategory.UNKNOWN): void {
    const handler = this.getHandler(category)
    handler.handle(error)
  }

  static categorizeError(error: any): ErrorCategory {
    if (!error) return ErrorCategory.UNKNOWN

    // Check for network errors
    if (error.message && (
      error.message.includes('network') ||
      error.message.includes('connection') ||
      error.message.includes('offline') ||
      error.name === 'NetworkError'
    )) {
      return ErrorCategory.NETWORK
    }

    // Check for authentication errors
    if (
      (error.code && error.code.startsWith('auth/')) ||
      (error.message && (
        error.message.includes('authentication') ||
        error.message.includes('auth') ||
        error.message.includes('login') ||
        error.message.includes('password') ||
        error.message.includes('email not confirmed')
      ))
    ) {
      return ErrorCategory.AUTHENTICATION
    }

    // Check for database errors
    if (
      (error.code && (
        error.code.startsWith('PGRST') ||
        error.code.match(/^[0-9]{5}$/)
      )) ||
      (error.message && (
        error.message.includes('database') ||
        error.message.includes('column') ||
        error.message.includes('table') ||
        error.message.includes('query')
      ))
    ) {
      return ErrorCategory.DATABASE
    }

    // Check for validation errors
    if (
      error.errors ||
      (error.message && (
        error.message.includes('validation') ||
        error.message.includes('required') ||
        error.message.includes('invalid')
      ))
    ) {
      return ErrorCategory.VALIDATION
    }

    return ErrorCategory.UNKNOWN
  }
}

// Helper function to handle errors
export function handleError(error: any): void {
  const category = ErrorHandlerFactory.categorizeError(error)
  ErrorHandlerFactory.handleError(error, category)
}

// Helper function to handle errors with a specific category
export function handleErrorWithCategory(error: any, category: ErrorCategory): void {
  ErrorHandlerFactory.handleError(error, category)
}

/**
 * Retry a function with exponential backoff
 * @param fn The function to retry
 * @param maxRetries Maximum number of retries
 * @param initialDelay Initial delay in milliseconds
 * @returns The result of the function or throws an error after max retries
 */
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  initialDelay: number = 300
): Promise<T> {
  let retries = 0
  let lastError: any = null

  while (retries <= maxRetries) {
    try {
      return await fn()
    } catch (error) {
      lastError = error
      retries++

      if (retries > maxRetries) {
        break
      }

      // Calculate delay with exponential backoff
      const delay = initialDelay * Math.pow(2, retries - 1)

      // Add some randomness to prevent all retries happening at the same time
      const jitter = Math.random() * 100

      console.log(`Retry ${retries}/${maxRetries} after ${delay + jitter}ms...`)

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay + jitter))
    }
  }

  throw lastError
}

/**
 * Create a function that will retry on failure
 * @param fn The function to wrap with retry logic
 * @param options Retry options
 * @returns A function that will retry on failure
 */
export function withRetry<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  options: { maxRetries?: number; initialDelay?: number } = {}
): T {
  const { maxRetries = 3, initialDelay = 300 } = options

  return (async (...args: Parameters<T>) => {
    return retryWithBackoff(
      () => fn(...args),
      maxRetries,
      initialDelay
    )
  }) as T
}
