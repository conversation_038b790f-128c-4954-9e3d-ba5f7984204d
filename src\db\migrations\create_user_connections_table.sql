-- Create user connections table
CREATE TABLE IF NOT EXISTS public.user_connections (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  connected_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  connection_type VARCHAR(50) NOT NULL,
  connection_status VARCHAR(20) DEFAULT 'pending',
  connection_strength FLOAT DEFAULT 1.0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, connected_user_id)
);

-- <PERSON><PERSON> indexes
CREATE INDEX IF NOT EXISTS idx_user_connections_user_id ON public.user_connections(user_id);
CREATE INDEX IF NOT EXISTS idx_user_connections_connected_user_id ON public.user_connections(connected_user_id);
CREATE INDEX IF NOT EXISTS idx_user_connections_status ON public.user_connections(connection_status);

-- Enable RLS
ALTER TABLE public.user_connections ENABLE ROW LEVEL SECURITY;

-- <PERSON><PERSON> policies
CREATE POLICY "Users can view their own connections"
  ON public.user_connections
  FOR SELECT
  USING (auth.uid() = user_id OR auth.uid() = connected_user_id);

CREATE POLICY "Users can create their own connections"
  ON public.user_connections
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own connections"
  ON public.user_connections
  FOR UPDATE
  USING (auth.uid() = user_id OR auth.uid() = connected_user_id);

CREATE POLICY "Users can delete their own connections"
  ON public.user_connections
  FOR DELETE
  USING (auth.uid() = user_id);
