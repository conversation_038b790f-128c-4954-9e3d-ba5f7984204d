// Type definitions for Profile interfaces
declare interface BaseProfile {
  id?: string;
  user_id?: string;
  email?: string;
  first_name?: string;
  last_name?: string;
  profile_state?: string;
  profile_type?: string;
  profile_name?: string;
  profile_completion?: number;
  created_at?: string;
  updated_at?: string;
  last_login_at?: string;
  phone_country_code?: string;
  phone_number?: string;
  gender?: string;
  bio?: string;
  phone?: string;
  [key: string]: any;
}

declare interface ProfileSection {
  id: string;
  title: string;
  description?: string;
  fields?: any[];
  questions?: any[];
  [key: string]: any;
}

declare interface ProfileType {
  id: string;
  name: string;
  description?: string;
  sections?: ProfileSection[];
  [key: string]: any;
}

declare interface ProfileQuestion {
  id: string;
  field: string;
  label: string;
  type: string;
  required?: boolean;
  options?: string[];
  section?: string;
  [key: string]: any;
}
