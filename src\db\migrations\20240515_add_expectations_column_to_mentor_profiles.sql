-- Add expectations column to mentor_profiles table
-- This migration adds the 'expectations' column to the mentor_profiles table
-- to ensure backward compatibility with code that's still using the old column name.
--
-- IMPORTANT: This migration is safe and will NOT reset or clear any existing data.

-- Add expectations column to mentor_profiles table
DO $$
BEGIN
  -- Check if the table exists
  IF EXISTS (
    SELECT FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name = 'mentor_profiles'
  ) THEN
    -- Add expectations column if it doesn't exist
    IF NOT EXISTS (
      SELECT FROM information_schema.columns
      WHERE table_schema = 'public'
      AND table_name = 'mentor_profiles'
      AND column_name = 'expectations'
    ) THEN
      -- Add the expectations column
      ALTER TABLE public.mentor_profiles
      ADD COLUMN expectations TEXT;
      
      -- If mentee_expectations column exists, copy its values to expectations
      IF EXISTS (
        SELECT FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'mentor_profiles'
        AND column_name = 'mentee_expectations'
      ) THEN
        -- Update expectations with values from mentee_expectations
        UPDATE public.mentor_profiles
        SET expectations = mentee_expectations
        WHERE mentee_expectations IS NOT NULL;
      END IF;
    END IF;
  END IF;
END
$$;

-- Refresh schema cache
SELECT pg_catalog.set_config('search_path', 'public', false);
