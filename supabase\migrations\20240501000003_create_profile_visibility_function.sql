-- Migration: Create profile visibility function
-- Description: Creates a function to check if a profile is visible to the current user

-- Function to check if a profile is visible to the current user
CREATE OR REPLACE FUNCTION public.is_profile_visible(profile_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    visibility TEXT;
    profile_user_id UUID;
BEGIN
    -- Get the visibility and user_id from the profiles table
    SELECT profile_visibility, user_id INTO visibility, profile_user_id
    FROM profiles
    WHERE id = profile_id;
    
    -- If the profile belongs to the current user, it's always visible
    IF profile_user_id = auth.uid() THEN
        RETURN TRUE;
    END IF;
    
    -- Check visibility settings
    CASE visibility
        WHEN 'public' THEN
            RETURN TRUE;
        WHEN 'private' THEN
            RETURN FALSE;
        WHEN 'connections_only' THEN
            -- For now, return false - in the future, check connections
            RETURN FALSE;
        ELSE
            RETURN FALSE;
    END CASE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a policy to allow users to view profiles based on visibility
CREATE POLICY "Users can view visible profiles"
    ON public.profiles FOR SELECT
    USING (
        user_id = auth.uid() OR
        (profile_visibility = 'public') OR
        (profile_visibility = 'connections_only' AND 
         -- This is a placeholder for future connection checking logic
         false)
    );

-- Create a policy to allow users to view visible innovator profiles
CREATE POLICY "Users can view visible innovator profiles"
    ON public.innovator_profiles FOR SELECT
    USING (is_profile_visible(profile_id));
