-- Fix mentor_profiles table structure
-- This migration ensures the mentor_profiles table has the correct structure,
-- including the user_id foreign key referencing personal_details(user_id).
--
-- IMPORTANT: This migration is safe and will NOT reset or clear any existing data.

-- Fix mentor_profiles table structure
DO $$
BEGIN
  -- Check if the table exists
  IF EXISTS (
    SELECT FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name = 'mentor_profiles'
  ) THEN
    -- Add completion_percentage column if it doesn't exist
    IF NOT EXISTS (
      SELECT FROM information_schema.columns
      WHERE table_schema = 'public'
      AND table_name = 'mentor_profiles'
      AND column_name = 'completion_percentage'
    ) THEN
      ALTER TABLE public.mentor_profiles
      ADD COLUMN completion_percentage INTEGER DEFAULT 0;
    END IF;

    -- Ensure user_id column exists and has the correct foreign key
    IF NOT EXISTS (
      SELECT FROM information_schema.columns
      WHERE table_schema = 'public'
      AND table_name = 'mentor_profiles'
      AND column_name = 'user_id'
    ) THEN
      -- Add user_id column if it doesn't exist
      ALTER TABLE public.mentor_profiles
      ADD COLUMN user_id UUID REFERENCES personal_details(user_id) ON DELETE CASCADE;
    END IF;

    -- Check if the foreign key constraint exists
    IF NOT EXISTS (
      SELECT FROM pg_constraint
      WHERE conname = 'mentor_profiles_user_id_fkey'
    ) THEN
      -- Add the foreign key constraint if it doesn't exist
      ALTER TABLE public.mentor_profiles
      ADD CONSTRAINT mentor_profiles_user_id_fkey
      FOREIGN KEY (user_id) REFERENCES personal_details(user_id) ON DELETE CASCADE;
    END IF;
  END IF;
END
$$;

-- Refresh schema cache
SELECT pg_catalog.set_config('search_path', 'public', false);
