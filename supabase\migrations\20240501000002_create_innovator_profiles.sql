-- Migration: Create innovator profiles table
-- Description: Creates the innovator_profiles table with enhanced fields

-- Create the innovator_profiles table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.innovator_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    profile_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    
    -- Basic Innovation Information
    innovation_area TEXT,
    innovation_stage TEXT,
    innovation_description TEXT,
    
    -- Team Information
    team_size INTEGER,
    team_description TEXT,
    
    -- Funding Information
    funding_needs BOOLEAN DEFAULT false,
    funding_amount NUMERIC,
    funding_stage TEXT,
    
    -- Prototype Information
    has_prototype BOOLEAN DEFAULT false,
    prototype_description TEXT,
    
    -- Goals and Challenges
    goals TEXT[],
    challenges TEXT[],
    
    -- Online Presence
    website TEXT,
    social_links JSONB DEFAULT '{}'::jsonb,
    
    -- Contact Information
    contact_email TEXT,
    contact_phone TEXT,
    contact_address TEXT,
    
    -- B<PERSON> and Achievements
    bio TEXT,
    achievements TEXT[],
    awards TEXT[],
    
    -- Additional Information
    target_market TEXT,
    business_model TEXT,
    competitive_advantage TEXT,
    intellectual_property TEXT,
    sustainability_impact TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security on the table
ALTER TABLE public.innovator_profiles ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for the innovator_profiles table
CREATE POLICY "Users can view their own innovator profiles"
    ON public.innovator_profiles FOR SELECT
    USING ((SELECT user_id FROM profiles WHERE id = profile_id) = auth.uid());

CREATE POLICY "Users can update their own innovator profiles"
    ON public.innovator_profiles FOR UPDATE
    USING ((SELECT user_id FROM profiles WHERE id = profile_id) = auth.uid());

CREATE POLICY "Users can insert their own innovator profiles"
    ON public.innovator_profiles FOR INSERT
    WITH CHECK ((SELECT user_id FROM profiles WHERE id = profile_id) = auth.uid());

CREATE POLICY "Users can delete their own innovator profiles"
    ON public.innovator_profiles FOR DELETE
    USING ((SELECT user_id FROM profiles WHERE id = profile_id) = auth.uid());

-- Grant permissions to authenticated users
GRANT ALL ON public.innovator_profiles TO authenticated;
GRANT ALL ON public.innovator_profiles TO service_role;
