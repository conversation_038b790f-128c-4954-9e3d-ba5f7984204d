-- Create profile_types table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.profile_types (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(50) NOT NULL UNIQUE,
  display_name VARCHAR(100) NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default profile types if they don't exist
INSERT INTO public.profile_types (name, display_name, description)
VALUES
  ('innovator', 'Innovator', 'Entrepreneurs and startups with innovative ideas'),
  ('investor', 'Business Investor', 'Investors looking to fund innovative projects'),
  ('mentor', 'Mentor', 'Experienced professionals offering guidance'),
  ('professional', 'Professional', 'Industry professionals seeking opportunities'),
  ('industry_expert', 'Industry Expert', 'Specialists with deep domain knowledge'),
  ('academic_student', 'Academic Student', 'Students looking for research or career opportunities'),
  ('academic_institution', 'Academic Institution', 'Universities and research institutions'),
  ('organisation', 'Organisation', 'Companies and organizations seeking innovation')
ON CONFLICT (name) DO NOTHING;

-- Add profile_type_id to profiles table if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'profiles' AND column_name = 'profile_type_id'
  ) THEN
    ALTER TABLE public.profiles ADD COLUMN profile_type_id UUID REFERENCES public.profile_types(id);
  END IF;
END $$;

-- Add profile_state to profiles table if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'profiles' AND column_name = 'profile_state'
  ) THEN
    ALTER TABLE public.profiles ADD COLUMN profile_state VARCHAR(20) DEFAULT 'in_progress';
  END IF;
END $$;

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add trigger to profile_types table
DROP TRIGGER IF EXISTS update_profile_types_updated_at ON public.profile_types;
CREATE TRIGGER update_profile_types_updated_at
BEFORE UPDATE ON public.profile_types
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();
