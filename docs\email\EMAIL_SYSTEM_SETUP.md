# ZB Innovation Hub Email System Setup

This document provides instructions for setting up the email system for ZB Innovation Hub, focusing on welcome emails for new users.

## Overview

The email system consists of:

1. **Welcome Emails**: Sent automatically when a user signs up
2. **Disabled Email Confirmation**: Email confirmation is disabled in Supabase to improve user experience

## Setup Instructions

### 1. Disable Email Confirmation in Supabase

1. Log in to the Supabase dashboard
2. Go to Authentication > Settings
3. Under "Email Auth", find the "Confirm email" option and disable it
4. Save changes

### 2. Deploy Edge Functions

Run the deployment script to deploy the necessary Edge Functions:

```bash
./deploy-email-verification.bat
```

This will deploy:
- `send-email-verification`: Handles all email sending, including welcome emails
- `auth-trigger`: Handles auth events like user creation

### 3. Set Up SendGrid API Key

1. Create a SendGrid account if you don't have one
2. Create an API key with email sending permissions
3. Set the API key in Supabase:

```bash
supabase secrets set SENDGRID_API_KEY=your_sendgrid_api_key_here
```

### 4. Set Up Sender Email

1. Verify the domain `zbinnovation.co.zw` in SendGrid
2. Set up DKIM, SPF, and other email authentication records
3. The system is configured to use `<EMAIL>` as the sender email

### 5. Apply Database Migrations

Run the SQL migration to set up the necessary database tables and triggers:

```bash
supabase db push
```

This will:
- Create tables for email logging
- Add last_login column to personal_details
- Create triggers to update last_login and handle new user creation

## Testing

### Test Welcome Email

1. Create a new user account
2. Check that the welcome email is sent to the user's email address
3. Verify that the email contains the correct branding and links

## Troubleshooting

### Check Email Logs

Query the email_logs table to check the status of sent emails:

```sql
SELECT * FROM email_logs ORDER BY sent_at DESC LIMIT 10;
```

### Function Logs

Check the logs for the Edge Functions in the Supabase dashboard:

1. Go to Edge Functions
2. Select the function you want to check
3. Click on "Logs" to view the execution logs

## Email Templates

The email templates use the ZB Innovation Hub branding colors:
- Primary color: #0D8A3E (green)
- Secondary color: #a4ca39 (light green)

The templates include:
- ZB Innovation Hub logo and branding
- Personalized greeting when possible
- Clear call-to-action buttons
- Footer with copyright information
