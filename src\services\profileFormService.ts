/**
 * Profile Form Service
 *
 * This service manages form state for profile editing, providing a centralized
 * way to handle form data, validation, and saving.
 */

import { ref, computed, watch } from 'vue'
import { useProfileStore } from '../stores/profile'
import { useNotificationStore } from '../stores/notifications'
import { processJsonFields } from './profileUtils'
import { debounce } from 'quasar'

export function useProfileFormService() {
  const profileStore = useProfileStore()
  const notifications = useNotificationStore()

  // Form state that components can bind to
  const formState = ref<Record<string, any>>({})

  // Track if form has unsaved changes
  const hasUnsavedChanges = ref(false)

  // Track loading state
  const isSaving = ref(false)

  // Initialize form with current profile data
  function initializeForm() {
    // Don't reinitialize if we're currently saving
    if (isSaving.value) {
      console.log('Currently saving, skipping form reinitialization')
      return
    }

    if (profileStore.currentProfile) {
      // Start with personal details
      formState.value = {
        // Base profile fields
        first_name: profileStore.currentProfile.first_name || '',
        last_name: profileStore.currentProfile.last_name || '',
        email: profileStore.currentProfile.email || '',
        phone_country_code: profileStore.currentProfile.phone_country_code || '',
        phone_number: profileStore.currentProfile.phone_number || '',
        gender: profileStore.currentProfile.gender || '',
        bio: profileStore.currentProfile.bio || '',
        profile_name: profileStore.currentProfile.profile_name || '',
        profile_type: profileStore.currentProfile.profile_type || '',
      }

      // Add specialized profile data if available
      if (profileStore.currentSpecializedProfile) {
        formState.value = {
          ...formState.value,
          ...profileStore.currentSpecializedProfile
        }
      }

      hasUnsavedChanges.value = false
    }
  }

  // Update form state (called by input components)
  function updateField(field: string, value: any) {
    formState.value[field] = value
    hasUnsavedChanges.value = true
  }

  // Update multiple fields at once
  function updateFields(fields: Record<string, any>) {
    formState.value = {
      ...formState.value,
      ...fields
    }
    hasUnsavedChanges.value = true
  }

  // Save form data to store and database
  async function saveForm() {
    if (!profileStore.currentProfile) {
      console.error('Cannot save form: No current profile');
      return false;
    }

    // Don't save if there are no unsaved changes
    if (!hasUnsavedChanges.value) {
      console.log('No unsaved changes, skipping save');
      return true;
    }

    // Don't save if already saving
    if (isSaving.value) {
      console.log('Already saving, skipping duplicate save');
      return true;
    }

    console.log('saveForm called. Has unsaved changes:', hasUnsavedChanges.value);
    console.log('Current form state:', formState.value);

    isSaving.value = true;

    try {
      // Extract personal details
      const personalDetails = {
        first_name: formState.value.first_name,
        last_name: formState.value.last_name,
        phone_country_code: formState.value.phone_country_code,
        phone_number: formState.value.phone_number,
        gender: formState.value.gender,
        bio: formState.value.bio,
        profile_name: formState.value.profile_name
      }

      console.log('Saving personal details:', personalDetails);

      // Update personal details in the store
      const personalDetailsResult = await profileStore.updatePersonalDetails(personalDetails);
      console.log('Personal details update result:', personalDetailsResult);

      // Update specialized profile data if we have a profile type
      if (profileStore.currentProfile.profile_type) {
        // Process the form data for the specialized profile
        const profileType = profileStore.currentProfile.profile_type;

        // Create a copy of the form state without personal details fields
        const specializedData = { ...formState.value };

        // Process JSON fields
        const processedData = processJsonFields(specializedData, profileType);
        console.log('Processed specialized data:', processedData);

        // Update specialized profile data in the store
        const specializedResult = await profileStore.updateProfileData(processedData);
        console.log('Specialized data update result:', specializedResult);
      }

      hasUnsavedChanges.value = false
      return true
    } catch (error: any) {
      notifications.error('Failed to save profile: ' + error.message)
      console.error('Error saving profile form:', error)
      return false
    } finally {
      isSaving.value = false
    }
  }

  // Autosave functionality removed to prevent form reloading issues
  // const autoSave = debounce(saveForm, 2000)

  // Watch for changes in the store and update form
  // Use a flag to prevent circular updates
  const isUpdatingFromStore = ref(false);

  watch(
    () => [profileStore.currentProfile, profileStore.currentSpecializedProfile],
    () => {
      // Only initialize the form if we're not currently saving
      // This prevents a circular update loop
      if (!isSaving.value && !isUpdatingFromStore.value) {
        console.log('Store profile changed, reinitializing form');
        isUpdatingFromStore.value = true;
        initializeForm();
        // Reset the flag after a short delay
        setTimeout(() => {
          isUpdatingFromStore.value = false;
        }, 100);
      }
    },
    { deep: true }
  )

  return {
    formState,
    hasUnsavedChanges,
    isSaving,
    initializeForm,
    updateField,
    updateFields,
    saveForm,
    // autoSave removed to prevent form reloading issues

    // Computed properties
    profileType: computed(() => profileStore.currentProfile?.profile_type),
    profileId: computed(() => profileStore.currentProfile?.user_id),
    profileCompletion: computed(() => profileStore.profileCompletion)
  }
}
