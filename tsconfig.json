{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "module": "ESNext", "moduleResolution": "node", "strict": false, "noImplicitAny": false, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "lib": ["ESNext", "DOM"], "skipLibCheck": true, "noEmit": true, "allowJs": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "types": ["vite/client", "vue-router", "pinia", "node"], "ignoreDeprecations": "5.0", "allowSyntheticDefaultImports": true}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"], "exclude": ["node_modules"], "references": [{"path": "./tsconfig.node.json"}]}