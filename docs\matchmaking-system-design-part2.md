# Matchmaking System Design Document - Part 2

## 9. Detailed Matching Algorithms

### 9.1 Scoring Mechanisms

#### 9.1.1 Array Intersection Scoring
For matching array fields like tags, interests, and categories:

```
function arrayIntersectionScore(array1, array2) {
  if (!array1 || !array2 || array1.length === 0 || array2.length === 0) {
    return 0;
  }
  
  const intersection = array1.filter(item => array2.includes(item));
  return intersection.length / Math.sqrt(array1.length * array2.length);
}
```

#### 9.1.2 Numeric Range Matching
For matching numeric ranges like funding amounts and price ranges:

```
function numericRangeMatch(value1, range2) {
  if (!value1 || !range2) {
    return 0;
  }
  
  const { min, max } = range2;
  if (value1 >= min && value1 <= max) {
    // Calculate how centered the value is in the range
    const rangeSize = max - min;
    const distanceFromCenter = Math.abs((max + min) / 2 - value1);
    return 1 - (distanceFromCenter / (rangeSize / 2));
  }
  
  return 0;
}
```

#### 9.1.3 Text Similarity Scoring
For matching text fields like descriptions and content:

```
function textSimilarityScore(text1, text2) {
  if (!text1 || !text2) {
    return 0;
  }
  
  // Extract keywords from both texts
  const keywords1 = extractKeywords(text1);
  const keywords2 = extractKeywords(text2);
  
  // Use array intersection on keywords
  return arrayIntersectionScore(keywords1, keywords2);
}
```

#### 9.1.4 Composite Scoring
Combining multiple scores with weighted importance:

```
function compositeScore(scores, weights) {
  let totalScore = 0;
  let totalWeight = 0;
  
  for (const key in scores) {
    if (weights[key]) {
      totalScore += scores[key] * weights[key];
      totalWeight += weights[key];
    }
  }
  
  return totalWeight > 0 ? totalScore / totalWeight : 0;
}
```

### 9.2 Profile-Specific Matching Algorithms

#### 9.2.1 Innovator-Investor Matching

```
function matchInnovatorToInvestor(innovator, investor) {
  const scores = {
    industry: arrayIntersectionScore(
      innovator.market_focus || [], 
      investor.investment_focus || []
    ),
    stage: arrayIntersectionScore(
      [innovator.innovation_stage] || [], 
      investor.investment_stages || []
    ),
    funding: numericRangeMatch(
      innovator.funding_needs, 
      { min: investor.min_ticket_size, max: investor.max_ticket_size }
    ),
    location: arrayIntersectionScore(
      innovator.preferred_locations || [], 
      investor.preferred_locations || []
    ),
    goals: arrayIntersectionScore(
      innovator.short_term_goals || [], 
      investor.collaboration_interests || []
    )
  };
  
  const weights = {
    industry: 0.3,
    stage: 0.3,
    funding: 0.2,
    location: 0.1,
    goals: 0.1
  };
  
  return {
    score: compositeScore(scores, weights),
    subscores: scores,
    reasons: generateMatchReasons(scores, weights)
  };
}
```

#### 9.2.2 Innovator-Mentor Matching

```
function matchInnovatorToMentor(innovator, mentor) {
  const scores = {
    expertise: arrayIntersectionScore(
      innovator.current_challenges || [], 
      mentor.expertise_areas || []
    ),
    industry: arrayIntersectionScore(
      [innovator.industry] || [], 
      mentor.industry_experience || []
    ),
    stage: stageCompatibilityScore(
      innovator.innovation_stage, 
      mentor.preferred_mentee_stage
    ),
    goals: arrayIntersectionScore(
      innovator.short_term_goals || [], 
      mentor.mentoring_interests || []
    ),
    approach: approachCompatibilityScore(
      innovator.looking_for, 
      mentor.mentoring_approach
    )
  };
  
  const weights = {
    expertise: 0.35,
    industry: 0.2,
    stage: 0.15,
    goals: 0.15,
    approach: 0.15
  };
  
  return {
    score: compositeScore(scores, weights),
    subscores: scores,
    reasons: generateMatchReasons(scores, weights)
  };
}
```

### 9.3 Match Reason Generation

```
function generateMatchReasons(scores, weights) {
  const reasons = [];
  const sortedFactors = Object.keys(scores)
    .map(key => ({ key, score: scores[key], weight: weights[key] }))
    .sort((a, b) => (b.score * b.weight) - (a.score * a.weight));
  
  // Take top 3 factors
  const topFactors = sortedFactors.slice(0, 3).filter(factor => factor.score > 0.3);
  
  for (const factor of topFactors) {
    reasons.push(getReasonText(factor.key, factor.score));
  }
  
  return reasons;
}

function getReasonText(factor, score) {
  const strength = score > 0.8 ? 'strong' : score > 0.5 ? 'good' : 'some';
  
  const reasonTexts = {
    industry: `${strength} industry alignment`,
    stage: `${strength} match in development/investment stage`,
    funding: `${strength} match in funding requirements`,
    location: `${strength} geographic compatibility`,
    goals: `${strength} alignment in goals and interests`,
    expertise: `${strength} match between expertise and needs`,
    approach: `${strength} compatibility in working/mentoring style`,
    availability: `${strength} match in availability`,
    tags: `${strength} match in topics and interests`,
    category: `${strength} relevance to your field`,
    recency: `recently published content`,
    popularity: `popular among similar users`
  };
  
  return reasonTexts[factor] || `${strength} match in ${factor}`;
}
```
