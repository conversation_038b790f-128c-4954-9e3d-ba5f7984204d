<template>
  <div class="article-page">
    <!-- Loading State -->
    <div v-if="loading" class="text-center q-pa-xl">
      <q-spinner-dots color="primary" size="40px" />
      <div class="text-subtitle1 q-mt-sm">Loading article...</div>
    </div>

    <!-- Error State -->
    <div v-else-if="!article" class="text-center q-pa-xl">
      <p class="text-h6">Article not found</p>
      <q-btn
        color="primary"
        :to="{ name: 'news' }"
        label="Back to News"
        class="q-mt-md"
      />
    </div>

    <!-- Article Content -->
    <template v-else>
      <!-- Hero Section -->
      <div class="article-hero" :style="{ backgroundImage: `url(${article.coverImage || 'https://picsum.photos/1920/600'})` }">
        <div class="overlay" />
      </div>

      <!-- Article Header Card -->
      <div class="container q-mx-auto q-px-md article-header-container">
        <div class="article-header-card">
          <!-- Back Button -->
          <q-btn
            flat
            color="primary"
            no-caps
            class="q-mb-md"
            :to="{ name: 'news' }"
          >
            <icons name="arrow_back" class="q-mr-xs" />
            Back to News
          </q-btn>

          <q-chip
            color="primary"
            text-color="white"
            class="q-mb-md category-chip"
          >
            {{ article.category }}
          </q-chip>
          <h1 class="text-h3 q-mb-md title">{{ article.title }}</h1>
          <div class="article-meta row items-center q-gutter-x-md">
            <div class="date-info row items-center">
              <icons name="event" class="q-mr-sm" />
              {{ formatDate(article.publishedAt) }}
            </div>
            <q-separator vertical />
            <div class="read-time row items-center">
              <icons name="event" class="q-mr-sm" />
              {{ article.readTime || '5 min read' }}
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="article-content q-py-xl">
        <div class="container q-mx-auto q-px-md">
          <div class="row justify-center">
            <!-- Share Sidebar -->
            <div class="col-auto gt-sm">
              <div class="share-sidebar">
                <div class="text-caption q-mb-sm">SHARE</div>
                <q-btn flat round color="primary" class="q-mb-sm">
                  <icons name="twitter" />
                  <q-tooltip>Share on Twitter</q-tooltip>
                </q-btn>
                <q-btn flat round color="primary" class="q-mb-sm">
                  <icons name="linkedin" />
                  <q-tooltip>Share on LinkedIn</q-tooltip>
                </q-btn>
                <q-btn flat round color="primary" class="q-mb-sm">
                  <icons name="facebook" />
                  <q-tooltip>Share on Facebook</q-tooltip>
                </q-btn>
                <q-btn flat round color="primary">
                  <icons name="content_copy" />
                  <q-tooltip>Copy Link</q-tooltip>
                </q-btn>
              </div>
            </div>

            <!-- Article Body -->
            <div class="col-md-8 col-sm-12 col-xs-12">
              <div class="content-wrapper">
                <!-- Article Excerpt -->
                <div class="article-excerpt text-h6 text-weight-regular text-grey-8 q-mb-xl">
                  {{ article.excerpt }}
                </div>

                <!-- Article Body -->
                <div class="article-body" v-html="formatContent(article.content)"></div>

                <!-- Tags -->
                <div class="article-tags q-mt-xl">
                  <q-chip
                    v-for="tag in article.tags"
                    :key="tag"
                    outline
                    color="primary"
                    class="q-mr-sm"
                  >
                    #{{ tag }}
                  </q-chip>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Related Stories Section -->
      <div class="related-stories-section q-py-xl">
        <div class="container q-mx-auto q-px-md">
          <h2 class="text-h4 q-mb-lg">Related Stories</h2>
          <div class="row q-col-gutter-lg">
            <div v-for="story in relatedStories" :key="story.id" class="col-12 col-md-4">
              <q-card class="related-story-card" flat bordered>
                <q-card-section>
                  <q-chip
                    :color="story.categoryColor || 'primary'"
                    text-color="white"
                    class="q-mb-sm"
                    size="sm"
                  >
                    {{ story.category }}
                  </q-chip>
                  <h3 class="text-h6 q-mb-sm">{{ story.title }}</h3>
                  <p class="text-body2 text-grey-8 q-mb-md">{{ story.excerpt }}</p>
                  <q-btn
                    flat
                    color="primary"
                    :to="{ name: 'article', params: { slug: story.slug }}"
                    label="Read More"
                    no-caps
                    class="q-px-md"
                  />
                </q-card-section>
              </q-card>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import Icons from '../../../components/ui/Icons.vue'
import { useRoute } from 'vue-router'
import { useNewsStore } from '@/stores/news'
import { useArticleStore } from '@/stores/articles'
// import MainLayout from '../layouts/MainLayout.vue'

const route = useRoute()
const newsStore = useNewsStore()
const articleStore = useArticleStore()
const article = ref<any>(null)
const loading = ref(true)

onMounted(async () => {
  const slug = route.params.slug as string
  loading.value = true

  try {
    // First try to get from article store
    article.value = await articleStore.getArticleBySlug(slug)

    // If not found in article store, try news store
    if (!article.value) {
      const newsItem = newsStore.newsItems.find(item => item.slug === slug)
      if (newsItem) {
        article.value = {
          ...newsItem,
          content: newsItem.content || newsItem.excerpt, // Fallback to excerpt if no content
          publishedAt: newsItem.date
        }
      }
    }

    if (!article.value) {
      throw new Error('Article not found')
    }
  } catch (error) {
    console.error('Failed to load article:', error)
    article.value = null
  } finally {
    loading.value = false
  }
})

const formatDate = (date: string) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// Add related stories computation
const relatedStories = computed(() => {
  if (!article.value) return []

  // Get stories from the same category, excluding current article
  return newsStore.newsItems
    .filter(item =>
      item.category === article.value.category &&
      item.slug !== article.value.slug
    )
    .slice(0, 3) // Limit to 3 related stories
})

const formatContent = (content: string): string => {
  if (!content) return '';

  // Remove specific time references (e.g., "10:00 AM - 11:00 AM")
  let formatted = content.replace(/\d{1,2}:\d{2}\s*(?:AM|PM)\s*-\s*\d{1,2}:\d{2}\s*(?:AM|PM)/gi, '');

  // Convert markdown to HTML
  formatted = formatted
    // Headers (making them smaller - h3, h4, h5)
    .replace(/^# (.*$)/gm, '<h3>$1</h3>')
    .replace(/^## (.*$)/gm, '<h4>$1</h4>')
    .replace(/^### (.*$)/gm, '<h5>$1</h5>')
    // Bold
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    // Italic
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    // Lists
    .replace(/^\- (.*$)/gm, '<li>$1</li>')
    .replace(/(<li>.*<\/li>)\n*/g, '$1')
    .replace(/(<li>.*<\/li>)+/g, '<ul>$&</ul>')
    // Clean up any remaining stray stars or hashes that aren't part of formatting
    .replace(/(?<![*#])[#*]+(?![*#])/g, '')
    // Split into paragraphs and wrap them
    .split(/\n\n+/)
    .map(para => para.trim())
    .filter(para => para) // Remove empty paragraphs
    .map(para => {
      // Don't wrap if it's already a header, list, or other HTML element
      if (para.startsWith('<')) return para;
      return `<p>${para}</p>`;
    })
    .join('\n\n');

  return formatted;
};
</script>

<style scoped>
.article-hero {
  position: relative;
  height: 160px; /* Reduced from 400px to 160px (60% reduction) */
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: flex-end;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(0,0,0,0.2), rgba(0,0,0,0.4));
}

.back-btn {
  position: relative;
  z-index: 2;
  margin: 0.5rem 0; /* Reduced margin */
}

.article-header-container {
  margin-top: -60px; /* Adjusted from -80px to maintain proportions */
  position: relative;
  z-index: 2;
}

.article-header-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  max-width: 800px;
  margin: 0 auto;
}

.title {
  font-weight: 600;
  line-height: 1.2;
  color: #1d1d1d;
}

.category-chip {
  font-weight: 500;
}

.article-meta {
  color: #546E7A;
  font-size: 0.95rem;
}

.share-sidebar {
  position: sticky;
  top: 100px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.content-wrapper {
  background: white;
  border-radius: 12px;
  padding: 3rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  margin-top: 2rem;
}

.article-excerpt {
  font-size: 1.4rem;
  line-height: 1.6;
  color: #546E7A;
  border-left: 4px solid var(--q-primary);
  padding-left: 1.5rem;
}

.article-body {
  font-size: 1.2rem;
  line-height: 1.8;
  color: #37474F;
}

.article-body p {
  margin-bottom: 1.8rem;
}

.article-body h2 {
  font-size: 2rem;
  margin: 2.5rem 0 1.5rem;
  font-weight: 600;
}

.article-body h3 {
  font-size: 1.5rem;
  margin: 2rem 0 1rem;
  font-weight: 600;
}

.article-body img {
  width: 100%;
  border-radius: 8px;
  margin: 2rem 0;
}

.article-body blockquote {
  border-left: 4px solid var(--q-primary);
  margin: 2rem 0;
  padding: 1rem 0 1rem 2rem;
  font-style: italic;
  color: #546E7A;
}

.related-stories-section {
  background-color: #f5f5f5;
}

.related-story-card {
  height: 100%;
  transition: all 0.3s ease;
}

.related-story-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.related-story-card h3 {
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.related-story-card p {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@media (max-width: 767px) {
  .article-hero {
    height: 120px; /* Reduced from 300px to 120px for mobile */
  }

  .article-header-container {
    margin-top: -40px; /* Adjusted for mobile */
  }

  .article-header-card {
    margin: 0 1rem;
    padding: 1.5rem;
  }

  .title {
    font-size: 2rem !important;
  }

  .content-wrapper {
    padding: 1.5rem;
    margin-top: 1rem;
  }

  .article-excerpt {
    font-size: 1.2rem;
  }

  .article-body {
    font-size: 1.1rem;
  }

  .article-meta {
    flex-direction: column;
    gap: 0.5rem;
  }

  .article-meta .q-separator-vertical {
    display: none;
  }

  .related-story-card {
    margin-bottom: 1rem;
  }
}
</style>
