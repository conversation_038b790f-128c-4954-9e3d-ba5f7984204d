<template>
  <q-card class="feature-roadmap-card">
    <q-card-section class="bg-primary text-white">
      <div class="text-h6">Coming Soon: Platform Features</div>
      <div class="text-subtitle2">Our roadmap for the next phase</div>
    </q-card-section>

    <q-card-section>
      <q-timeline color="secondary">
        <q-timeline-entry
          title="Marketplace for Innovation"
          subtitle="Q3 2025"
          icon="shopping_cart"
        >
          <div>
            A digital marketplace where innovators can showcase their projects and investors can discover opportunities.
          </div>
        </q-timeline-entry>

        <q-timeline-entry
          title="Smart Matchmaking"
          subtitle="Q3 2025"
          icon="people_alt"
        >
          <div>
            AI-powered matching between innovators, investors, mentors, and other stakeholders based on interests and needs.
          </div>
        </q-timeline-entry>

        <q-timeline-entry
          title="Mentorship Programs"
          subtitle="Q4 2025"
          icon="school"
        >
          <div>
            Structured mentorship programs connecting experienced professionals with innovators and entrepreneurs.
          </div>
        </q-timeline-entry>

        <q-timeline-entry
          title="Funding Opportunities"
          subtitle="Q4 2025"
          icon="attach_money"
        >
          <div>
            Access to grants, investments, and other funding sources for qualified projects and startups.
          </div>
        </q-timeline-entry>

        <q-timeline-entry
          title="Resource Sharing"
          subtitle="Q1 2026"
          icon="share"
        >
          <div>
            Platform for sharing knowledge, tools, and resources among community members.
          </div>
        </q-timeline-entry>

        <q-timeline-entry
          title="Community Engagement"
          subtitle="Q1 2026"
          icon="forum"
        >
          <div>
            Enhanced community features including forums, events, and collaborative spaces.
          </div>
        </q-timeline-entry>
      </q-timeline>
    </q-card-section>

    <q-card-actions align="right">
      <q-btn flat color="primary" label="Learn More" to="/about" />
    </q-card-actions>
  </q-card>
</template>

<script setup lang="ts">
// Feature Roadmap Component
</script>

<style scoped>
.feature-roadmap-card {
  height: 100%;
}

.q-timeline {
  padding-top: 0;
}
</style>
