import { defineStore } from 'pinia'
import { computed } from 'vue'
import { useEventsStore } from './events'

/**
 * Content Store
 *
 * This store serves as a wrapper around specialized content stores
 * to maintain backward compatibility while eliminating duplicate database calls.
 */
export const useContentStore = defineStore('content', () => {
  // Use the specialized stores
  const eventsStore = useEventsStore()

  // State passthrough
  const loading = computed(() => eventsStore.loading)
  const error = computed(() => eventsStore.error)

  // Map events to the expected interface
  const events = computed(() => {
    return eventsStore.events.map(event => ({
      id: String(event.id),
      title: event.title,
      date: event.date,
      description: event.description,
      location: event.location,
      link: ''
    }))
  })

  // Getters
  const getEvents = computed(() => events.value)
  const getUpcomingEvents = computed(() => {
    const now = new Date()
    return events.value
      .filter(event => new Date(event.date) > now)
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
  })

  // Actions
  async function fetchEvents() {
    await eventsStore.fetchEvents()
  }

  return {
    // State
    loading,
    error,
    events,

    // Getters
    getEvents,
    getUpcomingEvents,

    // Actions
    fetchEvents
  }
})