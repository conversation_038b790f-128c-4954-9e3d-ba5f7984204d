export type IssueType = 'Task' | 'Bug' | 'Story' | 'Epic'

export interface User {
  accountId: string
  emailAddress: string
  displayName: string
  active: boolean
}

export interface IssueStatus {
  id: string
  name: string
  statusCategory: {
    id: number
    key: string
    name: string
  }
}

export interface Issue {
  id: string
  key: string
  fields: {
    summary: string
    description?: {
      content: Array<{
        content: Array<{
          text: string
        }>
      }>
    }
    status: IssueStatus
    creator: User
    assignee?: User
    reporter: User
    created: string
    updated: string
    issuetype: {
      id: string
      name: IssueType
    }
  }
} 