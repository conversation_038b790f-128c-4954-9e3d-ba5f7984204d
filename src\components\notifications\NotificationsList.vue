<template>
  <div class="notifications-list">
    <div v-if="title" class="text-h6 q-mb-md">{{ title }}</div>

    <div v-if="loading" class="text-center q-pa-md">
      <q-spinner color="primary" size="2em" />
      <p>Loading notifications...</p>
    </div>

    <div v-else-if="notifications.length === 0" class="text-center q-pa-md text-grey">
      <q-icon name="info" size="2em" />
      <p>No notifications to display</p>
    </div>

    <div v-else>
      <div class="row q-col-gutter-md">
        <div
          v-for="notification in notifications"
          :key="notification.id"
          class="col-12"
        >
          <q-card 
            class="notification-card" 
            :class="{ 'unread': !notification.is_read }"
            @click="handleNotificationClick(notification)"
          >
            <q-item>
              <q-item-section avatar>
                <q-avatar :color="getNotificationColor(notification.type)" text-color="white">
                  <q-icon :name="getNotificationIcon(notification.type)" />
                </q-avatar>
              </q-item-section>

              <q-item-section>
                <q-item-label class="text-weight-medium">
                  {{ notification.title }}
                </q-item-label>
                <q-item-label caption>
                  {{ notification.content }}
                </q-item-label>
                <q-item-label caption>
                  {{ formatDate(notification.created_at) }}
                </q-item-label>
              </q-item-section>

              <q-item-section side>
                <q-btn
                  flat
                  round
                  color="grey"
                  icon="close"
                  @click.stop="markAsRead([notification.id])"
                >
                  <q-tooltip>Mark as read</q-tooltip>
                </q-btn>
              </q-item-section>
            </q-item>
          </q-card>
        </div>
      </div>

      <div v-if="hasMore" class="text-center q-mt-md">
        <q-btn
          outline
          color="primary"
          label="Load More"
          :loading="loadingMore"
          @click="loadMore"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { date } from 'quasar';
import { useUserNotificationsStore } from '../../stores/userNotifications';
import { useRouter } from 'vue-router';

const props = defineProps({
  title: {
    type: String,
    default: 'Notifications'
  },
  limit: {
    type: Number,
    default: 10
  }
});

const router = useRouter();
const notificationsStore = useUserNotificationsStore();
const currentPage = ref(1);
const hasMore = ref(false);
const loadingMore = ref(false);

// Computed properties from the store
const notifications = computed(() => notificationsStore.notifications);
const loading = computed(() => notificationsStore.loading);

onMounted(async () => {
  await loadNotifications();
});

async function loadNotifications() {
  try {
    const result = await notificationsStore.fetchNotifications(
      props.limit,
      currentPage.value,
      false
    );
    
    hasMore.value = result.length === props.limit;
  } catch (error) {
    console.error('Error loading notifications:', error);
  }
}

async function loadMore() {
  try {
    loadingMore.value = true;
    currentPage.value++;
    
    const result = await notificationsStore.fetchNotifications(
      props.limit,
      currentPage.value,
      false
    );
    
    hasMore.value = result.length === props.limit;
  } catch (error) {
    console.error('Error loading more notifications:', error);
  } finally {
    loadingMore.value = false;
  }
}

async function markAsRead(notificationIds: string[]) {
  await notificationsStore.markAsRead(notificationIds);
}

async function handleNotificationClick(notification: any) {
  // Mark the notification as read
  await markAsRead([notification.id]);
  
  // Navigate based on notification type
  if (notification.type === 'connection_request') {
    router.push('/dashboard/connections');
  } else if (notification.type === 'connection_accepted') {
    router.push('/dashboard/connections');
  } else if (notification.type === 'message') {
    router.push('/dashboard/messages');
  }
}

function formatDate(dateString: string) {
  if (!dateString) return '';
  
  const now = new Date();
  const notificationDate = new Date(dateString);
  const diffInDays = Math.floor((now.getTime() - notificationDate.getTime()) / (1000 * 60 * 60 * 24));
  
  if (diffInDays === 0) {
    return 'Today, ' + date.formatDate(notificationDate, 'h:mm A');
  } else if (diffInDays === 1) {
    return 'Yesterday, ' + date.formatDate(notificationDate, 'h:mm A');
  } else if (diffInDays < 7) {
    return date.formatDate(notificationDate, 'dddd, h:mm A');
  } else {
    return date.formatDate(notificationDate, 'MMMM D, YYYY, h:mm A');
  }
}

function getNotificationIcon(type: string): string {
  switch (type) {
    case 'connection_request':
      return 'person_add';
    case 'connection_accepted':
      return 'how_to_reg';
    case 'message':
      return 'chat';
    case 'post_like':
      return 'favorite';
    case 'post_comment':
      return 'comment';
    default:
      return 'notifications';
  }
}

function getNotificationColor(type: string): string {
  switch (type) {
    case 'connection_request':
      return 'purple';
    case 'connection_accepted':
      return 'green';
    case 'message':
      return 'blue';
    case 'post_like':
      return 'red';
    case 'post_comment':
      return 'orange';
    default:
      return 'grey';
  }
}
</script>

<style scoped>
.notification-card {
  cursor: pointer;
  transition: background-color 0.2s;
}

.notification-card:hover {
  background-color: #f5f5f5;
}

.notification-card.unread {
  background-color: #f0f7ff;
  border-left: 4px solid #1976d2;
}
</style>
