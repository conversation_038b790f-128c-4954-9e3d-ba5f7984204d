<template>
  <q-page padding>
    <div class="row q-col-gutter-lg">
      <div class="col-12 col-md-3">
        <q-card class="help-nav">
          <q-list padding>
            <q-item
              v-for="section in helpSections"
              :key="section.id"
              clickable
              v-ripple
              :active="activeSection === section.id"
              @click="activeSection = section.id"
              active-class="text-primary"
            >
              <q-item-section avatar>
                <unified-icon :name="section.icon" />
              </q-item-section>
              <q-item-section>{{ section.title }}</q-item-section>
            </q-item>
          </q-list>
        </q-card>
      </div>

      <div class="col-12 col-md-9">
        <q-card>
          <q-card-section>
            <div class="text-h5 q-mb-md">{{ currentSection.title }}</div>
            <div class="q-pa-md">
              <q-list separator>
                <q-expansion-item
                  v-for="faq in currentSection.faqs"
                  :key="faq.id"
                  :label="faq.question"
                  header-class="text-primary"
                  expand-separator
                  expand-icon="none"
                >
                  <template v-slot:expand-icon="{ expanded }">
                    <unified-icon :name="expanded ? 'expand_less' : 'expand_more'" class="text-primary" />
                  </template>
                  <q-card>
                    <q-card-section>
                      {{ faq.answer }}
                    </q-card-section>
                    <q-card-actions v-if="faq.helpful" align="right">
                      <q-btn flat color="primary" :to="faq.helpful.link" :label="faq.helpful.text" />
                    </q-card-actions>
                  </q-card>
                </q-expansion-item>
              </q-list>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import UnifiedIcon from '../../components/ui/UnifiedIcon.vue'

const activeSection = ref('getting-started')

const helpSections = [
  {
    id: 'getting-started',
    title: 'Getting Started',
    icon: 'work',
    faqs: [
      {
        id: 1,
        question: 'How do I join the Innovation Hub?',
        answer: 'To join the Innovation Hub, simply click the "Register" button on the homepage and follow the registration process. Once approved, you\'ll have access to all our resources and events.',
        helpful: {
          text: 'Go to Registration',
          link: '/register'
        }
      },
      {
        id: 2,
        question: 'What resources are available?',
        answer: 'The Innovation Hub provides access to co-working spaces, mentorship programs, workshops, funding opportunities, and networking events.'
      }
    ]
  },
  {
    id: 'events',
    title: 'Events & Programs',
    icon: 'today',
    faqs: [
      {
        id: 3,
        question: 'How do I register for events?',
        answer: 'Browse our events page and click "Register" for any event you\'re interested in. You\'ll receive a confirmation email with further details.'
      },
      {
        id: 4,
        question: 'Can I propose an event?',
        answer: 'Yes! Members can propose events through the Events section. Click on "Propose Event" and fill out the event proposal form.'
      }
    ]
  },
  {
    id: 'technical',
    title: 'Technical Support',
    icon: 'search',
    faqs: [
      {
        id: 5,
        question: 'I can\'t access my account',
        answer: 'Try resetting your password first. If issues persist, contact our support team through the help button below.'
      },
      {
        id: 6,
        question: 'How do I update my profile?',
        answer: 'Go to your profile page by clicking your avatar in the top right corner. Click "Edit Profile" to make changes.'
      }
    ]
  }
]

const currentSection = computed(() => {
  return helpSections.find(section => section.id === activeSection.value) || helpSections[0]
})
</script>

<style scoped>
.help-nav {
  position: sticky;
  top: 20px;
}

.icon {
  width: 20px;
  height: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.q-item-section.avatar .icon {
  width: 24px;
  height: 24px;
}
</style>