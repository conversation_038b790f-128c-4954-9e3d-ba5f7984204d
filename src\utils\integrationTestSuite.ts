/**
 * Comprehensive Integration Test Suite
 * 
 * Tests all migrated components to ensure unified services work correctly
 * across the entire platform.
 */

import { useUnifiedCache } from '../services/unifiedCacheService'
import { useUnifiedRealtime } from '../services/unifiedRealtimeService'
import { ProfileManager } from '../services/ProfileManager'
import { useMessagingStore } from '../stores/messaging'
import { clearAuthCache, getAuthCacheStats } from '../router/enhancedGuards'

export interface IntegrationTestResult {
  testName: string
  passed: boolean
  duration: number
  details: string
  errors?: string[]
}

export interface IntegrationTestSuite {
  suiteName: string
  totalTests: number
  passedTests: number
  failedTests: number
  totalDuration: number
  results: IntegrationTestResult[]
  summary: {
    cachePerformance: any
    realtimeHealth: any
    overallHealth: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR'
  }
}

export class IntegrationTestRunner {
  private cache = useUnifiedCache()
  private realtime = useUnifiedRealtime()
  private results: IntegrationTestResult[] = []

  /**
   * Run complete integration test suite
   */
  async runCompleteTestSuite(): Promise<IntegrationTestSuite> {
    console.log('🚀 Starting comprehensive integration test suite...')
    
    const startTime = Date.now()
    this.results = []

    // Test unified services
    await this.testUnifiedCacheService()
    await this.testUnifiedRealtimeService()
    
    // Test migrated components
    await this.testProfileManagerIntegration()
    await this.testMessagingIntegration()
    await this.testFeedContainerIntegration()
    await this.testRouteGuardsIntegration()
    await this.testUserStateServiceIntegration()
    
    // Test cross-component integration
    await this.testCrossComponentIntegration()
    
    const totalDuration = Date.now() - startTime
    const passedTests = this.results.filter(r => r.passed).length
    const failedTests = this.results.filter(r => !r.passed).length

    const suite: IntegrationTestSuite = {
      suiteName: 'Unified Services Integration Test',
      totalTests: this.results.length,
      passedTests,
      failedTests,
      totalDuration,
      results: this.results,
      summary: {
        cachePerformance: this.cache.getStats(),
        realtimeHealth: this.realtime.getStats(),
        overallHealth: this.calculateOverallHealth(passedTests, failedTests)
      }
    }

    console.log('✅ Integration test suite complete!')
    return suite
  }

  /**
   * Test unified cache service
   */
  private async testUnifiedCacheService(): Promise<void> {
    const startTime = Date.now()
    const errors: string[] = []
    let passed = true

    try {
      // Test basic cache operations
      this.cache.set('test:integration:basic', { test: 'data' })
      const retrieved = this.cache.get('test:integration:basic')
      if (!retrieved || retrieved.test !== 'data') {
        errors.push('Basic cache set/get failed')
        passed = false
      }

      // Test cache invalidation
      this.cache.set('test:integration:pattern1', 'data1')
      this.cache.set('test:integration:pattern2', 'data2')
      const invalidated = this.cache.invalidate('test:integration:*')
      if (invalidated < 2) {
        errors.push('Pattern-based invalidation failed')
        passed = false
      }

      // Test TTL
      this.cache.set('test:integration:ttl', 'data', { ttl: 100 })
      await new Promise(resolve => setTimeout(resolve, 150))
      const expired = this.cache.get('test:integration:ttl')
      if (expired !== null) {
        errors.push('TTL expiration failed')
        passed = false
      }

      // Test different storage backends
      this.cache.set('test:memory', 'data', { storage: 'memory' })
      this.cache.set('test:session', 'data', { storage: 'sessionStorage' })
      this.cache.set('test:local', 'data', { storage: 'localStorage' })

      const memoryData = this.cache.get('test:memory')
      const sessionData = this.cache.get('test:session')
      const localData = this.cache.get('test:local')

      if (!memoryData || !sessionData || !localData) {
        errors.push('Multi-storage backend test failed')
        passed = false
      }

    } catch (error: any) {
      errors.push(`Cache test exception: ${error.message}`)
      passed = false
    }

    this.results.push({
      testName: 'Unified Cache Service',
      passed,
      duration: Date.now() - startTime,
      details: passed ? 'All cache operations working correctly' : 'Cache operations failed',
      errors: errors.length > 0 ? errors : undefined
    })
  }

  /**
   * Test unified real-time service
   */
  private async testUnifiedRealtimeService(): Promise<void> {
    const startTime = Date.now()
    const errors: string[] = []
    let passed = true

    try {
      // Test subscription creation
      const subscription = this.realtime.subscribe(
        { table: 'test_table', event: '*' },
        () => {},
        { deduplicate: true }
      )

      if (!subscription) {
        errors.push('Subscription creation failed')
        passed = false
      }

      // Test connection state
      const connectionState = this.realtime.getConnectionState()
      if (!['OPEN', 'CONNECTING'].includes(connectionState)) {
        errors.push(`Invalid connection state: ${connectionState}`)
        passed = false
      }

      // Test subscription cleanup
      const unsubscribed = this.realtime.unsubscribe(subscription)
      if (!unsubscribed) {
        errors.push('Subscription cleanup failed')
        passed = false
      }

      // Test health check
      const isHealthy = this.realtime.isHealthy()
      if (!isHealthy) {
        errors.push('Real-time service health check failed')
        passed = false
      }

    } catch (error: any) {
      errors.push(`Real-time test exception: ${error.message}`)
      passed = false
    }

    this.results.push({
      testName: 'Unified Real-time Service',
      passed,
      duration: Date.now() - startTime,
      details: passed ? 'Real-time service functioning correctly' : 'Real-time service issues detected',
      errors: errors.length > 0 ? errors : undefined
    })
  }

  /**
   * Test ProfileManager integration
   */
  private async testProfileManagerIntegration(): Promise<void> {
    const startTime = Date.now()
    const errors: string[] = []
    let passed = true

    try {
      const profileManager = ProfileManager.getInstance()
      
      // Test cache invalidation
      profileManager.invalidateProfile('test-user-id')
      
      // This would require actual user data to test fully
      // For now, just verify the methods exist and don't throw
      if (typeof profileManager.invalidateProfile !== 'function') {
        errors.push('ProfileManager invalidateProfile method missing')
        passed = false
      }

    } catch (error: any) {
      errors.push(`ProfileManager test exception: ${error.message}`)
      passed = false
    }

    this.results.push({
      testName: 'ProfileManager Integration',
      passed,
      duration: Date.now() - startTime,
      details: passed ? 'ProfileManager integrated with unified cache' : 'ProfileManager integration issues',
      errors: errors.length > 0 ? errors : undefined
    })
  }

  /**
   * Test messaging integration
   */
  private async testMessagingIntegration(): Promise<void> {
    const startTime = Date.now()
    const errors: string[] = []
    let passed = true

    try {
      const messagingStore = useMessagingStore()
      
      // Test store methods exist
      if (typeof messagingStore.loadConversations !== 'function') {
        errors.push('MessagingStore loadConversations method missing')
        passed = false
      }

      if (typeof messagingStore.loadMessages !== 'function') {
        errors.push('MessagingStore loadMessages method missing')
        passed = false
      }

      if (typeof messagingStore.sendMessage !== 'function') {
        errors.push('MessagingStore sendMessage method missing')
        passed = false
      }

    } catch (error: any) {
      errors.push(`Messaging integration test exception: ${error.message}`)
      passed = false
    }

    this.results.push({
      testName: 'Messaging Integration',
      passed,
      duration: Date.now() - startTime,
      details: passed ? 'Messaging system integrated with unified services' : 'Messaging integration issues',
      errors: errors.length > 0 ? errors : undefined
    })
  }

  /**
   * Test FeedContainer integration
   */
  private async testFeedContainerIntegration(): Promise<void> {
    const startTime = Date.now()
    const errors: string[] = []
    let passed = true

    try {
      // Test feed cache keys
      this.cache.set('feed:tab:test', true, { ttl: 30000 })
      const cached = this.cache.get('feed:tab:test')
      
      if (!cached) {
        errors.push('FeedContainer cache integration failed')
        passed = false
      }

      // Test cache invalidation pattern
      const invalidated = this.cache.invalidate('feed:tab:*')
      if (invalidated < 1) {
        errors.push('FeedContainer cache invalidation failed')
        passed = false
      }

    } catch (error: any) {
      errors.push(`FeedContainer integration test exception: ${error.message}`)
      passed = false
    }

    this.results.push({
      testName: 'FeedContainer Integration',
      passed,
      duration: Date.now() - startTime,
      details: passed ? 'FeedContainer integrated with unified cache' : 'FeedContainer integration issues',
      errors: errors.length > 0 ? errors : undefined
    })
  }

  /**
   * Test route guards integration
   */
  private async testRouteGuardsIntegration(): Promise<void> {
    const startTime = Date.now()
    const errors: string[] = []
    let passed = true

    try {
      // Test auth cache functions
      clearAuthCache()
      const stats = getAuthCacheStats()
      
      if (typeof stats !== 'object') {
        errors.push('Route guards cache stats failed')
        passed = false
      }

      // Test auth cache key pattern
      this.cache.set('auth:userState:test', true)
      const authCached = this.cache.get('auth:userState:test')
      
      if (!authCached) {
        errors.push('Route guards cache integration failed')
        passed = false
      }

    } catch (error: any) {
      errors.push(`Route guards integration test exception: ${error.message}`)
      passed = false
    }

    this.results.push({
      testName: 'Route Guards Integration',
      passed,
      duration: Date.now() - startTime,
      details: passed ? 'Route guards integrated with unified cache' : 'Route guards integration issues',
      errors: errors.length > 0 ? errors : undefined
    })
  }

  /**
   * Test user state service integration
   */
  private async testUserStateServiceIntegration(): Promise<void> {
    const startTime = Date.now()
    const errors: string[] = []
    let passed = true

    try {
      // Test user state cache key pattern
      this.cache.set('userState:test', { state: 'COMPLETE_PROFILE', data: {} })
      const userStateCached = this.cache.get('userState:test')
      
      if (!userStateCached) {
        errors.push('User state service cache integration failed')
        passed = false
      }

    } catch (error: any) {
      errors.push(`User state service integration test exception: ${error.message}`)
      passed = false
    }

    this.results.push({
      testName: 'User State Service Integration',
      passed,
      duration: Date.now() - startTime,
      details: passed ? 'User state service integrated with unified cache' : 'User state service integration issues',
      errors: errors.length > 0 ? errors : undefined
    })
  }

  /**
   * Test cross-component integration
   */
  private async testCrossComponentIntegration(): Promise<void> {
    const startTime = Date.now()
    const errors: string[] = []
    let passed = true

    try {
      // Test cache key coordination
      const cacheKeys = [
        'profile:test:private',
        'messaging:conversations:10:1',
        'messaging:messages:test:20:1',
        'feed:tab:feed',
        'auth:userState:test',
        'userState:test'
      ]

      // Set all cache keys
      cacheKeys.forEach(key => {
        this.cache.set(key, { test: 'data' })
      })

      // Verify all are cached
      const allCached = cacheKeys.every(key => this.cache.get(key) !== null)
      if (!allCached) {
        errors.push('Cross-component cache coordination failed')
        passed = false
      }

      // Test pattern-based invalidation across components
      const profileInvalidated = this.cache.invalidate('profile:*')
      const messagingInvalidated = this.cache.invalidate('messaging:*')
      const feedInvalidated = this.cache.invalidate('feed:*')
      const authInvalidated = this.cache.invalidate('auth:*')

      if (profileInvalidated < 1 || messagingInvalidated < 2 || feedInvalidated < 1 || authInvalidated < 1) {
        errors.push('Cross-component cache invalidation failed')
        passed = false
      }

      // Test cache statistics
      const stats = this.cache.getStats()
      if (stats.hitRate < 0 || stats.totalEntries < 0) {
        errors.push('Cache statistics invalid')
        passed = false
      }

    } catch (error: any) {
      errors.push(`Cross-component integration test exception: ${error.message}`)
      passed = false
    }

    this.results.push({
      testName: 'Cross-Component Integration',
      passed,
      duration: Date.now() - startTime,
      details: passed ? 'All components working together correctly' : 'Cross-component integration issues',
      errors: errors.length > 0 ? errors : undefined
    })
  }

  /**
   * Calculate overall health based on test results
   */
  private calculateOverallHealth(passed: number, failed: number): 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR' {
    const total = passed + failed
    const passRate = passed / total

    if (passRate >= 0.95) return 'EXCELLENT'
    if (passRate >= 0.85) return 'GOOD'
    if (passRate >= 0.70) return 'FAIR'
    return 'POOR'
  }
}

/**
 * Quick integration test for immediate validation
 */
export async function runQuickIntegrationTest(): Promise<void> {
  const runner = new IntegrationTestRunner()
  
  try {
    console.log('🔍 Running quick integration test...')
    
    const results = await runner.runCompleteTestSuite()
    
    console.log(`
📊 Integration Test Results
${'='.repeat(50)}

🎯 Test Summary:
   Total Tests: ${results.totalTests}
   Passed: ${results.passedTests}
   Failed: ${results.failedTests}
   Duration: ${results.totalDuration}ms
   Overall Health: ${results.summary.overallHealth}

📈 Cache Performance:
   Hit Rate: ${(results.summary.cachePerformance.hitRate * 100).toFixed(1)}%
   Total Entries: ${results.summary.cachePerformance.totalEntries}
   Memory Usage: ${(results.summary.cachePerformance.memoryUsage / 1024).toFixed(1)} KB

📡 Real-time Health:
   Connection: ${results.summary.realtimeHealth.connectionState}
   Subscriptions: ${results.summary.realtimeHealth.activeSubscriptions}
   Events: ${results.summary.realtimeHealth.totalEvents}

🔍 Test Details:
${results.results.map(r => 
  `   ${r.passed ? '✅' : '❌'} ${r.testName} (${r.duration}ms)${r.errors ? '\n      Errors: ' + r.errors.join(', ') : ''}`
).join('\n')}
`)
    
    // Save results to localStorage for debugging
    localStorage.setItem('integrationTestResults', JSON.stringify(results, null, 2))
    console.log('💾 Results saved to localStorage as "integrationTestResults"')
    
  } catch (error) {
    console.error('❌ Integration test failed:', error)
  }
}

// Export for use in browser console
if (typeof window !== 'undefined') {
  (window as any).integrationTest = {
    runQuickTest: runQuickIntegrationTest,
    IntegrationTestRunner
  }
}
