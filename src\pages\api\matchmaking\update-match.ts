/**
 * API endpoint to update a match
 * 
 * This endpoint updates a match in the matchmaking_results table,
 * allowing users to save or dismiss matches.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow PATCH requests
  if (req.method !== 'PATCH') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  
  // Create Supabase server client
  const supabase = createServerSupabaseClient({ req, res });
  
  // Check if user is authenticated
  const {
    data: { session },
  } = await supabase.auth.getSession();
  
  if (!session) {
    return res.status(401).json({ error: 'Unauthorized' });
  }
  
  try {
    // Get the match ID and update data from the request body
    const { matchId, isSaved } = req.body as { matchId: string, isSaved: boolean };
    
    if (!matchId) {
      return res.status(400).json({ error: 'Match ID is required' });
    }
    
    // Verify the match belongs to the user
    const { data: match, error: matchError } = await supabase
      .from('matchmaking_results')
      .select('id')
      .eq('id', matchId)
      .eq('user_id', session.user.id)
      .single();
      
    if (matchError || !match) {
      return res.status(404).json({ error: 'Match not found or does not belong to user' });
    }
    
    // Update the match
    const { error: updateError } = await supabase
      .from('matchmaking_results')
      .update({ is_saved: isSaved })
      .eq('id', matchId);
      
    if (updateError) {
      console.error('Error updating match:', updateError);
      return res.status(500).json({ error: 'Failed to update match' });
    }
    
    // Track the interaction for future matching refinement
    await supabase
      .from('component_interactions')
      .insert({
        user_id: session.user.id,
        component_key: 'match_card',
        entity_id: matchId,
        interaction_type: isSaved ? 'save_match' : 'dismiss_match',
        interaction_data: { timestamp: new Date().toISOString() }
      });
    
    return res.status(200).json({ 
      success: true, 
      message: isSaved ? 'Match saved successfully' : 'Match dismissed successfully'
    });
  } catch (error) {
    console.error('Error updating match:', error);
    return res.status(500).json({ error: 'Failed to update match' });
  }
}
