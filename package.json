{"name": "ZbInnovation", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:e2e": "cypress open", "test:playwright": "playwright test", "test:playwright:ui": "playwright test --ui", "test:playwright:headed": "playwright test --headed"}, "dependencies": {"@anthropic-ai/sdk": "^0.39.0", "@chenfengyuan/vue-countdown": "^2.1.3", "@quasar/extras": "^1.16.17", "@supabase/supabase-js": "^2.49.4", "@types/dompurify": "^3.0.5", "@vueup/vue-quill": "^1.2.0", "boxen": "^7.1.1", "chalk": "^5.3.0", "cli-table3": "^0.6.3", "commander": "^11.1.0", "composio-core": "^0.5.31", "cors": "^2.8.5", "dompurify": "^3.2.6", "dotenv": "^16.5.0", "express": "^4.21.2", "fastmcp": "^1.20.5", "figlet": "^1.7.0", "fuse.js": "^7.0.0", "gradient-string": "^2.0.2", "helmet": "^8.1.0", "inquirer": "^12.5.0", "jsonwebtoken": "^9.0.2", "lru-cache": "^10.2.0", "openai": "^4.86.1", "ora": "^7.0.1", "pinia": "^2.1.7", "quasar": "^2.14.5", "task-master-ai": "^0.9.30", "vue": "^3.4.15", "vue-router": "^4.5.0", "vue3-flip-countdown": "^0.1.6"}, "devDependencies": {"@playwright/test": "^1.53.2", "@quasar/vite-plugin": "^1.6.0", "@testing-library/vue": "^8.0.1", "@types/node": "^20.11.16", "@vitejs/plugin-vue": "^5.0.3", "@vitest/coverage-v8": "^1.6.1", "@vitest/ui": "^1.6.1", "@vue/compiler-sfc": "^3.5.13", "@vue/test-utils": "^2.4.3", "cypress": "^13.6.4", "happy-dom": "^13.3.8", "sass": "^1.70.0", "supabase": "^2.20.12", "typescript": "^5.3.3", "vite": "^5.0.12", "vitest": "^1.6.1"}, "type": "module"}