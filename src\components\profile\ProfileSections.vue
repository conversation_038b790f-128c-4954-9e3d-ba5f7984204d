<template>
  <div class="profile-sections">
    <!-- Personal Details Section -->
    <q-card class="q-mb-md section-card">
      <q-card-section class="section-header bg-primary text-white">
        <div class="text-h6">
          <unified-icon name="person" class="q-mr-sm" />
          Personal Details
        </div>
      </q-card-section>
      <q-card-section>
        <div class="row q-col-gutter-md">
          <div class="col-12 col-md-6">
            <div class="text-subtitle2">First Name</div>
            <div>{{ profile.first_name || 'Not provided' }}</div>
          </div>
          <div class="col-12 col-md-6">
            <div class="text-subtitle2">Last Name</div>
            <div>{{ profile.last_name || 'Not provided' }}</div>
          </div>
          <div v-if="context === 'dashboard'" class="col-12 col-md-6">
            <div class="text-subtitle2">Email</div>
            <div>{{ profile.email || 'Not provided' }}</div>
          </div>
          <div class="col-12 col-md-6" v-if="profile.phone_number">
            <div class="text-subtitle2">Phone</div>
            <div>
              <span v-if="profile.phone_country_code">{{ profile.phone_country_code }} </span>
              {{ profile.phone_number }}
            </div>
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- Professional Information Section -->
    <q-card v-if="hasProfessionalInfo" class="q-mb-md section-card">
      <q-card-section class="section-header bg-primary text-white">
        <div class="text-h6">
          <unified-icon name="work" class="q-mr-sm" />
          Professional Information
        </div>
      </q-card-section>
      <q-card-section>
        <div class="row q-col-gutter-md">
          <div class="col-12 col-md-6" v-if="profile.job_title">
            <div class="text-subtitle2">Job Title</div>
            <div>{{ profile.job_title }}</div>
          </div>
          <div class="col-12 col-md-6" v-if="profile.company">
            <div class="text-subtitle2">Company</div>
            <div>{{ profile.company }}</div>
          </div>
          <div class="col-12 col-md-6" v-if="profile.industry">
            <div class="text-subtitle2">Industry</div>
            <div>{{ profile.industry }}</div>
          </div>
          <div class="col-12 col-md-6" v-if="profile.years_of_experience">
            <div class="text-subtitle2">Years of Experience</div>
            <div>{{ profile.years_of_experience }}</div>
          </div>
          <div class="col-12" v-if="profile.skills && Array.isArray(profile.skills) && profile.skills.length > 0">
            <div class="text-subtitle2">Skills</div>
            <div class="q-mt-sm">
              <q-chip 
                v-for="skill in profile.skills" 
                :key="skill" 
                color="primary" 
                text-color="white" 
                class="q-ma-xs"
              >
                {{ skill }}
              </q-chip>
            </div>
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- Innovator Profile Sections -->
    <template v-if="profile.profile_type === 'innovator'">
      <!-- Innovation Details Section -->
      <q-card v-if="hasInnovationDetails" class="q-mb-md section-card">
        <q-card-section class="section-header bg-primary text-white">
          <div class="text-h6">
            <unified-icon name="lightbulb" class="q-mr-sm" />
            Innovation Details
          </div>
        </q-card-section>
        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-6" v-if="profile.innovation_area">
              <div class="text-subtitle2">Innovation Area</div>
              <div>{{ profile.innovation_area }}</div>
            </div>
            <div class="col-12 col-md-6" v-if="profile.innovation_stage">
              <div class="text-subtitle2">Innovation Stage</div>
              <div>{{ profile.innovation_stage }}</div>
            </div>
            <div class="col-12" v-if="profile.innovation_description">
              <div class="text-subtitle2">Innovation Description</div>
              <div>{{ profile.innovation_description }}</div>
            </div>
            <div class="col-12 col-md-6" v-if="profile.team_size !== undefined">
              <div class="text-subtitle2">Team Size</div>
              <div>{{ profile.team_size }}</div>
            </div>
            <div class="col-12" v-if="profile.team_description">
              <div class="text-subtitle2">Team Description</div>
              <div>{{ profile.team_description }}</div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Funding & Prototype Section -->
      <q-card v-if="hasFundingInfo" class="q-mb-md section-card">
        <q-card-section class="section-header bg-primary text-white">
          <div class="text-h6">
            <unified-icon name="attach_money" class="q-mr-sm" />
            Funding & Prototype
          </div>
        </q-card-section>
        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-6" v-if="profile.funding_needs !== undefined">
              <div class="text-subtitle2">Seeking Funding</div>
              <div>{{ profile.funding_needs ? 'Yes' : 'No' }}</div>
            </div>
            <div class="col-12 col-md-6" v-if="profile.funding_amount">
              <div class="text-subtitle2">Funding Amount</div>
              <div>{{ profile.funding_amount }}</div>
            </div>
            <div class="col-12 col-md-6" v-if="profile.funding_stage">
              <div class="text-subtitle2">Funding Stage</div>
              <div>{{ profile.funding_stage }}</div>
            </div>
            <div class="col-12 col-md-6" v-if="profile.has_prototype !== undefined">
              <div class="text-subtitle2">Has Prototype</div>
              <div>{{ profile.has_prototype ? 'Yes' : 'No' }}</div>
            </div>
            <div class="col-12" v-if="profile.prototype_description">
              <div class="text-subtitle2">Prototype Description</div>
              <div>{{ profile.prototype_description }}</div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Market & Competition Section -->
      <q-card v-if="hasMarketInfo" class="q-mb-md section-card">
        <q-card-section class="section-header bg-primary text-white">
          <div class="text-h6">
            <unified-icon name="trending_up" class="q-mr-sm" />
            Market & Competition
          </div>
        </q-card-section>
        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12" v-if="profile.target_market">
              <div class="text-subtitle2">Target Market</div>
              <div>{{ profile.target_market }}</div>
            </div>
            <div class="col-12 col-md-6" v-if="profile.market_size">
              <div class="text-subtitle2">Market Size</div>
              <div>{{ profile.market_size }}</div>
            </div>
            <div class="col-12" v-if="profile.competitors">
              <div class="text-subtitle2">Competitors</div>
              <div>{{ profile.competitors }}</div>
            </div>
            <div class="col-12" v-if="profile.competitive_advantage">
              <div class="text-subtitle2">Competitive Advantage</div>
              <div>{{ profile.competitive_advantage }}</div>
            </div>
            <div class="col-12" v-if="profile.go_to_market">
              <div class="text-subtitle2">Go-to-Market Strategy</div>
              <div>{{ profile.go_to_market }}</div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Traction & Metrics Section -->
      <q-card v-if="hasTractionInfo" class="q-mb-md section-card">
        <q-card-section class="section-header bg-primary text-white">
          <div class="text-h6">
            <unified-icon name="insights" class="q-mr-sm" />
            Traction & Metrics
          </div>
        </q-card-section>
        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12" v-if="profile.current_traction">
              <div class="text-subtitle2">Current Traction</div>
              <div>{{ profile.current_traction }}</div>
            </div>
            <div class="col-12 col-md-6" v-if="profile.revenue_model">
              <div class="text-subtitle2">Revenue Model</div>
              <div>{{ profile.revenue_model }}</div>
            </div>
            <div class="col-12 col-md-6" v-if="profile.current_revenue">
              <div class="text-subtitle2">Current Annual Revenue</div>
              <div>{{ profile.current_revenue }}</div>
            </div>
            <div class="col-12" v-if="profile.key_metrics">
              <div class="text-subtitle2">Key Metrics</div>
              <div>{{ profile.key_metrics }}</div>
            </div>
            <div class="col-12 col-md-6" v-if="profile.growth_rate">
              <div class="text-subtitle2">Growth Rate</div>
              <div>{{ profile.growth_rate }}</div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Intellectual Property Section -->
      <q-card v-if="hasIPInfo" class="q-mb-md section-card">
        <q-card-section class="section-header bg-primary text-white">
          <div class="text-h6">
            <unified-icon name="gavel" class="q-mr-sm" />
            Intellectual Property
          </div>
        </q-card-section>
        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-6" v-if="profile.has_ip !== undefined">
              <div class="text-subtitle2">Has Intellectual Property</div>
              <div>{{ profile.has_ip ? 'Yes' : 'No' }}</div>
            </div>
            <div class="col-12" v-if="profile.ip_description">
              <div class="text-subtitle2">IP Description</div>
              <div>{{ profile.ip_description }}</div>
            </div>
            <div class="col-12 col-md-6" v-if="profile.ip_status">
              <div class="text-subtitle2">IP Status</div>
              <div>{{ profile.ip_status }}</div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Challenges Section -->
      <q-card v-if="profile.challenges" class="q-mb-md section-card">
        <q-card-section class="section-header bg-primary text-white">
          <div class="text-h6">
            <unified-icon name="flag" class="q-mr-sm" />
            Current Challenges
          </div>
        </q-card-section>
        <q-card-section>
          <goals-interests-display :data="profile.challenges" chip-color="orange" />
        </q-card-section>
      </q-card>
    </template>

    <!-- Mentor Profile Sections -->
    <template v-if="profile.profile_type === 'mentor'">
      <!-- Mentorship Details Section -->
      <q-card v-if="hasMentorshipDetails" class="q-mb-md section-card">
        <q-card-section class="section-header bg-primary text-white">
          <div class="text-h6">
            <unified-icon name="people" class="q-mr-sm" />
            Mentorship Details
          </div>
        </q-card-section>
        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-6" v-if="profile.mentorship_style">
              <div class="text-subtitle2">Mentorship Style</div>
              <div>{{ profile.mentorship_style }}</div>
            </div>
            <div class="col-12 col-md-6" v-if="profile.availability">
              <div class="text-subtitle2">Availability</div>
              <div>{{ profile.availability }}</div>
            </div>
            <div class="col-12 col-md-6" v-if="profile.previous_mentees">
              <div class="text-subtitle2">Previous Mentees</div>
              <div>{{ profile.previous_mentees }}</div>
            </div>
            <div class="col-12" v-if="profile.mentoring_experience">
              <div class="text-subtitle2">Mentorship Highlights</div>
              <div>{{ profile.mentoring_experience }}</div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Professional Background Section -->
      <q-card v-if="hasMentorProfessionalInfo" class="q-mb-md section-card">
        <q-card-section class="section-header bg-primary text-white">
          <div class="text-h6">
            <unified-icon name="work" class="q-mr-sm" />
            Professional Background
          </div>
        </q-card-section>
        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-6" v-if="profile.mentor_current_role">
              <div class="text-subtitle2">Current Role</div>
              <div>{{ profile.mentor_current_role }}</div>
            </div>
            <div class="col-12 col-md-6" v-if="profile.company">
              <div class="text-subtitle2">Company</div>
              <div>{{ profile.company }}</div>
            </div>
            <div class="col-12" v-if="profile.education">
              <div class="text-subtitle2">Education</div>
              <goals-interests-display :data="profile.education" chip-color="blue" />
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Areas of Expertise Section -->
      <q-card v-if="profile.areas_of_expertise" class="q-mb-md section-card">
        <q-card-section class="section-header bg-primary text-white">
          <div class="text-h6">
            <unified-icon name="star" class="q-mr-sm" />
            Areas of Expertise
          </div>
        </q-card-section>
        <q-card-section>
          <goals-interests-display :data="profile.areas_of_expertise" chip-color="green" />
        </q-card-section>
      </q-card>

      <!-- Success Stories Section -->
      <q-card v-if="hasSuccessStories" class="q-mb-md section-card">
        <q-card-section class="section-header bg-primary text-white">
          <div class="text-h6">
            <unified-icon name="emoji_events" class="q-mr-sm" />
            Success Stories
          </div>
        </q-card-section>
        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12" v-if="profile.success_stories">
              <div class="text-subtitle2">Success Stories</div>
              <div>{{ profile.success_stories }}</div>
            </div>
            <div class="col-12" v-if="profile.mentee_achievements">
              <div class="text-subtitle2">Mentee Achievements</div>
              <div>{{ profile.mentee_achievements }}</div>
            </div>
            <div class="col-12" v-if="profile.testimonials">
              <div class="text-subtitle2">Testimonials</div>
              <div>{{ profile.testimonials }}</div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Mentorship Methodology Section -->
      <q-card v-if="hasMentorshipMethodology" class="q-mb-md section-card">
        <q-card-section class="section-header bg-primary text-white">
          <div class="text-h6">
            <unified-icon name="psychology" class="q-mr-sm" />
            Mentorship Methodology
          </div>
        </q-card-section>
        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12" v-if="profile.mentorship_philosophy">
              <div class="text-subtitle2">Mentorship Philosophy</div>
              <div>{{ profile.mentorship_philosophy }}</div>
            </div>
            <div class="col-12" v-if="profile.mentorship_methods">
              <div class="text-subtitle2">Mentorship Methods</div>
              <goals-interests-display :data="profile.mentorship_methods" chip-color="purple" />
            </div>
            <div class="col-12" v-if="profile.mentorship_tools">
              <div class="text-subtitle2">Mentorship Tools</div>
              <goals-interests-display :data="profile.mentorship_tools" chip-color="indigo" />
            </div>
            <div class="col-12 col-md-6" v-if="profile.mentorship_duration">
              <div class="text-subtitle2">Preferred Mentorship Duration</div>
              <div>{{ profile.mentorship_duration }}</div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Mentee Preferences Section -->
      <q-card v-if="hasMenteePreferences" class="q-mb-md section-card">
        <q-card-section class="section-header bg-primary text-white">
          <div class="text-h6">
            <unified-icon name="person_search" class="q-mr-sm" />
            Mentee Preferences
          </div>
        </q-card-section>
        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12" v-if="profile.preferred_mentee_stage">
              <div class="text-subtitle2">Preferred Mentee Stage</div>
              <goals-interests-display :data="profile.preferred_mentee_stage" chip-color="cyan" />
            </div>
            <div class="col-12" v-if="profile.preferred_mentee_background">
              <div class="text-subtitle2">Preferred Mentee Background</div>
              <goals-interests-display :data="profile.preferred_mentee_background" chip-color="pink" />
            </div>
            <div class="col-12" v-if="profile.expectations">
              <div class="text-subtitle2">Mentee Expectations</div>
              <div>{{ profile.expectations }}</div>
            </div>
          </div>
        </q-card-section>
      </q-card>
    </template>

    <!-- Investor Profile Sections -->
    <template v-if="profile.profile_type === 'investor'">
      <!-- Investment Focus Section -->
      <q-card v-if="hasInvestmentFocus" class="q-mb-md section-card">
        <q-card-section class="section-header bg-primary text-white">
          <div class="text-h6">
            <unified-icon name="trending_up" class="q-mr-sm" />
            Investment Focus
          </div>
        </q-card-section>
        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12" v-if="profile.investment_focus">
              <div class="text-subtitle2">Investment Focus</div>
              <goals-interests-display :data="profile.investment_focus" chip-color="green" />
            </div>
            <div class="col-12" v-if="profile.investment_stage">
              <div class="text-subtitle2">Investment Stage</div>
              <goals-interests-display :data="profile.investment_stage" chip-color="blue" />
            </div>
            <div class="col-12 col-md-6" v-if="profile.ticket_size">
              <div class="text-subtitle2">Ticket Size</div>
              <div>{{ profile.ticket_size }}</div>
            </div>
            <div class="col-12 col-md-6" v-if="profile.previous_investments">
              <div class="text-subtitle2">Previous Investments</div>
              <div>{{ profile.previous_investments }}</div>
            </div>
            <div class="col-12" v-if="profile.investment_geography">
              <div class="text-subtitle2">Investment Geography</div>
              <goals-interests-display :data="profile.investment_geography" chip-color="purple" />
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Investment Criteria Section -->
      <q-card v-if="hasInvestmentCriteria" class="q-mb-md section-card">
        <q-card-section class="section-header bg-primary text-white">
          <div class="text-h6">
            <unified-icon name="rule" class="q-mr-sm" />
            Investment Criteria
          </div>
        </q-card-section>
        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12" v-if="profile.investment_criteria">
              <div class="text-subtitle2">Key Investment Criteria</div>
              <goals-interests-display :data="profile.investment_criteria" chip-color="orange" />
            </div>
            <div class="col-12" v-if="profile.success_stories">
              <div class="text-subtitle2">Success Stories</div>
              <div>{{ profile.success_stories }}</div>
            </div>
            <div class="col-12" v-if="profile.portfolio">
              <div class="text-subtitle2">Portfolio</div>
              <div>{{ profile.portfolio }}</div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Firm Details Section -->
      <q-card v-if="hasFirmDetails" class="q-mb-md section-card">
        <q-card-section class="section-header bg-primary text-white">
          <div class="text-h6">
            <unified-icon name="business" class="q-mr-sm" />
            Firm Details
          </div>
        </q-card-section>
        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-6" v-if="profile.firm_name">
              <div class="text-subtitle2">Firm Name</div>
              <div>{{ profile.firm_name }}</div>
            </div>
            <div class="col-12 col-md-6" v-if="profile.firm_type">
              <div class="text-subtitle2">Firm Type</div>
              <div>{{ profile.firm_type }}</div>
            </div>
            <div class="col-12 col-md-6" v-if="profile.firm_size">
              <div class="text-subtitle2">Firm Size</div>
              <div>{{ profile.firm_size }} employees</div>
            </div>
            <div class="col-12 col-md-6" v-if="profile.firm_website">
              <div class="text-subtitle2">Firm Website</div>
              <div>
                <a :href="profile.firm_website" target="_blank" class="text-primary">
                  {{ profile.firm_website }}
                </a>
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Investment Philosophy Section -->
      <q-card v-if="hasInvestmentPhilosophy" class="q-mb-md section-card">
        <q-card-section class="section-header bg-primary text-white">
          <div class="text-h6">
            <unified-icon name="psychology" class="q-mr-sm" />
            Investment Philosophy
          </div>
        </q-card-section>
        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12" v-if="profile.investment_philosophy">
              <div class="text-subtitle2">Investment Philosophy</div>
              <div>{{ profile.investment_philosophy }}</div>
            </div>
            <div class="col-12" v-if="profile.value_add">
              <div class="text-subtitle2">Value Addition</div>
              <goals-interests-display :data="profile.value_add" chip-color="teal" />
            </div>
            <div class="col-12 col-md-6" v-if="profile.investment_horizon">
              <div class="text-subtitle2">Investment Horizon</div>
              <div>{{ profile.investment_horizon }}</div>
            </div>
            <div class="col-12" v-if="profile.exit_strategy">
              <div class="text-subtitle2">Exit Strategy</div>
              <goals-interests-display :data="profile.exit_strategy" chip-color="indigo" />
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Investment Performance Section -->
      <q-card v-if="hasInvestmentPerformance" class="q-mb-md section-card">
        <q-card-section class="section-header bg-primary text-white">
          <div class="text-h6">
            <unified-icon name="trending_up" class="q-mr-sm" />
            Investment Performance
          </div>
        </q-card-section>
        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-6" v-if="profile.portfolio_size">
              <div class="text-subtitle2">Portfolio Size</div>
              <div>{{ profile.portfolio_size }}</div>
            </div>
            <div class="col-12 col-md-6" v-if="profile.average_return">
              <div class="text-subtitle2">Average Annual Return</div>
              <div>{{ profile.average_return }}</div>
            </div>
            <div class="col-12 col-md-6" v-if="profile.successful_exits">
              <div class="text-subtitle2">Successful Exits</div>
              <div>{{ profile.successful_exits }}</div>
            </div>
            <div class="col-12" v-if="profile.notable_investments">
              <div class="text-subtitle2">Notable Investments</div>
              <div>{{ profile.notable_investments }}</div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Due Diligence Process Section -->
      <q-card v-if="hasDueDiligenceInfo" class="q-mb-md section-card">
        <q-card-section class="section-header bg-primary text-white">
          <div class="text-h6">
            <unified-icon name="fact_check" class="q-mr-sm" />
            Due Diligence Process
          </div>
        </q-card-section>
        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12" v-if="profile.due_diligence_process">
              <div class="text-subtitle2">Due Diligence Process</div>
              <div>{{ profile.due_diligence_process }}</div>
            </div>
            <div class="col-12 col-md-6" v-if="profile.decision_timeline">
              <div class="text-subtitle2">Decision Timeline</div>
              <div>{{ profile.decision_timeline }}</div>
            </div>
            <div class="col-12" v-if="profile.key_metrics">
              <div class="text-subtitle2">Key Metrics</div>
              <goals-interests-display :data="profile.key_metrics" chip-color="cyan" />
            </div>
            <div class="col-12" v-if="profile.red_flags">
              <div class="text-subtitle2">Red Flags</div>
              <goals-interests-display :data="profile.red_flags" chip-color="red" />
            </div>
          </div>
        </q-card-section>
      </q-card>
    </template>

    <!-- Professional Profile Sections -->
    <template v-if="profile.profile_type === 'professional'">
      <!-- Professional Details Section -->
      <q-card v-if="hasProfessionalDetails" class="q-mb-md section-card">
        <q-card-section class="section-header bg-primary text-white">
          <div class="text-h6">
            <unified-icon name="work" class="q-mr-sm" />
            Professional Details
          </div>
        </q-card-section>
        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-6" v-if="profile.industry">
              <div class="text-subtitle2">Industry</div>
              <div>{{ profile.industry }}</div>
            </div>
            <div class="col-12 col-md-6" v-if="profile.job_title">
              <div class="text-subtitle2">Job Title</div>
              <div>{{ profile.job_title }}</div>
            </div>
            <div class="col-12 col-md-6" v-if="profile.company">
              <div class="text-subtitle2">Company</div>
              <div>{{ profile.company }}</div>
            </div>
            <div class="col-12 col-md-6" v-if="profile.years_of_experience">
              <div class="text-subtitle2">Years of Experience</div>
              <div>{{ profile.years_of_experience }}</div>
            </div>
            <div class="col-12" v-if="profile.expertise">
              <div class="text-subtitle2">Expertise</div>
              <goals-interests-display :data="profile.expertise" chip-color="blue" />
            </div>
          </div>
        </q-card-section>
      </q-card>
    </template>

    <!-- Industry Expert Profile Sections -->
    <template v-if="profile.profile_type === 'industry_expert'">
      <!-- Expert Details Section -->
      <q-card v-if="hasExpertDetails" class="q-mb-md section-card">
        <q-card-section class="section-header bg-primary text-white">
          <div class="text-h6">
            <unified-icon name="star" class="q-mr-sm" />
            Expert Details
          </div>
        </q-card-section>
        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-6" v-if="profile.industry">
              <div class="text-subtitle2">Industry</div>
              <div>{{ profile.industry }}</div>
            </div>
            <div class="col-12 col-md-6" v-if="profile.job_title">
              <div class="text-subtitle2">Job Title</div>
              <div>{{ profile.job_title }}</div>
            </div>
            <div class="col-12 col-md-6" v-if="profile.company">
              <div class="text-subtitle2">Company/Organization</div>
              <div>{{ profile.company }}</div>
            </div>
            <div class="col-12 col-md-6" v-if="profile.years_of_experience">
              <div class="text-subtitle2">Years of Experience</div>
              <div>{{ profile.years_of_experience }}</div>
            </div>
            <div class="col-12" v-if="profile.areas_of_expertise">
              <div class="text-subtitle2">Areas of Expertise</div>
              <goals-interests-display :data="profile.areas_of_expertise" chip-color="green" />
            </div>
          </div>
        </q-card-section>
      </q-card>
    </template>

    <!-- Academic Student Profile Sections -->
    <template v-if="profile.profile_type === 'academic_student'">
      <!-- Academic Details Section -->
      <q-card v-if="hasAcademicDetails" class="q-mb-md section-card">
        <q-card-section class="section-header bg-primary text-white">
          <div class="text-h6">
            <unified-icon name="school" class="q-mr-sm" />
            Academic Details
          </div>
        </q-card-section>
        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-6" v-if="profile.field_of_study">
              <div class="text-subtitle2">Field of Study</div>
              <div>{{ profile.field_of_study }}</div>
            </div>
            <div class="col-12 col-md-6" v-if="profile.institution">
              <div class="text-subtitle2">Institution</div>
              <div>{{ profile.institution }}</div>
            </div>
            <div class="col-12 col-md-6" v-if="profile.degree_level">
              <div class="text-subtitle2">Degree Level</div>
              <div>{{ profile.degree_level }}</div>
            </div>
            <div class="col-12 col-md-6" v-if="profile.graduation_year">
              <div class="text-subtitle2">Graduation Year</div>
              <div>{{ profile.graduation_year }}</div>
            </div>
            <div class="col-12" v-if="profile.languages">
              <div class="text-subtitle2">Languages</div>
              <goals-interests-display :data="profile.languages" chip-color="purple" />
            </div>
          </div>
        </q-card-section>
      </q-card>
    </template>

    <!-- Academic Institution Profile Sections -->
    <template v-if="profile.profile_type === 'academic_institution'">
      <!-- Institution Details Section -->
      <q-card v-if="hasInstitutionDetails" class="q-mb-md section-card">
        <q-card-section class="section-header bg-primary text-white">
          <div class="text-h6">
            <unified-icon name="school" class="q-mr-sm" />
            Institution Details
          </div>
        </q-card-section>
        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-6" v-if="profile.institution_name">
              <div class="text-subtitle2">Institution Name</div>
              <div>{{ profile.institution_name }}</div>
            </div>
            <div class="col-12 col-md-6" v-if="profile.institution_type">
              <div class="text-subtitle2">Institution Type</div>
              <div>{{ profile.institution_type }}</div>
            </div>
            <div class="col-12" v-if="profile.research_areas">
              <div class="text-subtitle2">Research Areas</div>
              <goals-interests-display :data="profile.research_areas" chip-color="teal" />
            </div>
            <div class="col-12" v-if="profile.academic_programs">
              <div class="text-subtitle2">Academic Programs</div>
              <goals-interests-display :data="profile.academic_programs" chip-color="indigo" />
            </div>
          </div>
        </q-card-section>
      </q-card>
    </template>

    <!-- Organisation Profile Sections -->
    <template v-if="profile.profile_type === 'organisation'">
      <!-- Organisation Details Section -->
      <q-card v-if="hasOrganisationDetails" class="q-mb-md section-card">
        <q-card-section class="section-header bg-primary text-white">
          <div class="text-h6">
            <unified-icon name="business" class="q-mr-sm" />
            Organisation Details
          </div>
        </q-card-section>
        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-6" v-if="profile.organisation_name">
              <div class="text-subtitle2">Organisation Name</div>
              <div>{{ profile.organisation_name }}</div>
            </div>
            <div class="col-12 col-md-6" v-if="profile.organisation_type">
              <div class="text-subtitle2">Organisation Type</div>
              <div>{{ profile.organisation_type }}</div>
            </div>
            <div class="col-12 col-md-6" v-if="profile.industry">
              <div class="text-subtitle2">Industry</div>
              <div>{{ profile.industry }}</div>
            </div>
            <div class="col-12 col-md-6" v-if="profile.organisation_size">
              <div class="text-subtitle2">Organisation Size</div>
              <div>{{ profile.organisation_size }}</div>
            </div>
            <div class="col-12" v-if="profile.services_offered">
              <div class="text-subtitle2">Services Offered</div>
              <goals-interests-display :data="profile.services_offered" chip-color="cyan" />
            </div>
          </div>
        </q-card-section>
      </q-card>
    </template>

    <!-- Goals & Interests Section -->
    <q-card v-if="hasGoalsAndInterests" class="q-mb-md section-card">
      <q-card-section class="section-header bg-primary text-white">
        <div class="text-h6">
          <unified-icon name="emoji_objects" class="q-mr-sm" />
          Goals & Interests
        </div>
      </q-card-section>
      <q-card-section>
        <!-- Short Term Goals -->
        <div v-if="profile.short_term_goals" class="q-mb-md">
          <div class="text-subtitle1 q-mb-sm">Short Term Goals</div>
          <goals-interests-display :data="profile.short_term_goals" />
        </div>

        <!-- Long Term Goals -->
        <div v-if="profile.long_term_goals" class="q-mb-md">
          <div class="text-subtitle1 q-mb-sm">Long Term Goals</div>
          <goals-interests-display :data="profile.long_term_goals" />
        </div>

        <!-- Goals (Array) -->
        <div v-if="profile.goals && Array.isArray(profile.goals) && profile.goals.length > 0" class="q-mb-md">
          <div class="text-subtitle1 q-mb-sm">Goals</div>
          <q-list dense>
            <q-item v-for="(goal, index) in profile.goals" :key="index">
              <q-item-section>
                <q-item-label>{{ goal }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </div>

        <!-- Interests -->
        <div v-if="profile.interests" class="q-mb-md">
          <div class="text-subtitle1 q-mb-sm">Interests</div>
          <goals-interests-display :data="profile.interests" chip-color="teal" />
        </div>

        <!-- Collaboration Interests -->
        <div v-if="profile.collaboration_interests" class="q-mb-md">
          <div class="text-subtitle1 q-mb-sm">Collaboration Interests</div>
          <goals-interests-display :data="profile.collaboration_interests" chip-color="purple" />
        </div>

        <!-- Additional Interests -->
        <div v-if="profile.additional_interests">
          <div class="text-subtitle1 q-mb-sm">Additional Interests</div>
          <div>{{ profile.additional_interests }}</div>
        </div>
      </q-card-section>
    </q-card>

    <!-- Contact Information Section -->
    <contact-information-section :profile="profile" />

    <!-- Social Media Section -->
    <social-media-section :profile="profile" />

    <!-- Location Section -->
    <location-section :profile="profile" />

    <!-- All Profile Data Section (for current user or debug mode) -->
    <q-card v-if="(isCurrentUser || showDebug) && context === 'dashboard'" class="q-mb-md section-card">
      <q-card-section class="section-header bg-primary text-white">
        <div class="text-h6">
          <unified-icon name="list_alt" class="q-mr-sm" />
          All Profile Data
        </div>
      </q-card-section>
      <q-card-section>
        <div class="row q-col-gutter-md">
          <template v-for="(value, key) in profile" :key="key">
            <div 
              class="col-12 col-md-6" 
              v-if="typeof value !== 'object' && key !== 'id' && key !== 'user_id' && !key.includes('_at') && value"
            >
              <div class="text-subtitle2">{{ formatFieldName(key) }}</div>
              <div>{{ value }}</div>
            </div>
          </template>
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import UnifiedIcon from '../ui/UnifiedIcon.vue'
import GoalsInterestsDisplay from './GoalsInterestsDisplay.vue'
import ContactInformationSection from './ContactInformationSection.vue'
import SocialMediaSection from './SocialMediaSection.vue'
import LocationSection from './LocationSection.vue'

// Props
const props = defineProps<{
  profile: any
  context: 'dashboard' | 'public'
  isCurrentUser?: boolean
  showDebug?: boolean
}>()

// Computed
const hasProfessionalInfo = computed(() => {
  return !!(
    props.profile.job_title ||
    props.profile.company ||
    props.profile.industry ||
    props.profile.years_of_experience ||
    (props.profile.skills && Array.isArray(props.profile.skills) && props.profile.skills.length > 0)
  )
})

// Innovator profile computed properties
const hasInnovationDetails = computed(() => {
  return !!(
    props.profile.innovation_area ||
    props.profile.innovation_stage ||
    props.profile.innovation_description ||
    props.profile.team_size !== undefined ||
    props.profile.team_description
  )
})

const hasFundingInfo = computed(() => {
  return !!(
    props.profile.funding_needs !== undefined ||
    props.profile.funding_amount ||
    props.profile.funding_stage ||
    props.profile.has_prototype !== undefined ||
    props.profile.prototype_description
  )
})

const hasMarketInfo = computed(() => {
  return !!(
    props.profile.target_market ||
    props.profile.market_size ||
    props.profile.competitors ||
    props.profile.competitive_advantage ||
    props.profile.go_to_market
  )
})

const hasTractionInfo = computed(() => {
  return !!(
    props.profile.current_traction ||
    props.profile.revenue_model ||
    props.profile.current_revenue ||
    props.profile.key_metrics ||
    props.profile.growth_rate
  )
})

const hasIPInfo = computed(() => {
  return !!(
    props.profile.has_ip !== undefined ||
    props.profile.ip_description ||
    props.profile.ip_status
  )
})

// Mentor profile computed properties
const hasMentorshipDetails = computed(() => {
  return !!(
    props.profile.mentorship_style ||
    props.profile.availability ||
    props.profile.previous_mentees ||
    props.profile.mentoring_experience
  )
})

const hasMentorProfessionalInfo = computed(() => {
  return !!(
    props.profile.mentor_current_role ||
    props.profile.company ||
    props.profile.education
  )
})

const hasSuccessStories = computed(() => {
  return !!(
    props.profile.success_stories ||
    props.profile.mentee_achievements ||
    props.profile.testimonials
  )
})

const hasMentorshipMethodology = computed(() => {
  return !!(
    props.profile.mentorship_philosophy ||
    props.profile.mentorship_methods ||
    props.profile.mentorship_tools ||
    props.profile.mentorship_duration
  )
})

const hasMenteePreferences = computed(() => {
  return !!(
    props.profile.preferred_mentee_stage ||
    props.profile.preferred_mentee_background ||
    props.profile.expectations
  )
})

// Investor profile computed properties
const hasInvestmentFocus = computed(() => {
  return !!(
    props.profile.investment_focus ||
    props.profile.investment_stage ||
    props.profile.ticket_size ||
    props.profile.previous_investments ||
    props.profile.investment_geography
  )
})

const hasInvestmentCriteria = computed(() => {
  return !!(
    props.profile.investment_criteria ||
    props.profile.success_stories ||
    props.profile.portfolio
  )
})

const hasFirmDetails = computed(() => {
  return !!(
    props.profile.firm_name ||
    props.profile.firm_type ||
    props.profile.firm_size ||
    props.profile.firm_website
  )
})

const hasInvestmentPhilosophy = computed(() => {
  return !!(
    props.profile.investment_philosophy ||
    props.profile.value_add ||
    props.profile.investment_horizon ||
    props.profile.exit_strategy
  )
})

const hasInvestmentPerformance = computed(() => {
  return !!(
    props.profile.portfolio_size ||
    props.profile.average_return ||
    props.profile.successful_exits ||
    props.profile.notable_investments
  )
})

const hasDueDiligenceInfo = computed(() => {
  return !!(
    props.profile.due_diligence_process ||
    props.profile.decision_timeline ||
    props.profile.key_metrics ||
    props.profile.red_flags
  )
})

// Professional profile computed properties
const hasProfessionalDetails = computed(() => {
  return !!(
    props.profile.industry ||
    props.profile.job_title ||
    props.profile.company ||
    props.profile.years_of_experience ||
    props.profile.expertise
  )
})

// Industry Expert profile computed properties
const hasExpertDetails = computed(() => {
  return !!(
    props.profile.industry ||
    props.profile.job_title ||
    props.profile.company ||
    props.profile.years_of_experience ||
    props.profile.areas_of_expertise
  )
})

// Academic Student profile computed properties
const hasAcademicDetails = computed(() => {
  return !!(
    props.profile.field_of_study ||
    props.profile.institution ||
    props.profile.degree_level ||
    props.profile.graduation_year ||
    props.profile.languages
  )
})

// Academic Institution profile computed properties
const hasInstitutionDetails = computed(() => {
  return !!(
    props.profile.institution_name ||
    props.profile.institution_type ||
    props.profile.research_areas ||
    props.profile.academic_programs
  )
})

// Organisation profile computed properties
const hasOrganisationDetails = computed(() => {
  return !!(
    props.profile.organisation_name ||
    props.profile.organisation_type ||
    props.profile.industry ||
    props.profile.organisation_size ||
    props.profile.services_offered
  )
})

const hasGoalsAndInterests = computed(() => {
  return !!(
    props.profile.short_term_goals ||
    props.profile.long_term_goals ||
    (props.profile.goals && Array.isArray(props.profile.goals) && props.profile.goals.length > 0) ||
    props.profile.interests ||
    props.profile.collaboration_interests ||
    props.profile.additional_interests
  )
})

// Methods
function formatFieldName(key: string): string {
  return key
    .replace(/_/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase())
}
</script>

<style scoped>
.section-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  border-radius: 8px 8px 0 0;
}
</style>
