/**
 * Comments Service
 * 
 * Centralized service for all comment-related operations.
 * Replaces direct Supabase calls in components with proper service layer.
 */

import { supabase } from '../lib/supabase'

export interface Comment {
  id: string
  post_id: string
  user_id: string
  content: string
  created_at: string
  updated_at: string
  // Extended fields from comments_with_authors view
  author_name?: string
  author_email?: string
  post_title?: string
  post_content?: string
}

export interface CommentCreateData {
  post_id: string
  user_id: string
  content: string
}

export interface CommentUpdateData {
  content: string
}

export interface CommentFilters {
  user_id?: string
  post_id?: string
  limit?: number
  offset?: number
}

/**
 * Comments Service Class
 */
export class CommentsService {
  private static instance: CommentsService

  static getInstance(): CommentsService {
    if (!CommentsService.instance) {
      CommentsService.instance = new CommentsService()
    }
    return CommentsService.instance
  }

  /**
   * Get comments with author information and post details
   */
  async getComments(filters: CommentFilters = {}): Promise<Comment[]> {
    try {
      let query = supabase
        .from('comments_with_authors')
        .select('*, posts:post_id(id, title, content)')
        .order('created_at', { ascending: false })

      // Apply filters
      if (filters.user_id) {
        query = query.eq('user_id', filters.user_id)
      }

      if (filters.post_id) {
        query = query.eq('post_id', filters.post_id)
      }

      if (filters.limit) {
        query = query.limit(filters.limit)
      }

      if (filters.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1)
      }

      const { data, error } = await query

      if (error) {
        console.error('CommentsService: Error fetching comments:', error)
        throw error
      }

      return data || []
    } catch (error) {
      console.error('CommentsService: Failed to get comments:', error)
      throw error
    }
  }

  /**
   * Get comments for a specific user
   */
  async getUserComments(userId: string, limit?: number): Promise<Comment[]> {
    return this.getComments({
      user_id: userId,
      limit
    })
  }

  /**
   * Get comments for a specific post
   */
  async getPostComments(postId: string, limit?: number): Promise<Comment[]> {
    return this.getComments({
      post_id: postId,
      limit
    })
  }

  /**
   * Create a new comment
   */
  async createComment(commentData: CommentCreateData): Promise<Comment> {
    try {
      const { data, error } = await supabase
        .from('comments')
        .insert([commentData])
        .select()
        .single()

      if (error) {
        console.error('CommentsService: Error creating comment:', error)
        throw error
      }

      return data
    } catch (error) {
      console.error('CommentsService: Failed to create comment:', error)
      throw error
    }
  }

  /**
   * Update an existing comment
   */
  async updateComment(commentId: string, updateData: CommentUpdateData): Promise<Comment> {
    try {
      const { data, error } = await supabase
        .from('comments')
        .update({
          ...updateData,
          updated_at: new Date().toISOString()
        })
        .eq('id', commentId)
        .select()
        .single()

      if (error) {
        console.error('CommentsService: Error updating comment:', error)
        throw error
      }

      return data
    } catch (error) {
      console.error('CommentsService: Failed to update comment:', error)
      throw error
    }
  }

  /**
   * Delete a comment
   */
  async deleteComment(commentId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('comments')
        .delete()
        .eq('id', commentId)

      if (error) {
        console.error('CommentsService: Error deleting comment:', error)
        throw error
      }
    } catch (error) {
      console.error('CommentsService: Failed to delete comment:', error)
      throw error
    }
  }

  /**
   * Get comment count for a post
   */
  async getCommentCount(postId: string): Promise<number> {
    try {
      const { count, error } = await supabase
        .from('comments')
        .select('*', { count: 'exact', head: true })
        .eq('post_id', postId)

      if (error) {
        console.error('CommentsService: Error getting comment count:', error)
        throw error
      }

      return count || 0
    } catch (error) {
      console.error('CommentsService: Failed to get comment count:', error)
      throw error
    }
  }

  /**
   * Check if user can edit/delete comment
   */
  canModifyComment(comment: Comment, userId: string): boolean {
    return comment.user_id === userId
  }
}

// Export singleton instance
export const commentsService = CommentsService.getInstance()

// Export composable for Vue components
export function useCommentsService() {
  return commentsService
}
