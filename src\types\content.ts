export interface Article {
  id: number
  title: string
  slug: string
  content: string
  excerpt: string
  coverImage: string
  publishedAt: string
  author: {
    name: string
    avatar: string
  }
  category: string
  tags: string[]
}

export interface Event {
  id: number
  title: string
  description: string
  date: string
  location: string
  type: string
  status: string
  capacity: number
  registeredCount: number
  coverImage: string
}

export interface ContentState {
  articles: Article[]
  events: Event[]
  loading: boolean
  error: string | null
} 