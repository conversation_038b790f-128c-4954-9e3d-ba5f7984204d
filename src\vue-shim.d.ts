// Type definitions for Vue
// This is a minimal type declaration file to fix the TypeScript errors

declare module 'vue' {
  export type Ref<T> = { value: T };
  export function ref<T>(value: T): Ref<T>;
  export function computed<T>(getter: () => T): Ref<T>;
  export function readonly<T>(ref: Ref<T>): Ref<readonly T>;
  export function onMounted(callback: () => void): void;
  export function nextTick(): Promise<void>;
  export type App = any;
  export function createApp(rootComponent: any, rootProps?: any): App;
  export type Component = any;
  export type ComponentPublicInstance = any;
  export type Plugin = any;
}
