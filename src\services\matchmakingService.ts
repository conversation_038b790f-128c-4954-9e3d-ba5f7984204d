/**
 * Matchmaking Service
 * 
 * This service handles the core matchmaking functionality for the platform.
 * It implements algorithms for matching different profile types based on
 * their attributes and preferences.
 */

import { supabase } from '@/lib/supabase';
import { ref, computed } from 'vue';

// Define types for the matchmaking service
export type ProfileType = 
  | 'innovator'
  | 'investor'
  | 'mentor'
  | 'organisation'
  | 'professional'
  | 'academic_institution'
  | 'industry_expert'
  | 'academic_student';

export type EntityType = 
  | ProfileType
  | 'event'
  | 'opportunity'
  | 'resource'
  | 'post';

export interface MatchResult {
  id: string;
  userId: string;
  matchedEntityId: string;
  entityType: EntityType;
  matchScore: number;
  matchReasons: Record<string, number>;
  isViewed: boolean;
  isSaved: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface MatchmakingRule {
  id: string;
  sourceProfileType: ProfileType;
  targetProfileType: ProfileType;
  ruleName: string;
  sourceFields: Record<string, any>;
  targetFields: Record<string, any>;
  weight: number;
}

export interface EnrichedMatch extends MatchResult {
  profile: any;
  user: {
    firstName?: string;
    lastName?: string;
    avatarUrl?: string;
  } | null;
}

/**
 * Calculate the intersection score between two arrays
 */
export function arrayIntersectionScore(array1: any[] | null, array2: any[] | null): number {
  if (!array1 || !array2 || array1.length === 0 || array2.length === 0) {
    return 0;
  }
  
  const intersection = array1.filter(item => array2.includes(item));
  return intersection.length / Math.sqrt(array1.length * array2.length);
}

/**
 * Calculate the similarity score between two text values
 */
export function textSimilarityScore(text1: string | null, text2: string | null): number {
  if (!text1 || !text2) {
    return 0;
  }
  
  // Simple implementation - can be enhanced with more sophisticated text similarity algorithms
  const normalizedText1 = text1.toLowerCase();
  const normalizedText2 = text2.toLowerCase();
  
  // Check for exact match
  if (normalizedText1 === normalizedText2) {
    return 1;
  }
  
  // Check if one contains the other
  if (normalizedText1.includes(normalizedText2) || normalizedText2.includes(normalizedText1)) {
    return 0.7;
  }
  
  // Split into words and check for word overlap
  const words1 = normalizedText1.split(/\s+/);
  const words2 = normalizedText2.split(/\s+/);
  
  return arrayIntersectionScore(words1, words2);
}

/**
 * Calculate the match score for a numeric value within a range
 */
export function numericRangeMatch(value: number | null, range: { min?: number, max?: number }): number {
  if (value === null) {
    return 0;
  }
  
  // If no range is specified, return 0.5 as a neutral score
  if (range.min === undefined && range.max === undefined) {
    return 0.5;
  }
  
  // If only min is specified
  if (range.min !== undefined && range.max === undefined) {
    return value >= range.min ? 1 : 0;
  }
  
  // If only max is specified
  if (range.min === undefined && range.max !== undefined) {
    return value <= range.max ? 1 : 0;
  }
  
  // If both min and max are specified
  if (range.min !== undefined && range.max !== undefined) {
    if (value < range.min || value > range.max) {
      return 0;
    }
    
    // Calculate how centered the value is in the range
    const rangeSize = range.max - range.min;
    const distanceFromMin = value - range.min;
    const normalizedPosition = distanceFromMin / rangeSize;
    
    // Score is highest (1.0) when in the middle of the range
    return 1 - Math.abs(normalizedPosition - 0.5) * 2;
  }
  
  return 0;
}

/**
 * Matchmaking service composable
 */
export function useMatchmakingService() {
  const loading = ref(false);
  const error = ref<string | null>(null);
  
  /**
   * Initialize matchmaking rules in the database
   */
  async function initializeMatchmakingRules(): Promise<boolean> {
    loading.value = true;
    error.value = null;
    
    try {
      // Check if rules already exist
      const { data: existingRules, error: checkError } = await supabase
        .from('matchmaking_rules')
        .select('id')
        .limit(1);
        
      if (checkError) {
        throw new Error(`Error checking existing rules: ${checkError.message}`);
      }
      
      // If rules already exist, don't recreate them
      if (existingRules && existingRules.length > 0) {
        console.log('Matchmaking rules already exist. Skipping initialization.');
        return true;
      }
      
      // Define the default rules
      const defaultRules = [
        // Innovator to Investor rules
        {
          source_profile_type: 'innovator',
          target_profile_type: 'investor',
          rule_name: 'industry_alignment',
          source_fields: { path: 'industry' },
          target_fields: { path: 'investment_focus' },
          weight: 0.3
        },
        {
          source_profile_type: 'innovator',
          target_profile_type: 'investor',
          rule_name: 'stage_compatibility',
          source_fields: { path: 'innovation_stage' },
          target_fields: { path: 'investment_stage' },
          weight: 0.3
        },
        {
          source_profile_type: 'innovator',
          target_profile_type: 'investor',
          rule_name: 'funding_match',
          source_fields: { path: 'funding_amount' },
          target_fields: { min_path: 'investment_range.min', max_path: 'investment_range.max' },
          weight: 0.2
        },
        {
          source_profile_type: 'innovator',
          target_profile_type: 'investor',
          rule_name: 'location_match',
          source_fields: { path: 'preferred_locations' },
          target_fields: { path: 'preferred_locations' },
          weight: 0.1
        },
        {
          source_profile_type: 'innovator',
          target_profile_type: 'investor',
          rule_name: 'goals_alignment',
          source_fields: { path: 'short_term_goals' },
          target_fields: { path: 'collaboration_interests' },
          weight: 0.1
        },
        
        // Innovator to Mentor rules
        {
          source_profile_type: 'innovator',
          target_profile_type: 'mentor',
          rule_name: 'expertise_match',
          source_fields: { path: 'current_challenges' },
          target_fields: { path: 'expertise_areas' },
          weight: 0.35
        },
        {
          source_profile_type: 'innovator',
          target_profile_type: 'mentor',
          rule_name: 'industry_match',
          source_fields: { path: 'industry' },
          target_fields: { path: 'industry_experience' },
          weight: 0.2
        },
        {
          source_profile_type: 'innovator',
          target_profile_type: 'mentor',
          rule_name: 'stage_match',
          source_fields: { path: 'innovation_stage' },
          target_fields: { path: 'preferred_mentee_stage' },
          weight: 0.15
        },
        {
          source_profile_type: 'innovator',
          target_profile_type: 'mentor',
          rule_name: 'goals_match',
          source_fields: { path: 'short_term_goals' },
          target_fields: { path: 'collaboration_interests' },
          weight: 0.15
        },
        {
          source_profile_type: 'innovator',
          target_profile_type: 'mentor',
          rule_name: 'approach_match',
          source_fields: { path: 'looking_for' },
          target_fields: { path: 'mentoring_approach' },
          weight: 0.15
        }
        
        // Additional rules for other profile types can be added here
      ];
      
      // Insert the rules into the database
      const { error: insertError } = await supabase
        .from('matchmaking_rules')
        .insert(defaultRules);
        
      if (insertError) {
        throw new Error(`Error inserting matchmaking rules: ${insertError.message}`);
      }
      
      console.log('Matchmaking rules initialized successfully.');
      return true;
    } catch (err: any) {
      error.value = err.message;
      console.error('Error initializing matchmaking rules:', err);
      return false;
    } finally {
      loading.value = false;
    }
  }
  
  /**
   * Generate matches for a user based on their profile type
   */
  async function generateMatches(profileType: ProfileType): Promise<MatchResult[]> {
    loading.value = true;
    error.value = null;
    
    try {
      // Call the database function to generate matches
      const { data, error: fnError } = await supabase
        .rpc('generate_user_matches', {
          profile_type: profileType
        });
        
      if (fnError) {
        throw new Error(`Error generating matches: ${fnError.message}`);
      }
      
      return data || [];
    } catch (err: any) {
      error.value = err.message;
      console.error('Error generating matches:', err);
      return [];
    } finally {
      loading.value = false;
    }
  }
  
  /**
   * Get matches for the current user
   */
  async function getMatches(
    entityType?: EntityType,
    limit: number = 10,
    offset: number = 0,
    minScore: number = 0
  ): Promise<EnrichedMatch[]> {
    loading.value = true;
    error.value = null;
    
    try {
      // Build the query
      let query = supabase
        .from('matchmaking_results')
        .select('*')
        .gte('match_score', minScore)
        .order('match_score', { ascending: false })
        .limit(limit)
        .range(offset, offset + limit - 1);
        
      // Add entity type filter if provided
      if (entityType) {
        query = query.eq('entity_type', entityType);
      }
      
      // Execute the query
      const { data: matches, error: queryError } = await query;
      
      if (queryError) {
        throw new Error(`Error fetching matches: ${queryError.message}`);
      }
      
      if (!matches || matches.length === 0) {
        return [];
      }
      
      // Enrich matches with profile data
      const enrichedMatches = await Promise.all(
        matches.map(async match => {
          // Get profile data based on entity type
          const { data: profileData, error: profileError } = await supabase
            .from(`${match.entity_type}_profiles`)
            .select('*')
            .eq('user_id', match.matched_entity_id)
            .single();
            
          if (profileError) {
            console.error(`Error fetching ${match.entity_type} profile:`, profileError);
            return {
              ...mapMatchFromDatabase(match),
              profile: null,
              user: null
            };
          }
          
          // Get basic user data
          const { data: userData, error: userError } = await supabase
            .from('profiles')
            .select('first_name, last_name, avatar_url')
            .eq('user_id', match.matched_entity_id)
            .single();
            
          if (userError) {
            console.error('Error fetching user data:', userError);
            return {
              ...mapMatchFromDatabase(match),
              profile: profileData,
              user: null
            };
          }
          
          // Mark match as viewed
          if (!match.is_viewed) {
            await supabase
              .from('matchmaking_results')
              .update({ is_viewed: true })
              .eq('id', match.id);
          }
          
          return {
            ...mapMatchFromDatabase(match),
            profile: profileData,
            user: {
              firstName: userData.first_name,
              lastName: userData.last_name,
              avatarUrl: userData.avatar_url
            }
          };
        })
      );
      
      return enrichedMatches;
    } catch (err: any) {
      error.value = err.message;
      console.error('Error retrieving matches:', err);
      return [];
    } finally {
      loading.value = false;
    }
  }
  
  /**
   * Update a match (save or dismiss)
   */
  async function updateMatch(matchId: string, isSaved: boolean): Promise<boolean> {
    loading.value = true;
    error.value = null;
    
    try {
      // Update the match
      const { error: updateError } = await supabase
        .from('matchmaking_results')
        .update({ is_saved: isSaved })
        .eq('id', matchId);
        
      if (updateError) {
        throw new Error(`Error updating match: ${updateError.message}`);
      }
      
      // Track the interaction for future matching refinement
      await supabase
        .from('component_interactions')
        .insert({
          component_key: 'match_card',
          entity_id: matchId,
          interaction_type: isSaved ? 'save_match' : 'dismiss_match',
          interaction_data: { timestamp: new Date().toISOString() }
        });
      
      return true;
    } catch (err: any) {
      error.value = err.message;
      console.error('Error updating match:', err);
      return false;
    } finally {
      loading.value = false;
    }
  }
  
  /**
   * Map a match from the database to the MatchResult type
   */
  function mapMatchFromDatabase(match: any): MatchResult {
    return {
      id: match.id,
      userId: match.user_id,
      matchedEntityId: match.matched_entity_id,
      entityType: match.entity_type as EntityType,
      matchScore: match.match_score,
      matchReasons: match.match_reasons || {},
      isViewed: match.is_viewed,
      isSaved: match.is_saved,
      createdAt: match.created_at,
      updatedAt: match.updated_at
    };
  }
  
  return {
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    initializeMatchmakingRules,
    generateMatches,
    getMatches,
    updateMatch
  };
}
