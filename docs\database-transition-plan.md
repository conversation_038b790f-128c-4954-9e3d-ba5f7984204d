# Database Transition Plan: Mock Data to Supabase

## Overview

This document outlines the transition plan for migrating from the current mock data setup to the actual Supabase database. It provides a step-by-step approach to ensure a smooth transition with minimal disruption to the application.

## Current State

The application currently uses a hybrid approach:
- TypeScript interfaces that partially match the Supabase schema
- Mock data for development and testing
- Commented-out Supabase integration code
- Fallback mechanisms when Supabase operations fail

## Target State

The target state is a fully integrated application with:
- TypeScript interfaces that exactly match the Supabase schema
- Direct integration with Supabase for all data operations
- Proper error handling and loading states
- Efficient data fetching using views (posts_with_authors, comments_with_authors, likes_with_authors)
- Type-safe operations throughout the application

## Transition Phases

### Phase 1: Schema Alignment (Week 1)

1. **Database Schema Verification**
   - Verify that the Supabase database schema matches the planned schema in `content-database-schema.md`
   - Create any missing tables, views, or indexes
   - Update existing tables if needed

2. **TypeScript Interface Updates**
   - Update TypeScript interfaces to match the Supabase schema
   - Create mapping functions between database and frontend models
   - Implement proper type guards and validation

3. **Store Implementation Updates**
   - Update store implementations to use the new interfaces
   - Implement proper Supabase queries for CRUD operations
   - Add error handling and loading states

### Phase 2: Component Updates (Week 2)

1. **Post Display Components**
   - Update post display components to use the new data structure
   - Ensure proper handling of different post types
   - Implement proper error and loading states

2. **Post Creation Forms**
   - Update post creation forms to match the database schema
   - Implement validation for required fields
   - Add support for specialized fields based on post type

3. **Interaction Components**
   - Update like and comment functionality
   - Implement proper error handling
   - Add optimistic updates for better user experience

### Phase 3: Testing and Validation (Week 3)

1. **Unit Testing**
   - Test store functions in isolation
   - Verify proper mapping between database and frontend models
   - Test error handling and edge cases

2. **Integration Testing**
   - Test component interactions
   - Verify data flow between components and stores
   - Test authentication integration

3. **End-to-End Testing**
   - Test complete user flows
   - Verify post creation, liking, and commenting
   - Test filtering and search functionality

### Phase 4: Deployment and Monitoring (Week 4)

1. **Staging Deployment**
   - Deploy to staging environment
   - Test with production-like data
   - Verify performance and security

2. **Production Deployment**
   - Deploy to production environment
   - Monitor for errors and performance issues
   - Gather user feedback

3. **Post-Deployment Optimization**
   - Optimize database queries
   - Implement caching if needed
   - Address any issues identified during monitoring

## Implementation Details

### Database Schema Updates

If the current Supabase schema doesn't match the planned schema, create migration files to update it:

```sql
-- Example migration to add missing fields
ALTER TABLE posts
ADD COLUMN IF NOT EXISTS sub_type VARCHAR(50),
ADD COLUMN IF NOT EXISTS featured_image TEXT;

-- Create or update views
CREATE OR REPLACE VIEW posts_with_authors AS
SELECT p.*, pd.first_name, pd.last_name, pd.email
FROM posts p
LEFT JOIN personal_details pd ON p.user_id = pd.user_id;
```

### TypeScript Interface Updates

Update TypeScript interfaces to match the Supabase schema:

```typescript
// Base database types that match the Supabase schema
export interface PostBase {
  id: number;
  user_id?: string;
  created_by_admin?: boolean;
  post_type: string;
  sub_type: string;
  content?: string;
  media_urls?: any;
  tags?: string[];
  created_at?: string;
  updated_at?: string;
  status: string;
  // ... other fields
}

// Frontend model with camelCase properties
export interface Post {
  id: number;
  userId?: string;
  createdByAdmin?: boolean;
  postType: string;
  subType: string;
  content?: string;
  // ... other fields
}

// Mapping function
export function mapPostFromDatabase(post: PostBase): Post {
  return {
    id: post.id,
    userId: post.user_id,
    // ... map other fields
  };
}
```

### Store Implementation Updates

Update store implementations to use Supabase:

```typescript
async function fetchPosts(filter?: PostFilter) {
  loading.value = true;
  error.value = null;

  try {
    // Use the posts_with_authors view
    let query = supabase
      .from('posts_with_authors')
      .select('*')
      .order('created_at', { ascending: false });
    
    // Apply filters
    if (filter?.searchQuery) {
      query = query.or(`content.ilike.%${filter.searchQuery}%,title.ilike.%${filter.searchQuery}%`);
    }
    
    // Execute the query
    const { data, error: err } = await query;

    if (err) throw err;

    // Map to frontend model
    posts.value = data.map(post => mapPostFromDatabase(post));
    
    return filteredPosts.value;
  } catch (err: any) {
    console.error('Error fetching posts:', err);
    error.value = err.message || 'Failed to fetch posts';
    return [];
  } finally {
    loading.value = false;
  }
}
```

## Fallback Strategy

During the transition, maintain fallback mechanisms for development:

```typescript
try {
  // Supabase operation
} catch (err) {
  console.error('Error:', err);
  
  // For development only
  if (process.env.NODE_ENV === 'development') {
    // Fallback behavior
    console.log('Using fallback for development');
  } else {
    // In production, propagate the error
    throw err;
  }
}
```

## Success Criteria

The transition will be considered successful when:

1. All data operations use the Supabase database
2. TypeScript interfaces match the database schema
3. Components properly handle the data structure
4. Error handling is implemented throughout the application
5. Performance meets or exceeds the requirements
6. User experience is smooth and intuitive

## Rollback Plan

If issues arise during the transition:

1. Identify the specific component or functionality causing issues
2. Revert that component to the previous implementation
3. Fix the issues in a development environment
4. Re-deploy the fixed component

## Next Steps

1. Verify the current Supabase schema
2. Update TypeScript interfaces
3. Update store implementations
4. Update components
5. Test thoroughly
6. Deploy to production

## References

- [Content Database Schema](./content-database-schema.md)
- [Content Management Plan](./content-management-plan.md)
- [Feed Component Structure](./feed-component-structure.md)
- [Supabase Documentation](https://supabase.io/docs)
