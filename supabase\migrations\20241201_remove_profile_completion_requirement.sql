-- Migration: Remove profile completion requirement for public visibility
-- Description: Updates the public_profiles_view to show all profiles regardless of completion percentage

-- Drop the existing view
DROP VIEW IF EXISTS public.public_profiles_view;

-- Recreate the view without the completion requirement
CREATE OR REPLACE VIEW public.public_profiles_view AS
SELECT
  pd.user_id,
  pd.first_name,
  pd.last_name,
  pd.email,
  pd.profile_name,
  pd.profile_type,
  pd.profile_state,
  pd.profile_visibility,
  pd.profile_completion,
  pd.bio AS base_bio,
  pd.avatar_url,
  pd.created_at,
  pd.updated_at,
  -- Innovator profile fields
  ip.innovation_area,
  ip.innovation_stage,
  ip.innovation_description,
  ip.team_size,
  ip.team_description,
  ip.funding_needs,
  ip.funding_amount,
  ip.funding_stage,
  ip.has_prototype,
  ip.prototype_description,
  ip.target_market,
  ip.market_size,
  ip.competitors,
  ip.competitive_advantage,
  ip.go_to_market,
  ip.current_traction,
  ip.revenue_model,
  ip.current_revenue,
  ip.key_metrics AS innovator_key_metrics,
  ip.growth_rate,
  ip.has_ip,
  ip.ip_description,
  ip.ip_status,
  ip.current_challenges,
  -- <PERSON><PERSON> profile fields
  mp.mentorship_style,
  mp.availability,
  mp.previous_mentees,
  mp.mentoring_experience,
  mp.mentor_current_role,
  mp.company AS mentor_company,
  mp.education AS mentor_education,
  mp.success_stories AS mentor_success_stories,
  mp.mentee_achievements,
  mp.testimonials,
  mp.mentorship_philosophy,
  mp.mentorship_methods,
  mp.mentorship_tools,
  mp.mentorship_duration,
  mp.preferred_mentee_stage,
  mp.preferred_mentee_background,
  mp.expectations AS mentor_expectations,
  -- Investor profile fields
  invp.investment_focus,
  invp.investment_stage,
  invp.ticket_size,
  invp.previous_investments,
  invp.investment_geography,
  invp.investment_criteria,
  invp.success_stories AS investor_success_stories,
  invp.portfolio,
  invp.firm_name,
  invp.firm_type,
  invp.firm_size,
  invp.firm_website,
  invp.investment_philosophy,
  invp.value_add,
  invp.investment_horizon,
  invp.exit_strategy,
  invp.portfolio_size,
  invp.average_return,
  invp.successful_exits,
  invp.notable_investments,
  invp.due_diligence_process,
  invp.decision_timeline,
  invp.key_metrics AS investor_key_metrics,
  invp.red_flags,
  -- Professional profile fields
  pp.industry AS professional_industry,
  pp.job_title AS professional_job_title,
  pp.company AS professional_company,
  pp.years_of_experience AS professional_years_of_experience,
  pp.expertise AS professional_expertise,
  -- Industry Expert profile fields
  iep.industry AS expert_industry,
  iep.job_title AS expert_job_title,
  iep.company AS expert_company,
  iep.years_of_experience AS expert_years_of_experience,
  iep.areas_of_expertise,
  -- Academic Student profile fields
  asp.field_of_study,
  asp.institution AS student_institution,
  asp.degree_level,
  asp.graduation_year,
  asp.languages AS student_languages,
  -- Academic Institution profile fields
  aip.institution_name,
  aip.institution_type,
  aip.research_areas,
  aip.academic_programs,
  -- Organisation profile fields
  op.organisation_name,
  op.organisation_type,
  op.industry AS organisation_industry,
  op.organisation_size,
  op.services_offered
FROM personal_details pd
LEFT JOIN innovator_profiles ip ON pd.user_id = ip.user_id AND pd.profile_type = 'innovator'
LEFT JOIN mentor_profiles mp ON pd.user_id = mp.user_id AND pd.profile_type = 'mentor'
LEFT JOIN investor_profiles invp ON pd.user_id = invp.user_id AND pd.profile_type = 'investor'
LEFT JOIN professional_profiles pp ON pd.user_id = pp.user_id AND pd.profile_type = 'professional'
LEFT JOIN industry_expert_profiles iep ON pd.user_id = iep.user_id AND pd.profile_type = 'industry_expert'
LEFT JOIN academic_student_profiles asp ON pd.user_id = asp.user_id AND pd.profile_type = 'academic_student'
LEFT JOIN academic_institution_profiles aip ON pd.user_id = aip.user_id AND pd.profile_type = 'academic_institution'
LEFT JOIN organisation_profiles op ON pd.user_id = op.user_id AND pd.profile_type = 'organisation'
WHERE
  -- All profiles are now publicly visible - no completion requirement
  pd.profile_visibility = 'public' OR pd.user_id = auth.uid();

-- Grant permissions on the view
GRANT SELECT ON public.public_profiles_view TO authenticated, anon, service_role;
