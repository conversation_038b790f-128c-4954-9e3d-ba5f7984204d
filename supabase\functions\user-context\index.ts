import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';

interface UserContextRequest {
  user_id: string;
  include_profile?: boolean;
  include_activity?: boolean;
  include_connections?: boolean;
  include_posts?: boolean;
  include_stats?: boolean;
}

interface UserContextResponse {
  user_id: string;
  profile?: any;
  activity?: any[];
  connections?: any[];
  posts?: any[];
  stats?: {
    profile_completion: number;
    posts_count: number;
    connections_count: number;
    activity_count: number;
    likes_received: number;
    comments_received: number;
    last_active: string;
    join_date: string;
  };
  insights?: {
    profile_strength: 'low' | 'medium' | 'high';
    engagement_level: 'low' | 'medium' | 'high';
    network_size: 'small' | 'medium' | 'large';
    content_activity: 'inactive' | 'moderate' | 'active';
    recommendations: string[];
  };
}

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  console.log('User Context function called');

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const requestBody = await req.json();
    console.log('User Context request:', requestBody);

    const { 
      user_id, 
      include_profile = true, 
      include_activity = false, 
      include_connections = false,
      include_posts = false,
      include_stats = true
    }: UserContextRequest = requestBody;

    if (!user_id) {
      return new Response(
        JSON.stringify({ error: 'User ID is required' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    const result: UserContextResponse = { user_id };

    // Fetch profile data
    if (include_profile) {
      console.log('Fetching profile data for user:', user_id);
      const { data: profile, error: profileError } = await supabase
        .from('personal_details')
        .select('*')
        .eq('user_id', user_id)
        .single();

      if (!profileError && profile) {
        result.profile = profile;
        console.log('Profile data fetched successfully');
      } else {
        console.warn('Profile fetch error:', profileError);
      }
    }

    // Fetch activity data
    if (include_activity) {
      console.log('Fetching activity data for user:', user_id);
      const { data: activity, error: activityError } = await supabase
        .from('user_activities')
        .select('*')
        .eq('user_id', user_id)
        .order('created_at', { ascending: false })
        .limit(20);

      if (!activityError && activity) {
        result.activity = activity;
        console.log('Activity data fetched:', activity.length, 'records');
      } else {
        console.warn('Activity fetch error:', activityError);
      }
    }

    // Fetch connections data
    if (include_connections) {
      console.log('Fetching connections data for user:', user_id);
      const { data: connections, error: connectionsError } = await supabase
        .from('connections')
        .select(`
          *,
          connected_user:personal_details!connections_connected_user_id_fkey(
            user_id,
            first_name,
            last_name,
            profile_type,
            avatar_url
          ),
          requester_user:personal_details!connections_user_id_fkey(
            user_id,
            first_name,
            last_name,
            profile_type,
            avatar_url
          )
        `)
        .or(`user_id.eq.${user_id},connected_user_id.eq.${user_id}`)
        .eq('status', 'accepted')
        .limit(50);

      if (!connectionsError && connections) {
        result.connections = connections;
        console.log('Connections data fetched:', connections.length, 'records');
      } else {
        console.warn('Connections fetch error:', connectionsError);
      }
    }

    // Fetch posts data
    if (include_posts) {
      console.log('Fetching posts data for user:', user_id);
      const { data: posts, error: postsError } = await supabase
        .from('posts')
        .select('*')
        .eq('user_id', user_id)
        .order('created_at', { ascending: false })
        .limit(10);

      if (!postsError && posts) {
        result.posts = posts;
        console.log('Posts data fetched:', posts.length, 'records');
      } else {
        console.warn('Posts fetch error:', postsError);
      }
    }

    // Calculate comprehensive stats
    if (include_stats) {
      console.log('Calculating user stats for:', user_id);
      const stats = await calculateUserStats(supabase, user_id);
      result.stats = stats;
      
      // Generate insights based on stats and profile
      const insights = generateUserInsights(result.profile, stats);
      result.insights = insights;
      
      console.log('Stats and insights calculated');
    }

    console.log('User context response prepared:', {
      user_id: result.user_id,
      has_profile: !!result.profile,
      activity_count: result.activity?.length || 0,
      connections_count: result.connections?.length || 0,
      posts_count: result.posts?.length || 0,
      has_stats: !!result.stats,
      has_insights: !!result.insights
    });

    return new Response(
      JSON.stringify(result),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error: any) {
    console.error('User Context error:', error);

    return new Response(
      JSON.stringify({
        error: 'Failed to fetch user context',
        details: error.message,
        timestamp: new Date().toISOString()
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});

async function calculateUserStats(supabase: any, userId: string) {
  const stats = {
    profile_completion: 0,
    posts_count: 0,
    connections_count: 0,
    activity_count: 0,
    likes_received: 0,
    comments_received: 0,
    last_active: '',
    join_date: ''
  };

  try {
    // Profile completion and join date
    const { data: profile } = await supabase
      .from('personal_details')
      .select('profile_completion, created_at, updated_at')
      .eq('user_id', userId)
      .single();

    if (profile) {
      stats.profile_completion = profile.profile_completion || 0;
      stats.join_date = profile.created_at;
      stats.last_active = profile.updated_at;
    }

    // Posts count
    const { count: postsCount } = await supabase
      .from('posts')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    stats.posts_count = postsCount || 0;

    // Connections count
    const { count: connectionsCount } = await supabase
      .from('connections')
      .select('*', { count: 'exact', head: true })
      .or(`user_id.eq.${userId},connected_user_id.eq.${userId}`)
      .eq('status', 'accepted');

    stats.connections_count = connectionsCount || 0;

    // Activity count
    const { count: activityCount } = await supabase
      .from('user_activities')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    stats.activity_count = activityCount || 0;

    // Likes received on posts
    const { data: likesData } = await supabase
      .from('post_likes')
      .select('post_id')
      .in('post_id', 
        supabase
          .from('posts')
          .select('id')
          .eq('user_id', userId)
      );

    stats.likes_received = likesData?.length || 0;

    // Comments received on posts
    const { data: commentsData } = await supabase
      .from('post_comments')
      .select('post_id')
      .in('post_id', 
        supabase
          .from('posts')
          .select('id')
          .eq('user_id', userId)
      );

    stats.comments_received = commentsData?.length || 0;

  } catch (error) {
    console.error('Error calculating user stats:', error);
  }

  return stats;
}

function generateUserInsights(profile: any, stats: any) {
  const insights = {
    profile_strength: 'low' as 'low' | 'medium' | 'high',
    engagement_level: 'low' as 'low' | 'medium' | 'high',
    network_size: 'small' as 'small' | 'medium' | 'large',
    content_activity: 'inactive' as 'inactive' | 'moderate' | 'active',
    recommendations: [] as string[]
  };

  // Profile strength assessment
  if (stats.profile_completion >= 80) {
    insights.profile_strength = 'high';
  } else if (stats.profile_completion >= 50) {
    insights.profile_strength = 'medium';
  } else {
    insights.profile_strength = 'low';
    insights.recommendations.push('Complete your profile to improve visibility');
  }

  // Engagement level assessment
  const totalEngagement = stats.likes_received + stats.comments_received + stats.activity_count;
  if (totalEngagement >= 50) {
    insights.engagement_level = 'high';
  } else if (totalEngagement >= 10) {
    insights.engagement_level = 'medium';
  } else {
    insights.engagement_level = 'low';
    insights.recommendations.push('Engage more with the community to build connections');
  }

  // Network size assessment
  if (stats.connections_count >= 50) {
    insights.network_size = 'large';
  } else if (stats.connections_count >= 10) {
    insights.network_size = 'medium';
  } else {
    insights.network_size = 'small';
    insights.recommendations.push('Connect with more innovators in your field');
  }

  // Content activity assessment
  if (stats.posts_count >= 10) {
    insights.content_activity = 'active';
  } else if (stats.posts_count >= 3) {
    insights.content_activity = 'moderate';
  } else {
    insights.content_activity = 'inactive';
    insights.recommendations.push('Share your innovations and insights with the community');
  }

  // Profile type specific recommendations
  if (profile?.profile_type) {
    switch (profile.profile_type) {
      case 'innovator':
        if (stats.posts_count < 5) {
          insights.recommendations.push('Share your innovation projects to attract investors');
        }
        break;
      case 'investor':
        if (stats.connections_count < 20) {
          insights.recommendations.push('Connect with more innovators to discover opportunities');
        }
        break;
      case 'mentor':
        if (stats.activity_count < 10) {
          insights.recommendations.push('Engage with innovators to offer mentorship');
        }
        break;
    }
  }

  return insights;
}
