<template>
  <div class="profile-summary">
    <q-card class="q-mb-md">
      <q-card-section>
        <div class="row items-center">
          <div class="col-auto q-mr-md">
            <q-avatar size="60px" class="bg-primary text-white">
              <img v-if="profile.avatar_url" :src="profile.avatar_url" alt="Profile Avatar">
              <div v-else class="text-white flex flex-center full-height">
                {{ getInitials(profile.first_name, profile.last_name) }}
              </div>
            </q-avatar>
          </div>
          
          <div class="col">
            <div class="text-h6">{{ profile.first_name }} {{ profile.last_name }}</div>
            <div class="text-subtitle2">{{ formatProfileType(profile.profile_type) }}</div>
            <div class="text-caption">{{ profile.email }}</div>
          </div>
          
          <div class="col-auto">
            <q-btn
              flat
              round
              color="primary"
              :to="{ name: 'profile-view', params: { id: profile.user_id } }"
              class="q-mr-xs"
            >
              <unified-icon name="visibility" />
              <q-tooltip>View Profile</q-tooltip>
            </q-btn>
            <q-btn
              flat
              round
              color="primary"
              :to="{ name: 'profile-edit', params: { id: profile.user_id } }"
            >
              <unified-icon name="edit" />
              <q-tooltip>Edit Profile</q-tooltip>
            </q-btn>
          </div>
        </div>
      </q-card-section>
      
      <q-separator />
      
      <q-card-section>
        <div class="row q-col-gutter-md">
          <div class="col-12 col-md-6">
            <div class="text-subtitle2">Profile Type</div>
            <q-chip
              :color="getProfileTypeColor(profile.profile_type)"
              text-color="white"
              v-if="profile.profile_type"
            >
              {{ formatProfileType(profile.profile_type) }}
            </q-chip>
            <span v-else class="text-grey">Not specified</span>
          </div>
          
          <div class="col-12 col-md-6">
            <div class="text-subtitle2">Profile Status</div>
            <q-chip
              :color="getProfileStateColor(profile.profile_state)"
              text-color="white"
            >
              {{ profile.profile_state }}
            </q-chip>
          </div>
          
          <div class="col-12">
            <div class="text-subtitle2">Profile Completion</div>
            <q-linear-progress
              :value="(profile.profile_completion || 0) / 100"
              color="primary"
              class="q-mb-xs"
              size="10px"
            />
            <div class="text-caption">
              {{ Math.round(profile.profile_completion || 0) }}% complete
            </div>
          </div>
        </div>
      </q-card-section>
      
      <q-card-actions align="right">
        <q-btn
          flat
          color="primary"
          label="View Full Profile"
          :to="{ name: 'profile-view', params: { id: profile.user_id } }"
        />
      </q-card-actions>
    </q-card>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import UnifiedIcon from '../ui/UnifiedIcon.vue'

const props = defineProps({
  profile: {
    type: Object,
    required: true
  }
})

// Methods
function formatProfileType(type) {
  if (!type) return 'Unknown'
  
  return type
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}

function getInitials(firstName, lastName) {
  const first = firstName ? firstName.charAt(0) : ''
  const last = lastName ? lastName.charAt(0) : ''
  return (first + last).toUpperCase()
}

function getProfileTypeColor(type) {
  if (!type) return 'grey'
  
  const colors = {
    'innovator': 'purple',
    'investor': 'green',
    'mentor': 'blue',
    'professional': 'teal',
    'industry_expert': 'deep-orange',
    'academic_student': 'indigo',
    'academic_institution': 'light-blue',
    'organisation': 'amber'
  }
  
  return colors[type] || 'grey'
}

function getProfileStateColor(state) {
  if (!state) return 'grey'
  
  const colors = {
    'DRAFT': 'grey',
    'IN_PROGRESS': 'blue',
    'PENDING_APPROVAL': 'orange',
    'ACTIVE': 'green',
    'DISABLED': 'red',
    'DECLINED': 'red-6'
  }
  
  return colors[state] || 'grey'
}
</script>

<style scoped>
.profile-summary {
  max-width: 800px;
  margin: 0 auto;
}
</style>
