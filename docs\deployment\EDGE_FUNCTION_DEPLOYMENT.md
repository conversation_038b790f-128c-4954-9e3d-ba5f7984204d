# Edge Function Deployment Guide

This document provides instructions for deploying Supabase Edge Functions for the ZB Innovation Hub application.

## Prerequisites

- Node.js and npm installed
- Supabase CLI installed (`npm install -g supabase`)
- A Supabase project with Edge Functions enabled
- Project environment variables set in `.env` file

## Deployment Method

We use a batch script (`deploy-email-verification.bat`) to deploy the Edge Functions. This method works even without Dock<PERSON> installed, making it more accessible for all team members.

### Why Use This Method?

- **No Docker Required**: Unlike the standard Supabase CLI deployment which requires Dock<PERSON>, this method uses the `--use-api` flag to deploy directly via the Supabase API.
- **Automatic Project Reference**: The script automatically extracts the project reference from your `.env` file.
- **Consistent Deployment**: Ensures everyone on the team deploys the function in the same way.
- **JWT Verification Disabled**: Uses the `--no-verify-jwt` flag to allow anonymous access to the function, which is required for email verification and password reset flows.

## Deployment Steps

1. Make sure your `.env` file contains the correct Supabase URL:
   ```
   VITE_SUPABASE_URL=https://your-project-ref.supabase.co
   ```

2. Open a terminal (PowerShell or Command Prompt) in the project root directory.

3. Run the deployment script:
   ```
   .\deploy-email-verification.bat
   ```

4. The script will:
   - Extract the project reference from your `.env` file
   - Deploy the `send-email-verification` function to your Supabase project
   - Display a success message when complete

## Troubleshooting

If you encounter any issues during deployment:

1. **Permission Errors**: Make sure you're logged in to Supabase CLI with `npx supabase login`.

2. **Path Issues**: If the script can't be found, make sure you're in the project root directory.

3. **Project Reference Errors**: If the script can't extract the project reference, check your `.env` file format.

4. **Deployment Failures**: Check the error message. Common issues include:
   - Network connectivity problems
   - Invalid project reference
   - Missing permissions

## Available Edge Functions

### send-email-verification

This function handles all email sending for the application, including:

- Welcome emails for new users
- Password reset emails
- Email verification
- Custom emails

### Function Configuration

The function uses the following environment variables:

- `SENDGRID_API_KEY`: Your SendGrid API key
- `SENDGRID_FROM_EMAIL`: The email address to send from (default: <EMAIL>)
- `SENDGRID_FROM_NAME`: The name to display as the sender (default: ZB Innovation Hub)

These are set as secrets in the Supabase project and don't need to be included in your local environment.

## Testing Deployed Functions

After deployment, you can test the function using the provided test pages:

1. `test-email-function.html`: Tests the function using the Supabase client
2. `test-email-direct.html`: Tests the function using direct fetch requests

Both test pages allow you to send different types of emails to verify the function is working correctly.

## Important Notes

- Always use this deployment method (`.\deploy-email-verification.bat`) rather than manual deployment to ensure consistency.
- The Edge Function is deployed with CORS enabled, allowing it to be called from any origin.
- The function does not require JWT verification, allowing it to be called by unauthenticated users.
- Any changes to the function code must be redeployed using the same method.

## Updating the Function

If you need to update the function:

1. Make your changes to the function code in `supabase/functions/send-email-verification/`.
2. Run the deployment script again to update the deployed function.
3. Test the updated function to ensure it works as expected.

## Checking Deployment Status

You can check the status of your deployed functions in the Supabase Dashboard:

1. Go to https://supabase.com/dashboard/project/[YOUR_PROJECT_REF]/functions
2. Look for the `send-email-verification` function
3. Check the status, version, and last updated timestamp
