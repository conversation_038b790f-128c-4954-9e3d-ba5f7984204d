/**
 * commands.js
 * Core command handling module for the Task Master CLI
 */

// Command modules will be imported here
import { taskCommands } from './task/index.js';
import { projectCommands } from './project/index.js';
import { contextCommands } from './context/index.js';

// Combine all command modules
const allCommands = {
  ...taskCommands,
  ...projectCommands,
  ...contextCommands
};

/**
 * Main CLI runner that processes commands
 * @param {string[]} args - Command line arguments
 */
export async function runCLI(args) {
  const [,, command, ...commandArgs] = args;

  if (!command) {
    console.log('Usage: node dev.js <command> [args]');
    console.log('\nAvailable commands:');
    Object.keys(allCommands).forEach(cmd => {
      console.log(`  ${cmd}`);
    });
    return;
  }

  const commandFn = allCommands[command];
  if (!commandFn) {
    console.error(`Unknown command: ${command}`);
    return;
  }

  try {
    await commandFn(commandArgs);
  } catch (error) {
    console.error(`<PERSON>rror executing command ${command}:`, error.message);
  }
} 