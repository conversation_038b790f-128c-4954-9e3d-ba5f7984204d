<template>
  <div class="group-post-form">
    <q-form @submit="handleSubmit" class="q-gutter-md">
      <!-- Group Name -->
      <q-input
        v-model="formData.title"
        label="Group Name"
        outlined
        :rules="[val => !!val || 'Group name is required', val => val.length <= 100 || 'Maximum 100 characters']"
        counter
        maxlength="100"
      />

      <!-- Group Category -->
      <q-select
        v-model="formData.groupCategory"
        :options="groupCategoryOptions"
        label="Group Category"
        outlined
        emit-value
        map-options
        :rules="[val => !!val || 'Group category is required']"
      >
        <template v-slot:prepend>
          <q-icon name="category" />
        </template>
      </q-select>

      <!-- Group Type -->
      <q-select
        v-model="formData.groupType"
        :options="groupTypeOptions"
        label="Group Type"
        outlined
        emit-value
        map-options
        :rules="[val => !!val || 'Group type is required']"
      >
        <template v-slot:prepend>
          <q-icon name="group" />
        </template>
      </q-select>

      <!-- Description -->
      <q-input
        v-model="formData.content"
        type="textarea"
        label="Description"
        outlined
        autogrow
        :rules="[val => !!val || 'Description is required', val => val.length <= 2000 || 'Maximum 2000 characters']"
        counter
        maxlength="2000"
        rows="5"
      />

      <!-- Tags -->
      <div class="q-mb-md">
        <div class="text-subtitle2 q-mb-xs">Tags (select multiple)</div>
        <q-input
          v-model="tagsInput"
          label="Enter tags (comma-separated)"
          outlined
          hint="Enter multiple tags separated by commas, then press Enter or click outside"
          @blur="processTags"
          @keyup.enter="processTags"
        >
          <template v-slot:prepend>
            <q-icon name="local_offer" />
          </template>
          <template v-slot:after>
            <q-badge color="primary" floating>
              {{ formData.tags.length }}
            </q-badge>
          </template>
        </q-input>
      </div>

      <!-- Tags Display -->
      <div v-if="formData.tags.length > 0" class="q-mb-md">
        <div class="text-subtitle2 q-mb-xs">Selected Tags:</div>
        <div class="row q-gutter-xs">
          <q-chip
            v-for="(tag, index) in formData.tags"
            :key="index"
            removable
            @remove="removeTag(index)"
            color="primary"
            text-color="white"
            size="md"
          >
            {{ tag }}
          </q-chip>
        </div>
      </div>

      <!-- Group Rules -->
      <q-input
        v-model="formData.rules"
        type="textarea"
        label="Group Rules (optional)"
        outlined
        autogrow
        rows="3"
        hint="Set guidelines for your group members"
      />

      <!-- Image Upload -->
      <div class="q-mb-md">
        <q-file
          v-model="imageFile"
          label="Add Group Image"
          outlined
          accept=".jpg, .jpeg, .png, .gif"
          @update:model-value="handleImageUpload"
          max-file-size="5242880"
          @rejected="onRejected"
          :rules="[val => !!val || 'Group image is required']"
        >
          <template v-slot:prepend>
            <q-icon name="attach_file" />
          </template>
        </q-file>

        <!-- Image Preview -->
        <div v-if="formData.image" class="image-preview q-mt-sm">
          <q-img :src="formData.image" style="max-height: 200px; max-width: 100%;" />
          <q-btn
            round
            color="negative"
            icon="delete"
            size="sm"
            class="absolute-top-right"
            @click="removeImage"
          />
        </div>
      </div>

      <!-- Visibility -->
      <div class="q-mb-md">
        <q-select
          v-model="formData.visibility"
          :options="visibilityOptions"
          label="Visibility"
          outlined
          emit-value
          map-options
        >
          <template v-slot:prepend>
            <q-icon name="visibility" />
          </template>
        </q-select>
      </div>

      <!-- Action Buttons -->
      <div class="row justify-end q-gutter-sm">
        <q-btn
          label="Cancel"
          color="grey"
          flat
          @click="$emit('cancel')"
        />
        <q-btn
          label="Create Group"
          type="submit"
          color="primary"
          :loading="loading"
        />
      </div>
    </q-form>
  </div>
</template>

<script setup lang="ts">
import { ref, defineEmits } from 'vue';
import { useQuasar } from 'quasar';

const emit = defineEmits(['submit', 'cancel']);
const $q = useQuasar();

// State
const loading = ref(false);
const imageFile = ref(null);
const tagsInput = ref('');
const formData = ref({
  title: '',
  content: '',
  image: null,
  groupCategory: '',
  groupType: '',
  tags: [],
  rules: '',
  visibility: 'public',
  postType: 'group',
  memberCount: 1 // Start with the creator as the only member
});

// Options
const groupCategoryOptions = [
  { label: 'Innovation', value: 'innovation', icon: 'lightbulb' },
  { label: 'Technology', value: 'technology', icon: 'devices' },
  { label: 'Entrepreneurship', value: 'entrepreneurship', icon: 'rocket_launch' },
  { label: 'Business', value: 'business', icon: 'business' },
  { label: 'Funding', value: 'funding', icon: 'attach_money' },
  { label: 'Research', value: 'research', icon: 'science' },
  { label: 'Education', value: 'education', icon: 'school' },
  { label: 'Healthcare', value: 'healthcare', icon: 'health_and_safety' },
  { label: 'Agriculture', value: 'agriculture', icon: 'grass' },
  { label: 'Energy', value: 'energy', icon: 'bolt' },
  { label: 'Finance', value: 'finance', icon: 'payments' },
  { label: 'Social Impact', value: 'social_impact', icon: 'public' }
];

const groupTypeOptions = [
  { label: 'Interest Group', value: 'interest', icon: 'favorite' },
  { label: 'Project Group', value: 'project', icon: 'assignment' },
  { label: 'Professional Network', value: 'professional', icon: 'work' },
  { label: 'Support Group', value: 'support', icon: 'support' },
  { label: 'Learning Circle', value: 'learning', icon: 'school' },
  { label: 'Incubator', value: 'incubator', icon: 'egg' },
  { label: 'Accelerator', value: 'accelerator', icon: 'speed' },
  { label: 'Research Group', value: 'research', icon: 'science' }
];

const visibilityOptions = [
  { label: 'Public', value: 'public', icon: 'public', hint: 'Anyone can see and join the group' },
  { label: 'Private', value: 'private', icon: 'lock', hint: 'Only members can see content, but anyone can request to join' },
  { label: 'Invite Only', value: 'invite', icon: 'mail', hint: 'Only invited members can join the group' }
];

// Methods
function handleImageUpload(file) {
  if (!file) return;

  // In a real implementation, this would upload the file to a server
  // For now, we'll just create a data URL
  const reader = new FileReader();
  reader.onload = (e) => {
    formData.value.image = e.target.result;
  };
  reader.readAsDataURL(file);
}

function removeImage() {
  formData.value.image = null;
  imageFile.value = null;
}

function onRejected(rejectedEntries) {
  // Display notification for rejected files
  rejectedEntries.forEach(entry => {
    let message = '';
    if (entry.failedPropValidation === 'accept') {
      message = 'Please upload only image files (JPG, PNG, GIF)';
    } else if (entry.failedPropValidation === 'max-file-size') {
      message = 'File is too large. Maximum size is 5MB';
    } else {
      message = 'File upload failed';
    }

    $q.notify({
      type: 'negative',
      message
    });
  });
}

function processTags() {
  if (!tagsInput.value) return;

  // Split by comma and trim whitespace
  const tags = tagsInput.value.split(',').map(tag => tag.trim()).filter(tag => tag);

  // Remove duplicates
  formData.value.tags = [...new Set(tags)];
}

function removeTag(index) {
  formData.value.tags.splice(index, 1);
}

async function handleSubmit() {
  loading.value = true;

  try {
    // Process tags one more time before submission
    processTags();

    // Generate a slug from the title
    const slug = formData.value.title
      .toLowerCase()
      .replace(/[^\w\s]/gi, '')
      .replace(/\s+/g, '-');

    // Create post object with postType
    const post = {
      ...formData.value,
      postType: 'group',
      id: Date.now(),
      author: 'Current User', // This would come from the auth store
      avatar: 'https://cdn.quasar.dev/img/avatar.png', // This would come from the auth store
      date: new Date().toLocaleString(),
      likes: 0,
      comments: 0,
      slug
    };

    emit('submit', post);
  } catch (error) {
    console.error('Error creating group:', error);
    $q.notify({
      type: 'negative',
      message: 'Failed to create group. Please try again.'
    });
  } finally {
    loading.value = false;
  }
}
</script>

<style scoped>
.image-preview {
  position: relative;
  display: inline-block;
}
</style>
