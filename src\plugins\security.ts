/**
 * Security plugin for Vue application
 * Initializes CSRF protection and other security measures
 */

import { App } from 'vue'
import { useSecurity, vSanitize, vSanitizeText } from '@/composables/useSecurity'

export default {
  install(app: App) {
    // Initialize security composable
    const security = useSecurity()
    
    // Initialize CSRF protection
    security.initCSRF()
    
    // Register global directives for sanitization
    app.directive('sanitize', vSanitize)
    app.directive('sanitize-text', vSanitizeText)
    
    // Make security utilities available globally
    app.config.globalProperties.$security = security
    
    // Add global error handler for security-related errors
    app.config.errorHandler = (err: any, instance, info) => {
      console.error('Vue Error:', err)
      console.error('Component:', instance)
      console.error('Info:', info)
      
      // Check for security-related errors
      if (err.message?.includes('CSRF') || err.message?.includes('sanitiz')) {
        console.error('Security-related error detected:', err.message)
        // You could send this to a monitoring service
      }
    }
    
    // Add global warning for development
    if (import.meta.env.DEV) {
      console.log('🔒 Security plugin initialized with CSRF protection')
    }
  }
}
