-- Create profile_types table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.profile_types (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(50) NOT NULL UNIQUE,
  display_name VARCHAR(100) NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert profile types if they don't exist
INSERT INTO public.profile_types (name, display_name, description)
VALUES
  ('innovator', 'Innovator', 'Entrepreneurs and startups with innovative ideas')
ON CONFLICT (name) DO NOTHING;

INSERT INTO public.profile_types (name, display_name, description)
VALUES
  ('investor', 'Business Investor', 'Investors looking to fund innovative projects')
ON CONFLICT (name) DO NOTHING;

INSERT INTO public.profile_types (name, display_name, description)
VALUES
  ('mentor', 'Mentor', 'Experienced professionals offering guidance')
ON CONFLICT (name) DO NOTHING;

INSERT INTO public.profile_types (name, display_name, description)
VALUES
  ('professional', 'Professional', 'Industry professionals seeking opportunities')
ON CONFLICT (name) DO NOTHING;

INSERT INTO public.profile_types (name, display_name, description)
VALUES
  ('industry_expert', 'Industry Expert', 'Specialists with deep domain knowledge')
ON CONFLICT (name) DO NOTHING;

INSERT INTO public.profile_types (name, display_name, description)
VALUES
  ('academic_student', 'Academic Student', 'Students looking for research or career opportunities')
ON CONFLICT (name) DO NOTHING;

INSERT INTO public.profile_types (name, display_name, description)
VALUES
  ('academic_institution', 'Academic Institution', 'Universities and research institutions')
ON CONFLICT (name) DO NOTHING;

INSERT INTO public.profile_types (name, display_name, description)
VALUES
  ('organisation', 'Organisation', 'Companies and organizations seeking innovation')
ON CONFLICT (name) DO NOTHING;

-- Create innovator_profiles table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.innovator_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  profile_type_id UUID NOT NULL REFERENCES public.profile_types(id),
  company_name VARCHAR(255),
  industry TEXT[],
  innovation_category TEXT[],
  innovation_area TEXT,
  stage VARCHAR(50),
  funding_status VARCHAR(50),
  pitch_deck_url VARCHAR(255),
  website VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(profile_id)
);

-- Create investor_profiles table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.investor_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  profile_type_id UUID NOT NULL REFERENCES public.profile_types(id),
  firm_name VARCHAR(255),
  investment_focus TEXT[],
  investment_stages TEXT[],
  ticket_size VARCHAR(50),
  portfolio TEXT[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(profile_id)
);

-- Create mentor_profiles table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.mentor_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  profile_type_id UUID NOT NULL REFERENCES public.profile_types(id),
  areas_of_expertise TEXT[],
  industry_experience TEXT[],
  years_of_experience INTEGER,
  mentoring_experience TEXT,
  mentoring_approach TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(profile_id)
);

-- Create professional_profiles table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.professional_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  profile_type_id UUID NOT NULL REFERENCES public.profile_types(id),
  company VARCHAR(255),
  job_title VARCHAR(255),
  industry TEXT[],
  skills TEXT[],
  experience_years INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(profile_id)
);

-- Create industry_expert_profiles table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.industry_expert_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  profile_type_id UUID NOT NULL REFERENCES public.profile_types(id),
  areas_of_expertise TEXT[],
  industry TEXT[],
  experience_years INTEGER,
  publications TEXT[],
  speaking_engagements TEXT[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(profile_id)
);

-- Create academic_student_profiles table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.academic_student_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  profile_type_id UUID NOT NULL REFERENCES public.profile_types(id),
  institution VARCHAR(255),
  degree_program VARCHAR(255),
  field_of_study VARCHAR(255),
  graduation_year INTEGER,
  research_interests TEXT[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(profile_id)
);

-- Create academic_institution_profiles table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.academic_institution_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  profile_type_id UUID NOT NULL REFERENCES public.profile_types(id),
  institution_name VARCHAR(255),
  institution_type VARCHAR(50),
  departments TEXT[],
  research_areas TEXT[],
  website VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(profile_id)
);

-- Create organisation_profiles table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.organisation_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  profile_type_id UUID NOT NULL REFERENCES public.profile_types(id),
  organisation_name VARCHAR(255),
  organisation_type VARCHAR(50),
  industry TEXT[],
  size VARCHAR(50),
  website VARCHAR(255),
  mission_statement TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(profile_id)
);

-- Add RLS policies for each profile type table
ALTER TABLE public.innovator_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.investor_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.mentor_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.professional_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.industry_expert_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.academic_student_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.academic_institution_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.organisation_profiles ENABLE ROW LEVEL SECURITY;

-- Create policies for innovator_profiles
CREATE POLICY "Users can view their own innovator profile"
ON public.innovator_profiles FOR SELECT
USING (
  profile_id IN (
    SELECT id FROM public.profiles
    WHERE user_id = auth.uid()
  )
);

CREATE POLICY "Users can update their own innovator profile"
ON public.innovator_profiles FOR UPDATE
USING (
  profile_id IN (
    SELECT id FROM public.profiles
    WHERE user_id = auth.uid()
  )
);

CREATE POLICY "Users can insert their own innovator profile"
ON public.innovator_profiles FOR INSERT
WITH CHECK (
  profile_id IN (
    SELECT id FROM public.profiles
    WHERE user_id = auth.uid()
  )
);

-- Create policies for investor_profiles
CREATE POLICY "Users can view their own investor profile"
ON public.investor_profiles FOR SELECT
USING (
  profile_id IN (
    SELECT id FROM public.profiles
    WHERE user_id = auth.uid()
  )
);

CREATE POLICY "Users can update their own investor profile"
ON public.investor_profiles FOR UPDATE
USING (
  profile_id IN (
    SELECT id FROM public.profiles
    WHERE user_id = auth.uid()
  )
);

CREATE POLICY "Users can insert their own investor profile"
ON public.investor_profiles FOR INSERT
WITH CHECK (
  profile_id IN (
    SELECT id FROM public.profiles
    WHERE user_id = auth.uid()
  )
);

-- Create policies for mentor_profiles
CREATE POLICY "Users can view their own mentor profile"
ON public.mentor_profiles FOR SELECT
USING (
  profile_id IN (
    SELECT id FROM public.profiles
    WHERE user_id = auth.uid()
  )
);

CREATE POLICY "Users can update their own mentor profile"
ON public.mentor_profiles FOR UPDATE
USING (
  profile_id IN (
    SELECT id FROM public.profiles
    WHERE user_id = auth.uid()
  )
);

CREATE POLICY "Users can insert their own mentor profile"
ON public.mentor_profiles FOR INSERT
WITH CHECK (
  profile_id IN (
    SELECT id FROM public.profiles
    WHERE user_id = auth.uid()
  )
);

-- Create policies for professional_profiles
CREATE POLICY "Users can view their own professional profile"
ON public.professional_profiles FOR SELECT
USING (
  profile_id IN (
    SELECT id FROM public.profiles
    WHERE user_id = auth.uid()
  )
);

CREATE POLICY "Users can update their own professional profile"
ON public.professional_profiles FOR UPDATE
USING (
  profile_id IN (
    SELECT id FROM public.profiles
    WHERE user_id = auth.uid()
  )
);

CREATE POLICY "Users can insert their own professional profile"
ON public.professional_profiles FOR INSERT
WITH CHECK (
  profile_id IN (
    SELECT id FROM public.profiles
    WHERE user_id = auth.uid()
  )
);

-- Create policies for industry_expert_profiles
CREATE POLICY "Users can view their own industry expert profile"
ON public.industry_expert_profiles FOR SELECT
USING (
  profile_id IN (
    SELECT id FROM public.profiles
    WHERE user_id = auth.uid()
  )
);

CREATE POLICY "Users can update their own industry expert profile"
ON public.industry_expert_profiles FOR UPDATE
USING (
  profile_id IN (
    SELECT id FROM public.profiles
    WHERE user_id = auth.uid()
  )
);

CREATE POLICY "Users can insert their own industry expert profile"
ON public.industry_expert_profiles FOR INSERT
WITH CHECK (
  profile_id IN (
    SELECT id FROM public.profiles
    WHERE user_id = auth.uid()
  )
);

-- Create policies for academic_student_profiles
CREATE POLICY "Users can view their own academic student profile"
ON public.academic_student_profiles FOR SELECT
USING (
  profile_id IN (
    SELECT id FROM public.profiles
    WHERE user_id = auth.uid()
  )
);

CREATE POLICY "Users can update their own academic student profile"
ON public.academic_student_profiles FOR UPDATE
USING (
  profile_id IN (
    SELECT id FROM public.profiles
    WHERE user_id = auth.uid()
  )
);

CREATE POLICY "Users can insert their own academic student profile"
ON public.academic_student_profiles FOR INSERT
WITH CHECK (
  profile_id IN (
    SELECT id FROM public.profiles
    WHERE user_id = auth.uid()
  )
);

-- Create policies for academic_institution_profiles
CREATE POLICY "Users can view their own academic institution profile"
ON public.academic_institution_profiles FOR SELECT
USING (
  profile_id IN (
    SELECT id FROM public.profiles
    WHERE user_id = auth.uid()
  )
);

CREATE POLICY "Users can update their own academic institution profile"
ON public.academic_institution_profiles FOR UPDATE
USING (
  profile_id IN (
    SELECT id FROM public.profiles
    WHERE user_id = auth.uid()
  )
);

CREATE POLICY "Users can insert their own academic institution profile"
ON public.academic_institution_profiles FOR INSERT
WITH CHECK (
  profile_id IN (
    SELECT id FROM public.profiles
    WHERE user_id = auth.uid()
  )
);

-- Create policies for organisation_profiles
CREATE POLICY "Users can view their own organisation profile"
ON public.organisation_profiles FOR SELECT
USING (
  profile_id IN (
    SELECT id FROM public.profiles
    WHERE user_id = auth.uid()
  )
);

CREATE POLICY "Users can update their own organisation profile"
ON public.organisation_profiles FOR UPDATE
USING (
  profile_id IN (
    SELECT id FROM public.profiles
    WHERE user_id = auth.uid()
  )
);

CREATE POLICY "Users can insert their own organisation profile"
ON public.organisation_profiles FOR INSERT
WITH CHECK (
  profile_id IN (
    SELECT id FROM public.profiles
    WHERE user_id = auth.uid()
  )
);

-- Add profile_state enum type if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'profile_state_enum') THEN
        CREATE TYPE profile_state_enum AS ENUM ('in_progress', 'active', 'disabled', 'banned');
    END IF;
END$$;

-- Add profile_state column to profiles table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'profile_state') THEN
        ALTER TABLE public.profiles ADD COLUMN profile_state profile_state_enum DEFAULT 'in_progress';
    END IF;
END$$;

-- Create or update trigger function for updating updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for all profile tables
DO $$
DECLARE
    tables TEXT[] := ARRAY['profiles', 'innovator_profiles', 'investor_profiles', 'mentor_profiles',
                          'professional_profiles', 'industry_expert_profiles', 'academic_student_profiles',
                          'academic_institution_profiles', 'organisation_profiles'];
    t TEXT;
BEGIN
    FOREACH t IN ARRAY tables
    LOOP
        EXECUTE format('
            DROP TRIGGER IF EXISTS update_%s_updated_at ON public.%s;
            CREATE TRIGGER update_%s_updated_at
            BEFORE UPDATE ON public.%s
            FOR EACH ROW
            EXECUTE FUNCTION public.update_updated_at_column();
        ', t, t, t, t);
    END LOOP;
END$$;
