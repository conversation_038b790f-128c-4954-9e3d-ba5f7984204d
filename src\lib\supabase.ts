import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseKey) {
  throw new Error('Missing Supabase environment variables')
}

// Configure Supabase client with enhanced auth options
export const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce',
    debug: false, // Disable debug mode for auth
    // Define the site URL for redirects
    site_url: window.location.origin,
    // Set the redirect URL for email verification
    redirectTo: `${window.location.origin}/auth/verify`,
    // Don't require email confirmation to sign in
    // This allows users to sign in even if email verification fails
    shouldCreateUser: true,
    // Customize the email verification flow
    emailVerificationExpiryTime: 24 * 60 * 60, // 24 hours in seconds
    // Customize the email verification URL
    verifyEmailRedirectTo: `${window.location.origin}/auth/verify`
  },
  global: {
    // Set headers for all requests
    headers: {
      'X-Client-Info': 'ZbInnovation Web App'
    }
  }
})

// Define types for database tables
export type Tables = {
  newsletter_subscribers: {
    Row: {
      id: string
      email: string
      first_name?: string
      subscribed_at: string
      status: 'active' | 'unsubscribed'
    }
    Insert: Omit<Tables['newsletter_subscribers']['Row'], 'id' | 'subscribed_at'>
    Update: Partial<Tables['newsletter_subscribers']['Insert']>
  }
  profiles: {
    Row: {
      id: string
      first_name: string
      last_name: string
      email: string
      role: string
      phone?: string
      hear_about_us?: string
      avatar?: string
      profile_completion?: number
      profile_type?: string
      profile_type_id?: string
      created_at: string
      updated_at: string
    }
    Insert: Omit<Tables['profiles']['Row'], 'id' | 'created_at' | 'updated_at'>
    Update: Partial<Tables['profiles']['Insert']>
  }
  profile_types: {
    Row: {
      id: string
      name: string
      description: string
      created_at: string
      updated_at: string
    }
    Insert: Omit<Tables['profile_types']['Row'], 'id' | 'created_at' | 'updated_at'>
    Update: Partial<Tables['profile_types']['Insert']>
  }
  innovator_profiles: {
    Row: {
      id: string
      profile_id: string
      innovation_area: string
      innovation_stage: string
      funding_needs: boolean
      funding_amount: number
      team_size: number
      has_prototype: boolean
      prototype_description: string
      goals: string[]
      challenges: string[]
      created_at: string
      updated_at: string
    }
    Insert: Omit<Tables['innovator_profiles']['Row'], 'id' | 'created_at' | 'updated_at'>
    Update: Partial<Tables['innovator_profiles']['Insert']>
  }
  investor_profiles: {
    Row: {
      id: string
      profile_id: string
      investment_focus: string[]
      investment_stage: string[]
      investment_range: string
      previous_investments: number
      investment_geography: string[]
      investment_criteria: string
      created_at: string
      updated_at: string
    }
    Insert: Omit<Tables['investor_profiles']['Row'], 'id' | 'created_at' | 'updated_at'>
    Update: Partial<Tables['investor_profiles']['Insert']>
  }
  mentor_profiles: {
    Row: {
      mentor_profile_id: string
      user_id: string
      profile_name: string
      is_public: boolean
      completion_percentage: number
      bio: string | null
      website: string | null
      linkedin: string | null
      twitter: string | null
      facebook: string | null
      instagram: string | null
      youtube: string | null
      github: string | null
      medium: string | null
      other_social: string | null
      contact_email: string | null
      contact_phone_country_code: string | null
      contact_phone_number: string | null
      whatsapp_country_code: string | null
      whatsapp_number: string | null
      telegram: string | null
      skype: string | null
      preferred_contact_method: string | null
      availability_for_contact: string | null
      address: string | null
      city: string | null
      state_province: string | null
      country: string | null
      postal_code: string | null
      willing_to_relocate: boolean | null
      preferred_locations: any | null
      short_term_goals: any | null
      long_term_goals: any | null
      current_challenges: any | null
      looking_for: any | null
      collaboration_interests: any | null
      sdg_alignment: any | null
      additional_interests: string | null
      areas_of_expertise: any | null
      years_of_experience: number | null
      industry_experience: any | null
      notable_awards: any | null
      mentoring_approach: string | null
      availability: string | null
      previous_mentees: string | null
      mentoring_experience: string | null
      mentor_current_role: string | null
      company: string | null
      education: any | null
      success_stories: string | null
      mentee_achievements: string | null
      testimonials: string | null
      mentorship_philosophy: string | null
      mentorship_methods: any | null
      mentorship_tools: any | null
      mentorship_duration: string | null
      preferred_mentee_stage: any | null
      preferred_mentee_background: any | null
      mentee_expectations: string | null
      created_at: string
      updated_at: string
    }
    Insert: Omit<Tables['mentor_profiles']['Row'], 'mentor_profile_id' | 'created_at' | 'updated_at'>
    Update: Partial<Tables['mentor_profiles']['Insert']>
  }
  professional_profiles: {
    Row: {
      id: string
      profile_id: string
      industry: string
      job_title: string
      company: string
      skills: string[]
      years_of_experience: number
      certifications: string[]
      created_at: string
      updated_at: string
    }
    Insert: Omit<Tables['professional_profiles']['Row'], 'id' | 'created_at' | 'updated_at'>
    Update: Partial<Tables['professional_profiles']['Insert']>
  }
  industry_expert_profiles: {
    Row: {
      id: string
      profile_id: string
      industry: string
      areas_of_expertise: string[]
      years_of_experience: number
      publications: string[]
      speaking_engagements: string[]
      created_at: string
      updated_at: string
    }
    Insert: Omit<Tables['industry_expert_profiles']['Row'], 'id' | 'created_at' | 'updated_at'>
    Update: Partial<Tables['industry_expert_profiles']['Insert']>
  }
  academic_student_profiles: {
    Row: {
      id: string
      profile_id: string
      institution: string
      field_of_study: string
      degree_level: string
      graduation_year: number
      research_interests: string[]
      skills: string[]
      created_at: string
      updated_at: string
    }
    Insert: Omit<Tables['academic_student_profiles']['Row'], 'id' | 'created_at' | 'updated_at'>
    Update: Partial<Tables['academic_student_profiles']['Insert']>
  }
  academic_institution_profiles: {
    Row: {
      id: string
      profile_id: string
      institution_name: string
      institution_type: string
      location: string
      research_areas: string[]
      programs_offered: string[]
      collaboration_interests: string[]
      created_at: string
      updated_at: string
    }
    Insert: Omit<Tables['academic_institution_profiles']['Row'], 'id' | 'created_at' | 'updated_at'>
    Update: Partial<Tables['academic_institution_profiles']['Insert']>
  }
  organisation_profiles: {
    Row: {
      id: string
      profile_id: string
      organisation_name: string
      industry: string
      size: string
      location: string
      website: string
      focus_areas: string[]
      collaboration_interests: string[]
      created_at: string
      updated_at: string
    }
    Insert: Omit<Tables['organisation_profiles']['Row'], 'id' | 'created_at' | 'updated_at'>
    Update: Partial<Tables['organisation_profiles']['Insert']>
  }
  articles: {
    Row: {
      id: string
      title: string
      slug: string
      content: string
      excerpt: string
      cover_image: string | null
      author_name: string
      author_avatar: string | null
      category: string
      tags: string[]
      published_at: string
      created_at: string
      updated_at: string
    }
    Insert: Omit<Tables['articles']['Row'], 'id' | 'created_at' | 'updated_at'>
    Update: Partial<Tables['articles']['Insert']>
  }
  events: {
    Row: {
      id: string
      title: string
      description: string
      date: string
      location: string
      type: string
      capacity: number
      registeredCount: number
      image?: string
    }
    Insert: Omit<Tables['events']['Row'], 'id' | 'registeredCount'>
    Update: Partial<Tables['events']['Insert']>
  }
}

export type Database = {
  public: {
    Tables: Tables
  }
}



// Test Supabase connection
export async function testSupabaseConnection() {
  try {
    // Try to get the current user - this is a simple way to test the connection
    const { error } = await supabase.auth.getUser();

    if (error) {
      console.error('❌ Supabase connection error:', error.message);
      return false;
    }

    // Only log in development mode
    if (import.meta.env.DEV) {
      console.log('✅ Supabase connection successful!');
    }
    return true;
  } catch (error: any) {
    console.error('❌ Supabase connection error:', error.message);
    return false;
  }
}

// Check if database tables exist and create them if they don't
export async function checkDatabaseTables() {
  try {
    // Check if personal_details table exists
    const { error: profilesError } = await supabase.from('personal_details').select('user_id').limit(1);

    if (profilesError) {
      // Only log in development mode
      if (import.meta.env.DEV) {
        console.log('Personal details table does not exist. Creating database tables...');
      }
      return await createDatabaseTables();
    }

    // Only log in development mode
    if (import.meta.env.DEV) {
      console.log('✅ Database tables already exist');
    }
    return { success: true, message: 'Database tables already exist' };
  } catch (error: any) {
    console.error('❌ Error checking database tables:', error.message);
    return { success: false, message: error.message };
  }
}

// Create database tables
export async function createDatabaseTables() {
  try {
    // Only log in development mode
    if (import.meta.env.DEV) {
      console.log('Creating database tables...');
    }

    // Create personal_details table
    const createProfilesTable = await supabase.rpc('create_personal_details_table');
    if (createProfilesTable.error) {
      throw new Error(`Error creating personal_details table: ${createProfilesTable.error.message}`);
    }

    // Create auth triggers
    const createAuthTriggers = await supabase.rpc('create_auth_triggers');
    if (createAuthTriggers.error) {
      throw new Error(`Error creating auth triggers: ${createAuthTriggers.error.message}`);
    }

    // Only log in development mode
    if (import.meta.env.DEV) {
      console.log('✅ Database tables created successfully');
    }
    return { success: true, message: 'Database tables created successfully' };
  } catch (error: any) {
    console.error('❌ Error creating database tables:', error.message);
    return { success: false, message: error.message };
  }
}
