<template>
  <div>
    <!-- Standard Select Component -->
    <q-select
      v-model="internalValue"
      :options="options"
      :label="label"
      :hint="hint"
      :rules="rules"
      :multiple="multiple"
      :use-chips="useChips"
      :outlined="outlined"
      hide-dropdown-icon
      @update:model-value="onSelectChange"
    >
      <template v-slot:append>
        <unified-icon name="arrow_drop_down" />
      </template>
    </q-select>

    <!-- "Other" Input Field with Submit and Cancel buttons -->
    <div v-if="showOtherInput" class="q-mt-sm">
      <q-input
        v-model="otherValue"
        :label="`Please specify ${label.toLowerCase()}`"
        outlined
        @keyup.enter="submitOtherValue"
      >
        <template v-slot:append>
          <q-btn
            round
            flat
            icon="close"
            size="sm"
            @click="cancelOtherInput"
            class="q-ml-xs"
            color="grey-7"
          />
        </template>
      </q-input>
      <div class="row justify-end q-mt-sm">
        <q-btn
          color="primary"
          label="Submit"
          @click="submitOtherValue"
          :disable="!otherValue"
        />
      </div>
    </div>
  </div>
</template>

<script>
import UnifiedIcon from './UnifiedIcon.vue';

export default {
  name: 'EnhancedSelect',
  components: {
    UnifiedIcon
  },
  props: {
    modelValue: {
      type: [String, Number, Array, Object],
      default: null,
      validator(value) {
        // Accept any value, including arrays and strings
        return true;
      }
    },
    options: {
      type: Array,
      required: true
    },
    label: {
      type: String,
      default: ''
    },
    hint: {
      type: String,
      default: ''
    },
    rules: {
      type: Array,
      default: () => []
    },
    multiple: {
      type: Boolean,
      default: false
    },
    useChips: {
      type: Boolean,
      default: false
    },
    outlined: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      internalValue: this.modelValue,
      otherValue: ''
    };
  },
  computed: {
    showOtherInput() {
      if (!this.internalValue) return false;

      if (Array.isArray(this.internalValue)) {
        // Check for 'Other' in array of strings or objects
        return this.internalValue.some(item => {
          if (typeof item === 'string') return item === 'Other';
          if (typeof item === 'object' && item !== null) return item.value === 'other';
          return false;
        });
      }

      // Check for 'Other' in single string or object
      if (typeof this.internalValue === 'string') return this.internalValue === 'Other';
      if (typeof this.internalValue === 'object' && this.internalValue !== null) {
        return this.internalValue.value === 'other';
      }

      return false;
    }
  },
  watch: {
    modelValue(newVal) {
      this.internalValue = newVal;
    }
  },
  methods: {
    onSelectChange(val) {
      this.internalValue = val;

      // Check if 'Other' is selected
      let hasOther = false;

      if (Array.isArray(val)) {
        // Check for 'Other' in array
        hasOther = val.some(item => {
          if (typeof item === 'string') return item === 'Other';
          if (typeof item === 'object' && item !== null) return item.value === 'other';
          return false;
        });
      } else {
        // Check for 'Other' in single value
        if (typeof val === 'string') hasOther = val === 'Other';
        if (typeof val === 'object' && val !== null) hasOther = val.value === 'other';
      }

      // If the user selects 'Other', don't emit an update yet
      // We'll wait for them to submit the custom value
      if (hasOther) {
        // Reset the other value when 'Other' is selected
        this.otherValue = '';
        return;
      }

      // Handle special case for research_areas, other_research_areas, research_centers
      // If the parent component expects a string but we have an array, convert it
      if (Array.isArray(val) &&
          (this.label.includes('Research Areas') ||
           this.label.includes('Research Centers') ||
           this.label.includes('Other Research'))) {
        // Convert array to JSON string for these specific fields
        const jsonString = JSON.stringify(val);
        console.log(`EnhancedSelect: Converting array to JSON string for ${this.label}:`, jsonString);
        this.$emit('update:modelValue', jsonString);
      } else {
        // Normal case - emit the value as is
        this.$emit('update:modelValue', val);
      }
    },

    // Submit the custom 'Other' value
    submitOtherValue() {
      if (!this.otherValue) return;

      // For multiple select
      if (this.multiple && Array.isArray(this.internalValue)) {
        // Filter out 'Other' from the selection
        const result = this.internalValue.filter(item => {
          if (typeof item === 'string') return item !== 'Other';
          if (typeof item === 'object' && item !== null) return item.value !== 'other';
          return true;
        });

        // Add the custom value
        result.push(this.otherValue);

        // Handle special case for research fields
        if (this.label.includes('Research Areas') ||
            this.label.includes('Research Centers') ||
            this.label.includes('Other Research')) {
          // Convert array to JSON string for these specific fields
          const jsonString = JSON.stringify(result);
          console.log(`EnhancedSelect: Converting array to JSON string in submitOtherValue for ${this.label}:`, jsonString);
          this.$emit('update:modelValue', jsonString);
        } else {
          // Normal case
          this.$emit('update:modelValue', result);
        }
      }
      // For single select
      else {
        this.$emit('update:modelValue', this.otherValue);
      }

      // Clear the input after submission
      this.otherValue = '';
    },

    // Cancel the 'Other' input
    cancelOtherInput() {
      // For multiple select, remove 'Other' from the selection
      if (this.multiple && Array.isArray(this.internalValue)) {
        // Filter out 'Other' from the selection
        const result = this.internalValue.filter(item => {
          if (typeof item === 'string') return item !== 'Other';
          if (typeof item === 'object' && item !== null) return item.value !== 'other';
          return true;
        });

        // Handle special case for research fields
        if (this.label.includes('Research Areas') ||
            this.label.includes('Research Centers') ||
            this.label.includes('Other Research')) {
          // Convert array to JSON string for these specific fields
          const jsonString = JSON.stringify(result);
          console.log(`EnhancedSelect: Converting array to JSON string in cancelOtherInput for ${this.label}:`, jsonString);
          this.$emit('update:modelValue', jsonString);
        } else {
          // Normal case
          this.$emit('update:modelValue', result);
        }
      }
      // For single select, clear the selection
      else {
        this.$emit('update:modelValue', null);
      }

      // Clear the input
      this.otherValue = '';
    }
  }
};
</script>
