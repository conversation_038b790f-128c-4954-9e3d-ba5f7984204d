<template>
  <div class="notifications-container">
    <TransitionGroup name="notification">
      <div
        v-for="notification in notificationStore.notifications"
        :key="notification.id"
        :class="['notification', notification.type, notification.color ? `bg-${notification.color}` : '']"
      >
        <div class="notification-content">
          <div v-if="notification.icon" class="notification-icon">
            <q-icon :name="notification.icon" size="24px" />
          </div>
          <div class="notification-text">
            <div class="notification-message">{{ notification.message }}</div>
            <div v-if="notification.caption" class="notification-caption">{{ notification.caption }}</div>
          </div>
          <div class="notification-close" @click="notificationStore.remove(notification.id)">
            <q-icon name="close" size="16px" />
          </div>
        </div>
        <div v-if="notification.actions && notification.actions.length > 0" class="notification-actions">
          <q-btn
            v-for="(action, index) in notification.actions"
            :key="index"
            flat
            dense
            :color="action.color || 'white'"
            :label="action.label"
            @click="handleAction(action, notification.id)"
          />
        </div>
      </div>
    </TransitionGroup>
  </div>
</template>

<script setup lang="ts">
import { useNotificationStore } from '../../stores/notifications'

const notificationStore = useNotificationStore()

function handleAction(action: any, notificationId: number) {
  // Execute the action handler
  if (action.handler && typeof action.handler === 'function') {
    action.handler()
  }

  // Remove the notification after action is taken
  notificationStore.remove(notificationId)
}
</script>

<style scoped>
.notifications-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.notification {
  padding: 12px 16px;
  border-radius: 8px;
  color: white;
  min-width: 300px;
  max-width: 400px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  margin-bottom: 10px;
  overflow: hidden;
}

.notification-content {
  display: flex;
  align-items: flex-start;
}

.notification-icon {
  margin-right: 12px;
  display: flex;
  align-items: center;
}

.notification-text {
  flex: 1;
}

.notification-message {
  font-weight: 500;
  margin-bottom: 4px;
}

.notification-caption {
  font-size: 0.85rem;
  opacity: 0.9;
}

.notification-close {
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s;
  padding: 4px;
}

.notification-close:hover {
  opacity: 1;
}

.notification-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.notification.success {
  background-color: #4caf50;
}

.notification.error {
  background-color: #f44336;
}

.notification.warning {
  background-color: #ff9800;
}

.notification.info {
  background-color: #2196f3;
}

.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from,
.notification-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style>