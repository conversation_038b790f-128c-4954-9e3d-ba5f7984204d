interface SubscribeData {
  email: string
  firstName?: string
  lastName?: string
}

export async function subscribeToNewsletter(data: SubscribeData): Promise<Response> {
  // SECURITY: Direct MailChimp API calls disabled for client-side security
  // Newsletter subscriptions are handled via Supabase database instead
  console.warn('Direct MailChimp API calls are disabled for security. Newsletter subscriptions are handled via Supabase database.');

  // Return a mock successful response since newsletter functionality works via Supabase
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(new Response(JSON.stringify({ success: true, message: 'Newsletter subscription handled via Supabase' }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }))
    }, 500)
  })
}

export async function sendWelcomeEmail(email: string): Promise<Response> {
  // This would typically be handled by a backend service
  // For now, we'll just simulate the API call
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(new Response(JSON.stringify({ success: true }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }))
    }, 1000)
  })
} 