-- Update profiles table to include profile_completion field
ALTER TABLE profiles
ADD COLUMN IF NOT EXISTS profile_completion FLOAT DEFAULT 0.0,
ADD COLUMN IF NOT EXISTS profile_type VARCHAR(50),
ADD COLUMN IF NOT EXISTS profile_type_id UUID;

-- Create profile_types table to store the different types of profiles
CREATE TABLE IF NOT EXISTS profile_types (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(50) NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default profile types
INSERT INTO profile_types (name, description) VALUES
('innovator', 'Individuals with innovative ideas or projects'),
('business_investor', 'Investors looking to fund innovative projects'),
('mentor', 'Experienced professionals offering guidance'),
('professional', 'Industry professionals'),
('industry_expert', 'Experts in specific industries'),
('academic_student', 'Students from academic institutions'),
('academic_institution', 'Academic institutions'),
('organisation', 'Organizations and companies')
ON CONFLICT (name) DO NOTHING;

-- Create innovator_profiles table
CREATE TABLE IF NOT EXISTS innovator_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  profile_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  innovation_area TEXT,
  innovation_stage VARCHAR(50),
  funding_needs BOOLEAN,
  funding_amount NUMERIC,
  team_size INTEGER,
  has_prototype BOOLEAN,
  prototype_description TEXT,
  goals TEXT[],
  challenges TEXT[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create investor_profiles table
CREATE TABLE IF NOT EXISTS investor_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  profile_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  investment_focus TEXT[],
  investment_stage TEXT[],
  investment_range TEXT,
  previous_investments INTEGER,
  investment_geography TEXT[],
  investment_criteria TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create mentor_profiles table
CREATE TABLE IF NOT EXISTS mentor_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  profile_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  areas_of_expertise TEXT[],
  years_of_experience INTEGER,
  mentorship_style TEXT,
  availability TEXT,
  previous_mentees INTEGER,
  expectations TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create professional_profiles table
CREATE TABLE IF NOT EXISTS professional_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  profile_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  industry TEXT,
  job_title TEXT,
  company TEXT,
  skills TEXT[],
  years_of_experience INTEGER,
  certifications TEXT[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create industry_expert_profiles table
CREATE TABLE IF NOT EXISTS industry_expert_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  profile_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  industry TEXT,
  areas_of_expertise TEXT[],
  years_of_experience INTEGER,
  publications TEXT[],
  speaking_engagements TEXT[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create academic_student_profiles table
CREATE TABLE IF NOT EXISTS academic_student_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  profile_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  institution TEXT,
  field_of_study TEXT,
  degree_level TEXT,
  graduation_year INTEGER,
  research_interests TEXT[],
  skills TEXT[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create academic_institution_profiles table
CREATE TABLE IF NOT EXISTS academic_institution_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  profile_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  institution_name TEXT,
  institution_type TEXT,
  location TEXT,
  research_areas TEXT[],
  programs_offered TEXT[],

  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create organisation_profiles table
CREATE TABLE IF NOT EXISTS organisation_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  profile_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  organisation_name TEXT,
  industry TEXT,
  size TEXT,
  location TEXT,
  website TEXT,
  focus_areas TEXT[],
  collaboration_interests TEXT[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
