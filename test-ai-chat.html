<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Chat Test</title>
</head>
<body>
    <h1>AI Chat Test</h1>
    <div id="messages"></div>
    <input type="text" id="messageInput" placeholder="Type a message..." />
    <button onclick="sendMessage()">Send</button>

    <script>
        const SUPABASE_URL = 'https://dpicnvisvxpmgjtbeicf.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRwaWNudmlzdnhwbWdqdGJlaWNmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM3OTE0MDgsImV4cCI6MjA1OTM2NzQwOH0.6x_KSp-mDrLNqZiSW3ZB4VFDPM5tQas0mc68Md-Uvrc';

        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            if (!message) return;

            const messagesDiv = document.getElementById('messages');
            messagesDiv.innerHTML += `<div><strong>You:</strong> ${message}</div>`;
            input.value = '';

            try {
                console.log('Sending message to AI chat...');
                
                const response = await fetch(`${SUPABASE_URL}/functions/v1/ai-chat-public`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                    },
                    body: JSON.stringify({
                        message: message,
                        conversation_history: [],
                        user_context: {
                            profile_type: 'visitor'
                        }
                    })
                });

                console.log('Response status:', response.status);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Error response:', errorText);
                    messagesDiv.innerHTML += `<div><strong>Error:</strong> ${errorText}</div>`;
                    return;
                }

                const data = await response.json();
                console.log('AI response:', data);
                
                messagesDiv.innerHTML += `<div><strong>AI:</strong> ${data.response}</div>`;
                
                if (data.actions && data.actions.length > 0) {
                    messagesDiv.innerHTML += `<div><strong>Actions:</strong> ${JSON.stringify(data.actions)}</div>`;
                }
                
                if (data.suggestions && data.suggestions.length > 0) {
                    messagesDiv.innerHTML += `<div><strong>Suggestions:</strong> ${data.suggestions.join(', ')}</div>`;
                }
                
            } catch (error) {
                console.error('Error:', error);
                messagesDiv.innerHTML += `<div><strong>Error:</strong> ${error.message}</div>`;
            }
        }

        // Allow Enter key to send message
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    </script>
</body>
</html>
