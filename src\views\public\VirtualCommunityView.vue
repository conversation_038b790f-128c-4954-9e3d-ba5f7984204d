<template>
  <q-page class="virtual-community-page">
    <feed-container />
  </q-page>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import FeedContainer from '../../components/feed/FeedContainer.vue';

const route = useRoute();
const router = useRouter();

onMounted(() => {
  // Simple redirect to feed tab if no tab specified
  if (!route.query.tab) {
    router.replace({ query: { tab: 'feed' } });
  }
});
</script>

<style scoped>
.virtual-community-page {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 0; /* Remove default padding */
  width: 100%;
  max-width: 100%;
}

@media (max-width: 767px) {
  .virtual-community-page {
    margin: 0;
    display: flex;
    justify-content: center;
  }

  .virtual-community-page > div {
    width: 100%;
    max-width: 100%;
  }
}
</style>
