<template>
  <q-page class="news-page q-py-xl" style="min-height: 100vh;">
    <div class="container q-mx-auto q-px-md">
      <div class="row justify-center">
        <div class="col-md-10 col-sm-12">
          <div class="text-center q-mb-xl">
            <h1 class="text-h2 text-weight-light q-mb-md">Latest News & Updates</h1>
            <p class="text-body1 text-grey-8">Stay informed about our latest innovations, partnerships, and events</p>
          </div>

          <!-- Filters Section -->
          <div class="filters-section q-mb-xl">
            <div class="row q-col-gutter-lg">
              <!-- Search Input -->
              <div class="col-md-6 col-sm-12">
                <q-input
                  v-model="searchQuery"
                  outlined
                  class="search-input"
                  placeholder="Search by title or content..."
                  bg-color="white"
                >
                  <template v-slot:prepend>
                    <q-icon name="img:data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230D8A3E'%3E%3Cpath d='M15.5 14h-.79l-.28-.27A6.471 6.471 0 0 0 16 9.5 6.5 6.5 0 1 0 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z'/%3E%3C/svg%3E" />
                  </template>
                  <template v-slot:append v-if="searchQuery">
                    <q-icon
                      class="cursor-pointer"
                      name="img:data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666666'%3E%3Cpath d='M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z'/%3E%3C/svg%3E"
                      @click="searchQuery = ''"
                    />
                  </template>
                </q-input>
              </div>

              <!-- Category Filter -->
              <div class="col-md-6 col-sm-12">
                <q-select
                  v-model="selectedCategory"
                  :options="categories"
                  label="Filter by category"
                  outlined
                  emit-value
                  map-options
                  options-dense
                  bg-color="white"
                  :options-cover="false"
                  behavior="menu"
                  hide-dropdown-icon
                >
                  <template v-slot:prepend>
                    <unified-icon name="filter_list" />
                  </template>
                  <template v-slot:append>
                    <span class="icon icon-arrow-down"></span>
                  </template>
                  <template v-slot:option="{ opt }">
                    <div class="row items-center q-py-sm">
                      {{ opt.charAt(0).toUpperCase() + opt.slice(1) }}
                    </div>
                  </template>
                </q-select>
              </div>
            </div>
          </div>

          <!-- Loading State -->
          <div v-if="isLoading" class="text-center q-pa-xl">
            <q-spinner-dots color="primary" size="40px" />
            <div class="text-subtitle1 q-mt-sm">Loading news articles...</div>
          </div>

          <!-- News Grid -->
          <div v-else>
            <transition-group
              name="news-list"
              tag="div"
              class="row q-col-gutter-lg"
            >
              <div
                v-for="item in filteredNews"
                :key="item.id"
                class="col-md-6 col-sm-12"
              >
                <article class="news-card q-pa-md">
                  <header>
                    <q-chip
                      :color="item.categoryColor"
                      :text-color="item.textColor"
                      class="q-mb-sm"
                      size="sm"
                    >
                      {{ item.category }}
                    </q-chip>
                    <h3 class="text-h6 q-mb-sm">{{ item.title }}</h3>
                    <time :datetime="item.date" class="text-caption text-grey">
                      {{ new Date(item.date).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      }) }}
                    </time>
                  </header>
                  <p class="q-mt-md q-mb-lg text-body2">{{ item.excerpt }}</p>
                  <div class="row items-center justify-end">
                    <q-btn
                      flat
                      color="primary"
                      :to="`/articles/${item.slug}`"
                      label="Read More"
                      no-caps
                      class="q-px-md"
                    >
                      <q-tooltip>View full article</q-tooltip>
                    </q-btn>
                  </div>
                </article>
              </div>
            </transition-group>

            <!-- Empty State -->
            <div v-if="filteredNews.length === 0" class="text-center q-pa-xl">
              <q-icon
                name="img:data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z M3 9.5C3 5.91 5.91 3 9.5 3S16 5.91 16 9.5 13.09 16 9.5 16 3 13.09 3 9.5zm2 0C5 11.99 7.01 14 9.5 14S14 11.99 14 9.5 11.99 5 9.5 5 5 7.01 5 9.5z'/%3E%3C/svg%3E"
                size="48px"
                class="q-mb-md"
              />
              <h3 class="text-h6 q-mt-md text-grey-8">No results found</h3>
              <p class="text-body2 text-grey-7">Try adjusting your search or filter criteria</p>
              <q-btn
                flat
                color="primary"
                label="Clear filters"
                class="q-mt-md"
                @click="() => { searchQuery = ''; selectedCategory = 'all'; }"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useNewsStore } from '../stores/news';
import UnifiedIcon from '../components/ui/UnifiedIcon.vue';

interface NewsItem {
  id: number;
  category: string;
  categoryColor: string;
  textColor: string;
  title: string;
  date: string;
  excerpt: string;
  content: string;
  author: string;
  authorRole: string;
  readTime: string;
  slug: string;
}

const newsStore = useNewsStore();
const selectedCategory = ref<string>('all');
const searchQuery = ref('');
const isLoading = ref(true);

const categories = computed(() => {
  const uniqueCategories = new Set(newsStore.newsItems.map((item: NewsItem) => item.category));
  return ['all', ...Array.from(uniqueCategories)] as string[];
});

const getCategoryColor = (category: string): string => {
  switch (category.toLowerCase()) {
    case 'all':
      return 'primary';
    case 'announcement':
      return 'primary';
    case 'partnership':
      return 'secondary';
    case 'event':
      return 'accent';
    default:
      return 'primary';
  }
};

const getCategoryIcon = (category: string): string => {
  switch (category.toLowerCase()) {
    case 'all':
      return 'apps';
    case 'announcement':
      return 'campaign';
    case 'partnership':
      return 'handshake';
    case 'event':
      return 'event';
    default:
      return 'label';
  }
};

const filteredNews = computed(() => {
  return newsStore.newsItems.filter((item: NewsItem) => {
    const matchesCategory = selectedCategory.value === 'all' || item.category === selectedCategory.value;
    const matchesSearch =
      searchQuery.value === '' ||
      item.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      item.excerpt.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      item.author.toLowerCase().includes(searchQuery.value.toLowerCase());
    return matchesCategory && matchesSearch;
  });
});

onMounted(() => {
  // Simulate loading time to ensure store is ready
  setTimeout(() => {
    isLoading.value = false;
  }, 500);
});
</script>

<style scoped>
.container {
  width: 100%;
  max-width: 1400px;
  min-height: calc(100vh - 300px);
}

.news-card {
  border-radius: 12px;
  transition: all 0.3s ease;
  height: 100%;
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.news-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.search-input {
  width: 100%;
}

/* Animation for news items */
.news-list-enter-active,
.news-list-leave-active {
  transition: all 0.5s ease;
}

.news-list-enter-from,
.news-list-leave-to {
  opacity: 0;
  transform: translateY(30px);
}

.news-list-move {
  transition: transform 0.5s ease;
}

@media (max-width: 599px) {
  .container {
    padding: 16px;
  }

  .category-filter {
    margin-top: 16px;
  }
}
</style>