<template>
  <div class="goals-interests-display">
    <!-- JSON Object Display -->
    <div v-if="isJsonObject(data)">
      <q-list dense>
        <q-item v-for="(value, key) in data" :key="key">
          <q-item-section>
            <q-item-label>{{ formatFieldName(key) }}: {{ value }}</q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
    </div>

    <!-- Array Display as Chips -->
    <div v-else-if="Array.isArray(data) && data.length > 0">
      <q-chip 
        v-for="item in data" 
        :key="item" 
        :color="chipColor" 
        text-color="white" 
        class="q-ma-xs"
      >
        {{ item }}
      </q-chip>
    </div>

    <!-- String Display -->
    <div v-else-if="typeof data === 'string'">
      {{ data }}
    </div>

    <!-- Fallback -->
    <div v-else class="text-italic text-grey">
      No data available
    </div>
  </div>
</template>

<script setup lang="ts">
// Props
const props = defineProps<{
  data: any
  chipColor?: string
}>()

// Default chip color
const chipColor = props.chipColor || 'primary'

// Methods
function isJsonObject(value: any): boolean {
  return value !== null && 
         typeof value === 'object' && 
         !Array.isArray(value) &&
         Object.keys(value).length > 0
}

function formatFieldName(key: string): string {
  return key
    .replace(/_/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase())
}
</script>

<style scoped>
.goals-interests-display {
  /* Add any specific styling here */
}
</style>
