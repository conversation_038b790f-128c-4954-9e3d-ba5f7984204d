/**
 * Consolidated Profile Store
 *
 * This store centralizes all profile state management and provides a clean
 * separation between handling new and existing profiles. It uses the unified
 * profile service for all database operations.
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useNotificationStore } from './notifications'
import { useAuthStore } from './auth'
import { supabase } from '../lib/supabase'
import {
  useProfileService,
  type BaseProfile,
  type SpecializedProfile
} from '../services/profileService'
import { profileManager } from '../services/ProfileManager'

export const useProfileStore = defineStore('profile', () => {
  const notifications = useNotificationStore()
  const authStore = useAuthStore()
  const profileService = useProfileService()

  // State
  const currentProfile = ref<BaseProfile | null>(null)
  const currentSpecializedProfile = ref<SpecializedProfile | null>(null)
  const viewedProfile = ref<BaseProfile | null>(null)
  const viewedSpecializedProfile = ref<SpecializedProfile | null>(null)
  const userProfiles = ref<BaseProfile[]>([])
  const publicProfiles = ref<BaseProfile[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const showProfileCompletionPopup = ref(false)
  const hasMorePublicProfiles = ref(false)

  // Computed properties
  const profileCompletion = computed(() => {
    if (!currentProfile.value) return 0
    return currentProfile.value.profile_completion || 0
  })

  // Check if we have a profile for a specific user ID
  const hasProfile = (userId: string) => {
    return userProfiles.value.some(p => p.user_id === userId)
  }

  // Get a profile by user ID
  const getProfileByUserId = (userId: string) => {
    return userProfiles.value.find(p => p.user_id === userId) || null
  }

  const hasIncompleteProfile = computed(() => {
    return userProfiles.value.some(profile =>
      (profile.profile_state === 'IN_PROGRESS' || profile.profile_state === 'DRAFT') &&
      (profile.profile_completion || 0) < 100
    )
  })

  const hasSpecializedProfile = computed(() => {
    return userProfiles.value.some(profile => !!profile.profile_type)
  })

  const hasPartialProfile = computed(() => {
    // First check if we have any profiles at all
    if (userProfiles.value.length === 0) {
      return false
    }

    return userProfiles.value.some(profile => {
      // Check if profile is in a partial state
      const isPartialState = profile.profile_state === 'DRAFT' || profile.profile_state === 'IN_PROGRESS'

      // Check if profile completion is low
      const isLowCompletion = (profile.profile_completion || 0) < 50

      // Check if profile has a type but is incomplete
      const hasTypeButIncomplete = !!profile.profile_type && isLowCompletion

      return isPartialState || hasTypeButIncomplete
    })
  })

  const partialProfileId = computed(() => {
    if (!hasPartialProfile.value) {
      return null
    }

    // Find the first partial profile
    const partialProfile = userProfiles.value.find(profile => {
      const isPartialState = profile.profile_state === 'DRAFT' || profile.profile_state === 'IN_PROGRESS'
      const isLowCompletion = (profile.profile_completion || 0) < 50
      const hasTypeButIncomplete = !!profile.profile_type && isLowCompletion

      return isPartialState || hasTypeButIncomplete
    })

    return partialProfile?.user_id || null
  })

  // Actions

  /**
   * Load all profiles for the current user or a specific user
   * Now uses ProfileManager for unified caching and deduplication
   *
   * @param specificUserId Optional user ID to load profiles for
   */
  async function loadUserProfiles(specificUserId?: string): Promise<void> {
    // If a specific user ID is provided, use it, otherwise use the current user's ID
    const userId = specificUserId || (authStore.isAuthenticated ? authStore.currentUser?.id : null)

    if (!userId) {
      console.log('ProfileStore: No user ID available, skipping profile fetch')
      return
    }

    loading.value = true
    error.value = null

    try {
      console.log('ProfileStore: Loading profile for user:', userId)

      // Use ProfileManager instead of direct service calls
      const profile = await profileManager.getProfile(userId, {
        context: 'private',
        includeSpecialized: true
      })

      if (profile) {
        // If loading for the current user, update the store
        if (!specificUserId || specificUserId === authStore.currentUser?.id) {
          // Update userProfiles array with single profile
          const existingIndex = userProfiles.value.findIndex(p => p.user_id === userId)
          if (existingIndex >= 0) {
            userProfiles.value[existingIndex] = profile
          } else {
            userProfiles.value = [profile]
          }

          currentProfile.value = profile
          console.log('ProfileStore: Current profile set via ProfileManager:', currentProfile.value)

          // Load specialized profile data if the profile has a type
          if (currentProfile.value.profile_type) {
            await loadSpecializedProfileData(currentProfile.value.user_id, currentProfile.value.profile_type)
          }

          // Check if we should show the profile completion popup
          if (hasIncompleteProfile.value) {
            showProfileCompletionPopup.value = true
          }
        } else {
          // If loading for a different user, add to store without overwriting
          console.log('ProfileStore: Profile loaded for specific user:', userId)

          const existingIndex = userProfiles.value.findIndex(p => p.user_id === userId)
          if (existingIndex >= 0) {
            userProfiles.value[existingIndex] = profile
          } else {
            userProfiles.value.push(profile)
          }
          console.log('ProfileStore: Added profile to store via ProfileManager')
        }
      } else {
        console.log('ProfileStore: No profile found for user:', userId)
        if (!specificUserId) {
          userProfiles.value = []
          currentProfile.value = null
        }
      }

      return
    } catch (err: any) {
      console.error('Failed to load profiles:', err.message)
      error.value = err.message
      notifications.error('Failed to load profiles: ' + err.message)
    } finally {
      loading.value = false
    }
  }

  /**
   * Create a new profile
   *
   * @param profileType The profile type
   * @param profileName The profile name
   * @returns The ID of the created profile
   */
  async function createProfile(profileType: string | null, profileName: string): Promise<string | null> {
    if (!authStore.isAuthenticated || !authStore.currentUser?.id) {
      const errorMsg = 'You must be logged in to create a profile';
      notifications.error(errorMsg);
      error.value = errorMsg;
      return null;
    }

    if (!profileType) {
      const errorMsg = 'Profile type is required';
      notifications.error(errorMsg);
      error.value = errorMsg;
      return null;
    }

    if (!profileName || profileName.trim() === '') {
      const errorMsg = 'Profile name is required';
      notifications.error(errorMsg);
      error.value = errorMsg;
      return null;
    }

    loading.value = true;
    error.value = null;

    try {
      const userId = authStore.currentUser.id;
      const email = authStore.currentUser.email || '';

      console.log('Creating new profile:', { profileType, profileName });

      // Create the base profile
      const baseProfile = await profileService.createBaseProfile(
        userId,
        email,
        profileType,
        profileName
      );

      if (!baseProfile) {
        throw new Error('Failed to create base profile - no profile data returned');
      }

      // If we have a profile type, create a specialized profile
      if (profileType) {
        try {
          const specializedProfile = await profileService.createSpecializedProfile(
            userId,
            profileType,
            profileName
          );

          if (!specializedProfile) {
            console.warn(`Failed to create specialized ${profileType} profile, but base profile was created`);
          }
        } catch (specializedError: any) {
          // Log the error but continue since we already created the base profile
          console.warn(`Error creating specialized ${profileType} profile:`, specializedError);
          console.warn('Continuing with base profile only');
        }
      }

      // Reload user profiles to ensure we have the latest data
      await loadUserProfiles();

      // Set the newly created profile as the current profile
      await setCurrentProfile(baseProfile.user_id);

      notifications.success('Profile created successfully');
      return baseProfile.user_id;
    } catch (err: any) {
      console.error('Profile creation error:', err);
      const errorMessage = err.message || 'Failed to create profile';
      error.value = errorMessage;
      notifications.error(`Failed to create profile: ${errorMessage}`);
      return null;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Update personal details
   *
   * @param details The personal details to update
   * @returns True if the update was successful
   */
  async function updatePersonalDetails(details: Partial<BaseProfile>): Promise<boolean> {
    if (!currentProfile.value) {
      // If no profile is selected but we have profiles, try to select one with a type
      if (userProfiles.value.length > 0) {
        const validProfiles = userProfiles.value.filter(p => p.profile_type)
        if (validProfiles.length > 0) {
          console.log('No profile selected, attempting to select first valid profile')
          await setCurrentProfile(validProfiles[0].user_id)
        } else {
          console.error('No profile with a category selected')
          return false
        }
      } else {
        console.error('No profile selected')
        return false
      }
    }

    // Validate required fields
    if (details.first_name === '' || details.last_name === '' || details.email === '') {
      notifications.error('First name, last name, and email are required')
      return false
    }

    loading.value = true
    error.value = null

    try {
      const userId = currentProfile.value.user_id
      console.log('Updating personal details:', details)

      const updatedProfile = await profileService.updateBaseProfile(userId, details)

      if (!updatedProfile) {
        throw new Error('Failed to update personal details')
      }

      // Update local state
      currentProfile.value = updatedProfile

      // Update the profile in userProfiles array
      const profileIndex = userProfiles.value.findIndex(p => p.user_id === userId)
      if (profileIndex >= 0) {
        userProfiles.value[profileIndex] = updatedProfile
      }

      // Update profile completion
      await updateProfileCompletion()

      notifications.success('Personal details updated successfully')
      return true
    } catch (err: any) {
      console.error('Failed to update personal details:', err)
      error.value = err.message
      notifications.error('Failed to update personal details: ' + err.message)
      return false
    } finally {
      loading.value = false
    }
  }

  /**
   * Update profile (alias for updatePersonalDetails for test compatibility)
   *
   * @param details The profile details to update
   * @returns True if the update was successful
   */
  async function updateProfile(details: Partial<BaseProfile>): Promise<boolean> {
    return await updatePersonalDetails(details);
  }

  /**
   * Update profile completion
   *
   * @returns True if the update was successful
   */
  async function updateProfileCompletion(): Promise<boolean> {
    if (!currentProfile.value) {
      return false
    }

    try {
      const userId = currentProfile.value.user_id
      const profileType = currentProfile.value.profile_type

      // Calculate completion using the service
      const completion = profileService.calculateProfileCompletion(
        currentProfile.value,
        currentSpecializedProfile.value
      )

      // Update completion in database
      const success = await profileService.updateProfileCompletion(
        userId,
        profileType,
        completion
      )

      if (!success) {
        console.warn('Failed to update profile completion in database')
      }

      // Update local state
      if (currentProfile.value) {
        currentProfile.value.profile_completion = completion
      }

      // Update the profile in userProfiles array
      const profileIndex = userProfiles.value.findIndex(p => p.user_id === userId)
      if (profileIndex >= 0) {
        userProfiles.value[profileIndex].profile_completion = completion
      }

      return true
    } catch (err: any) {
      console.error('Failed to update profile completion:', err)
      return false
    }
  }

  /**
   * Update profile state
   *
   * @param userId The user ID
   * @param newState The new profile state
   * @returns True if the update was successful
   */
  async function updateProfileState(userId: string, newState: BaseProfile['profile_state']): Promise<boolean> {
    loading.value = true
    error.value = null

    try {
      console.log(`Updating profile state for user ${userId} to ${newState}`)

      const updatedProfile = await profileService.updateBaseProfile(userId, {
        profile_state: newState
      })

      if (!updatedProfile) {
        throw new Error('Failed to update profile state')
      }

      // Update local state
      const profileIndex = userProfiles.value.findIndex(p => p.user_id === userId)
      if (profileIndex >= 0) {
        userProfiles.value[profileIndex].profile_state = newState
      }

      // If this is the current profile, update it too
      if (currentProfile.value && currentProfile.value.user_id === userId) {
        currentProfile.value.profile_state = newState
      }

      return true
    } catch (err: any) {
      console.error('Failed to update profile state:', err)
      error.value = err.message
      notifications.error('Failed to update profile state: ' + err.message)
      return false
    } finally {
      loading.value = false
    }
  }

  /**
   * Set the current profile and load its specialized data
   *
   * @param userId The user ID
   * @returns True if the profile was set successfully
   */
  async function setCurrentProfile(userId: string): Promise<boolean> {
    const profile = userProfiles.value.find(p => p.user_id === userId)

    if (!profile) {
      console.error(`Profile with ID ${userId} not found`)
      return false
    }

    currentProfile.value = profile

    // If the profile has a type, load its specialized data
    if (profile.profile_type) {
      await loadSpecializedProfileData(profile.user_id, profile.profile_type)
    } else {
      // Clear specialized profile data if no profile type
      currentSpecializedProfile.value = null
    }

    return true
  }

  /**
   * Load specialized profile data
   *
   * @param userId The user ID
   * @param profileType The profile type
   */
  async function loadSpecializedProfileData(userId: string, profileType: string): Promise<void> {
    console.log(`Starting to load specialized profile data for ${profileType} profile with user ID ${userId}`)
    loading.value = true
    error.value = null

    try {
      console.log(`Loading specialized profile data for ${profileType} profile with user ID ${userId}`)

      // Use the profile service to load the specialized profile
      const specializedProfile = await profileService.loadSpecializedProfile(userId, profileType)

      if (specializedProfile) {
        console.log(`Specialized profile data loaded via service for ${profileType} profile:`, specializedProfile)
        currentSpecializedProfile.value = specializedProfile
      } else {
        console.log(`No specialized profile data found for ${profileType} profile, creating new record`)

        // Create a new specialized profile
        const newSpecializedProfile = await profileService.createSpecializedProfile(
          userId,
          profileType,
          currentProfile.value?.profile_name || 'My Profile'
        )

        if (newSpecializedProfile) {
          console.log(`New specialized profile created for ${profileType} profile:`, newSpecializedProfile)
          currentSpecializedProfile.value = newSpecializedProfile
        } else {
          console.error(`Failed to create new specialized profile for ${profileType} profile`)
          currentSpecializedProfile.value = null
        }
      }
    } catch (err: any) {
      console.error('Failed to load specialized profile data:', err)
      error.value = err.message
      notifications.warning(`Failed to load ${profileType} profile data: ${err.message}`)
      currentSpecializedProfile.value = null
    } finally {
      console.log(`Finished loading specialized profile data for ${profileType} profile with user ID ${userId}`)
      loading.value = false
    }
  }

  /**
   * Update specialized profile data
   *
   * @param profileData The profile data to update
   * @returns True if the update was successful
   */
  async function updateSpecializedProfileData(profileData: Partial<SpecializedProfile>): Promise<boolean> {
    if (!currentProfile.value || !currentProfile.value.profile_type) {
      notifications.error('No profile type selected')
      return false
    }

    loading.value = true
    error.value = null

    try {
      const userId = currentProfile.value.user_id
      const profileType = currentProfile.value.profile_type

      console.log(`Updating specialized profile data for ${profileType} profile:`, profileData)

      // Filter out fields that might not exist in this profile type's table
      // This prevents errors when trying to update fields that don't exist
      const filteredData: Record<string, any> = {}

      // Define fields specific to each profile type
      const profileTypeFields: Record<string, string[]> = {
        'innovator': [
          'profile_name', 'is_public', 'bio', 'website',
          'linkedin', 'twitter', 'facebook', 'instagram',
          'youtube', 'github', 'medium', 'other_social',
          'contact_email', 'contact_phone', 'contact_phone_country_code',
          'contact_phone_number', 'whatsapp', 'whatsapp_country_code',
          'whatsapp_number', 'telegram', 'skype', 'preferred_contact_method',
          // Innovator-specific fields
          'project_name', 'project_description', 'project_stage', 'funding_stage',
          'industry', 'technology', 'target_market', 'business_model',
          'team_size', 'founding_date', 'location', 'challenges',
          'achievements', 'goals', 'seeking', 'pitch_deck_url'
        ],
        'investor': [
          'profile_name', 'is_public', 'bio', 'website',
          'linkedin', 'twitter', 'facebook', 'instagram',
          'youtube', 'github', 'medium', 'other_social',
          'contact_email', 'contact_phone', 'contact_phone_country_code',
          'contact_phone_number', 'whatsapp', 'whatsapp_country_code',
          'whatsapp_number', 'telegram', 'skype', 'preferred_contact_method',
          'availability_for_contact',
          // Location fields
          'address', 'city', 'state_province', 'country', 'postal_code',
          'willing_to_relocate', 'preferred_locations',
          // Goals fields
          'short_term_goals', 'long_term_goals', 'current_challenges',
          'looking_for', 'collaboration_interests', 'sdg_alignment', 'additional_interests',
          // Investor-specific fields
          'investment_focus', 'investment_stage', 'ticket_size', 'previous_investments',
          'investment_geography', 'investment_criteria', 'success_stories', 'portfolio',
          'firm_name', 'firm_type', 'firm_size', 'firm_website',
          'investment_philosophy', 'value_add', 'investment_horizon', 'exit_strategy',
          'portfolio_size', 'average_return', 'successful_exits', 'notable_investments',
          'due_diligence_process', 'decision_timeline', 'key_metrics', 'red_flags'
        ],
        'mentor': [
          'profile_name', 'is_public', 'bio', 'website',
          'linkedin', 'twitter', 'facebook', 'instagram',
          'youtube', 'github', 'medium', 'other_social',
          'contact_email', 'contact_phone', 'contact_phone_country_code',
          'contact_phone_number', 'whatsapp', 'whatsapp_country_code',
          'whatsapp_number', 'telegram', 'skype', 'preferred_contact_method',
          // Mentor-specific fields
          'expertise_areas', 'years_of_experience', 'industry_experience',
          'notable_awards', 'mentoring_approach', 'availability', 'previous_mentees',
          'mentoring_experience', 'mentor_current_role', 'company', 'education',
          'success_stories', 'mentee_achievements', 'testimonials', 'mentorship_philosophy',
          'mentorship_methods', 'mentorship_tools', 'mentorship_duration', 'preferred_mentee_stage',
          'preferred_mentee_background', 'mentee_expectations', 'mentorship_style'
        ],
        'organisation': [
          'profile_name', 'is_public', 'completion_percentage', 'bio', 'website',
          'linkedin', 'twitter', 'facebook', 'instagram',
          'youtube', 'github', 'medium', 'other_social',
          'contact_email', 'contact_phone_country_code', 'contact_phone_number',
          'whatsapp_country_code', 'whatsapp_number', 'telegram', 'skype',
          'preferred_contact_method', 'availability_for_contact',
          // Organization-specific fields
          'organisation_name', 'organisation_type', 'your_position', 'founding_year',
          'industry', 'organisation_description', 'mission_statement', 'vision_statement',
          'organisation_size', 'organisation_stage', 'target_markets', 'products_services',
          'key_differentiators', 'achievements', 'has_multiple_locations', 'number_of_branches',
          'contact_name', 'contact_position', 'contact_function', 'general_email',
          'general_phone_country_code', 'general_phone_number', 'has_rnd', 'innovation_focus',
          'rnd_budget', 'innovation_strategy', 'patents', 'innovation_achievements',
          'collaboration_types', 'preferred_partners', 'resources_offered', 'collaboration_goals',
          'previous_collaborations', 'international_collaborations',
          'reference1_name', 'reference1_position', 'reference1_email',
          'reference1_phone_country_code', 'reference1_phone_number',
          'reference2_name', 'reference2_position', 'reference2_email',
          'reference2_phone_country_code', 'reference2_phone_number',
          // Location fields
          'address', 'city', 'state_province', 'country', 'postal_code',
          // Goals fields
          'short_term_goals', 'long_term_goals', 'current_challenges',
          'looking_for', 'collaboration_interests', 'sdg_alignment', 'additional_interests'
        ],
        'industry_expert': [
          'profile_name', 'is_public', 'completion_percentage', 'bio', 'website',
          'linkedin', 'twitter', 'facebook', 'instagram',
          'youtube', 'github', 'medium', 'other_social',
          'contact_email', 'contact_phone_country_code', 'contact_phone_number',
          'whatsapp_country_code', 'whatsapp_number', 'telegram', 'skype',
          'preferred_contact_method', 'availability_for_contact',
          // Industry Expert-specific fields
          'industry', 'job_title', 'company', 'years_in_industry',
          'areas_of_expertise', 'industry_specialization', 'certifications',
          'skills', 'languages', 'publications', 'speaking_engagements',
          'notable_projects', 'industry_impact', 'awards',
          'collaboration_types', 'preferred_stages', 'availability',
          'consulting_available', 'mentoring_available', 'international_collaborations',
          // Location fields
          'address', 'city', 'country',
          // Additional fields
          'willing_to_relocate', 'preferred_locations',
          // Goals fields
          'short_term_goals', 'long_term_goals', 'current_challenges',
          'looking_for', 'collaboration_interests', 'sdg_alignment', 'additional_interests'
        ],
        // Add other profile types as needed
      }

      // Default to common fields if profile type is not specifically defined
      const commonFields = ['profile_name', 'is_public', 'bio', 'website',
                           'linkedin', 'twitter', 'facebook', 'instagram',
                           'youtube', 'github', 'medium', 'other_social',
                           'contact_email', 'contact_phone', 'contact_phone_country_code',
                           'contact_phone_number', 'whatsapp', 'whatsapp_country_code',
                           'whatsapp_number', 'telegram', 'skype', 'preferred_contact_method',
                           // Location fields
                           'address', 'city', 'state_province', 'country', 'postal_code',
                           'willing_to_relocate', 'preferred_locations',
                           // Goals fields
                           'short_term_goals', 'long_term_goals', 'current_challenges',
                           'looking_for', 'collaboration_interests', 'sdg_alignment', 'additional_interests']

      // Get the allowed fields for this profile type
      const allowedFields = profileTypeFields[profileType] || commonFields

      // Copy only the fields that are allowed for this profile type
      for (const key in profileData) {
        if (allowedFields.includes(key)) {
          filteredData[key] = profileData[key]
        }
      }

      console.log(`Filtered profile data for ${profileType} profile:`, filteredData)

      const updatedProfile = await profileService.updateSpecializedProfile(
        userId,
        profileType,
        filteredData
      )

      if (!updatedProfile) {
        throw new Error(`Failed to update ${profileType} profile data`)
      }

      // Update local state
      currentSpecializedProfile.value = updatedProfile

      // Update profile completion
      await updateProfileCompletion()

      notifications.success('Profile data updated successfully')
      return true
    } catch (err: any) {
      console.error('Failed to update specialized profile data:', err)
      error.value = err.message
      notifications.error('Failed to update profile data: ' + err.message)
      return false
    } finally {
      loading.value = false
    }
  }

  /**
   * Update profile data
   *
   * @param profileData The profile data to update
   * @param profileType Optional profile type override
   * @returns True if the update was successful
   */
  async function updateProfileData(profileData: Partial<SpecializedProfile> = {}, profileType?: string): Promise<boolean> {
    console.log('updateProfileData called with:', { profileData, profileType });

    if (!currentProfile.value) {
      // If no profile is selected but we have profiles, try to select one with a type
      if (userProfiles.value.length > 0) {
        const validProfiles = userProfiles.value.filter(p => p.profile_type)
        if (validProfiles.length > 0) {
          console.log('No profile selected, attempting to select first valid profile')
          await setCurrentProfile(validProfiles[0].user_id)
        } else {
          console.error('No profile with a category selected')
          return false
        }
      } else {
        console.error('No profile selected')
        return false
      }
    }

    console.log('Current profile:', currentProfile.value);

    // Use provided profile type or fall back to current profile type
    const type = profileType || currentProfile.value.profile_type

    if (!type) {
      notifications.error('No profile type selected')
      return false
    }

    loading.value = true
    error.value = null

    try {
      const userId = currentProfile.value.user_id

      console.log(`Updating profile data for ${type} profile:`, profileData)

      // Add extra debug logging for network issues
      console.log('DEBUG: Network status before update:', navigator.onLine ? 'Online' : 'Offline')

      // Filter out fields that might not exist in this profile type's table
      // This prevents errors when trying to update fields that don't exist
      const filteredData: Record<string, any> = {}

      // Define fields specific to each profile type
      const profileTypeFields: Record<string, string[]> = {
        'innovator': [
          'profile_name', 'is_public', 'bio', 'website',
          'linkedin', 'twitter', 'facebook', 'instagram',
          'youtube', 'github', 'medium', 'other_social',
          'contact_email', 'contact_phone', 'contact_phone_country_code',
          'contact_phone_number', 'whatsapp', 'whatsapp_country_code',
          'whatsapp_number', 'telegram', 'skype', 'preferred_contact_method',
          // Innovator-specific fields
          'project_name', 'project_description', 'project_stage', 'funding_stage',
          'industry', 'technology', 'target_market', 'business_model',
          'team_size', 'founding_date', 'location', 'challenges',
          'achievements', 'goals', 'seeking', 'pitch_deck_url'
        ],
        'investor': [
          'profile_name', 'is_public', 'bio', 'website',
          'linkedin', 'twitter', 'facebook', 'instagram',
          'youtube', 'github', 'medium', 'other_social',
          'contact_email', 'contact_phone', 'contact_phone_country_code',
          'contact_phone_number', 'whatsapp', 'whatsapp_country_code',
          'whatsapp_number', 'telegram', 'skype', 'preferred_contact_method',
          'availability_for_contact',
          // Location fields
          'address', 'city', 'state_province', 'country', 'postal_code',
          'willing_to_relocate', 'preferred_locations',
          // Goals fields
          'short_term_goals', 'long_term_goals', 'current_challenges',
          'looking_for', 'collaboration_interests', 'sdg_alignment', 'additional_interests',
          // Investor-specific fields
          'investment_focus', 'investment_stage', 'ticket_size', 'previous_investments',
          'investment_geography', 'investment_criteria', 'success_stories', 'portfolio',
          'firm_name', 'firm_type', 'firm_size', 'firm_website',
          'investment_philosophy', 'value_add', 'investment_horizon', 'exit_strategy',
          'portfolio_size', 'average_return', 'successful_exits', 'notable_investments',
          'due_diligence_process', 'decision_timeline', 'key_metrics', 'red_flags'
        ],
        'mentor': [
          'profile_name', 'is_public', 'bio', 'website',
          'linkedin', 'twitter', 'facebook', 'instagram',
          'youtube', 'github', 'medium', 'other_social',
          'contact_email', 'contact_phone', 'contact_phone_country_code',
          'contact_phone_number', 'whatsapp', 'whatsapp_country_code',
          'whatsapp_number', 'telegram', 'skype', 'preferred_contact_method',
          // Mentor-specific fields
          'expertise_areas', 'years_of_experience', 'industry_experience',
          'notable_awards', 'mentoring_approach', 'availability', 'previous_mentees',
          'mentoring_experience', 'mentor_current_role', 'company', 'education',
          'success_stories', 'mentee_achievements', 'testimonials', 'mentorship_philosophy',
          'mentorship_methods', 'mentorship_tools', 'mentorship_duration', 'preferred_mentee_stage',
          'preferred_mentee_background', 'mentee_expectations', 'mentorship_style'
        ],
        'organisation': [
          'profile_name', 'is_public', 'completion_percentage', 'bio', 'website',
          'linkedin', 'twitter', 'facebook', 'instagram',
          'youtube', 'github', 'medium', 'other_social',
          'contact_email', 'contact_phone_country_code', 'contact_phone_number',
          'whatsapp_country_code', 'whatsapp_number', 'telegram', 'skype',
          'preferred_contact_method', 'availability_for_contact',
          // Organization-specific fields
          'organisation_name', 'organisation_type', 'your_position', 'founding_year',
          'industry', 'organisation_description', 'mission_statement', 'vision_statement',
          'organisation_size', 'organisation_stage', 'target_markets', 'products_services',
          'key_differentiators', 'achievements', 'has_multiple_locations', 'number_of_branches',
          'contact_name', 'contact_position', 'contact_function', 'general_email',
          'general_phone_country_code', 'general_phone_number', 'has_rnd', 'innovation_focus',
          'rnd_budget', 'innovation_strategy', 'patents', 'innovation_achievements',
          'collaboration_types', 'preferred_partners', 'resources_offered', 'collaboration_goals',
          'previous_collaborations', 'international_collaborations',
          'reference1_name', 'reference1_position', 'reference1_email',
          'reference1_phone_country_code', 'reference1_phone_number',
          'reference2_name', 'reference2_position', 'reference2_email',
          'reference2_phone_country_code', 'reference2_phone_number',
          // Location fields
          'address', 'city', 'state_province', 'country', 'postal_code',
          // Goals fields
          'short_term_goals', 'long_term_goals', 'current_challenges',
          'looking_for', 'collaboration_interests', 'sdg_alignment', 'additional_interests'
        ],
        'industry_expert': [
          'profile_name', 'is_public', 'completion_percentage', 'bio', 'website',
          'linkedin', 'twitter', 'facebook', 'instagram',
          'youtube', 'github', 'medium', 'other_social',
          'contact_email', 'contact_phone_country_code', 'contact_phone_number',
          'whatsapp_country_code', 'whatsapp_number', 'telegram', 'skype',
          'preferred_contact_method', 'availability_for_contact',
          // Industry Expert-specific fields
          'industry', 'job_title', 'company', 'years_in_industry',
          'areas_of_expertise', 'industry_specialization', 'certifications',
          'skills', 'languages', 'publications', 'speaking_engagements',
          'notable_projects', 'industry_impact', 'awards',
          'collaboration_types', 'preferred_stages', 'availability',
          'consulting_available', 'mentoring_available', 'international_collaborations',
          // Location fields
          'address', 'city', 'country',
          // Additional fields
          'willing_to_relocate', 'preferred_locations',
          // Goals fields
          'short_term_goals', 'long_term_goals', 'current_challenges',
          'looking_for', 'collaboration_interests', 'sdg_alignment', 'additional_interests'
        ],
        // Add other profile types as needed
      }

      // Default to common fields if profile type is not specifically defined
      const commonFields = ['profile_name', 'is_public', 'bio', 'website',
                           'linkedin', 'twitter', 'facebook', 'instagram',
                           'youtube', 'github', 'medium', 'other_social',
                           'contact_email', 'contact_phone', 'contact_phone_country_code',
                           'contact_phone_number', 'whatsapp', 'whatsapp_country_code',
                           'whatsapp_number', 'telegram', 'skype', 'preferred_contact_method',
                           // Location fields
                           'address', 'city', 'state_province', 'country', 'postal_code',
                           'willing_to_relocate', 'preferred_locations',
                           // Goals fields
                           'short_term_goals', 'long_term_goals', 'current_challenges',
                           'looking_for', 'collaboration_interests', 'sdg_alignment', 'additional_interests']

      // Get the allowed fields for this profile type
      const allowedFields = profileTypeFields[type] || commonFields

      // Special debug logging for organization, investor, and industry expert profiles
      if (type === 'organisation' || type === 'investor' || type === 'industry_expert') {
        console.log(`DEBUG: ${type} profile update - allowed fields:`, allowedFields)
        console.log(`DEBUG: ${type} profile update - input data keys:`, Object.keys(profileData))
      }

      // Copy only the fields that are allowed for this profile type
      for (const key in profileData) {
        if (allowedFields.includes(key)) {
          filteredData[key] = profileData[key]
        } else if (type === 'organisation' || type === 'investor' || type === 'industry_expert') {
          console.log(`DEBUG: Skipping field '${key}' for ${type} profile - not in allowed fields`)
        }
      }

      // Add contact fields from personal details if they're not already set
      if (currentProfile.value) {
        // Map personal details to specialized profile fields
        const { mapPersonalDetailsToSpecialized } = profileService
        const personalDetails = {
          email: currentProfile.value.email,
          phone_country_code: currentProfile.value.phone_country_code,
          phone_number: currentProfile.value.phone_number,
          bio: currentProfile.value.bio
        }

        const mappedFields = mapPersonalDetailsToSpecialized(personalDetails)

        // Only add mapped fields if they're not already set in the filtered data
        for (const key in mappedFields) {
          if (!filteredData[key]) {
            filteredData[key] = mappedFields[key]
            console.log(`DEBUG: Adding mapped field '${key}' from personal details:`, mappedFields[key])
          }
        }
      }

      // Special debug logging for organization, investor, and industry expert profiles
      if (type === 'organisation' || type === 'investor' || type === 'industry_expert') {
        console.log(`DEBUG: ${type} profile update - filtered data keys:`, Object.keys(filteredData))
      }

      console.log(`Filtered profile data for ${type} profile:`, filteredData)

      // CRITICAL DEBUG: Log specific fields we're having trouble with
      console.log('CRITICAL DEBUG - Location fields in filtered data:', {
        address: filteredData.address,
        city: filteredData.city,
        state_province: filteredData.state_province,
        country: filteredData.country,
        postal_code: filteredData.postal_code,
        willing_to_relocate: filteredData.willing_to_relocate,
        preferred_locations: filteredData.preferred_locations
      });

      console.log('CRITICAL DEBUG - Goals fields in filtered data:', {
        short_term_goals: filteredData.short_term_goals,
        long_term_goals: filteredData.long_term_goals,
        current_challenges: filteredData.current_challenges,
        looking_for: filteredData.looking_for,
        collaboration_interests: filteredData.collaboration_interests,
        sdg_alignment: filteredData.sdg_alignment,
        additional_interests: filteredData.additional_interests
      });

      console.log('Calling profileService.updateSpecializedProfile with:', {
        userId,
        type,
        filteredDataKeys: Object.keys(filteredData)
      });

      const updatedProfile = await profileService.updateSpecializedProfile(
        userId,
        type,
        filteredData
      )

      console.log('profileService.updateSpecializedProfile returned:', updatedProfile ? 'success' : 'null');

      if (!updatedProfile) {
        throw new Error(`Failed to update ${type} profile data`)
      }

      // Update local state
      currentSpecializedProfile.value = updatedProfile

      // Update profile completion
      await updateProfileCompletion()

      return true
    } catch (err: any) {
      console.error('Failed to update profile data:', err)

      // Enhanced error logging
      console.error('DEBUG: Error details:', {
        message: err.message,
        stack: err.stack,
        type: err.constructor.name,
        networkStatus: navigator.onLine ? 'Online' : 'Offline'
      })

      // Try to get more details for fetch errors
      if (err.message && err.message.includes('fetch')) {
        console.error('DEBUG: Fetch error detected. This could be due to network issues or CORS problems.')

        // Check if it's a network connectivity issue
        if (!navigator.onLine) {
          error.value = 'Network connection lost. Please check your internet connection and try again.'
          notifications.error('Network connection lost. Please check your internet connection and try again.')
        } else {
          error.value = `Failed to update profile data: ${err.message}. This might be due to network issues.`
          notifications.error(`Failed to update profile data: ${err.message}. This might be due to network issues.`)
        }
      } else {
        error.value = err.message
        notifications.error('Failed to update profile data: ' + err.message)
      }

      return false
    } finally {
      loading.value = false
    }
  }

  /**
   * Update profile visibility - this is now a no-op since all profiles are publicly visible
   * Kept for backward compatibility
   *
   * @param isPublic Whether the profile should be public (ignored)
   * @returns Always returns true
   */
  async function updateProfileVisibility(isPublic: boolean): Promise<boolean> {
    console.log('updateProfileVisibility: This function is now a no-op as all profiles are publicly visible')

    // Always return true since visibility is no longer a concept
    return true
  }

  /**
   * Dismiss the profile completion popup
   */
  function dismissProfileCompletionPopup(): void {
    showProfileCompletionPopup.value = false
  }

  /**
   * Initialize the store
   */
  async function initialize(): Promise<void> {
    if (authStore.isAuthenticated) {
      await loadUserProfiles()
    }
  }

  /**
   * Reset the store state
   */
  function reset(): void {
    currentProfile.value = null
    currentSpecializedProfile.value = null
    userProfiles.value = []
    loading.value = false
    error.value = null
    showProfileCompletionPopup.value = false
  }

  /**
   * Get a user profile by ID
   *
   * @param userId The user ID
   * @returns The user profile
   */
  function getUserProfile(userId: string): BaseProfile | null {
    if (!userId) {
      console.error('getUserProfile: No user ID provided')
      return null
    }

    const profile = userProfiles.value.find(p => p.user_id === userId)

    if (!profile) {
      console.log(`getUserProfile: Profile with ID ${userId} not found in store`)
    }

    return profile || null
  }

  /**
   * Load a profile by ID - centralized method for all components
   * This method will try multiple approaches to load the profile
   *
   * @param userId The user ID
   * @returns The profile data or null if not found
   */
  async function loadProfileById(userId: string): Promise<BaseProfile | null> {
    if (!userId) {
      console.error('loadProfileById: No user ID provided')
      return null
    }

    console.log(`loadProfileById: Starting to load profile for user ${userId}`)
    loading.value = true
    error.value = null

    try {
      console.log(`loadProfileById: Loading profile for user ${userId}`)

      // Step 1: Check if the profile is already in the store
      let profile = getUserProfile(userId)

      if (profile) {
        console.log(`loadProfileById: Profile found in store for user ${userId}`)
        return profile
      }

      // Step 2: Use ProfileManager to load the profile directly
      console.log(`loadProfileById: Using ProfileManager to load profile for user ${userId}`)
      profile = await profileManager.getProfile(userId, { context: 'private' })

      if (profile) {
        console.log(`loadProfileById: Profile loaded via ProfileManager for user ${userId}`)

        // Add this profile to the store for future use
        const existingIndex = userProfiles.value.findIndex(p => p.user_id === userId)
        if (existingIndex >= 0) {
          userProfiles.value[existingIndex] = profile
        } else {
          userProfiles.value.push(profile)
        }

        return profile
      }

      // If we get here, the profile was not found
      console.error(`loadProfileById: Profile not found for user ${userId} after all attempts`)
      return null
    } catch (err) {
      console.error(`loadProfileById: Error:`, err)
      error.value = err.message
      return null
    } finally {
      console.log(`loadProfileById: Finished loading profile for user ${userId}, setting loading to false`)
      loading.value = false
    }
  }



  /**
   * Fetch a profile by ID - alias for loadProfileById for backward compatibility
   *
   * @param userId The user ID
   * @param forceRefresh Whether to force refresh from database
   * @returns The profile data or null if not found
   */
  async function fetchProfile(userId: string, forceRefresh: boolean = false): Promise<BaseProfile | null> {
    console.log('profileStore.fetchProfile called with userId:', userId, 'forceRefresh:', forceRefresh);
    console.log('profileStore.fetchProfile: Current auth state:', {
      isAuthenticated: authStore.isAuthenticated,
      currentUserId: authStore.currentUser?.id
    });
    loading.value = true;
    error.value = null;

    try {
      // Skip store cache if force refresh is requested
      let baseProfile: BaseProfile | null = null;

      if (!forceRefresh) {
        // First try to get the profile from the store
        baseProfile = getUserProfile(userId);

        if (baseProfile) {
          console.log('profileStore.fetchProfile: Profile found in store:', baseProfile);
          // Still set as viewed profile if it's not the current user
          if (!authStore.isAuthenticated || authStore.currentUser?.id !== userId) {
            viewedProfile.value = baseProfile;
            console.log('profileStore.fetchProfile: Set existing profile as viewed profile');
          }
          return baseProfile;
        }
      } else {
        console.log('profileStore.fetchProfile: Force refresh requested, skipping store cache');
      }

      // Use ProfileManager for unified profile loading
      console.log('profileStore.fetchProfile: Using ProfileManager to load profile');
      baseProfile = await profileManager.getProfile(userId, {
        context: 'private',
        forceRefresh
      })

      if (baseProfile) {
        console.log('profileStore.fetchProfile: Profile loaded via ProfileManager:', baseProfile);

            // Add to the store for future use (avoid duplicates)
            const existingIndex = userProfiles.value.findIndex(p => p.user_id === userId);
            if (existingIndex >= 0) {
              userProfiles.value[existingIndex] = baseProfile;
            } else {
              userProfiles.value.push(baseProfile);
            }

            // Set as current profile if it's the current user, otherwise set as viewed profile
            if (authStore.isAuthenticated && authStore.currentUser?.id === userId) {
              console.log('profileStore.fetchProfile: Setting as current profile (same user)');
              currentProfile.value = baseProfile;
            } else {
              // For other users, set as viewed profile
              console.log('profileStore.fetchProfile: Setting as viewed profile (different user)');
              viewedProfile.value = baseProfile;
              console.log('profileStore.fetchProfile: viewedProfile.value set to:', viewedProfile.value);

              // Load specialized profile data for viewing if profile has a type
              if (baseProfile.profile_type) {
                console.log('profileStore.fetchProfile: Loading specialized profile data for public profile type:', baseProfile.profile_type);
                try {
                  const profileService = useProfileService();
                  const specializedProfile = await profileService.loadSpecializedProfile(userId, baseProfile.profile_type);
                  if (specializedProfile) {
                    viewedSpecializedProfile.value = specializedProfile;
                    console.log('profileStore.fetchProfile: Specialized profile loaded for public profile:', specializedProfile);
                  } else {
                    console.log('profileStore.fetchProfile: No specialized profile data found');
                    viewedSpecializedProfile.value = null;
                  }
                } catch (err) {
                  console.error('profileStore.fetchProfile: Error loading specialized profile data for public profile:', err);
                  viewedSpecializedProfile.value = null;
                }
              } else {
                console.log('profileStore.fetchProfile: No profile type, clearing specialized profile');
                viewedSpecializedProfile.value = null;
              }
            }

            console.log('profileStore.fetchProfile: Successfully loaded public profile, returning:', baseProfile);
            return baseProfile;
          } else {
            console.log('profileStore.fetchProfile: fetchPublicProfile returned null/undefined');
          }

      // If public profile not found, try the regular method
      console.log('profileStore.fetchProfile: Public profile fetch failed, trying loadProfileById');
      baseProfile = await loadProfileById(userId);

      if (!baseProfile) {
        console.error('profileStore.fetchProfile: Profile not found in database after all attempts');
        return null;
      }

      console.log('profileStore.fetchProfile: Base profile loaded via loadProfileById:', baseProfile);

      // Only set as current profile if it's the current user
      if (authStore.isAuthenticated && authStore.currentUser?.id === userId) {
        console.log('profileStore.fetchProfile: Setting as current profile (loadProfileById path)');
        currentProfile.value = baseProfile;

        // If the profile has a type, load the specialized profile data
        if (baseProfile.profile_type) {
          console.log('profileStore.fetchProfile: Loading specialized profile data for current user type:', baseProfile.profile_type);
          try {
            await loadSpecializedProfileData(baseProfile.user_id, baseProfile.profile_type);
          } catch (err) {
            console.error('profileStore.fetchProfile: Error loading specialized profile data:', err);
            // Continue anyway, we'll just show the base profile
          }
        }
      } else {
        // For other users, set as viewed profile
        console.log('profileStore.fetchProfile: Setting as viewed profile (loadProfileById path)');
        viewedProfile.value = baseProfile;
        console.log('profileStore.fetchProfile: viewedProfile.value set to:', viewedProfile.value);

        // If the profile has a type, load the specialized profile data for viewing
        if (baseProfile.profile_type) {
          console.log('profileStore.fetchProfile: Loading specialized profile data for viewed user type:', baseProfile.profile_type);
          console.log('profileStore.fetchProfile: Base profile data:', baseProfile);
          try {
            const profileService = useProfileService();
            console.log('profileStore.fetchProfile: About to call loadSpecializedProfile for userId:', userId, 'profileType:', baseProfile.profile_type);
            const specializedProfile = await profileService.loadSpecializedProfile(userId, baseProfile.profile_type);
            console.log('profileStore.fetchProfile: loadSpecializedProfile returned:', specializedProfile);
            if (specializedProfile) {
              viewedSpecializedProfile.value = specializedProfile;
              console.log('profileStore.fetchProfile: Specialized profile loaded for viewed user:', specializedProfile);
              console.log('profileStore.fetchProfile: viewedSpecializedProfile.value is now:', viewedSpecializedProfile.value);
            } else {
              console.log('profileStore.fetchProfile: No specialized profile found for viewed user');
              viewedSpecializedProfile.value = null;
            }
          } catch (err) {
            console.error('profileStore.fetchProfile: Error loading specialized profile data for viewed user:', err);
            console.error('profileStore.fetchProfile: Error details:', err);
            viewedSpecializedProfile.value = null;
            // Continue anyway, we'll just show the base profile
          }
        } else {
          console.log('profileStore.fetchProfile: Base profile has no profile_type, clearing viewedSpecializedProfile');
          viewedSpecializedProfile.value = null;
        }
      }

      return baseProfile;
    } catch (error) {
      console.error('profileStore.fetchProfile: Error fetching profile:', error);
      return null;
    } finally {
      console.log('profileStore.fetchProfile: Finished, setting loading to false');
      loading.value = false;
    }
  }

  /**
   * Load public profiles with at least 50% completion
   *
   * @param page Page number for pagination
   * @param limit Number of profiles per page
   * @param filters Additional filters to apply
   */
  async function loadPublicProfiles(
    page: number = 1,
    limit: number = 12,
    filters: Record<string, any> = {}
  ): Promise<void> {
    loading.value = true
    error.value = null

    try {
      console.log('Loading profiles with at least 50% completion and filters:', filters)

      try {
        // Import the publicProfiles module
        const publicProfilesModule = await import('../services/api/publicProfiles');

        // Use the publicProfiles service to fetch profiles with at least 50% completion
        const result = await publicProfilesModule.loadPublicProfiles(page, limit, filters);

        // Convert the profiles to base profiles
        const profiles = result.profiles.map(p => publicProfilesModule.convertToBaseProfile(p));

        // If this is page 1, replace the profiles array
        // Otherwise, append the new profiles to the existing array
        if (page === 1) {
          publicProfiles.value = profiles
        } else {
          // Append new profiles, avoiding duplicates by checking user_id
          const existingIds = new Set(publicProfiles.value.map(p => p.user_id))
          const newProfiles = profiles.filter(p => !existingIds.has(p.user_id))
          publicProfiles.value = [...publicProfiles.value, ...newProfiles]
        }

        hasMorePublicProfiles.value = result.hasMore

        console.log(`Loaded ${profiles.length} profiles with at least 50% completion, hasMore: ${result.hasMore}`)
      } catch (fetchError) {
        // Handle errors gracefully
        console.error('Error fetching profiles:', fetchError)

        // Set empty profiles but don't show error to user
        if (page === 1) {
          publicProfiles.value = []
        }
        hasMorePublicProfiles.value = false

        // Don't rethrow the error - we're handling it here
      }
    } catch (err: any) {
      console.error('Failed to load profiles:', err.message)
      error.value = err.message
      notifications.error('Failed to load profiles: ' + err.message)
    } finally {
      loading.value = false
    }
  }

  /**
   * Set the specialized profile directly
   *
   * @param profile The specialized profile to set
   * @param isViewed Whether this is for a viewed profile (public context) or current profile (dashboard context)
   */
  function setSpecializedProfile(profile: SpecializedProfile | null, isViewed: boolean = false): void {
    console.log('profileStore.setSpecializedProfile:', profile, 'isViewed:', isViewed)
    if (isViewed) {
      viewedSpecializedProfile.value = profile
    } else {
      currentSpecializedProfile.value = profile
    }
  }

  /**
   * Get the combined viewed profile data (base + specialized)
   *
   * @returns The combined profile data or null if no profile is being viewed
   */
  function getCombinedViewedProfile(): any | null {
    console.log('profileStore.getCombinedViewedProfile: Called');
    console.log('profileStore.getCombinedViewedProfile: viewedProfile:', viewedProfile.value);
    console.log('profileStore.getCombinedViewedProfile: viewedSpecializedProfile:', viewedSpecializedProfile.value);

    if (!viewedProfile.value) {
      console.log('profileStore.getCombinedViewedProfile: No viewed profile available');
      return null;
    }

    const combined = {
      ...viewedProfile.value,
      ...(viewedSpecializedProfile.value || {})
    };

    console.log('profileStore.getCombinedViewedProfile: Combined profile keys:', Object.keys(combined));
    console.log('profileStore.getCombinedViewedProfile: Combined profile sample fields:', {
      user_id: combined.user_id,
      profile_type: combined.profile_type,
      profile_name: combined.profile_name,
      first_name: combined.first_name,
      last_name: combined.last_name,
      bio: combined.bio,
      investment_focus: combined.investment_focus // investor-specific field
    });
    return combined;
  }

  /**
   * Clear the viewed profile data
   */
  function clearViewedProfile(): void {
    viewedProfile.value = null;
    viewedSpecializedProfile.value = null;
  }

  return {
    // State
    currentProfile,
    currentSpecializedProfile,
    viewedProfile,
    viewedSpecializedProfile,
    userProfiles,
    publicProfiles,
    loading,
    error,
    showProfileCompletionPopup,
    hasMorePublicProfiles,

    // Computed properties
    profileCompletion,
    hasIncompleteProfile,
    hasSpecializedProfile,
    hasPartialProfile,
    partialProfileId,
    hasProfile,
    getProfileByUserId,

    // Actions
    loadUserProfiles,
    loadPublicProfiles,
    createProfile,
    updatePersonalDetails,
    updateProfile,
    updateProfileCompletion,
    updateProfileState,
    setCurrentProfile,
    loadSpecializedProfileData,
    updateSpecializedProfileData,
    updateProfileData,
    updateProfileVisibility,
    dismissProfileCompletionPopup,
    initialize,
    reset,
    getUserProfile,
    loadProfileById,
    fetchProfile,
    setSpecializedProfile,
    getCombinedViewedProfile,
    clearViewedProfile,
  }
})
