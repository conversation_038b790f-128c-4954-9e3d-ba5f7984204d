# Post Types UI Design

## Overview

This document outlines the UI design for different post types in the ZB Innovation Hub platform. Each post type has specific UI requirements to effectively communicate its purpose and content.

## Common UI Elements

All post types share these common elements:

- **Author Information**: Avatar, name, and profile type
- **Timestamp**: When the post was created
- **Interaction Buttons**: Like, comment, and share
- **Comments Section**: Expandable section for viewing and adding comments

## Post Type-Specific UI Designs

### 1. General Post

A standard post for general updates and content.

```
┌─────────────────────────────────────────────┐
│ ○ Author Name                    • Time ago │
│ Profile Type                                │
├─────────────────────────────────────────────┤
│                                             │
│ Post content goes here. This can be text    │
│ only or include images.                     │
│                                             │
│ [Optional Image]                            │
│                                             │
├─────────────────────────────────────────────┤
│ ♥ Like (count) | 💬 Comment (count) | Share │
│                                   | Save    │
└─────────────────────────────────────────────┘
```

**Key Features:**
- Clean, simple layout
- Optional image attachment
- Standard interaction buttons
- Save option for later reference

### 2. Opportunity Post

Posts about specific opportunities (funding, collaboration, mentorship).

```
┌─────────────────────────────────────────────┐
│ ○ Author Name                    • Time ago │
│ Profile Type                                │
│ [OPPORTUNITY BADGE]                         │
├─────────────────────────────────────────────┤
│ Title of the Opportunity                    │
│                                             │
│ Post content describing the opportunity     │
│ with relevant details.                      │
│                                             │
│ [Optional Image]                            │
│                                             │
│ Deadline: [Date if applicable]              │
│                                             │
├─────────────────────────────────────────────┤
│ ♥ Like | 💬 Comment | Share | 📝 Apply      │
│                              | 📞 Contact   │
│                              | 💾 Save      │
└─────────────────────────────────────────────┘
```

**Key Features:**
- Prominent opportunity type badge (color-coded)
- Title field
- Optional deadline field
- Additional "Apply" button when applicable
- Contact button for direct communication
- Save option for later reference
- Structured format for opportunity details

### 3. Blog Article

Longer-form content with a title, excerpt, and category.

```
┌─────────────────────────────────────────────┐
│ ○ Author Name                    • Time ago │
│ Profile Type                                │
│ [BLOG ARTICLE] [Category Badge]             │
├─────────────────────────────────────────────┤
│ [Featured Image - Larger than other posts]  │
│                                             │
│ Title of the Blog Article                   │
│                                             │
│ Excerpt or introduction to the article...   │
│                                             │
│ Read Time: X min                            │
│                                             │
├─────────────────────────────────────────────┤
│ ♥ Like | 💬 Comment | Share | 📖 Read More  │
│                              | 💾 Save      │
└─────────────────────────────────────────────┘
```

**Key Features:**
- Prominent featured image
- Clear title and excerpt
- Category badge
- Read time indicator
- "Read More" button leading to full article page
- Save option for later reference

### 4. Event Post

Posts announcing events with specific details.

```
┌─────────────────────────────────────────────┐
│ ○ Author Name                    • Time ago │
│ Profile Type                                │
│ [EVENT] [Physical/Virtual Badge]            │
├─────────────────────────────────────────────┤
│ [Event Image/Banner]                        │
│                                             │
│ Event Title                                 │
│                                             │
│ 📅 Date: [Date and Time]                    │
│ 📍 Location: [Physical location or "Online"] │
│ 🏷️ Theme: [Event Theme]                     │
│                                             │
│ Event description and details...            │
│                                             │
├─────────────────────────────────────────────┤
│ ♥ Like | 💬 Comment | Share | 📝 Register   │
│                              | 📅 Add to Cal│
│                              | 🔔 Remind Me │
└─────────────────────────────────────────────┘
```

**Key Features:**
- Event-specific details (date, location, theme)
- Physical/Virtual badge
- Event banner image
- "Register" button with link to registration
- Add to Calendar integration
- Reminder functionality
- Structured format for event information

### 5. Resource Post

Posts sharing valuable resources for the innovation community.

```
┌─────────────────────────────────────────────┐
│ ○ Author Name                    • Time ago │
│ Profile Type                                │
│ [RESOURCE] [Resource Type Badge]            │
├─────────────────────────────────────────────┤
│ Title of the Resource                       │
│                                             │
│ Description of the resource and its value   │
│ to the community.                           │
│                                             │
│ [Resource Preview/Thumbnail]                │
│                                             │
│ File Type: PDF | Size: 2.4 MB               │
│                                             │
├─────────────────────────────────────────────┤
│ ♥ Like | 💬 Comment | Share | ⬇️ Download   │
│                              | 💾 Save      │
└─────────────────────────────────────────────┘
```

**Key Features:**
- Resource type badge
- File information (type, size)
- Resource preview when possible
- Download button
- Save option for later reference

### 6. Success Story Post

Posts highlighting achievements within the community.

```
┌─────────────────────────────────────────────┐
│ ○ Author Name                    • Time ago │
│ Profile Type                                │
│ [SUCCESS STORY]                             │
├─────────────────────────────────────────────┤
│ Title of the Success Story                  │
│                                             │
│ Description of the achievement with details │
│ about the journey and results.              │
│                                             │
│ [Image showcasing the success]              │
│                                             │
│ 🏆 Achievement: [Type of achievement]        │
│                                             │
├─────────────────────────────────────────────┤
│ ♥ Like | 💬 Comment | Share | 👏 Congratulate│
│                              | 🔗 Connect   │
└─────────────────────────────────────────────┘
```

**Key Features:**
- Success story badge
- Achievement highlight
- Congratulate button
- Connect button for networking
- Visual showcase of the achievement

### 7. Question/Help Post

Posts from community members seeking advice or assistance.

```
┌─────────────────────────────────────────────┐
│ ○ Author Name                    • Time ago │
│ Profile Type                                │
│ [QUESTION] [Topic Badge]                    │
├─────────────────────────────────────────────┤
│ Title of the Question                       │
│                                             │
│ Detailed description of the problem or      │
│ question requiring assistance.              │
│                                             │
│ [Optional supporting image]                 │
│                                             │
│ Tags: #tag1 #tag2 #tag3                     │
│                                             │
├─────────────────────────────────────────────┤
│ ♥ Like | 💬 Comment | Share | ✍️ Answer     │
│                              | 🤝 Offer Help│
└─────────────────────────────────────────────┘
```

**Key Features:**
- Question badge
- Topic categorization
- Relevant tags
- Answer button for direct responses
- Offer Help button for private assistance

### 8. Job/Talent Post

Posts about employment opportunities or talent availability.

```
┌─────────────────────────────────────────────┐
│ ○ Author Name                    • Time ago │
│ Profile Type                                │
│ [JOB OPPORTUNITY] [Job Type Badge]          │
├─────────────────────────────────────────────┤
│ Job Title                                   │
│                                             │
│ Company/Organization                        │
│                                             │
│ 📍 Location: [Location or "Remote"]          │
│ 💼 Type: [Full-time/Part-time/Contract]     │
│ 💰 Compensation: [If disclosed]             │
│                                             │
│ Job description and requirements...         │
│                                             │
├─────────────────────────────────────────────┤
│ ♥ Like | 💬 Comment | Share | 📝 Apply      │
│                              | 📞 Contact   │
│                              | 💾 Save Job  │
└─────────────────────────────────────────────┘
```

**Key Features:**
- Job type badge
- Structured job details
- Apply button
- Contact button for inquiries
- Save Job option

### 9. Innovation Challenge Post

Posts about challenges seeking innovative solutions.

```
┌─────────────────────────────────────────────┐
│ ○ Author Name                    • Time ago │
│ Profile Type                                │
│ [INNOVATION CHALLENGE]                      │
├─────────────────────────────────────────────┤
│ Challenge Title                             │
│                                             │
│ Description of the challenge and problem    │
│ to be solved.                               │
│                                             │
│ [Challenge Image]                           │
│                                             │
│ 🏆 Prize: [Prize details if applicable]      │
│ ⏱️ Deadline: [Submission deadline]           │
│                                             │
├─────────────────────────────────────────────┤
│ ♥ Like | 💬 Comment | Share | 💡 Submit     │
│                              | ℹ️ Learn More│
└─────────────────────────────────────────────┘
```

**Key Features:**
- Challenge badge
- Prize information
- Deadline for submissions
- Submit button for solutions
- Learn More button for detailed information

### 10. Automated System Post

System-generated posts based on user activity.

```
┌─────────────────────────────────────────────┐
│ ⚙️ System Notification             • Time ago │
├─────────────────────────────────────────────┤
│ [Icon relevant to the notification]         │
│                                             │
│ Automated message about user activity:      │
│ - Profile creation                          │
│ - Profile completion                        │
│ - Group creation                            │
│ - New marketplace listing                   │
│                                             │
│ [Link to relevant profile/group/listing]    │
│                                             │
├─────────────────────────────────────────────┤
│ ♥ Like | 💬 Comment | 👁️ View               │
└─────────────────────────────────────────────┘
```

**Key Features:**
- Distinct system notification styling
- Icon representing the type of activity
- Link to the relevant entity
- View button to see the referenced item
- Simpler interaction options

### 11. Admin Announcement

Official platform announcements and notifications.

```
┌─────────────────────────────────────────────┐
│ 📢 ZB Innovation Hub                • Time ago │
│ [ANNOUNCEMENT]                              │
├─────────────────────────────────────────────┤
│ Title of the Announcement                   │
│                                             │
│ Announcement content with important         │
│ information for platform users.             │
│                                             │
│ [Optional Image]                            │
│                                             │
├─────────────────────────────────────────────┤
│ ♥ Like | 💬 Comment | Share | 📌 Pin        │
│                              | ✓ Mark Read  │
└─────────────────────────────────────────────┘
```

**Key Features:**
- Official branding
- Announcement badge
- Option to pin important announcements
- Mark as Read functionality
- Distinct styling to stand out in the feed

## Responsive Design Considerations

- All post types should adapt to different screen sizes
- On mobile, interaction buttons may use icons only to save space
- Images should be responsive and load appropriately for device size
- Expandable sections (like comments) should be easy to toggle on mobile

## Color Coding System

- **Funding Opportunities**: Green (#0D8A3E)
- **Collaboration Opportunities**: Blue (#2196F3)
- **Mentorship Opportunities**: Purple (#9C27B0)
- **Events**: Orange (#FF9800)
- **Blog Articles**: Teal (#009688)
- **Resource Posts**: Deep Purple (#673AB7)
- **Success Story Posts**: Green (#4CAF50)
- **Question/Help Posts**: Amber (#FFC107)
- **Job/Talent Posts**: Indigo (#3F51B5)
- **Innovation Challenge Posts**: Deep Orange (#FF5722)
- **System Notifications**: Grey (#757575)
- **Admin Announcements**: Red (#F44336)

## Next Steps

1. Create component prototypes for each post type
2. Test with sample content
3. Gather feedback on usability and clarity
4. Refine designs based on feedback
5. Implement final designs in Vue components
