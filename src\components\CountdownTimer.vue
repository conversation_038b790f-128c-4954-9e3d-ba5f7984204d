<template>
  <div class="countdown-container">
    <Countdown
      :deadline-ISO="targetDate"
      :mainColor="mainColor"
      :labelColor="labelColor"
      :countdown-size="countdownSize"
      :label-size="labelSize"
      :show-days="true"
      :show-hours="true"
      :show-minutes="true"
      :show-seconds="true"
      @timeElapsed="() => {}"
    />
  </div>
</template>

<script setup lang="ts" name="CountdownTimer">
import { Countdown } from 'vue3-flip-countdown'

// Remove defineOptions as it might not be supported in this Vue version
// and use the script name attribute instead

const props = defineProps({
  targetDate: {
    type: String,
    required: true
  },
  mainColor: {
    type: String,
    default: '#74b524'
  },
  labelColor: {
    type: String,
    default: '#245926'
  },
  countdownSize: {
    type: String,
    default: '3.5rem'
  },
  labelSize: {
    type: String,
    default: '1.1rem'
  }
});
</script>

<style scoped>
.countdown-container {
  padding: 0;
  width: 100%;
}

:deep(.flip-countdown) {
  display: flex;
  justify-content: flex-start;
  gap: 0.5rem;
  width: 100%;
  flex-wrap: nowrap !important;
}

:deep(.flip-countdown .flip-card) {
  background: white !important;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 0.5rem;
}

@media (max-width: 599px) {
  :deep(.flip-countdown) {
    flex-wrap: nowrap !important;
    gap: 0.25rem;
    justify-content: flex-start;
  }

  :deep(.flip-countdown .flip-card) {
    padding: 0.25rem;
  }

  :deep(.flip-countdown .flip-card .flip-card-label) {
    font-size: 0.7rem !important;
  }
}

/* Force all time elements to stay on the same line */
:deep(.flip-countdown) {
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
}

/* Adjust card sizes for smaller screens */
@media (max-width: 480px) {
  :deep(.flip-countdown .flip-card) {
    transform: scale(0.9);
    margin: -2px;
  }
}
</style>