# Post Creation UI Design

## Overview

This document outlines the UI design for creating different types of posts in the ZB Innovation Hub platform. It provides mockups and specifications for the post creation interface.

## Post Creation Flow

### Step 1: Post Type Selection

When a user initiates post creation, they first select the type of post they want to create:

```
┌─────────────────────────────────────────────┐
│ Create New Post                             │
├─────────────────────────────────────────────┤
│                                             │
│ Select Post Type:                           │
│                                             │
│ ○ General Post                              │
│ ○ Opportunity                               │
│   ├─ ○ Funding Opportunity                  │
│   ├─ ○ Collaboration Opportunity            │
│   └─ ○ Mentorship Opportunity               │
│ ○ Blog Article                              │
│ ○ Event                                     │
│                                             │
│                                             │
│ [Cancel]                [Continue]          │
└─────────────────────────────────────────────┘
```

### Step 2: Dynamic Form Based on Post Type

#### General Post Form

```
┌─────────────────────────────────────────────┐
│ Create General Post                         │
├─────────────────────────────────────────────┤
│                                             │
│ [Content Text Area]                         │
│ What's on your mind?                        │
│                                             │
│                                             │
│                                             │
│ [📎 Add Image]                              │
│                                             │
│ Visibility: [Public ▼]                      │
│                                             │
│ [Cancel]                [Post]              │
└─────────────────────────────────────────────┘
```

#### Opportunity Post Form

```
┌─────────────────────────────────────────────┐
│ Create Funding Opportunity                  │
├─────────────────────────────────────────────┤
│                                             │
│ Title: [                                  ] │
│                                             │
│ Description:                                │
│ [Content Text Area]                         │
│                                             │
│                                             │
│ Category: [Select Category ▼]               │
│                                             │
│ Deadline: [Date Picker]                     │
│                                             │
│ Application URL: [                        ] │
│                                             │
│ [📎 Add Image]                              │
│                                             │
│ Visibility: [Public ▼]                      │
│                                             │
│ [Cancel]                [Post]              │
└─────────────────────────────────────────────┘
```

#### Blog Article Form

```
┌─────────────────────────────────────────────┐
│ Create Blog Article                         │
├─────────────────────────────────────────────┤
│                                             │
│ Title: [                                  ] │
│                                             │
│ Excerpt/Summary:                            │
│ [                                         ] │
│                                             │
│ Content:                                    │
│ [Rich Text Editor]                          │
│                                             │
│                                             │
│                                             │
│ Category: [Select Category ▼]               │
│                                             │
│ Tags: [                                   ] │
│                                             │
│ [📎 Add Cover Image]                        │
│                                             │
│ Visibility: [Public ▼]                      │
│                                             │
│ [Cancel]     [Save Draft]     [Publish]     │
└─────────────────────────────────────────────┘
```

#### Event Post Form

```
┌─────────────────────────────────────────────┐
│ Create Event                                │
├─────────────────────────────────────────────┤
│                                             │
│ Title: [                                  ] │
│                                             │
│ Description:                                │
│ [Content Text Area]                         │
│                                             │
│                                             │
│ Event Type: ○ Physical  ○ Virtual           │
│                                             │
│ Date & Time: [Date/Time Picker]             │
│                                             │
│ Location: [                               ] │
│                                             │
│ Theme: [Select Theme ▼]                     │
│                                             │
│ Registration URL: [                       ] │
│                                             │
│ [📎 Add Event Image]                        │
│                                             │
│ Visibility: [Public ▼]                      │
│                                             │
│ [Cancel]                [Post]              │
└─────────────────────────────────────────────┘
```

## Form Validation Rules

### General Post
- Content is required
- Maximum content length: 2000 characters

### Opportunity Post
- Title is required (max 100 characters)
- Description is required (max 2000 characters)
- Category is required
- Application URL must be a valid URL (if provided)

### Blog Article
- Title is required (max 100 characters)
- Excerpt is required (max 200 characters)
- Content is required
- Category is required
- Tags should be comma-separated

### Event Post
- Title is required (max 100 characters)
- Description is required (max 2000 characters)
- Event type is required
- Date & time is required
- Location is required
- Registration URL must be a valid URL (if provided)

## UI Components

### Rich Text Editor

For blog articles, a rich text editor will be provided with the following features:
- Basic formatting (bold, italic, underline)
- Headings (H1, H2, H3)
- Lists (ordered and unordered)
- Links
- Images
- Code blocks
- Quotes

### Image Upload

All post types support image uploads with the following features:
- Drag and drop functionality
- File size limit: 5MB
- Supported formats: JPG, PNG, GIF
- Image preview before posting
- Option to remove uploaded image

### Date and Time Pickers

For events and opportunities with deadlines:
- Calendar date picker
- Time picker with hour and minute selection
- Timezone selection

## Mobile Responsiveness

The post creation UI will adapt to different screen sizes:

### Mobile View
- Full-screen modal for post creation
- Simplified controls
- Collapsible sections for advanced options
- Bottom-fixed action buttons

### Tablet View
- Centered modal with responsive width
- Optimized input fields for touch interaction

### Desktop View
- Wide modal with full feature set
- Side-by-side layout for some sections

## Accessibility Considerations

- All form fields have proper labels
- Error messages are clear and descriptive
- Keyboard navigation is supported
- Color contrast meets WCAG standards
- Screen reader compatibility

## Implementation Notes

1. Use Quasar form components for consistent styling
2. Implement form validation using Vuelidate or similar
3. Use Quasar's responsive utilities for adaptive layouts
4. Store draft posts locally before submission
5. Provide clear feedback during image upload process
