# Clean Slate Database Implementation

This directory contains files related to the clean slate database implementation.

## Overview

The clean slate database implementation is designed to provide a more flexible approach to profile creation and editing, where users can fill in only the fields they want to provide. It also removes default values from the database schema to avoid issues with updating values.

## Files

### `cleanSlateDatabase.ts`

This file contains functions to create a clean slate database schema with tables that allow NULL values and follow a consistent naming convention.

```typescript
import { createCleanSlateDatabase } from './cleanSlateDatabase';

// Create a clean slate database
const result = await createCleanSlateDatabase();
if (result.success) {
  console.log('Clean slate database created successfully');
} else {
  console.error('Failed to create clean slate database:', result.message);
}
```

### `disableDatabaseDebugging.ts`

This file contains functions to disable database debugging functions temporarily while we implement a clean slate approach for the database.

```typescript
import { disableDatabaseDebugging } from './disableDatabaseDebugging';

// Disable database debugging functions
const result = await disableDatabaseDebugging();
if (result.success) {
  console.log('Database debugging functions disabled successfully');
} else {
  console.error('Failed to disable database debugging:', result.message);
}
```

### `handleNullValues.ts`

This file contains utility functions for handling NULL values in the database.

```typescript
import { sanitizeProfileData, sanitizeProfileTypeData } from './handleNullValues';

// Sanitize profile data to handle NULL values
const sanitizedProfile = sanitizeProfileData(profile);

// Sanitize profile-specific data to handle NULL values
const sanitizedProfileData = sanitizeProfileTypeData(profileType, data);
```

## Database Management Component

A database management component is available in the `src/components/dev/DatabaseManagement.vue` file. This component provides tools for managing the database during development, including:

- Disabling database debugging functions
- Creating a clean slate database
- Refreshing the schema cache
- Checking the database structure

## Profile Store with NULL Value Handling

The unified profile store in `src/stores/unifiedProfile.ts` (exported via `profile.ts`) now handles NULL values properly. It incorporates the best features from previous implementations and is designed to work with the clean slate database approach.

## Documentation

For more information about the database structure, see the `docs/database-structure.md` file.
