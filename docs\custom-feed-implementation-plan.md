# Custom Feed Implementation Plan

## Overview

This document outlines the strategy and implementation plan for creating a personalized feed system in the ZB Innovation Hub platform. The custom feed will deliver relevant content to users based on their profile type, interests, network, and activity, enhancing user engagement and platform value.

## Current State Analysis

### Database Structure

The Supabase database currently has:

- **Posts Table**: Comprehensive structure supporting different post types
  - Core fields: `id`, `user_id`, `post_type`, `sub_type`, `content`, `media_urls`, `tags`, etc.
  - Specialized fields for blogs, events, announcements, etc.

- **Post Types and Subtypes**:
  - `platform` with subtypes: `event`, `blog_article`, `general`
  - `admin` with subtype: `announcement`

- **User Profile Structure**:
  - Base profile table: `personal_details`
  - Specialized profile tables for different user types:
    - `innovator_profiles`
    - `investor_profiles`
    - `mentor_profiles`
    - `industry_expert_profiles`
    - `academic_student_profiles`
    - `academic_institution_profiles`
    - `organisation_profiles`
    - `professional_profiles`

- **Related Tables**:
  - `comments` and `comments_with_authors`
  - `likes` and `likes_with_authors`
  - `posts_with_authors` view

### Current Implementation

- **Post Store**: Pinia store with CRUD operations and basic filtering
- **Feed UI**: Tabbed interface with sections for Feed, Profiles, Blog, Events, etc.
- **Filter Implementation**: Filter store with tab-specific filters

## User Types and Their Needs

1. **Innovators**:
   - Interested in: funding opportunities, mentorship, collaboration
   - Need to connect with: investors, mentors

2. **Investors**:
   - Interested in: promising innovations, investment opportunities, success stories
   - Need to connect with: innovators with funding needs

3. **Mentors**:
   - Interested in: innovators seeking guidance, industry trends
   - Need to connect with: innovators needing mentorship

4. **Industry Experts**:
   - Interested in: industry trends, innovations in their field
   - Need to connect with: organizations, innovators

5. **Academic Students**:
   - Interested in: learning opportunities, resources, events
   - Need to connect with: mentors, potential employers

6. **Academic Institutions**:
   - Interested in: partnerships, collaborations, research opportunities
   - Need to connect with: students, industry, other institutions

7. **Organizations**:
   - Interested in: innovations, talent, industry news
   - Need to connect with: innovators, experts, other organizations

8. **Professionals**:
   - Interested in: career opportunities, networking, professional development
   - Need to connect with: organizations, peers

## Custom Feed Implementation Strategy

### A. Feed Personalization Approach

1. **Profile-Based Content Filtering**
   - Filter content based on user's profile type
   - Prioritize content relevant to user's interests and needs
   - Use profile fields to match with post content

2. **Interest-Based Recommendations**
   - Use post tags to match with user interests
   - Implement recommendation algorithm
   - Prioritize posts with matching tags

3. **Network-Based Content**
   - Show content from users the current user follows or interacts with
   - Prioritize posts from users in the same industry
   - Implement connection strength scoring

4. **Activity-Based Personalization**
   - Track user interactions (likes, comments, views)
   - Use interaction data to refine recommendations
   - Implement relevance scoring

### B. Technical Implementation

1. **Enhanced Database Structure**

```sql
-- User interests table
CREATE TABLE IF NOT EXISTS public.user_interests (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  interest_type VARCHAR(50) NOT NULL,
  interest_value VARCHAR(100) NOT NULL,
  weight FLOAT DEFAULT 1.0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, interest_type, interest_value)
);

-- User connections table
CREATE TABLE IF NOT EXISTS public.user_connections (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  connected_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  connection_type VARCHAR(50) NOT NULL,
  connection_strength FLOAT DEFAULT 1.0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, connected_user_id)
);

-- User post interactions table
CREATE TABLE IF NOT EXISTS public.user_post_interactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  post_id BIGINT REFERENCES public.posts(id) ON DELETE CASCADE,
  interaction_type VARCHAR(50) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, post_id, interaction_type)
);

-- Personalized feed view
CREATE OR REPLACE VIEW public.personalized_feed_view AS
SELECT 
  p.*,
  pd.first_name,
  pd.last_name,
  pd.email,
  pd.profile_type,
  COALESCE(upi.interaction_score, 0) as interaction_score,
  COALESCE(uc.connection_strength, 0) as connection_strength,
  COALESCE(ui.interest_match, 0) as interest_match,
  (COALESCE(upi.interaction_score, 0) + 
   COALESCE(uc.connection_strength, 0) + 
   COALESCE(ui.interest_match, 0)) as relevance_score
FROM 
  posts p
  LEFT JOIN personal_details pd ON p.user_id = pd.user_id
  LEFT JOIN (
    -- Interaction score calculation
    SELECT 
      post_id, 
      user_id,
      SUM(CASE 
        WHEN interaction_type = 'view' THEN 0.5
        WHEN interaction_type = 'like' THEN 1.0
        WHEN interaction_type = 'comment' THEN 2.0
        WHEN interaction_type = 'share' THEN 3.0
        ELSE 0
      END) as interaction_score
    FROM user_post_interactions
    GROUP BY post_id, user_id
  ) upi ON p.id = upi.post_id
  LEFT JOIN (
    -- Connection strength
    SELECT 
      connected_user_id,
      user_id,
      connection_strength
    FROM user_connections
  ) uc ON p.user_id = uc.connected_user_id
  LEFT JOIN (
    -- Interest matching
    SELECT 
      p.id as post_id,
      ui.user_id,
      SUM(ui.weight) as interest_match
    FROM posts p
    CROSS JOIN user_interests ui
    WHERE 
      (p.tags && ARRAY[ui.interest_value]) OR
      (p.post_type = ui.interest_type) OR
      (p.sub_type = ui.interest_value)
    GROUP BY p.id, ui.user_id
  ) ui ON p.id = ui.post_id AND upi.user_id = ui.user_id;
```

2. **Core Components**

- **Personalized Posts Store**: Enhanced store with personalization features
- **User Interests Service**: Service for managing user interests
- **Profile Interest Extractor**: Utility for extracting interests from profiles
- **Personalized Feed Component**: UI component for displaying the custom feed

3. **Key Features**

- **Feed Mode Toggle**: Switch between personalized, latest, and popular feeds
- **Interest Management**: UI for users to manage their interests
- **Interaction Tracking**: System to track and learn from user interactions
- **Relevance Scoring**: Algorithm to calculate content relevance for each user

### C. User-Specific Feed Strategies

1. **For Innovators**
   - Prioritize: funding opportunities, mentorship offers, collaboration requests
   - Show: success stories from similar innovation areas
   - Highlight: events related to their innovation stage

2. **For Investors**
   - Prioritize: innovations in their investment areas
   - Show: success stories and investment opportunities
   - Highlight: events related to investment and funding

3. **For Mentors**
   - Prioritize: innovators seeking mentorship in their expertise areas
   - Show: success stories related to mentorship
   - Highlight: events and resources for mentors

4. **For Academic Students**
   - Prioritize: educational content and resources
   - Show: internship and job opportunities
   - Highlight: events and programs for students

5. **For Organizations**
   - Prioritize: innovations and talent in their industry
   - Show: industry news and events
   - Highlight: collaboration opportunities

### D. Implementation Phases

#### Phase 1: Basic Personalization (2 weeks)
- Create database tables for user interests and interactions
- Implement interest extraction from user profiles
- Create basic personalized feed component
- Implement profile-based content filtering

#### Phase 2: Interaction Tracking (2 weeks)
- Implement user interaction tracking
- Create relevance scoring algorithm
- Update feed to use relevance scores
- Implement feed mode toggle

#### Phase 3: Advanced Personalization (3 weeks)
- Implement network-based content recommendations
- Create user connections table and management
- Implement interest learning from interactions
- Refine relevance scoring algorithm

#### Phase 4: User-Specific Strategies (3 weeks)
- Implement user-specific feed strategies
- Create specialized feed components for each user type
- Implement A/B testing for feed algorithms
- Gather and analyze user feedback

## Success Metrics

1. **Engagement Metrics**
   - Increase in time spent on platform
   - Increase in post interactions (views, likes, comments)
   - Decrease in bounce rate

2. **User Satisfaction**
   - Positive feedback on feed relevance
   - Increased return rate
   - Higher user retention

3. **Platform Growth**
   - Increase in user-generated content
   - Growth in user connections
   - Increase in new user registrations

## Implementation Recommendations

1. **Start with Data Collection**
   - Begin tracking user interactions
   - Extract interests from existing profiles
   - Collect engagement metrics as baseline

2. **Implement Basic Personalization First**
   - Start with simple profile-type matching
   - Use tags for basic interest matching
   - Provide toggle between personalized and chronological feeds

3. **Gradually Enhance the Algorithm**
   - Start with a simple scoring system
   - Refine based on user feedback
   - Implement A/B testing

4. **Optimize Database Queries**
   - Use materialized views for complex calculations
   - Implement caching for frequently accessed data
   - Consider database functions for scoring

5. **Provide User Control**
   - Allow users to set interests explicitly
   - Provide feedback mechanisms
   - Allow toggling between feed modes

6. **Monitor and Iterate**
   - Track engagement metrics
   - Gather user feedback
   - Continuously refine the algorithm

## Next Steps

1. Create database migration scripts for new tables
2. Implement user interest extraction from profiles
3. Develop personalized feed store and component
4. Set up interaction tracking
5. Begin testing with a small user group

## References

- [Content Database Schema](./content-database-schema.md)
- [Content Management Plan](./content-management-plan.md)
- [Feed Component Structure](./feed-component-structure.md)
