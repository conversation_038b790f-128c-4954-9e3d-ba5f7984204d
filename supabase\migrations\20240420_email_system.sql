-- Create table for email logging
CREATE TABLE IF NOT EXISTS public.email_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL,
  email_type TEXT NOT NULL,
  status TEXT NOT NULL,
  result JSONB,
  sent_at TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add last_login column to personal_details if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                WHERE table_schema = 'public'
                AND table_name = 'personal_details'
                AND column_name = 'last_login') THEN
    ALTER TABLE public.personal_details ADD COLUMN last_login TIMESTAMP WITH TIME ZONE;
  END IF;
END
$$;

-- <PERSON>reate function to update last_login timestamp
CREATE OR REPLACE FUNCTION public.update_last_login()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE public.personal_details
  SET last_login = now()
  WHERE user_id = auth.uid();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to update last_login on auth.users sign in
DROP TRIGGER IF EXISTS update_last_login_trigger ON auth.users;
CREATE TRIGGER update_last_login_trigger
AFTER UPDATE OF last_sign_in_at ON auth.users
FOR EACH ROW
EXECUTE FUNCTION public.update_last_login();

-- Create function to handle user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert into personal_details if not exists
  INSERT INTO public.personal_details (user_id, email, profile_state, profile_completion, created_at, updated_at, last_login)
  VALUES (NEW.id, NEW.email, 'DRAFT', 0, now(), now(), now())
  ON CONFLICT (user_id) DO NOTHING;

  -- Call the auth-trigger function to send welcome email
  PERFORM net.http_post(
    url := CONCAT(current_setting('supabase_functions_endpoint', true), '/auth-trigger'),
    headers := jsonb_build_object(
      'Content-Type', 'application/json',
      'Authorization', CONCAT('Bearer ', current_setting('supabase.service_key', true))
    ),
    body := jsonb_build_object(
      'type', 'INSERT',
      'table', 'auth.users',
      'record', jsonb_build_object(
        'id', NEW.id,
        'email', NEW.email,
        'user_metadata', NEW.raw_user_meta_data
      )
    )
  );

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
AFTER INSERT ON auth.users
FOR EACH ROW
EXECUTE FUNCTION public.handle_new_user();

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON public.email_logs TO authenticated, service_role;
GRANT SELECT, INSERT, UPDATE ON public.personal_details TO authenticated, service_role;
