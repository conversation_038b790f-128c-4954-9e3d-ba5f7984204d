/**
 * Matchmaking Utility Functions
 * 
 * This file contains utility functions for the matchmaking system.
 * These functions are used to calculate scores between different entities.
 */

import { SupabaseClient } from '@supabase/supabase-js';

/**
 * Calculate the intersection score between two arrays
 * @param array1 First array
 * @param array2 Second array
 * @returns Score between 0 and 1 representing the normalized intersection
 */
export function arrayIntersectionScore(array1: any[] | null, array2: any[] | null): number {
  if (!array1 || !array2 || array1.length === 0 || array2.length === 0) {
    return 0;
  }
  
  const intersection = array1.filter(item => array2.includes(item));
  return intersection.length / Math.sqrt(array1.length * array2.length);
}

/**
 * Calculate the similarity score between two text values
 * @param text1 First text
 * @param text2 Second text
 * @returns Score between 0 and 1 representing the similarity
 */
export function textSimilarityScore(text1: string | null, text2: string | null): number {
  if (!text1 || !text2) {
    return 0;
  }
  
  // Simple implementation - can be enhanced with more sophisticated text similarity algorithms
  const normalizedText1 = text1.toLowerCase();
  const normalizedText2 = text2.toLowerCase();
  
  // Check for exact match
  if (normalizedText1 === normalizedText2) {
    return 1;
  }
  
  // Check if one contains the other
  if (normalizedText1.includes(normalizedText2) || normalizedText2.includes(normalizedText1)) {
    return 0.7;
  }
  
  // Split into words and check for word overlap
  const words1 = normalizedText1.split(/\s+/);
  const words2 = normalizedText2.split(/\s+/);
  
  return arrayIntersectionScore(words1, words2);
}

/**
 * Calculate the match score for a numeric value within a range
 * @param value The value to check
 * @param range The range object with min and max properties
 * @returns Score between 0 and 1 representing how well the value fits in the range
 */
export function numericRangeMatch(value: number | null, range: { min?: number, max?: number }): number {
  if (value === null) {
    return 0;
  }
  
  // If no range is specified, return 0.5 as a neutral score
  if (range.min === undefined && range.max === undefined) {
    return 0.5;
  }
  
  // If only min is specified
  if (range.min !== undefined && range.max === undefined) {
    return value >= range.min ? 1 : 0;
  }
  
  // If only max is specified
  if (range.min === undefined && range.max !== undefined) {
    return value <= range.max ? 1 : 0;
  }
  
  // If both min and max are specified
  if (range.min !== undefined && range.max !== undefined) {
    if (value < range.min || value > range.max) {
      return 0;
    }
    
    // Calculate how centered the value is in the range
    const rangeSize = range.max - range.min;
    const distanceFromMin = value - range.min;
    const normalizedPosition = distanceFromMin / rangeSize;
    
    // Score is highest (1.0) when in the middle of the range
    return 1 - Math.abs(normalizedPosition - 0.5) * 2;
  }
  
  return 0;
}

/**
 * Calculate the compatibility score between two stage values
 * @param stage1 First stage
 * @param stage2 Second stage or array of stages
 * @returns Score between 0 and 1 representing compatibility
 */
export function stageCompatibilityScore(stage1: string | null, stage2: string | string[] | null): number {
  if (!stage1 || !stage2) {
    return 0;
  }
  
  // If stage2 is an array, check if stage1 is in it
  if (Array.isArray(stage2)) {
    return stage2.includes(stage1) ? 1 : 0;
  }
  
  // Define stage progression for compatibility scoring
  const stages = [
    'idea', 
    'concept', 
    'prototype', 
    'validation', 
    'early_growth', 
    'scaling', 
    'established'
  ];
  
  const index1 = stages.indexOf(stage1);
  const index2 = stages.indexOf(stage2);
  
  // If either stage is not in our defined progression, use text similarity
  if (index1 === -1 || index2 === -1) {
    return textSimilarityScore(stage1, stage2);
  }
  
  // Calculate distance between stages
  const distance = Math.abs(index1 - index2);
  const maxDistance = stages.length - 1;
  
  // Convert to a score where 0 is furthest apart and 1 is same stage
  return 1 - (distance / maxDistance);
}

/**
 * Calculate the compatibility score between approach values
 * @param approach1 First approach
 * @param approach2 Second approach
 * @returns Score between 0 and 1 representing compatibility
 */
export function approachCompatibilityScore(approach1: any, approach2: any): number {
  // If both are strings, use text similarity
  if (typeof approach1 === 'string' && typeof approach2 === 'string') {
    return textSimilarityScore(approach1, approach2);
  }
  
  // If both are arrays, use array intersection
  if (Array.isArray(approach1) && Array.isArray(approach2)) {
    return arrayIntersectionScore(approach1, approach2);
  }
  
  // If one is array and other is string, check if string is in array
  if (Array.isArray(approach1) && typeof approach2 === 'string') {
    return approach1.includes(approach2) ? 1 : 0;
  }
  
  if (typeof approach1 === 'string' && Array.isArray(approach2)) {
    return approach2.includes(approach1) ? 1 : 0;
  }
  
  // If they're objects, compare keys and values
  if (typeof approach1 === 'object' && approach1 !== null && 
      typeof approach2 === 'object' && approach2 !== null) {
    const keys1 = Object.keys(approach1);
    const keys2 = Object.keys(approach2);
    
    const keyScore = arrayIntersectionScore(keys1, keys2);
    
    // Calculate value similarity for common keys
    let valueScore = 0;
    let commonKeys = 0;
    
    keys1.forEach(key => {
      if (key in approach2) {
        commonKeys++;
        valueScore += textSimilarityScore(String(approach1[key]), String(approach2[key]));
      }
    });
    
    if (commonKeys === 0) {
      return keyScore;
    }
    
    return (keyScore + (valueScore / commonKeys)) / 2;
  }
  
  return 0;
}

/**
 * Extract interests from a profile based on its type
 * @param profile The profile object
 * @param profileType The type of profile
 * @returns Array of extracted interests
 */
export function extractInterestsFromProfile(profile: any, profileType: string): { type: string, value: string }[] {
  const interests: { type: string, value: string }[] = [];
  
  // Common fields across profile types
  if (profile.interests && Array.isArray(profile.interests)) {
    profile.interests.forEach((interest: any) => {
      interests.push({ type: 'general', value: interest });
    });
  }
  
  if (profile.sdg_alignment && Array.isArray(profile.sdg_alignment)) {
    profile.sdg_alignment.forEach((sdg: any) => {
      interests.push({ type: 'sdg', value: sdg });
    });
  }
  
  // Profile-specific fields
  switch (profileType) {
    case 'innovator':
      if (profile.innovation_area) {
        interests.push({ type: 'industry', value: profile.innovation_area });
      }
      if (profile.innovation_category) {
        interests.push({ type: 'category', value: profile.innovation_category });
      }
      if (profile.challenges && Array.isArray(profile.challenges)) {
        profile.challenges.forEach((challenge: any) => {
          interests.push({ type: 'challenge', value: challenge });
        });
      }
      break;
      
    case 'investor':
      if (profile.investment_focus && Array.isArray(profile.investment_focus)) {
        profile.investment_focus.forEach((focus: any) => {
          interests.push({ type: 'industry', value: focus });
        });
      }
      break;
      
    case 'mentor':
      if (profile.expertise_areas && Array.isArray(profile.expertise_areas)) {
        profile.expertise_areas.forEach((area: any) => {
          interests.push({ type: 'expertise', value: area });
        });
      }
      if (profile.industry_experience && Array.isArray(profile.industry_experience)) {
        profile.industry_experience.forEach((industry: any) => {
          interests.push({ type: 'industry', value: industry });
        });
      }
      break;
  }
  
  return interests;
}
