<template>
  <div class="single-blog-layout">
    <!-- Loading State -->
    <div v-if="loading" class="text-center q-pa-xl">
      <q-spinner-dots color="primary" size="40px" />
      <div class="text-subtitle1 q-mt-sm">Loading article...</div>
    </div>

    <!-- Error State -->
    <div v-else-if="!article" class="text-center q-pa-xl">
      <p class="text-h6">Article not found</p>
      <q-btn
        color="primary"
        :to="{ name: 'news' }"
        label="Back to News"
        class="q-mt-md"
      />
    </div>

    <!-- Article Content -->
    <template v-else>
      <!-- Hero Section with Featured Image -->
      <div class="article-hero" :style="{ backgroundImage: `url(${article.image || 'https://picsum.photos/1920/600'})` }">
        <div class="overlay" />
      </div>

      <div class="article-container q-mx-auto q-px-md">
        <!-- Article Header -->
        <div class="article-header-container">
          <div class="article-header-card">
            <!-- Back Button -->
            <q-btn
              flat
              color="primary"
              icon="arrow_back"
              label="Back"
              class="q-mb-md"
              @click="goBack"
            />
            <q-chip
              color="primary"
              text-color="white"
              class="q-mb-md category-chip"
            >
              {{ article.category }}
            </q-chip>
            <h1 class="text-h3 q-mb-md title">{{ article.title }}</h1>
            <div class="article-meta row items-center q-gutter-x-md">
              <!-- AUTHOR INFO TEMPORARILY REMOVED -->
              <!-- <div class="author-info row items-center">
                <q-avatar size="30px" class="q-mr-sm">
                  <img :src="article.authorAvatar || 'https://cdn.quasar.dev/img/avatar.png'" />
                </q-avatar>
                {{ article.author }}
              </div>
              <q-separator vertical /> -->
              <div class="date-info row items-center">
                <q-icon name="event" class="q-mr-sm" />
                {{ formatDate(article.date || article.publishedAt) }}
              </div>
              <q-separator vertical />
              <div class="read-time row items-center">
                <q-icon name="schedule" class="q-mr-sm" />
                {{ article.readTime || '5 min read' }}
              </div>
            </div>
          </div>
        </div>

        <!-- Main Content Area -->
        <div class="row q-mt-xl">
          <!-- Social Share Sidebar -->
          <div class="col-md-1 col-sm-12 col-xs-12 q-mb-md-none q-mb-md">
            <div class="share-sidebar gt-sm">
              <div class="text-subtitle2 q-mb-md">Share</div>
              <q-btn round flat color="primary" icon="fab fa-twitter" class="q-mb-md" @click="shareOnTwitter" />
              <q-btn round flat color="primary" icon="fab fa-facebook" class="q-mb-md" @click="shareOnFacebook" />
              <q-btn round flat color="primary" icon="fab fa-linkedin" class="q-mb-md" @click="shareOnLinkedIn" />
              <q-btn round flat color="primary" icon="content_copy" class="q-mb-md" @click="copyLink" />
            </div>
            <!-- Mobile Share Row -->
            <div class="row justify-center q-mt-md lt-md">
              <q-btn round flat color="primary" icon="fab fa-twitter" class="q-mx-sm" @click="shareOnTwitter" />
              <q-btn round flat color="primary" icon="fab fa-facebook" class="q-mx-sm" @click="shareOnFacebook" />
              <q-btn round flat color="primary" icon="fab fa-linkedin" class="q-mx-sm" @click="shareOnLinkedIn" />
              <q-btn round flat color="primary" icon="content_copy" class="q-mx-sm" @click="copyLink" />
            </div>
          </div>

          <!-- Article Body -->
          <div class="col-md-8 col-sm-12 col-xs-12">
            <div class="content-wrapper">
              <!-- Article Excerpt -->
              <div class="article-excerpt text-h6 text-weight-regular text-grey-8 q-mb-xl">
                {{ article.excerpt }}
              </div>

              <!-- Article Body -->
              <div class="article-body" v-html="formatContent(article.content)"></div>

              <!-- Tags -->
              <div class="article-tags q-mt-xl">
                <q-chip
                  v-for="tag in article.tags"
                  :key="tag"
                  outline
                  color="primary"
                  class="q-mr-sm"
                >
                  #{{ tag }}
                </q-chip>
              </div>
            </div>
          </div>

          <!-- Related Articles Sidebar -->
          <div class="col-md-3 col-sm-12 col-xs-12">
            <div class="sidebar">
              <div class="text-h6 q-mb-md">Related Articles</div>
              <q-card v-for="(relatedArticle, index) in relatedStories" :key="index" class="q-mb-md">
                <q-img :src="relatedArticle.image || 'https://picsum.photos/id/3/500/300'" height="150px" />
                <q-card-section>
                  <q-chip size="sm" :color="getCategoryColor(relatedArticle.category)" text-color="white" class="q-mb-sm">
                    {{ relatedArticle.category }}
                  </q-chip>
                  <div class="text-subtitle1 q-mb-xs">{{ relatedArticle.title }}</div>
                  <div class="text-caption text-grey">{{ formatDate(relatedArticle.date) }}</div>
                  <q-btn flat color="primary" size="sm" class="q-mt-sm" :to="getArticleRoute(relatedArticle.slug)">
                    Read More
                  </q-btn>
                </q-card-section>
              </q-card>
            </div>
          </div>
        </div>
      </div>

      <!-- Related Stories Section -->
      <div class="related-stories-section q-py-xl">
        <div class="container q-mx-auto q-px-md">
          <h2 class="text-h4 q-mb-lg">Related Stories</h2>
          <div class="row q-col-gutter-lg">
            <div v-for="(relatedArticle, index) in relatedStories" :key="index" class="col-12 col-md-4">
              <q-card class="related-story-card" flat bordered>
                <q-card-section>
                  <q-chip
                    :color="getCategoryColor(relatedArticle.category)"
                    text-color="white"
                    class="q-mb-sm"
                    size="sm"
                  >
                    {{ relatedArticle.category }}
                  </q-chip>
                  <h3 class="text-h6 q-mb-sm">{{ relatedArticle.title }}</h3>
                  <p class="text-body2 text-grey-8 q-mb-md">{{ relatedArticle.excerpt }}</p>
                  <q-btn
                    flat
                    color="primary"
                    :to="getArticleRoute(relatedArticle.slug)"
                    label="Read More"
                    no-caps
                    class="q-px-md"
                  />
                </q-card-section>
              </q-card>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useNewsStore } from '@/stores/news'
import { useArticleStore } from '@/stores/articles'
import { useQuasar } from 'quasar'

const route = useRoute()
const router = useRouter()
const newsStore = useNewsStore()
const articleStore = useArticleStore()
const $q = useQuasar()
const article = ref<any>(null)
const loading = ref(true)

onMounted(async () => {
  const slug = route.params.slug as string
  loading.value = true

  try {
    // Get article from the article store
    article.value = await articleStore.getArticleBySlug(slug)

    if (!article.value) {
      throw new Error('Article not found')
    }
  } catch (error) {
    console.error('Failed to load article:', error)
    article.value = null
  } finally {
    loading.value = false
  }
})

const formatDate = (date: string) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// Add related stories computation
const relatedStories = computed(() => {
  if (!article.value) return []

  // Get stories from the same category, excluding current article
  return newsStore.newsItems
    .filter(item =>
      item.category === article.value.category &&
      item.slug !== article.value.slug
    )
    .slice(0, 3) // Limit to 3 related stories
})

const formatContent = (content: string): string => {
  if (!content) return '';

  // Basic formatting for content
  let formatted = content;

  // Replace markdown headers with HTML headers
  formatted = formatted.replace(/^# (.*$)/gm, '<h1>$1</h1>');
  formatted = formatted.replace(/^## (.*$)/gm, '<h2>$1</h2>');
  formatted = formatted.replace(/^### (.*$)/gm, '<h3>$1</h3>');

  // Replace markdown paragraphs with HTML paragraphs
  formatted = formatted.replace(/^(?!<h[1-6]>)(.*$)/gm, '<p>$1</p>');

  return formatted;
}

// Social sharing functions
const shareOnTwitter = () => {
  const url = window.location.href;
  const text = article.value.title;
  window.open(`https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(text)}`, '_blank');
}

const shareOnFacebook = () => {
  const url = window.location.href;
  window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`, '_blank');
}

const shareOnLinkedIn = () => {
  const url = window.location.href;
  const title = article.value.title;
  window.open(`https://www.linkedin.com/shareArticle?mini=true&url=${encodeURIComponent(url)}&title=${encodeURIComponent(title)}`, '_blank');
}

const copyLink = () => {
  const url = window.location.href;
  navigator.clipboard.writeText(url).then(() => {
    $q.notify({
      type: 'positive',
      message: 'Link copied to clipboard',
      position: 'top',
      timeout: 2000
    });
  });
}

// Helper function to get category color
const getCategoryColor = (category: string): string => {
  const categoryColors: Record<string, string> = {
    'Events': 'green',
    'News': 'blue',
    'Success Stories': 'orange',
    'Funding': 'green-9',
    'Collaboration': 'blue-7',
    'Mentorship': 'purple-7',
    'Innovation': 'deep-orange',
    'Research': 'teal',
    'Training': 'indigo',
    'Partnership': 'green-8'
  };

  return categoryColors[category] || 'primary';
}

// Back button functionality
function goBack() {
  router.back();
}

// Helper function to determine the correct route for articles
const getArticleRoute = (slug: string) => {
  // Determine if we're in the virtual community context
  const isVirtualCommunity = route.path.includes('virtual-community');
  const routeName = isVirtualCommunity ? 'virtual-community-article' : 'article';

  return { name: routeName, params: { slug } };
}
</script>

<style scoped>
.article-container {
  max-width: 1200px;
}

.article-hero {
  height: 180px; /* Reduced from 400px to 180px (55% reduction) */
  background-size: cover;
  background-position: center;
  position: relative;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
}

.article-header-container {
  margin-top: -60px; /* Adjusted from -80px to maintain proportions with reduced hero height */
  position: relative;
  z-index: 10;
}

.article-header-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  max-width: 800px;
  margin: 0 auto;
}

.title {
  font-weight: 600;
  line-height: 1.2;
  color: #1d1d1d;
}

.category-chip {
  font-weight: 500;
}

.article-meta {
  color: #546E7A;
  font-size: 0.95rem;
}

.share-sidebar {
  position: sticky;
  top: 100px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.content-wrapper {
  background: white;
  border-radius: 12px;
  padding: 3rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  margin-top: 2rem;
}

.article-excerpt {
  font-size: 1.4rem;
  line-height: 1.6;
  color: #546E7A;
  border-left: 4px solid var(--q-primary);
  padding-left: 1.5rem;
}

.article-body {
  font-size: 1.2rem;
  line-height: 1.8;
  color: #37474F;
}

.article-body p {
  margin-bottom: 1.8rem;
}

.article-body h2 {
  font-size: 2rem;
  margin: 2.5rem 0 1.5rem;
  font-weight: 600;
}

.article-body h3 {
  font-size: 1.5rem;
  margin: 2rem 0 1rem;
  font-weight: 600;
}

.article-body img {
  width: 100%;
  border-radius: 8px;
  margin: 2rem 0;
}

.article-body blockquote {
  border-left: 4px solid var(--q-primary);
  margin: 2rem 0;
  padding: 1rem 0 1rem 2rem;
  font-style: italic;
  color: #546E7A;
}

.related-stories-section {
  background-color: #f5f5f5;
}

.related-story-card {
  height: 100%;
  transition: all 0.3s ease;
}

.related-story-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.sidebar {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

/* Responsive styles */
@media (max-width: 767px) {
  .article-hero {
    height: 120px; /* Reduced from 200px to 120px (40% reduction for mobile) */
  }

  .article-header-container {
    margin-top: -30px; /* Adjusted from -40px to maintain proportions */
  }

  .article-header-card {
    margin: 0 1rem;
    padding: 1.5rem;
  }

  .title {
    font-size: 2rem !important;
  }

  .content-wrapper {
    padding: 1.5rem;
    margin-top: 1rem;
  }

  .article-excerpt {
    font-size: 1.2rem;
  }

  .article-body {
    font-size: 1.1rem;
  }

  .article-meta {
    flex-direction: column;
    gap: 0.5rem;
  }

  .article-meta .q-separator-vertical {
    display: none;
  }

  .related-story-card {
    margin-bottom: 1rem;
  }

  .sidebar {
    margin-top: 2rem;
  }
}
</style>
