<template>
  <q-page class="q-pa-md">
    <div class="row q-col-gutter-md">
      <div class="col-12">
        <q-card class="content-header-card bg-primary text-white">
          <q-card-section>
            <div class="text-h4">Announcements</div>
            <p class="text-body1 q-mt-md">
              Stay informed with the latest platform updates and community announcements.
            </p>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-12">
        <q-card>
          <q-card-section>
            <div class="text-h6">Recent Announcements</div>
          </q-card-section>

          <q-separator />

          <q-list>
            <q-item v-for="(announcement, index) in announcements" :key="index" clickable>
              <q-item-section avatar>
                <q-avatar color="primary" text-color="white">
                  <unified-icon name="campaign" />
                </q-avatar>
              </q-item-section>

              <q-item-section>
                <q-item-label>{{ announcement.title }}</q-item-label>
                <q-item-label caption>{{ announcement.date }}</q-item-label>
                <q-item-label class="q-mt-sm">{{ announcement.summary }}</q-item-label>
              </q-item-section>

              <q-item-section side>
                <q-btn flat round color="primary" icon="arrow_forward" />
              </q-item-section>
            </q-item>
          </q-list>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { useAuthStore } from '../../stores/auth';
import UnifiedIcon from '../../components/ui/UnifiedIcon.vue';

const router = useRouter();
const $q = useQuasar();
const authStore = useAuthStore();

// Sample announcements data
const announcements = ref([
  {
    title: 'Platform Update: New Features Released',
    date: 'July 15, 2025',
    summary: 'We\'ve added new features to enhance your experience, including improved messaging and profile customization options.'
  },
  {
    title: 'Community Guidelines Update',
    date: 'August 28, 2025',
    summary: 'Please review our updated community guidelines to ensure a positive experience for all members.'
  },
  {
    title: 'Upcoming Maintenance',
    date: 'September 15, 2025',
    summary: 'The platform will be undergoing maintenance on September 20 from 2-4 AM EST. Some features may be temporarily unavailable.'
  }
]);

// Check if user is authenticated
onMounted(() => {
  if (!authStore.isAuthenticated) {
    router.push('/login');
    $q.notify({
      color: 'negative',
      message: 'You must be logged in to view this page',
      icon: 'error'
    });
  }
});
</script>

<style scoped>
.content-header-card {
  border-radius: 8px;
  overflow: hidden;
}
</style>
