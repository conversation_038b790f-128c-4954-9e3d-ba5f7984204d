/**
 * Profile View Service
 *
 * This service manages the display of profile data, providing formatted
 * data for viewing profiles.
 */

import { ref, computed } from 'vue'
import { useProfileStore } from '../stores/profile'
import { useProfileQuestions } from './profileQuestions'
import { formatProfileType } from './profileUtils'
import { supabase } from '../lib/supabase'
import { useNotificationStore } from '../stores/notifications'

export function useProfileViewService() {
  const profileStore = useProfileStore()
  const notifications = useNotificationStore()
  const { setProfileType, currentQuestions } = useProfileQuestions()

  // State
  const loading = ref(false)
  const error = ref(null)
  const viewMode = ref(true) // true = view mode, false = edit mode

  // Set the profile type for viewing
  function setProfileForViewing(profileType: string) {
    setProfileType(profileType)
  }

  // Toggle between view and edit modes
  function toggleEditMode() {
    viewMode.value = !viewMode.value
  }

  // Set view mode
  function setViewMode(isViewMode: boolean) {
    viewMode.value = isViewMode
  }

  // Load profile data
  async function loadProfileData(userId: string) {
    console.log('ProfileViewService: Starting loadProfileData for userId:', userId)
    loading.value = true
    error.value = null

    try {
      console.log('ProfileViewService: Loading profile data for userId:', userId)

      // Load the profile from the store
      const profile = await profileStore.fetchProfile(userId)

      if (!profile) {
        throw new Error('Profile not found')
      }

      console.log('ProfileViewService: Profile loaded:', profile)

      // Set the profile type for viewing
      if (profile.profile_type) {
        console.log('ProfileViewService: Setting profile type for viewing:', profile.profile_type)
        try {
          setProfileForViewing(profile.profile_type)

          // Explicitly load specialized profile data if not already loaded
          if (!profileStore.currentSpecializedProfile) {
            console.log('ProfileViewService: Explicitly loading specialized profile data')
            const profileService = await import('./profileService')
              .then(module => module.useProfileService())

            const specializedProfile = await profileService.loadSpecializedProfile(userId, profile.profile_type)

            if (specializedProfile) {
              console.log('ProfileViewService: Specialized profile loaded:', specializedProfile)
              // Update the store with the specialized profile
              profileStore.setSpecializedProfile(specializedProfile)
            } else {
              console.warn('ProfileViewService: No specialized profile data found')
            }
          }
        } catch (err) {
          console.error('ProfileViewService: Error setting profile type or loading specialized data:', err)
          // Continue anyway, we'll just show what we can
        }
      } else {
        console.warn('ProfileViewService: Profile has no type')
      }

      return true
    } catch (err) {
      console.error('ProfileViewService: Error loading profile:', err)
      error.value = err.message || 'Error loading profile'
      notifications.error(error.value)
      return false
    } finally {
      console.log('ProfileViewService: Finished loadProfileData, setting loading to false')
      loading.value = false
    }
  }

  // Get combined profile data
  const profileData = computed(() => {
    if (!profileStore.currentProfile) {
      return {}
    }

    // Combine data from both profile sources
    return {
      ...profileStore.currentProfile,
      ...profileStore.currentSpecializedProfile
    }
  })

  // Get formatted profile data for display
  const formattedProfileData = computed(() => {
    if (!profileStore.currentProfile) {
      return {}
    }

    const result = {}

    // Format data according to field types
    if (currentQuestions.value) {
      currentQuestions.value.sections.forEach(section => {
        section.questions.forEach(question => {
          const value = profileData.value[question.id]

          // Format based on field type
          if (question.type === 'multi-select' && Array.isArray(value)) {
            result[question.id] = value.join(', ')
          } else if (question.type === 'boolean') {
            result[question.id] = value ? 'Yes' : 'No'
          } else {
            result[question.id] = value
          }
        })
      })
    }

    return result
  })

  // Get profile sections with their data
  const profileSections = computed(() => {
    if (!currentQuestions.value) {
      return []
    }

    return currentQuestions.value.sections.map(section => {
      const questions = section.questions.map(question => {
        return {
          ...question,
          value: profileData.value[question.id],
          formattedValue: formattedProfileData.value[question.id]
        }
      })

      return {
        ...section,
        questions
      }
    })
  })

  return {
    // State
    loading,
    error,
    viewMode,

    // Methods
    setProfileForViewing,
    toggleEditMode,
    setViewMode,
    loadProfileData,

    // Computed properties
    profileData,
    formattedProfileData,
    profileSections,
    isLoading: computed(() => loading.value),
    hasError: computed(() => !!error.value),
    errorMessage: computed(() => error.value),
    isViewMode: computed(() => viewMode.value),
    isEditMode: computed(() => !viewMode.value),
    profileType: computed(() => profileStore.currentProfile?.profile_type),
    profileId: computed(() => profileStore.currentProfile?.user_id),
    profileName: computed(() => profileStore.currentProfile?.profile_name),
    formattedProfileType: computed(() =>
      formatProfileType(profileStore.currentProfile?.profile_type)
    ),
    profileCompletion: computed(() => profileStore.profileCompletion)
  }
}
