<template>
  <q-select
    v-bind="$attrs"
    @input="onInput"
    @change="onChange"
  >
    <template v-slot:dropdown-icon>
      <Icons name="arrow_drop_down" />
    </template>
    <slot></slot>
  </q-select>
</template>

<script>
import Icons from './Icons.vue';

export default {
  name: 'CustomSelect',
  components: {
    Icons
  },
  inheritAttrs: false,
  emits: ['update:modelValue', 'input', 'change'],
  methods: {
    onInput(val) {
      this.$emit('input', val);
      this.$emit('update:modelValue', val);
    },
    onChange(val) {
      this.$emit('change', val);
    }
  }
}
</script>
