import { ref, computed, onMounted, watch } from 'vue'
import { useConnectionService } from '../services/connectionService'
import { useNotificationStore } from '../stores/notifications'
import { useAuthStore } from '../stores/auth'
import { useActivityNotificationsStore } from '../stores/activityNotifications'
import { useConnectionsStore } from '../stores/connections'
import { useRouter } from 'vue-router'
import { supabase } from '../lib/supabase'

export interface ConnectionButtonConfig {
  color: string
  icon: string
  label: string
  disabled: boolean
  loading: boolean
}

export function useConnectionButton(targetUserId: string) {
  // Services and stores
  const connectionService = useConnectionService()
  const notifications = useNotificationStore()
  const authStore = useAuthStore()
  const activityNotificationsStore = useActivityNotificationsStore()
  const connectionsStore = useConnectionsStore()
  const router = useRouter()

  // State
  const connectionStatus = ref<string>('none') // 'none' | 'pending' | 'accepted' | 'self'
  const loading = ref(false)
  const isCurrentUser = ref(false)

  // Computed button configuration
  const buttonConfig = computed<ConnectionButtonConfig>(() => {
    const currentStatus = connectionStatus.value
    const isCurrentUserValue = isCurrentUser.value
    const loadingValue = loading.value

    console.log('Computing button config for:', targetUserId, {
      status: currentStatus,
      isCurrentUser: isCurrentUserValue,
      loading: loadingValue
    })

    if (isCurrentUserValue) {
      const config = {
        color: 'grey',
        icon: 'person',
        label: 'You',
        disabled: true,
        loading: loadingValue
      }
      console.log('Returning current user config:', config)
      return config
    }

    let config: ConnectionButtonConfig

    switch (currentStatus) {
      case 'none':
        config = {
          color: 'green',
          icon: 'person_add',
          label: 'Connect',
          disabled: false,
          loading: loadingValue
        }
        break
      case 'pending':
        config = {
          color: 'orange',
          icon: 'hourglass_empty',
          label: 'Request Sent',
          disabled: true,
          loading: loadingValue
        }
        break
      case 'incoming_pending':
        config = {
          color: 'blue',
          icon: 'person_add_alt',
          label: 'Accept Request',
          disabled: false,
          loading: loadingValue
        }
        break
      case 'accepted':
        config = {
          color: 'positive',
          icon: 'check_circle',
          label: 'Connected',
          disabled: true,
          loading: loadingValue
        }
        break
      default:
        config = {
          color: 'grey',
          icon: 'person_add',
          label: 'Connect',
          disabled: false,
          loading: loadingValue
        }
        break
    }

    console.log('Returning button config:', config)
    return config
  })

  // Computed tooltip text
  const tooltipText = computed(() => {
    if (isCurrentUser.value) return 'This is your profile'
    
    switch (connectionStatus.value) {
      case 'none':
        return 'Send connection request'
      case 'pending':
        return 'Connection request pending'
      case 'incoming_pending':
        return 'Accept incoming connection request'
      case 'accepted':
        return 'Already connected'
      default:
        return 'Connect with this user'
    }
  })

  // Check connection status
  async function checkConnectionStatus() {
    if (!targetUserId) {
      console.log('No targetUserId provided to checkConnectionStatus')
      return
    }

    console.log('Checking connection status for:', targetUserId)

    try {
      // Check if current user
      const { data } = await supabase.auth.getUser()
      const currentUser = data.user

      if (currentUser && currentUser.id === targetUserId) {
        console.log('Target user is current user')
        isCurrentUser.value = true
        connectionStatus.value = 'self'
        return
      }

      // Get connection status from service
      console.log('Getting connection status from service...')
      const status = await connectionService.getConnectionStatus(targetUserId)
      console.log('Service returned status:', status)

      connectionStatus.value = status

      console.log('Connection status updated:', {
        targetUserId,
        oldStatus: connectionStatus.value,
        newStatus: status,
        buttonConfig: buttonConfig.value
      })
    } catch (error) {
      console.error('Error checking connection status:', error)
      connectionStatus.value = 'none'
    }
  }

  // Handle connection button click
  async function handleConnect() {
    console.log('handleConnect called for:', targetUserId, 'Current status:', connectionStatus.value)

    if (!authStore.isAuthenticated) {
      notifications.warning('Please sign in to connect with users')
      router.push({ name: 'sign-in', query: { redirect: router.currentRoute.value.fullPath } })
      return
    }

    if (isCurrentUser.value || buttonConfig.value.disabled || loading.value) {
      console.log('Button click ignored:', {
        isCurrentUser: isCurrentUser.value,
        disabled: buttonConfig.value.disabled,
        loading: loading.value
      })
      return
    }

    console.log('Processing connection request...')
    loading.value = true

    try {
      if (connectionStatus.value === 'incoming_pending') {
        console.log('Accepting incoming connection request...')
        // Accept incoming connection request - need to find the connection ID
        // Get the connection record to find the ID
        const { data } = await supabase.auth.getUser()
        const currentUser = data.user

        if (currentUser) {
          const { data: connectionData } = await supabase
            .from('user_connections')
            .select('id')
            .eq('user_id', targetUserId)
            .eq('connected_user_id', currentUser.id)
            .eq('connection_status', 'pending')
            .single()

          if (connectionData) {
            await connectionService.acceptConnection(connectionData.id)
            notifications.success('Connection request accepted!')
            // Refresh status from database instead of manual update
            await checkConnectionStatus()
            // Refresh notification counts
            await activityNotificationsStore.fetchConnectionRequestsCount()
            // Refresh connections list to update count
            await connectionsStore.fetchUserConnections()
          }
        }
      } else if (connectionStatus.value === 'none') {
        console.log('Sending new connection request...')
        // Check if connection already exists before sending
        const currentStatus = await connectionService.getConnectionStatus(targetUserId)
        console.log('Double-checked status before sending:', currentStatus)

        if (currentStatus !== 'none') {
          // Status has changed, refresh and return
          console.log('Status changed, updating UI...')
          connectionStatus.value = currentStatus
          if (currentStatus === 'pending') {
            notifications.info('Connection request already sent')
          } else if (currentStatus === 'accepted') {
            notifications.info('You are already connected with this user')
          }
          return
        }

        // Send new connection request
        console.log('Calling connectWithUser...')
        const success = await connectionService.connectWithUser(targetUserId)
        console.log('connectWithUser result:', success)

        if (success) {
          notifications.success('Connection request sent!')
          // Refresh status from database instead of manual update
          console.log('Refreshing connection status after successful request...')
          await checkConnectionStatus()
          // Refresh notification counts for the recipient
          await activityNotificationsStore.fetchConnectionRequestsCount()
        } else {
          notifications.error('Failed to send connection request')
        }
      }
    } catch (error) {
      console.error('Error handling connection:', error)
      notifications.error('An error occurred while connecting')
      // Refresh status in case of error to ensure UI is in sync
      await checkConnectionStatus()
    } finally {
      loading.value = false
      console.log('handleConnect completed. Final status:', connectionStatus.value)
    }
  }

  // Initialize on mount
  onMounted(() => {
    checkConnectionStatus()
  })

  // Watch for global connection status changes
  watch(
    () => connectionsStore.connectionStatusChangeEvent,
    () => {
      console.log('Connection status change event detected, refreshing status for user:', targetUserId)
      checkConnectionStatus()
    }
  )

  // Refresh connection status (useful for external updates)
  async function refreshStatus() {
    await checkConnectionStatus()
  }

  return {
    connectionStatus,
    buttonConfig,
    tooltipText,
    loading,
    isCurrentUser,
    handleConnect,
    refreshStatus,
    checkConnectionStatus
  }
}
