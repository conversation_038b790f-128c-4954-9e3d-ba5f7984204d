import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { usePostsStore } from './posts';
import { useNotificationStore } from './notifications';

export interface NewsItem {
  id: number;
  category: string;
  categoryColor: string;
  textColor: string;
  title: string;
  date: string;
  excerpt: string;
  content: string;
  author: string;
  authorRole: string;
  readTime: string;
  slug: string;
}

// Helper function to determine category color
function getCategoryColor(category: string): { bgColor: string, textColor: string } {
  switch (category?.toLowerCase()) {
    case 'partnership':
      return { bgColor: 'green', textColor: 'white' };
    case 'event':
      return { bgColor: 'accent', textColor: 'white' };
    case 'announcement':
      return { bgColor: 'primary', textColor: 'white' };
    case 'update':
      return { bgColor: 'blue', textColor: 'white' };
    default:
      return { bgColor: 'primary', textColor: 'white' };
  }
}

// Helper function to estimate read time
function estimateReadTime(content: string): string {
  if (!content) return '1 min read';

  // Average reading speed: 200 words per minute
  const words = content.split(/\s+/).length;
  const minutes = Math.max(1, Math.round(words / 200));

  return `${minutes} min read`;
}

export const useNewsStore = defineStore('news', () => {
  // Use the posts store
  const postsStore = usePostsStore();
  const notifications = useNotificationStore();

  // Local state for error handling
  const error = ref<string | null>(null);

  // Map posts to news items
  const newsItems = computed(() => {
    return postsStore.news.map(post => {
      const { bgColor, textColor } = getCategoryColor(post.subType);

      return {
        id: post.id,
        category: post.subType || 'Announcement',
        categoryColor: bgColor,
        textColor: textColor,
        title: post.title || '',
        date: post.createdAt ? new Date(post.createdAt).toISOString().split('T')[0] : '',
        excerpt: post.excerpt || '',
        content: post.content || '',
        author: post.author || 'ZbInnovation',
        authorRole: post.authorRole || 'Team Member',
        readTime: estimateReadTime(post.content || ''),
        slug: post.slug || `news-${post.id}`
      };
    });
  });

  // Pass through loading state
  const loading = computed(() => postsStore.loading);

  // Getters
  const getNewsById = computed(() => {
    return (id: number) => newsItems.value.find(item => item.id === id);
  });

  const getRelatedNews = computed(() => {
    return (currentId: number) => {
      return newsItems.value
        .filter(item => item.id !== currentId)
        .slice(0, 2);
    };
  });

  // Actions
  async function fetchNews() {
    try {
      error.value = null;
      await postsStore.fetchNews();
    } catch (err: any) {
      console.error('Error fetching news:', err);
      error.value = err.message || 'Failed to fetch news';
      notifications.error('Failed to load news: ' + error.value);
    }
  }

  async function getNewsBySlug(slug: string): Promise<NewsItem | null> {
    try {
      // First check if it's in our local state
      const localItem = newsItems.value.find(item => item.slug === slug);
      if (localItem) return localItem;

      // If not found locally, fetch from the posts store
      const post = await postsStore.getPostBySlug(slug, 'NEWS');

      if (post && post.postType === 'NEWS') {
        const { bgColor, textColor } = getCategoryColor(post.subType);

        return {
          id: post.id,
          category: post.subType || 'Announcement',
          categoryColor: bgColor,
          textColor: textColor,
          title: post.title || '',
          date: post.createdAt ? new Date(post.createdAt).toISOString().split('T')[0] : '',
          excerpt: post.excerpt || '',
          content: post.content || '',
          author: post.author || 'ZbInnovation',
          authorRole: post.authorRole || 'Team Member',
          readTime: estimateReadTime(post.content || ''),
          slug: post.slug || `news-${post.id}`
        };
      }

      return null;
    } catch (err: any) {
      console.error('Error fetching news item by slug:', err);
      error.value = err.message || 'Failed to fetch news item';
      return null;
    }
  }

  return {
    // State
    newsItems,
    loading,
    error,

    // Getters
    getNewsById,
    getRelatedNews,

    // Actions
    fetchNews,
    getNewsBySlug
  };
});
