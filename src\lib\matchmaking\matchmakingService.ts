/**
 * Matchmaking Service
 *
 * This service handles the core matchmaking functionality for the platform.
 * It implements algorithms for matching different profile types based on
 * their attributes and preferences.
 */

import { SupabaseClient } from '@supabase/supabase-js';
import {
  arrayIntersectionScore,
  textSimilarityScore,
  numericRangeMatch,
  stageCompatibilityScore,
  approachCompatibilityScore
} from './matchingUtils';

// Define types for the matchmaking service
export type ProfileType =
  | 'innovator'
  | 'investor'
  | 'mentor'
  | 'organisation'
  | 'professional'
  | 'academic_institution'
  | 'industry_expert'
  | 'academic_student';

export type EntityType =
  | ProfileType
  | 'event'
  | 'opportunity'
  | 'resource'
  | 'post';

export interface MatchResult {
  id: string;
  entityId: string;
  entityType: EntityType;
  score: number;
  reasons: Record<string, number>;
}

export interface MatchmakingRule {
  id: string;
  sourceProfileType: ProfileType;
  targetProfileType: ProfileType;
  ruleName: string;
  sourceFields: Record<string, any>;
  targetFields: Record<string, any>;
  weight: number;
}

export class MatchmakingService {
  private supabase: SupabaseClient;

  constructor(supabaseClient: SupabaseClient) {
    this.supabase = supabaseClient;
  }

  /**
   * Get matching rules for a specific source and target profile type
   */
  async getMatchingRules(sourceType: ProfileType, targetType: ProfileType): Promise<MatchmakingRule[]> {
    const { data, error } = await this.supabase
      .from('matchmaking_rules')
      .select('*')
      .eq('source_profile_type', sourceType)
      .eq('target_profile_type', targetType);

    if (error) {
      console.error('Error fetching matching rules:', error);
      return [];
    }

    return data.map(rule => ({
      id: rule.id,
      sourceProfileType: rule.source_profile_type,
      targetProfileType: rule.target_profile_type,
      ruleName: rule.rule_name,
      sourceFields: rule.source_fields,
      targetFields: rule.target_fields,
      weight: rule.weight
    }));
  }

  /**
   * Generate matches for a user based on their profile type
   */
  async generateMatches(userId: string, profileType: ProfileType): Promise<MatchResult[]> {
    // Get the user's profile
    const { data: userProfile, error: profileError } = await this.supabase
      .from(`${profileType}_profiles`)
      .select('*')
      .eq('user_id', userId)
      .single();

    if (profileError || !userProfile) {
      console.error(`Error fetching ${profileType} profile:`, profileError);
      return [];
    }

    // Get all potential match types for this profile type
    const matchTypes = await this.getMatchableProfileTypes(profileType);

    // Generate matches for each match type
    const allMatches: MatchResult[] = [];

    for (const matchType of matchTypes) {
      const matches = await this.matchProfileToProfileType(userProfile, profileType, matchType);
      allMatches.push(...matches);
    }

    // Sort matches by score (highest first)
    return allMatches.sort((a, b) => b.score - a.score);
  }

  /**
   * Get all profile types that can be matched with the given profile type
   */
  async getMatchableProfileTypes(profileType: ProfileType): Promise<ProfileType[]> {
    const { data, error } = await this.supabase
      .from('matchmaking_rules')
      .select('target_profile_type')
      .eq('source_profile_type', profileType);

    if (error) {
      console.error('Error fetching matchable profile types:', error);
      return [];
    }

    // Get unique profile types
    const uniqueTypes = [...new Set(data?.map((item: any) => item.target_profile_type as ProfileType) || [])];
    return uniqueTypes;
  }

  /**
   * Match a profile to all profiles of a specific type
   */
  async matchProfileToProfileType(
    sourceProfile: any,
    sourceType: ProfileType,
    targetType: ProfileType
  ): Promise<MatchResult[]> {
    // Get matching rules for these profile types
    const rules = await this.getMatchingRules(sourceType, targetType);

    if (rules.length === 0) {
      console.log(`No matching rules found for ${sourceType} to ${targetType}`);
      return [];
    }

    // Get all profiles of the target type
    const { data: targetProfiles, error } = await this.supabase
      .from(`${targetType}_profiles`)
      .select('*');

    if (error || !targetProfiles) {
      console.error(`Error fetching ${targetType} profiles:`, error);
      return [];
    }

    // Filter out the source profile if it's the same type
    const filteredProfiles = sourceType === targetType
      ? targetProfiles.filter(profile => profile.user_id !== sourceProfile.user_id)
      : targetProfiles;

    // Calculate match scores for each target profile
    const matches: MatchResult[] = [];

    for (const targetProfile of filteredProfiles) {
      const { score, reasons } = this.calculateMatchScore(sourceProfile, targetProfile, rules);

      matches.push({
        id: `${sourceProfile.user_id}-${targetProfile.user_id}`,
        entityId: targetProfile.user_id,
        entityType: targetType,
        score,
        reasons
      });
    }

    return matches;
  }

  /**
   * Calculate match score between two profiles based on rules
   */
  calculateMatchScore(
    sourceProfile: any,
    targetProfile: any,
    rules: MatchmakingRule[]
  ): { score: number, reasons: Record<string, number> } {
    const reasons: Record<string, number> = {};
    let totalWeight = 0;
    let weightedScore = 0;

    for (const rule of rules) {
      const ruleScore = this.applyMatchingRule(sourceProfile, targetProfile, rule);
      reasons[rule.ruleName] = ruleScore;

      weightedScore += ruleScore * rule.weight;
      totalWeight += rule.weight;
    }

    // Calculate final score
    const finalScore = totalWeight > 0 ? weightedScore / totalWeight : 0;

    return {
      score: finalScore,
      reasons
    };
  }

  /**
   * Apply a specific matching rule to calculate a score
   */
  applyMatchingRule(sourceProfile: any, targetProfile: any, rule: MatchmakingRule): number {
    const { sourceFields, targetFields } = rule;

    // Handle different rule types
    switch (rule.ruleName) {
      case 'array_intersection':
        return this.applyArrayIntersectionRule(sourceProfile, targetProfile, sourceFields, targetFields);

      case 'text_similarity':
        return this.applyTextSimilarityRule(sourceProfile, targetProfile, sourceFields, targetFields);

      case 'numeric_range':
        return this.applyNumericRangeRule(sourceProfile, targetProfile, sourceFields, targetFields);

      case 'stage_compatibility':
        return this.applyStageCompatibilityRule(sourceProfile, targetProfile, sourceFields, targetFields);

      case 'approach_compatibility':
        return this.applyApproachCompatibilityRule(sourceProfile, targetProfile, sourceFields, targetFields);

      default:
        console.warn(`Unknown rule type: ${rule.ruleName}`);
        return 0;
    }
  }

  /**
   * Apply array intersection rule
   */
  private applyArrayIntersectionRule(
    sourceProfile: any,
    targetProfile: any,
    sourceFields: Record<string, any>,
    targetFields: Record<string, any>
  ): number {
    const sourceValue = this.getNestedValue(sourceProfile, sourceFields.path);
    const targetValue = this.getNestedValue(targetProfile, targetFields.path);

    return arrayIntersectionScore(sourceValue, targetValue);
  }

  /**
   * Apply text similarity rule
   */
  private applyTextSimilarityRule(
    sourceProfile: any,
    targetProfile: any,
    sourceFields: Record<string, any>,
    targetFields: Record<string, any>
  ): number {
    const sourceValue = this.getNestedValue(sourceProfile, sourceFields.path);
    const targetValue = this.getNestedValue(targetProfile, targetFields.path);

    return textSimilarityScore(sourceValue, targetValue);
  }

  /**
   * Apply numeric range rule
   */
  private applyNumericRangeRule(
    sourceProfile: any,
    targetProfile: any,
    sourceFields: Record<string, any>,
    targetFields: Record<string, any>
  ): number {
    const sourceValue = this.getNestedValue(sourceProfile, sourceFields.path);
    const minPath = targetFields.min_path;
    const maxPath = targetFields.max_path;

    const min = minPath ? this.getNestedValue(targetProfile, minPath) : undefined;
    const max = maxPath ? this.getNestedValue(targetProfile, maxPath) : undefined;

    return numericRangeMatch(sourceValue, { min, max });
  }

  /**
   * Apply stage compatibility rule
   */
  private applyStageCompatibilityRule(
    sourceProfile: any,
    targetProfile: any,
    sourceFields: Record<string, any>,
    targetFields: Record<string, any>
  ): number {
    const sourceValue = this.getNestedValue(sourceProfile, sourceFields.path);
    const targetValue = this.getNestedValue(targetProfile, targetFields.path);

    return stageCompatibilityScore(sourceValue, targetValue);
  }

  /**
   * Apply approach compatibility rule
   */
  private applyApproachCompatibilityRule(
    sourceProfile: any,
    targetProfile: any,
    sourceFields: Record<string, any>,
    targetFields: Record<string, any>
  ): number {
    const sourceValue = this.getNestedValue(sourceProfile, sourceFields.path);
    const targetValue = this.getNestedValue(targetProfile, targetFields.path);

    return approachCompatibilityScore(sourceValue, targetValue);
  }

  /**
   * Get a nested value from an object using a path string
   * e.g., "user.profile.name" -> obj.user.profile.name
   */
  private getNestedValue(obj: any, path: string): any {
    if (!path) return undefined;

    const parts = path.split('.');
    let current = obj;

    for (const part of parts) {
      if (current === null || current === undefined) {
        return undefined;
      }
      current = current[part];
    }

    return current;
  }
}
