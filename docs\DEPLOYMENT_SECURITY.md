# Domain-Specific Security Deployment Guide

This guide explains how to deploy the ZB Innovation Hub platform with proper security configurations for different environments.

## 🌐 **Domain Configuration**

### Environments
- **Development**: `localhost:5173` (or local dev server)
- **Test**: `ZbInnovation.co.zw`
- **Production**: `fullsite.zbinnovation.co.zw`

## 📁 **Security Configuration Files**

### Available Configurations
1. **`.htaccess`** - Current development configuration (permissive for localhost)
2. **`.htaccess.test`** - Test environment configuration for `ZbInnovation.co.zw`
3. **`.htaccess.production`** - Production configuration for `fullsite.zbinnovation.co.zw`

## 🚀 **Deployment Instructions**

### For Test Environment (ZbInnovation.co.zw)

1. **Copy test configuration:**
   ```bash
   cp public/.htaccess.test public/.htaccess
   ```

2. **Verify SSL certificate is installed** on ZbInnovation.co.zw

3. **Deploy and test:**
   - Images should load from all sources
   - External scripts (Google Analytics, Iconify) should work
   - CSP violations should be minimal

### For Production Environment (fullsite.zbinnovation.co.zw)

1. **Copy production configuration:**
   ```bash
   cp public/.htaccess.production public/.htaccess
   ```

2. **Verify SSL certificate is installed** on fullsite.zbinnovation.co.zw

3. **Deploy and test:**
   - All functionality should work
   - Security headers should be active
   - Monitor for any CSP violations

## 🔍 **Domain-Specific CSP Policies**

### Test Environment CSP
The test configuration includes:
- `https://ZbInnovation.co.zw` in all relevant directives
- Allows images from any HTTPS source
- Permits necessary external resources (Google Analytics, CDNs)

### Production Environment CSP
The production configuration includes:
- `https://fullsite.zbinnovation.co.zw` and `https://zbinnovation.co.zw`
- More restrictive but still functional
- Optimized for production security

## 🛠 **Customizing for Additional Domains**

If you need to add more domains or subdomains, modify the CSP policy:

### Example: Adding a new subdomain
```apache
# Add your-subdomain.zbinnovation.co.zw to the CSP
Header always set Content-Security-Policy "default-src 'self' https://your-subdomain.zbinnovation.co.zw https://fullsite.zbinnovation.co.zw https://zbinnovation.co.zw; ..."
```

### Common CSP Directives to Update
- **default-src**: Base policy for all resources
- **script-src**: JavaScript sources
- **img-src**: Image sources
- **style-src**: CSS sources
- **connect-src**: AJAX/fetch requests
- **frame-src**: Iframe sources

## 🧪 **Testing Security Configuration**

### 1. Test Image Loading
```javascript
// Test in browser console
const img = new Image();
img.onload = () => console.log('✅ Image loaded successfully');
img.onerror = () => console.log('❌ Image blocked by CSP');
img.src = 'https://your-image-url.com/test.jpg';
```

### 2. Check CSP Violations
Open browser DevTools → Console and look for CSP violation messages:
```
Refused to load the script 'https://example.com/script.js' because it violates the following Content Security Policy directive...
```

### 3. Verify Security Headers
Use browser DevTools → Network tab → Select any request → Check Response Headers:
- `Content-Security-Policy`
- `Strict-Transport-Security`
- `X-Content-Type-Options`
- `X-Frame-Options`

## 🔧 **Troubleshooting Common Issues**

### Images Not Loading
**Problem**: Images from external sources are blocked
**Solution**: Add the domain to `img-src` in CSP:
```apache
img-src 'self' data: blob: https: http: https://your-image-domain.com;
```

### External Scripts Blocked
**Problem**: Google Analytics or other scripts are blocked
**Solution**: Add the domain to `script-src`:
```apache
script-src 'self' 'unsafe-inline' 'unsafe-eval' https://your-script-domain.com;
```

### API Calls Failing
**Problem**: AJAX requests to external APIs are blocked
**Solution**: Add the API domain to `connect-src`:
```apache
connect-src 'self' https://your-api-domain.com;
```

## 📊 **Security Monitoring**

### CSP Violation Reporting
To monitor CSP violations in production, you can add reporting:
```apache
Header always set Content-Security-Policy-Report-Only "default-src 'self'; report-uri /csp-violation-report-endpoint;"
```

### Security Headers Testing Tools
- **securityheaders.com** - Test your domain's security headers
- **Mozilla Observatory** - Comprehensive security analysis
- **CSP Evaluator** - Google's CSP testing tool

## 🔄 **Rollback Plan**

If security configurations cause issues:

1. **Quick rollback to development config:**
   ```bash
   git checkout HEAD -- public/.htaccess
   ```

2. **Disable specific security headers temporarily:**
   ```apache
   # Comment out problematic headers
   # Header always set Content-Security-Policy "..."
   ```

3. **Use permissive CSP for debugging:**
   ```apache
   Header always set Content-Security-Policy "default-src *; script-src * 'unsafe-inline' 'unsafe-eval'; style-src * 'unsafe-inline';"
   ```

## 📝 **Environment-Specific Notes**

### Development (localhost)
- Most permissive configuration
- HTTPS redirect disabled
- HSTS disabled
- Allows all image sources

### Test (ZbInnovation.co.zw)
- Balanced security and functionality
- HTTPS enforced
- Domain-specific CSP
- Full security headers enabled

### Production (fullsite.zbinnovation.co.zw)
- Maximum security
- Strict CSP with necessary allowlists
- All security headers enabled
- Optimized for performance and security

## 🚨 **Security Checklist Before Deployment**

- [ ] SSL certificate installed and working
- [ ] Correct `.htaccess` file copied for environment
- [ ] Images load properly
- [ ] External scripts work (Google Analytics, etc.)
- [ ] Forms submit successfully
- [ ] No CSP violations in console
- [ ] Security headers present in response
- [ ] Application functionality intact

## 📞 **Support**

If you encounter issues with the security configuration:
1. Check browser console for CSP violations
2. Test with a more permissive CSP temporarily
3. Verify SSL certificate is properly configured
4. Check Apache error logs for configuration issues

Remember: Security configurations can be iteratively tightened. Start with a working configuration and gradually make it more restrictive while testing functionality.
