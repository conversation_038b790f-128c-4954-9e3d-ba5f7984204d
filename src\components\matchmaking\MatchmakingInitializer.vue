<template>
  <div class="matchmaking-initializer">
    <!-- Always show the content -->
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { useMatchmakingStore } from '@/stores/matchmaking';

// Stores
const matchmakingStore = useMatchmakingStore();

// Lifecycle hooks
onMounted(async () => {
  // Always initialize matchmaking automatically
  if (!matchmakingStore.initialized) {
    await matchmakingStore.initialize();
  }
});
</script>

<style scoped>
.matchmaking-initializer {
  margin-bottom: 1rem;
}
</style>
