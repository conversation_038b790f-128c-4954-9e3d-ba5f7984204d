# ZB Innovation Hub Email System Setup Guide

This comprehensive guide covers setting up the email system for ZB Innovation Hub, including SendGrid integration, Supabase configuration, and Edge Functions deployment.

## 📋 Overview

The email system consists of:

1. **Welcome Emails**: Sent automatically when a user signs up
2. **Password Reset Emails**: For user account recovery
3. **Email Verification**: For account confirmation (currently disabled)
4. **Profile Completion Reminders**: To encourage user engagement
5. **Custom Emails**: For various application needs

## 🔧 Architecture

The integration uses:
- **SendGrid**: Primary email service provider
- **Supabase Edge Functions**: Secure server-side email sending
- **Supabase Auth Webhooks**: Automatic trigger for user events
- **Environment Variables**: Secure API key management

## 🚀 Setup Instructions

### Step 1: Create SendGrid Account

1. Go to [SendGrid's website](https://sendgrid.com/)
2. Sign up for a free account (allows 100 emails per day)
3. Complete the verification process

### Step 2: Create SendGrid API Key

1. Log in to your SendGrid account
2. Navigate to Settings > API Keys
3. Click "Create API Key"
4. Name your key (e.g., "ZB Innovation Hub Email")
5. Select "Full Access" or "Restricted Access" with at least "Mail Send" permissions
6. Click "Create & View"
7. Copy the API key (it will only be shown once)

### Step 3: Verify Sender Identity

SendGrid requires you to verify the email address you'll be sending from:

1. Navigate to Settings > Sender Authentication
2. Choose either "Single Sender Verification" or "Domain Authentication"
   - For testing: Single Sender Verification is simpler
   - For production: Domain Authentication is recommended

### Step 4: Configure Supabase

#### Disable Email Confirmation (Recommended)

1. Log in to the Supabase dashboard
2. Go to Authentication > Settings
3. Under "Email Auth", find the "Confirm email" option and disable it
4. Save changes

#### Set SendGrid API Key as Secret

```bash
npx supabase secrets set SENDGRID_API_KEY="your_api_key_here" --project-ref your_project_ref
```

### Step 5: Deploy Edge Functions

Run the deployment scripts to deploy the necessary Edge Functions:

```bash
# Deploy the auth webhook function
./deploy-auth-webhook.bat

# Deploy the email verification function
./deploy-email-verification.bat
```

This will deploy:
- `send-email-verification`: Handles all email sending, including welcome emails
- `auth-webhook`: Receives webhooks from Supabase Auth

### Step 6: Configure Auth Webhook

After deploying the functions, configure the webhook in the Supabase dashboard:

1. Go to Authentication > Settings
2. Scroll down to "Auth Hooks"
3. Add a new webhook with the URL: `https://your-project-ref.supabase.co/functions/v1/auth-webhook`
4. Select the events you want to trigger emails for (e.g., "user.created")

## 🔒 Security Considerations

**IMPORTANT**: Direct client-side SendGrid API calls have been disabled for security reasons. All email functionality now goes through Supabase Edge Functions to protect API keys.

### Environment Variables

For local development, the following environment variables are used:

```env
SENDGRID_API_KEY=your_sendgrid_api_key
SENDGRID_FROM_EMAIL=your_verified_sender_email
```

### Production Secrets

In production, these values are stored as Supabase secrets:
- `SENDGRID_API_KEY`
- `SENDGRID_FROM_EMAIL`

## 📧 Email Types

### Welcome Emails
- Triggered automatically when a user signs up
- Sent via the auth webhook function
- Includes platform introduction and next steps

### Password Reset
- Triggered when user requests password reset
- Handled by Supabase Auth with custom templates

### Profile Completion Reminders
- Sent to users with incomplete profiles
- Triggered by scheduled functions or manual processes

## 🧪 Testing

### Local Testing

1. Ensure environment variables are set in `.env`
2. Run the development server
3. Test email functionality through the application

### Production Testing

1. Deploy Edge Functions
2. Configure webhooks
3. Test with a new user registration

## 🔧 Troubleshooting

### Common Issues

1. **Emails not sending**: Check API key configuration and Supabase secrets
2. **Webhook not triggering**: Verify webhook URL and event configuration
3. **Sender verification**: Ensure sender email is verified in SendGrid

### Debug Steps

1. Check Supabase Edge Function logs
2. Verify SendGrid activity dashboard
3. Test API key permissions
4. Confirm webhook configuration

## 📚 Related Documentation

- [SendGrid Deployment Guide](./DEPLOY_SENDGRID.md) - Detailed deployment instructions
- [Resend Setup Guide](./RESEND_SETUP_GUIDE.md) - Alternative email service setup

## 🎯 Next Steps

After completing this setup:

1. Test email functionality thoroughly
2. Configure email templates in SendGrid
3. Set up monitoring and alerts
4. Plan for scaling (upgrade SendGrid plan if needed)

---

**Last Updated**: 2025-01-11  
**Status**: ✅ Consolidated from multiple setup guides  
**Security**: ✅ Server-side only implementation
