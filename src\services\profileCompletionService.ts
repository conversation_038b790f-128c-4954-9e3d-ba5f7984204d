import { computed, ref } from 'vue'
import { useProfileQuestions } from './profileQuestions/index'
import type { BaseProfile } from '@/stores/profile'

// Define types for profile completion tracking
export interface ProfileField {
  name: string
  required: boolean
}

export interface ProfileSection {
  title: string
  fields: ProfileField[]
}

// Create a composable function for profile completion
export function useProfileCompletion() {
  const { currentProfileType, currentQuestions, profileQuestionsMap } = useProfileQuestions()

  // Calculate profile completion percentage based on the 3-step process
  const calculateCompletion = (profile: BaseProfile, profileData: any = {}) => {
    if (!profile) return 0

    // Define the three steps in the profile completion process
    const steps = [
      { name: 'profile_category', completed: !!profile.profile_type },
      { name: 'personal_details', completed: !!profile.first_name && !!profile.last_name },
      { name: 'profile_details', completed: false }
    ]

    // For the third step (profile details), check if required fields are completed
    if (profile.profile_type && profileQuestionsMap[profile.profile_type]) {
      const questions = profileQuestionsMap[profile.profile_type]

      // Get all required fields from the profile type
      const requiredFields: string[] = []

      // Add null checks for questions, sections and fields
      if (questions && questions.sections) {
        questions.sections.forEach(section => {
          if (section && section.questions) {
            section.questions.forEach(question => {
              if (question && question.required) {
                requiredFields.push(question.name)
              }
            })
          }
        })
      }

      // Count completed specialized fields
      let completedSpecializedFields = 0
      const totalSpecializedFields = requiredFields.length

      if (totalSpecializedFields > 0) {
        requiredFields.forEach(field => {
          if (profileData[field]) {
            completedSpecializedFields++
          }
        })

        // Mark the third step as completed if all required fields are filled
        steps[2].completed = completedSpecializedFields === totalSpecializedFields
      }
    }

    // Calculate overall completion percentage based on completed steps
    const completedSteps = steps.filter(step => step.completed).length
    return Math.round((completedSteps / steps.length) * 100)
  }

  // Get required fields for a profile type
  const getRequiredFields = (profileType: string): string[] => {
    if (!profileType || !profileQuestionsMap[profileType]) {
      return []
    }

    const questions = profileQuestionsMap[profileType]
    const requiredFields: string[] = []

    if (questions && questions.sections) {
      questions.sections.forEach(section => {
        if (section && section.questions) {
          section.questions.forEach(question => {
            if (question && question.required) {
              requiredFields.push(question.name)
            }
          })
        }
      })
    }

    return requiredFields
  }

  // Check if a profile is complete
  const isProfileComplete = computed(() => {
    return (profile: BaseProfile, profileData: any = {}): boolean => {
      return calculateCompletion(profile, profileData) >= 100
    }
  })

  // Get missing required fields
  const getMissingFields = (profile: BaseProfile, profileData: any = {}): string[] => {
    if (!profile || !profile.profile_type) {
      return []
    }

    const requiredFields = getRequiredFields(profile.profile_type)
    return requiredFields.filter(field => !profileData[field])
  }

  return {
    calculateCompletion,
    getRequiredFields,
    isProfileComplete,
    getMissingFields
  }
}
