<template>
  <q-dialog
    v-model="showDialog"
    persistent
    maximized
    transition-show="slide-up"
    transition-hide="slide-down"
  >
    <q-card class="message-dialog" :flat="$q.screen.lt.md">
      <q-card-section class="row items-center bg-primary text-white">
        <div class="text-h6">
          <q-avatar v-if="recipient" size="28px" class="q-mr-sm">
            <img v-if="recipient.avatar_url" :src="recipient.avatar_url" />
            <div v-else class="bg-white text-primary flex flex-center full-height">
              {{ getInitials(recipient.first_name, recipient.last_name) }}
            </div>
          </q-avatar>
          {{ dialogTitle }}
        </div>
        <q-space />
        <q-btn
          icon="close"
          flat
          round
          dense
          v-close-popup
          aria-label="Close"
        />
      </q-card-section>

      <q-card-section class="message-container q-pa-none">
        <div class="messages-list" ref="messagesListRef">
          <div v-if="loading" class="text-center q-pa-lg">
            <q-spinner color="primary" size="3em" />
            <p>Loading messages...</p>
          </div>

          <div v-else-if="error" class="text-center q-pa-lg text-negative">
            <p>{{ error }}</p>
            <q-btn color="primary" label="Retry" @click="loadMessages" />
          </div>

          <div v-else-if="messages.length === 0" class="text-center q-pa-lg">
            <p>No messages yet. Start the conversation!</p>
          </div>

          <div v-else class="q-pa-md">
            <div
              v-for="message in sortedMessages"
              :key="message.id"
              class="message-item q-mb-md"
              :class="{ 'sent': message.sender_id === currentUserId, 'received': message.sender_id !== currentUserId }"
            >
              <div class="message-bubble">
                {{ message.content }}
              </div>
              <div class="message-meta text-caption">
                {{ formatDate(message.created_at) }}
              </div>
            </div>
          </div>
        </div>
      </q-card-section>

      <q-card-section class="message-input-container q-pa-md">
        <q-form @submit="sendMessage">
          <div class="row items-center">
            <div class="col">
              <q-input
                v-model="newMessage"
                outlined
                dense
                placeholder="Type your message..."
                autofocus
                :disable="sendingMessage"
                @keydown.enter.prevent="sendMessage"
              />
            </div>
            <div class="col-auto q-ml-sm">
              <q-btn
                color="primary"
                icon="send"
                round
                type="submit"
                :loading="sendingMessage"
                :disable="!newMessage.trim()"
              />
            </div>
          </div>
        </q-form>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue';
import { date, useQuasar } from 'quasar';
import { useMessagingStore, type Message } from '../../stores/messaging';
import { useAuthStore } from '../../stores/auth';
import { useProfileStore } from '../../stores/profile';
import { useNotificationStore } from '../../stores/notifications';
import { getUniversalUsername } from '../../utils/userUtils';

const props = defineProps({
  userId: {
    type: String,
    required: true
  },
  userName: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['close']);

// Services and stores
const messagingStore = useMessagingStore();
const authStore = useAuthStore();
const profileStore = useProfileStore();
const notifications = useNotificationStore();
const $q = useQuasar();

// State
const showDialog = ref(true);
const loading = ref(false);
const error = ref<string | null>(null);
const messages = ref<Message[]>([]);
const newMessage = ref('');
const sendingMessage = ref(false);
const recipient = ref<any>(null);
const messagesListRef = ref<HTMLElement | null>(null);

// Computed
const currentUserId = computed(() => authStore.currentUser?.id || '');
const dialogTitle = computed(() => {
  if (recipient.value) {
    return `Message ${getUniversalUsername(recipient.value)}`;
  }
  return props.userName ? `Message ${props.userName}` : 'New Message';
});

const sortedMessages = computed(() => {
  return [...messages.value].sort((a, b) => {
    return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
  });
});

// Watch for dialog close
watch(showDialog, (newValue) => {
  if (!newValue) {
    emit('close');
  }
});

// Load recipient profile
onMounted(async () => {
  try {
    // Load recipient profile
    const profile = await profileStore.loadProfileById(props.userId);
    if (profile) {
      recipient.value = profile;
    }
  } catch (err) {
    console.error('Error loading recipient profile:', err);
  }

  loadMessages();
});

// Methods
async function loadMessages() {
  loading.value = true;
  error.value = null;

  try {
    console.log('Loading messages for user:', props.userId);

    // Initialize messages array as empty
    messages.value = [];

    // Try to load messages
    try {
      const result = await messagingStore.loadMessages(props.userId);

      if (result && result.length > 0) {
        console.log(`Loaded ${result.length} messages`);
        messages.value = result;

        // Mark all messages as read
        try {
          await messagingStore.markAllMessagesAsRead(props.userId);
        } catch (markError) {
          console.error('Error marking messages as read:', markError);
          // Continue even if marking as read fails
        }
      } else {
        console.log('No messages found');
      }
    } catch (loadError) {
      console.error('Error loading messages:', loadError);
      error.value = loadError.message || 'Failed to load messages';
      // Continue with empty messages array
    }

    // Scroll to bottom after messages load (even if empty)
    await nextTick();
    scrollToBottom();
  } catch (err: any) {
    console.error('Unexpected error in loadMessages:', err);
    error.value = err.message || 'An unexpected error occurred';
  } finally {
    loading.value = false;
  }
}

async function sendMessage() {
  if (!newMessage.value.trim()) return;

  sendingMessage.value = true;

  try {
    // Store the message content before clearing the input
    const messageContent = newMessage.value;

    // Clear the input field immediately for better UX
    newMessage.value = '';

    // Send the message
    const success = await messagingStore.sendMessage(props.userId, messageContent);

    if (success) {
      console.log('Message sent successfully:', messageContent);

      // Add the message to the local UI immediately for better UX
      const tempMessage = {
        id: `temp-${Date.now()}`,
        sender_id: currentUserId.value,
        recipient_id: props.userId,
        content: messageContent,
        is_read: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        sender: {
          id: currentUserId.value,
          email: '',
          first_name: '',
          last_name: '',
          avatar_url: ''
        }
      };

      // Add to local messages array
      messages.value.push(tempMessage);

      // Scroll to bottom immediately
      await nextTick();
      scrollToBottom();

      // Wait a moment before reloading messages to ensure the message is saved
      setTimeout(async () => {
        try {
          await loadMessages();
          // Scroll to bottom after messages load
          await nextTick();
          scrollToBottom();
        } catch (loadErr) {
          console.error('Error loading messages after send:', loadErr);
        }
      }, 1000);
    } else {
      notifications.error('Failed to send message. Please try again.');
      // Restore the message content if sending failed
      newMessage.value = messageContent;
    }
  } catch (err) {
    console.error('Error sending message:', err);
    notifications.error('An error occurred while sending your message.');
  } finally {
    sendingMessage.value = false;
  }
}

function scrollToBottom() {
  if (messagesListRef.value) {
    messagesListRef.value.scrollTop = messagesListRef.value.scrollHeight;
  }
}

function formatDate(dateString: string): string {
  if (!dateString) return '';

  const messageDate = new Date(dateString);
  const today = new Date();

  // If the message is from today, just show the time
  if (messageDate.toDateString() === today.toDateString()) {
    return date.formatDate(dateString, 'h:mm A');
  }

  // If the message is from this year, show the month and day
  if (messageDate.getFullYear() === today.getFullYear()) {
    return date.formatDate(dateString, 'MMM D, h:mm A');
  }

  // Otherwise, show the full date
  return date.formatDate(dateString, 'MMM D, YYYY, h:mm A');
}

function getInitials(firstName?: string, lastName?: string): string {
  const first = firstName ? firstName.charAt(0) : '';
  const last = lastName ? lastName.charAt(0) : '';
  return (first + last).toUpperCase();
}
</script>

<style scoped>
.message-dialog {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.message-container {
  flex: 1;
  overflow: hidden;
}

.messages-list {
  height: 100%;
  overflow-y: auto;
}

.message-item {
  display: flex;
  flex-direction: column;
  max-width: 80%;
}

.message-item.sent {
  align-self: flex-end;
  margin-left: auto;
}

.message-item.received {
  align-self: flex-start;
  margin-right: auto;
}

.message-bubble {
  padding: 8px 12px;
  border-radius: 12px;
  word-break: break-word;
}

.message-item.sent .message-bubble {
  background-color: #0D8A3E;
  color: white;
  border-bottom-right-radius: 4px;
}

.message-item.received .message-bubble {
  background-color: #f0f0f0;
  color: #333;
  border-bottom-left-radius: 4px;
}

.message-meta {
  margin-top: 2px;
  opacity: 0.7;
  align-self: flex-end;
}

.message-item.sent .message-meta {
  text-align: right;
}

.message-input-container {
  border-top: 1px solid #e0e0e0;
}

@media (min-width: 768px) {
  .message-dialog {
    width: 600px;
    height: 80vh;
    max-height: 600px;
  }
}
</style>
