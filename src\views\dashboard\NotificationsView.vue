<template>
  <q-page class="q-pa-md">
    <div class="row q-col-gutter-md">
      <div class="col-12">
        <q-card class="content-header-card bg-primary text-white">
          <q-card-section>
            <div class="text-h4">Notifications</div>
            <p class="text-body1 q-mt-md">
              View and manage your notifications.
            </p>
          </q-card-section>
        </q-card>
      </div>

      <!-- Notifications Section -->
      <div class="col-12">
        <q-card class="notification-section-card">
          <q-card-section class="bg-grey-2">
            <div class="row items-center justify-between">
              <div class="col">
                <div class="text-subtitle1 text-primary">
                  All Notifications
                  <notification-badge
                    v-if="unreadCount > 0"
                    :count="unreadCount"
                    color="red"
                    rounded
                    class="q-ml-sm"
                  />
                </div>
                <div class="text-caption text-grey">Your recent notifications</div>
              </div>
              <div>
                <q-btn
                  v-if="unreadCount > 0"
                  flat
                  color="primary"
                  label="Mark All as Read"
                  icon="done_all"
                  @click="markAllAsRead"
                  :loading="markingAll"
                  size="sm"
                />
              </div>
            </div>
          </q-card-section>
          <q-card-section>
            <div class="q-mb-md">
              <q-btn-toggle
                v-model="filterType"
                toggle-color="primary"
                :options="[
                  { label: 'All', value: 'all' },
                  { label: 'Unread', value: 'unread' },
                  { label: 'Connections', value: 'connection' },
                  { label: 'Messages', value: 'message' }
                ]"
                size="sm"
                class="q-mb-md"
              />
            </div>
            <notifications-list />
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { onMounted, computed, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { useAuthStore } from '../../stores/auth';
import { useUserNotificationsStore } from '../../stores/userNotifications';
import { useActivityNotificationsStore } from '../../stores/activityNotifications';
import NotificationsList from '../../components/notifications/NotificationsList.vue';
import NotificationBadge from '../../components/common/NotificationBadge.vue';

const router = useRouter();
const $q = useQuasar();
const authStore = useAuthStore();
const userNotificationsStore = useUserNotificationsStore();
const activityNotificationsStore = useActivityNotificationsStore();

// UI state
const filterType = ref('all');
const markingAll = ref(false);

// Computed properties
const unreadCount = computed(() => userNotificationsStore.unreadCount);

// Watch for filter changes
watch(filterType, async (newValue) => {
  await loadNotifications();
});

// Function to load notifications based on filter
async function loadNotifications() {
  try {
    // Determine if we should only show unread notifications
    const unreadOnly = filterType.value === 'unread';

    // Fetch notifications
    await userNotificationsStore.fetchNotifications(20, 1, unreadOnly);
  } catch (error) {
    console.error('Error loading notifications:', error);
  }
}

// Function to mark all notifications as read
async function markAllAsRead() {
  try {
    markingAll.value = true;

    const success = await userNotificationsStore.markAllAsRead();

    if (success) {
      $q.notify({
        color: 'positive',
        message: 'All notifications marked as read',
        icon: 'done_all',
        position: 'top',
        timeout: 2000
      });

      // Also mark activity notifications as read
      activityNotificationsStore.markActivitiesAsRead();
      activityNotificationsStore.markConnectionRequestsAsViewed();
    }
  } catch (error) {
    console.error('Error marking all notifications as read:', error);

    $q.notify({
      color: 'negative',
      message: 'Failed to mark notifications as read',
      icon: 'error',
      position: 'top',
      timeout: 2000
    });
  } finally {
    markingAll.value = false;
  }
}

// Check if user is authenticated and initialize data
onMounted(async () => {
  if (!authStore.isAuthenticated) {
    router.push('/login');
    $q.notify({
      color: 'negative',
      message: 'You must be logged in to view this page',
      icon: 'error'
    });
    return;
  }

  // Initialize user notifications store
  await userNotificationsStore.initialize();

  // Load notifications
  await loadNotifications();

  // Mark connection requests as viewed since the user is now viewing notifications
  activityNotificationsStore.markConnectionRequestsAsViewed();
  activityNotificationsStore.markActivitiesAsRead();
});
</script>

<style scoped>
.content-header-card {
  border-radius: 8px;
  overflow: hidden;
}

.notification-section-card {
  border-radius: 8px;
  overflow: hidden;
}
</style>
