# Auth Webhook Function

This function handles Supabase Auth webhooks for user events, specifically for sending welcome emails when new users sign up.

## How It Works

1. Supabase Auth sends a webhook to this function when a user is created
2. The function extracts the user's email and other information from the webhook payload
3. It generates a welcome email and sends it using SendGrid
4. It returns a success or error response

## Deployment

To deploy this function, run the `deploy-auth-webhook.bat` script in the root directory:

```bash
./deploy-auth-webhook.bat
```

## Webhook Configuration

After deploying the function, you need to configure the webhook in the Supabase dashboard:

1. Go to your Supabase project dashboard
2. Navigate to Authentication > Webhooks
3. Click "Add a new webhook"
4. Select the "User Created" event
5. Enter the function URL: `https://<your-project-id>.functions.supabase.co/auth-webhook`
6. Save the webhook configuration

## Environment Variables

The function uses the following environment variables:

- `SENDGRID_API_KEY`: Your SendGrid API key
- `SENDGRID_FROM_EMAIL`: The email address to send from (default: <EMAIL>)
- `SENDGRID_FROM_NAME`: The name to display as the sender (default: ZbInnovation)
- `SITE_URL`: The base URL of your website (default: https://ZbInnovation.co.zw)

These should be set as secrets in your Supabase project.

## Testing

You can test the webhook by creating a new user in your application. The function should receive the webhook and send a welcome email to the new user.
