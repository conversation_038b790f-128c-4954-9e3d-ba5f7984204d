// Investor Profile Questions
import { ProfileType } from '../types';
import { commonOptions } from '../common';
import { bioSection, socialMediaSection, businessContactSection, locationSection, goalsSection } from '../sections/index';

// Investor profile questions
export const investorProfile: ProfileType = {
  type: 'investor',
  displayName: 'Investor',
  sections: [
    // Bio & Basic Information (common across all profiles)
    bioSection,

    // Investment Preferences and Strategy
    {
      title: 'Investment Preferences',
      icon: 'attach_money',
      description: 'Tell us about your investment preferences',
      questions: [
        {
          id: 'investment_focus',
          name: 'investment_focus',
          label: 'Investment Focus',
          type: 'multi-select',
          required: true,
          options: 'investmentFocusOptions',
          fullWidth: true,
          hint: 'What areas do you focus your investments on?'
        },
        {
          id: 'investment_stage',
          name: 'investment_stage',
          label: 'Investment Stage',
          type: 'multi-select',
          required: true,
          options: 'investmentStageOptions',
          fullWidth: true,
          hint: 'What stages do you typically invest in?'
        },
        {
          id: 'ticket_size',
          name: 'ticket_size',
          label: 'Investment Range',
          type: 'select',
          required: true,
          options: 'ticketSizeOptions',
          hint: 'What is your typical investment range?'
        },
        {
          id: 'previous_investments',
          name: 'previous_investments',
          label: 'Previous Investments',
          type: 'number',
          hint: 'How many investments have you made?'
        },
        {
          id: 'investment_geography',
          name: 'investment_geography',
          label: 'Investment Geography',
          type: 'multi-select',
          options: 'geographyOptions',
          fullWidth: true,
          hint: 'What geographical areas do you invest in?'
        }
      ]
    },
    {
      title: 'Investment Criteria',
      icon: 'rule',
      description: 'Tell us about your investment criteria',
      questions: [
        {
          id: 'investment_criteria',
          name: 'investment_criteria',
          label: 'Key Investment Criteria',
          type: 'multi-select',
          options: 'criteriaOptions',
          fullWidth: true,
          hint: 'What are your key criteria for investments?'
        },
        {
          id: 'success_stories',
          name: 'success_stories',
          label: 'Success Stories',
          type: 'textarea',
          fullWidth: true,
          hint: 'Share some of your investment success stories'
        },
        {
          id: 'portfolio',
          name: 'portfolio',
          label: 'Portfolio',
          type: 'text',
          fullWidth: true,
          hint: 'List some companies in your portfolio'
        }
      ]
    },
    {
      title: 'Firm Details',
      icon: 'business',
      description: 'Tell us about your investment firm',
      questions: [
        {
          id: 'firm_name',
          name: 'firm_name',
          label: 'Firm Name',
          type: 'text',
          hint: 'Name of your investment firm (if applicable)'
        },
        {
          id: 'firm_type',
          name: 'firm_type',
          label: 'Firm Type',
          type: 'select',
          options: 'firmTypeOptions',
          hint: 'Type of investment firm'
        },
        {
          id: 'firm_size',
          name: 'firm_size',
          label: 'Firm Size',
          type: 'number',
          hint: 'Number of employees at your firm'
        },
        {
          id: 'firm_website',
          name: 'firm_website',
          label: 'Firm Website',
          type: 'text',
          hint: 'Website of your investment firm'
        }
      ]
    },

    // Investment Philosophy and Approach
    {
      title: 'Investment Philosophy',
      icon: 'psychology',
      description: 'Tell us about your investment philosophy and approach',
      questions: [
        {
          id: 'investment_philosophy',
          name: 'investment_philosophy',
          label: 'Investment Philosophy',
          type: 'textarea',
          fullWidth: true,
          hint: 'Describe your overall investment philosophy and approach'
        },
        {
          id: 'value_add',
          name: 'value_add',
          label: 'Value Addition',
          type: 'multi-select',
          options: 'valueAddOptions',
          fullWidth: true,
          hint: 'How do you add value to your portfolio companies?'
        },
        {
          id: 'investment_horizon',
          name: 'investment_horizon',
          label: 'Investment Horizon',
          type: 'select',
          options: 'investmentHorizonOptions',
          hint: 'What is your typical investment horizon?'
        },
        {
          id: 'exit_strategy',
          name: 'exit_strategy',
          label: 'Exit Strategy',
          type: 'multi-select',
          options: 'exitStrategyOptions',
          hint: 'What exit strategies do you typically pursue?'
        }
      ]
    },

    // Investment Performance
    {
      title: 'Investment Performance',
      icon: 'trending_up',
      description: 'Tell us about your investment performance',
      questions: [
        {
          id: 'portfolio_size',
          name: 'portfolio_size',
          label: 'Portfolio Size',
          type: 'select',
          options: 'portfolioSizeOptions',
          hint: 'What is the total size of your investment portfolio?'
        },
        {
          id: 'average_return',
          name: 'average_return',
          label: 'Average Annual Return',
          type: 'select',
          options: 'returnRateOptions',
          hint: 'What is your average annual return on investments?'
        },
        {
          id: 'successful_exits',
          name: 'successful_exits',
          label: 'Successful Exits',
          type: 'number',
          hint: 'How many successful exits have you had?'
        },
        {
          id: 'notable_investments',
          name: 'notable_investments',
          label: 'Notable Investments',
          type: 'textarea',
          fullWidth: true,
          hint: 'Describe some of your most notable investments'
        }
      ]
    },

    // Due Diligence Process
    {
      title: 'Due Diligence Process',
      icon: 'fact_check',
      description: 'Tell us about your due diligence process',
      questions: [
        {
          id: 'due_diligence_process',
          name: 'due_diligence_process',
          label: 'Due Diligence Process',
          type: 'textarea',
          fullWidth: true,
          hint: 'Describe your due diligence process'
        },
        {
          id: 'decision_timeline',
          name: 'decision_timeline',
          label: 'Decision Timeline',
          type: 'select',
          options: 'decisionTimelineOptions',
          hint: 'What is your typical timeline for making investment decisions?'
        },
        {
          id: 'key_metrics',
          name: 'key_metrics',
          label: 'Key Metrics',
          type: 'multi-select',
          options: 'keyMetricsOptions',
          fullWidth: true,
          hint: 'What key metrics do you look at when evaluating investments?'
        },
        {
          id: 'red_flags',
          name: 'red_flags',
          label: 'Red Flags',
          type: 'multi-select',
          options: 'redFlagsOptions',
          fullWidth: true,
          hint: 'What are your investment red flags?'
        }
      ]
    },

    // Social Media Section (common across all profiles)
    socialMediaSection,

    // Business Contact Information (separate from personal contact details)
    businessContactSection,

    // Location Information (common across all profiles)
    locationSection,

    // Goals and Interests (common across all profiles for matchmaking)
    goalsSection
  ],
  options: {
    ...commonOptions,
    // Investment Focus and Stage Options
    investmentFocusOptions: commonOptions.investmentFocusOptions,
    investmentStageOptions: commonOptions.investmentStageOptions,
    ticketSizeOptions: commonOptions.investmentRangeOptions,
    geographyOptions: commonOptions.preferredLocationsOptions,

    // Investment Criteria Options
    criteriaOptions: [
      'Team Experience', 'Market Size', 'Traction',
      'Technology', 'Business Model', 'Competitive Advantage',
      'Scalability', 'Exit Potential', 'Social Impact',
      'Revenue Growth', 'Profitability', 'Unit Economics',
      'Customer Acquisition Cost', 'Lifetime Value', 'Retention Rates',
      'Intellectual Property', 'Regulatory Compliance', 'Environmental Impact',
      'Diversity & Inclusion', 'Governance Structure', 'Other'
    ],

    // Firm Type Options
    firmTypeOptions: commonOptions.investorTypeOptions,

    // Investment Philosophy Options
    valueAddOptions: [
      'Strategic Guidance', 'Operational Support', 'Technical Expertise',
      'Industry Connections', 'Customer Introductions', 'Talent Acquisition',
      'Follow-on Funding', 'International Expansion', 'Marketing Support',
      'Financial Management', 'Board Representation', 'Mentorship',
      'Legal Support', 'Regulatory Navigation', 'Other'
    ],

    investmentHorizonOptions: [
      'Less than 1 year', '1-3 years', '3-5 years',
      '5-7 years', '7-10 years', 'More than 10 years'
    ],

    exitStrategyOptions: [
      'Acquisition', 'IPO', 'Secondary Sale', 'Management Buyout',
      'Strategic Partnership', 'Merger', 'Recapitalization',
      'Revenue-based Financing', 'Other'
    ],

    // Performance Metrics Options
    portfolioSizeOptions: [
      'Under $100K', '$100K - $500K', '$500K - $1M',
      '$1M - $5M', '$5M - $10M', '$10M - $50M',
      '$50M - $100M', '$100M - $500M', 'Over $500M'
    ],

    returnRateOptions: [
      'Less than 5%', '5-10%', '10-15%', '15-20%',
      '20-25%', '25-30%', 'Over 30%'
    ],

    // Due Diligence Options
    decisionTimelineOptions: [
      'Less than 2 weeks', '2-4 weeks', '1-2 months',
      '2-3 months', '3-6 months', 'More than 6 months'
    ],

    keyMetricsOptions: [
      'Revenue Growth', 'Customer Acquisition Cost', 'Lifetime Value',
      'Churn Rate', 'Gross Margin', 'Burn Rate', 'Runway',
      'Market Size', 'Market Growth Rate', 'Market Share',
      'Team Experience', 'Technology Differentiation', 'IP Protection',
      'Competitive Landscape', 'Unit Economics', 'Scalability',
      'Regulatory Risks', 'Exit Opportunities', 'Other'
    ],

    redFlagsOptions: [
      'Inexperienced Team', 'High Burn Rate', 'Limited Traction',
      'Small Market Size', 'Intense Competition', 'Weak Unit Economics',
      'Regulatory Challenges', 'Unclear Business Model', 'Poor Execution',
      'Lack of Focus', 'Unrealistic Valuation', 'Governance Issues',
      'Legal Disputes', 'High Customer Concentration', 'Other'
    ]
  }
}
