#!/bin/bash

# Security Configuration Deployment Script
# Usage: ./scripts/deploy-security.sh [environment]
# Environments: dev, test, production

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [environment]"
    echo ""
    echo "Environments:"
    echo "  dev        - Development configuration (localhost)"
    echo "  test       - Test configuration (ZbInnovation.co.zw)"
    echo "  production - Production configuration (fullsite.zbinnovation.co.zw)"
    echo ""
    echo "Examples:"
    echo "  $0 test        # Deploy test configuration"
    echo "  $0 production  # Deploy production configuration"
    echo "  $0 dev         # Restore development configuration"
}

# Function to backup current .htaccess
backup_htaccess() {
    if [ -f "public/.htaccess" ]; then
        cp "public/.htaccess" "public/.htaccess.backup.$(date +%Y%m%d_%H%M%S)"
        print_status "Backed up current .htaccess file"
    fi
}

# Function to deploy configuration
deploy_config() {
    local env=$1
    local source_file=""
    local domain=""
    
    case $env in
        "dev")
            # Restore original development configuration
            if [ -f "public/.htaccess.dev" ]; then
                source_file="public/.htaccess.dev"
            else
                print_warning "Development .htaccess not found, using git to restore"
                git checkout HEAD -- public/.htaccess 2>/dev/null || {
                    print_error "Could not restore development .htaccess"
                    exit 1
                }
                print_success "Restored development configuration"
                return 0
            fi
            domain="localhost"
            ;;
        "test")
            source_file="public/.htaccess.test"
            domain="ZbInnovation.co.zw"
            ;;
        "production")
            source_file="public/.htaccess.production"
            domain="fullsite.zbinnovation.co.zw"
            ;;
        *)
            print_error "Invalid environment: $env"
            show_usage
            exit 1
            ;;
    esac
    
    # Check if source file exists
    if [ ! -f "$source_file" ]; then
        print_error "Configuration file not found: $source_file"
        exit 1
    fi
    
    # Backup current configuration
    backup_htaccess
    
    # Copy new configuration
    cp "$source_file" "public/.htaccess"
    print_success "Deployed $env configuration for $domain"
}

# Function to validate configuration
validate_config() {
    local env=$1
    
    print_status "Validating configuration..."
    
    # Check if .htaccess exists
    if [ ! -f "public/.htaccess" ]; then
        print_error ".htaccess file not found"
        exit 1
    fi
    
    # Check for basic Apache syntax (very basic check)
    if ! grep -q "RewriteEngine On" "public/.htaccess"; then
        print_warning "RewriteEngine directive not found - this might be intentional"
    fi
    
    # Check for security headers
    if grep -q "Content-Security-Policy" "public/.htaccess"; then
        print_success "Content Security Policy found"
    else
        print_warning "Content Security Policy not found"
    fi
    
    # Environment-specific validations
    case $env in
        "test")
            if grep -q "ZbInnovation.co.zw" "public/.htaccess"; then
                print_success "Test domain configuration found"
            else
                print_warning "Test domain not found in CSP"
            fi
            ;;
        "production")
            if grep -q "fullsite.zbinnovation.co.zw" "public/.htaccess"; then
                print_success "Production domain configuration found"
            else
                print_warning "Production domain not found in CSP"
            fi
            ;;
    esac
    
    print_success "Configuration validation completed"
}

# Function to show current configuration info
show_current_config() {
    print_status "Current .htaccess configuration:"
    
    if [ ! -f "public/.htaccess" ]; then
        print_error ".htaccess file not found"
        return 1
    fi
    
    # Try to determine current environment
    if grep -q "ZbInnovation.co.zw" "public/.htaccess"; then
        echo "  Environment: TEST (ZbInnovation.co.zw)"
    elif grep -q "fullsite.zbinnovation.co.zw" "public/.htaccess"; then
        echo "  Environment: PRODUCTION (fullsite.zbinnovation.co.zw)"
    else
        echo "  Environment: DEVELOPMENT (localhost)"
    fi
    
    # Check HTTPS redirect
    if grep -q "RewriteRule.*https" "public/.htaccess" && ! grep -q "^#.*RewriteRule.*https" "public/.htaccess"; then
        echo "  HTTPS Redirect: ENABLED"
    else
        echo "  HTTPS Redirect: DISABLED"
    fi
    
    # Check HSTS
    if grep -q "Strict-Transport-Security" "public/.htaccess" && ! grep -q "^#.*Strict-Transport-Security" "public/.htaccess"; then
        echo "  HSTS: ENABLED"
    else
        echo "  HSTS: DISABLED"
    fi
    
    # Check CSP
    if grep -q "Content-Security-Policy" "public/.htaccess" && ! grep -q "^#.*Content-Security-Policy" "public/.htaccess"; then
        echo "  CSP: ENABLED"
    else
        echo "  CSP: DISABLED"
    fi
}

# Main script logic
main() {
    print_status "ZB Innovation Hub - Security Configuration Deployment"
    echo ""
    
    # Check if we're in the right directory
    if [ ! -d "public" ] || [ ! -f "package.json" ]; then
        print_error "This script must be run from the project root directory"
        exit 1
    fi
    
    # If no arguments, show current config and usage
    if [ $# -eq 0 ]; then
        show_current_config
        echo ""
        show_usage
        exit 0
    fi
    
    local environment=$1
    
    # Special commands
    case $environment in
        "status"|"info")
            show_current_config
            exit 0
            ;;
        "help"|"-h"|"--help")
            show_usage
            exit 0
            ;;
    esac
    
    # Deploy configuration
    print_status "Deploying $environment configuration..."
    deploy_config "$environment"
    
    # Validate configuration
    validate_config "$environment"
    
    echo ""
    print_success "Security configuration deployment completed!"
    print_status "Remember to:"
    echo "  1. Ensure SSL certificate is installed (for test/production)"
    echo "  2. Test the application thoroughly"
    echo "  3. Monitor browser console for CSP violations"
    echo "  4. Check that images and external resources load properly"
}

# Run main function with all arguments
main "$@"
