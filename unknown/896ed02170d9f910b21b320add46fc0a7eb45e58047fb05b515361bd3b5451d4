/**
 * Initialize Matchmaking Rules
 * 
 * This file contains functions to initialize the matchmaking rules in the database.
 * It defines the default rules for matching different profile types.
 */

import { SupabaseClient } from '@supabase/supabase-js';

/**
 * Initialize matchmaking rules in the database
 * @param supabase Supabase client
 */
export async function initializeMatchmakingRules(supabase: SupabaseClient): Promise<void> {
  console.log('Initializing matchmaking rules...');
  
  // Check if rules already exist
  const { data: existingRules, error: checkError } = await supabase
    .from('matchmaking_rules')
    .select('id')
    .limit(1);
    
  if (checkError) {
    console.error('Error checking existing rules:', checkError);
    return;
  }
  
  // If rules already exist, don't recreate them
  if (existingRules && existingRules.length > 0) {
    console.log('Matchmaking rules already exist. Skipping initialization.');
    return;
  }
  
  // Define the default rules
  const defaultRules = [
    // Innovator to Investor rules
    {
      source_profile_type: 'innovator',
      target_profile_type: 'investor',
      rule_name: 'industry_alignment',
      source_fields: { path: 'industry' },
      target_fields: { path: 'investment_focus' },
      weight: 0.3
    },
    {
      source_profile_type: 'innovator',
      target_profile_type: 'investor',
      rule_name: 'stage_compatibility',
      source_fields: { path: 'innovation_stage' },
      target_fields: { path: 'investment_stage' },
      weight: 0.3
    },
    {
      source_profile_type: 'innovator',
      target_profile_type: 'investor',
      rule_name: 'funding_match',
      source_fields: { path: 'funding_amount' },
      target_fields: { min_path: 'investment_range.min', max_path: 'investment_range.max' },
      weight: 0.2
    },
    {
      source_profile_type: 'innovator',
      target_profile_type: 'investor',
      rule_name: 'location_match',
      source_fields: { path: 'preferred_locations' },
      target_fields: { path: 'preferred_locations' },
      weight: 0.1
    },
    {
      source_profile_type: 'innovator',
      target_profile_type: 'investor',
      rule_name: 'goals_alignment',
      source_fields: { path: 'short_term_goals' },
      target_fields: { path: 'collaboration_interests' },
      weight: 0.1
    },
    
    // Innovator to Mentor rules
    {
      source_profile_type: 'innovator',
      target_profile_type: 'mentor',
      rule_name: 'expertise_match',
      source_fields: { path: 'current_challenges' },
      target_fields: { path: 'expertise_areas' },
      weight: 0.35
    },
    {
      source_profile_type: 'innovator',
      target_profile_type: 'mentor',
      rule_name: 'industry_match',
      source_fields: { path: 'industry' },
      target_fields: { path: 'industry_experience' },
      weight: 0.2
    },
    {
      source_profile_type: 'innovator',
      target_profile_type: 'mentor',
      rule_name: 'stage_match',
      source_fields: { path: 'innovation_stage' },
      target_fields: { path: 'preferred_mentee_stage' },
      weight: 0.15
    },
    {
      source_profile_type: 'innovator',
      target_profile_type: 'mentor',
      rule_name: 'goals_match',
      source_fields: { path: 'short_term_goals' },
      target_fields: { path: 'collaboration_interests' },
      weight: 0.15
    },
    {
      source_profile_type: 'innovator',
      target_profile_type: 'mentor',
      rule_name: 'approach_match',
      source_fields: { path: 'looking_for' },
      target_fields: { path: 'mentoring_approach' },
      weight: 0.15
    },
    
    // Investor to Innovator rules
    {
      source_profile_type: 'investor',
      target_profile_type: 'innovator',
      rule_name: 'industry_alignment',
      source_fields: { path: 'investment_focus' },
      target_fields: { path: 'industry' },
      weight: 0.3
    },
    {
      source_profile_type: 'investor',
      target_profile_type: 'innovator',
      rule_name: 'stage_compatibility',
      source_fields: { path: 'investment_stage' },
      target_fields: { path: 'innovation_stage' },
      weight: 0.3
    },
    {
      source_profile_type: 'investor',
      target_profile_type: 'innovator',
      rule_name: 'funding_match',
      source_fields: { min_path: 'investment_range.min', max_path: 'investment_range.max' },
      target_fields: { path: 'funding_amount' },
      weight: 0.2
    },
    {
      source_profile_type: 'investor',
      target_profile_type: 'innovator',
      rule_name: 'location_match',
      source_fields: { path: 'preferred_locations' },
      target_fields: { path: 'preferred_locations' },
      weight: 0.1
    },
    {
      source_profile_type: 'investor',
      target_profile_type: 'innovator',
      rule_name: 'goals_alignment',
      source_fields: { path: 'collaboration_interests' },
      target_fields: { path: 'short_term_goals' },
      weight: 0.1
    },
    
    // Mentor to Innovator rules
    {
      source_profile_type: 'mentor',
      target_profile_type: 'innovator',
      rule_name: 'expertise_match',
      source_fields: { path: 'expertise_areas' },
      target_fields: { path: 'current_challenges' },
      weight: 0.35
    },
    {
      source_profile_type: 'mentor',
      target_profile_type: 'innovator',
      rule_name: 'industry_match',
      source_fields: { path: 'industry_experience' },
      target_fields: { path: 'industry' },
      weight: 0.2
    },
    {
      source_profile_type: 'mentor',
      target_profile_type: 'innovator',
      rule_name: 'stage_match',
      source_fields: { path: 'preferred_mentee_stage' },
      target_fields: { path: 'innovation_stage' },
      weight: 0.15
    },
    {
      source_profile_type: 'mentor',
      target_profile_type: 'innovator',
      rule_name: 'goals_match',
      source_fields: { path: 'collaboration_interests' },
      target_fields: { path: 'short_term_goals' },
      weight: 0.15
    },
    {
      source_profile_type: 'mentor',
      target_profile_type: 'innovator',
      rule_name: 'approach_match',
      source_fields: { path: 'mentoring_approach' },
      target_fields: { path: 'looking_for' },
      weight: 0.15
    }
    
    // Additional rules for other profile types can be added here
  ];
  
  // Insert the rules into the database
  const { error: insertError } = await supabase
    .from('matchmaking_rules')
    .insert(defaultRules);
    
  if (insertError) {
    console.error('Error inserting matchmaking rules:', insertError);
    return;
  }
  
  console.log('Matchmaking rules initialized successfully.');
}
