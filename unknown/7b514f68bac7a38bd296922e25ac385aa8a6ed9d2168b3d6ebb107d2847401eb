/**
 * Welcome email template
 */

/**
 * Generates a welcome email
 * @param email The recipient's email
 * @param firstName Optional first name of the user
 * @returns HTML and subject for the email
 */
export function generateWelcomeEmail(
  email: string, 
  firstName?: string
): { html: string; subject: string } {
  // Try to extract name from email if not provided
  const extractedName = !firstName ? extractNameFromEmail(email) : undefined;
  const greeting = firstName 
    ? `Hi ${firstName},` 
    : extractedName 
      ? `Hi ${extractedName},` 
      : 'Hi there,';

  const subject = 'Welcome to ZB Innovation Hub';

  const html = `
    <div style="max-width: 600px; margin: 0 auto; padding: 24px; font-family: Arial, sans-serif;">
      <div style="text-align: center; margin-bottom: 24px;">
        <img src="https://zbinnovation.com/logo.png" alt="ZB Innovation Hub" style="max-width: 200px;">
      </div>

      <h1 style="color: #0D8A3E; margin-bottom: 16px;">${greeting}</h1>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        Thank you for joining the ZB Innovation Hub platform. We're excited to have you on board!
      </p>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        Our platform connects innovators, mentors, organizations, and academic institutions to foster collaboration and drive innovation in Zimbabwe.
      </p>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        To get started, please complete your profile to unlock all the features of our platform:
      </p>

      <div style="text-align: center; margin: 32px 0;">
        <a href="https://zbinnovation.co.zw/dashboard" 
           style="background-color: #0D8A3E; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold; margin-right: 10px;">
          Go to Dashboard
        </a>
        <a href="https://zbinnovation.co.zw/dashboard/profile" 
           style="background-color: #a4ca39; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">
          Complete Profile
        </a>
      </div>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        If you have any questions or need assistance, please don't hesitate to contact our support team.
      </p>

      <p style="margin-bottom: 8px; line-height: 1.5;">
        Best regards,
      </p>

      <p style="line-height: 1.5;">
        The ZB Innovation Hub Team
      </p>

      <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666;">
        <p>
          © ${new Date().getFullYear()} ZB Innovation Hub. All rights reserved.
        </p>
        <p>
          This email was sent to ${email}
        </p>
      </div>
    </div>
  `;

  return { html, subject };
}

/**
 * Extracts a name from an email address
 * @param email The email address
 * @returns The extracted name or undefined
 */
function extractNameFromEmail(email: string): string | undefined {
  if (!email || !email.includes('@')) return undefined;
  
  const localPart = email.split('@')[0];
  
  // Remove numbers and special characters
  const nameOnly = localPart.replace(/[0-9_\-.+]/g, ' ').trim();
  
  // If nothing left, return undefined
  if (!nameOnly) return undefined;
  
  // Capitalize first letter of each word
  return nameOnly
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}
