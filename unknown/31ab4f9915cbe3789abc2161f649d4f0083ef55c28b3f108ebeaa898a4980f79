<template>
  <div class="q-pa-md">
    <p>These details will be used across all your profiles.</p>

    <div class="row q-col-gutter-md q-mt-md">
      <div class="col-12 col-md-6">
        <q-input
          v-model="formData.first_name"
          label="First Name *"
          outlined
          :rules="[val => !!val || 'First name is required']"
        />
      </div>

      <div class="col-12 col-md-6">
        <q-input
          v-model="formData.last_name"
          label="Last Name *"
          outlined
          :rules="[val => !!val || 'Last name is required']"
        />
      </div>

      <div class="col-12 col-md-6">
        <q-input
          v-model="formData.email"
          label="Email *"
          outlined
          type="email"
          readonly
          disable
        />
      </div>

      <div class="col-12 col-md-6">
        <phone-number-input
          v-model="phoneModel"
          @update:model-value="updatePhone"
        />
      </div>

      <div class="col-12 col-md-6">
        <q-select
          v-model="formData.gender"
          :options="genderOptions"
          label="Gender"
          outlined
          emit-value
          map-options
        />
      </div>

      <div class="col-12">
        <q-input
          v-model="formData.bio"
          label="Bio"
          type="textarea"
          outlined
          autogrow
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useNotificationStore } from '../../stores/notifications'

const notifications = useNotificationStore()

// Define props
const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  isCreatingNewProfile: {
    type: Boolean,
    default: false
  },
  profileCreatedInDatabase: {
    type: Boolean,
    default: false
  }
})

// Define emits
const emit = defineEmits(['update:modelValue', 'save'])

// Local form data
const formData = ref({
  first_name: '',
  last_name: '',
  email: '',
  phone_country_code: '',
  phone_number: '',
  gender: '',
  bio: ''
})

// Phone model for the phone input component
const phoneModel = ref({
  countryCode: '',
  number: ''
})

// Gender options
const genderOptions = [
  { label: 'Male', value: 'male' },
  { label: 'Female', value: 'female' }
]

// Watch for changes to the props
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    formData.value = { ...newValue }

    // Update phone model
    phoneModel.value = {
      countryCode: newValue.phone_country_code || '',
      number: newValue.phone_number || ''
    }

    console.log('PersonalDetailsForm: Updated form data from props:', formData.value)
  }
}, { immediate: true })

// Watch for changes to the form data
watch(formData, (newValue) => {
  emit('update:modelValue', newValue)

  // Removed auto-save functionality
  // No longer emitting save event on every change
}, { deep: true })

// Update phone number
function updatePhone(value) {
  formData.value.phone_country_code = value.countryCode
  formData.value.phone_number = value.number

  console.log('PersonalDetailsForm: Phone updated', value)
}
</script>

<style scoped>
/* Add any component-specific styles here */
</style>
