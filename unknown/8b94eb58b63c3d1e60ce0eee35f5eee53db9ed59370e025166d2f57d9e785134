// This script runs the database migrations
import { applyAllMigrations } from '../lib/applyMigrations';

async function runMigrations() {
  console.log('Running database migrations...');
  
  try {
    const result = await applyAllMigrations();
    
    if (result.success) {
      console.log('✅ Migrations applied successfully!');
    } else {
      console.error('❌ Failed to apply migrations:', result.message);
    }
  } catch (error) {
    console.error('❌ Error applying migrations:', error);
  }
}

runMigrations();
