# Setting Up Supabase Secrets for SendGrid

To properly configure the SendGrid API key in your Supabase Edge Functions, follow these steps:

## 1. Install the Supabase CLI

If you haven't already installed the Supabase CLI, you can do so with:

```bash
npm install -g supabase
```

## 2. Login to Supabase CLI

```bash
supabase login
```

## 3. Set the SendGrid API Key as a Secret

```bash
supabase secrets set SENDGRID_API_KEY="*********************************************************************" --project-ref dpicnvisvxpmgjtbeicf
```

## 4. Set Additional SendGrid Configuration (Optional)

```bash
supabase secrets set SENDGRID_FROM_EMAIL="<EMAIL>" --project-ref dpicnvisvxpmgjtbeicf
supabase secrets set SENDGRID_FROM_NAME="ZB Innovation Hub" --project-ref dpicnvisvxpmgjtbeicf
```

## 5. Verify the Secrets

```bash
supabase secrets list --project-ref dpicnvisvxpmgjtbeicf
```

## 6. Deploy the Edge Function

```bash
supabase functions deploy send-email --project-ref dpicnvisvxpmgjtbeicf
```

## 7. Test the Function

You can test the function using the provided test script:

```bash
node test-sendgrid.js
```

## Alternative: Configure SMTP in Supabase Auth

If you prefer to use Supabase's built-in email service instead of the Edge Function:

1. Go to the Supabase dashboard: https://app.supabase.com/
2. Select your project
3. Navigate to Authentication > Email Templates > Email Settings
4. Configure the SMTP settings:
   - SMTP Host: smtp.sendgrid.net
   - SMTP Port: 587
   - SMTP Username: apikey
   - SMTP Password: *********************************************************************
   - Sender Name: ZB Innovation Hub
   - Sender Email: <EMAIL>
5. Save the settings
