import { supabase } from '../lib/supabase';
import { v4 as uuidv4 } from 'uuid';

/**
 * Service for handling file uploads to Supabase Storage
 */
export const useStorageService = () => {
  /**
   * Upload a file to Supabase Storage
   * @param file The file to upload
   * @param bucket The storage bucket to upload to (default: 'imagefiles')
   * @param folder The folder within the bucket (default: 'posts')
   * @returns The public URL of the uploaded file
   */
  const uploadFile = async (
    file: File,
    bucket: string = 'imagefiles',
    folder: string = 'posts'
  ): Promise<string> => {
    try {
      // Check if user is authenticated
      const { data: authData } = await supabase.auth.getSession();
      if (!authData.session) {
        console.error('User is not authenticated. Cannot upload files.');
        throw new Error('Authentication required to upload files');
      }

      // Validate file
      if (!file || !(file instanceof File)) {
        throw new Error('Invalid file object');
      }

      // Check file size (max 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        throw new Error(`File size exceeds maximum allowed (5MB). Current size: ${(file.size / (1024 * 1024)).toFixed(2)}MB`);
      }

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        throw new Error(`File type not allowed. Allowed types: ${allowedTypes.join(', ')}`);
      }

      // Generate a unique file name to prevent collisions
      const fileExt = file.name.split('.').pop() || 'png';
      const fileName = `${uuidv4()}.${fileExt}`;
      const filePath = `${folder}/${fileName}`;

      console.log(`Uploading file to bucket: ${bucket}, path: ${filePath}, type: ${file.type}, size: ${(file.size / 1024).toFixed(2)}KB`);

      // Upload the file to Supabase Storage
      const { data, error } = await supabase.storage
        .from(bucket)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: true, // Changed to true to overwrite existing files
          contentType: file.type // Explicitly set content type
        });

      if (error) {
        console.error('Error uploading file:', error);
        console.error('Error details:', JSON.stringify(error));

        // Handle specific error cases
        if (error.message && error.message.includes('storage quota')) {
          throw new Error('Storage quota exceeded. Please contact support.');
        } else if (error.message && error.message.includes('permission')) {
          throw new Error('You do not have permission to upload files to this bucket.');
        } else {
          throw error;
        }
      }

      console.log('File uploaded successfully:', data);

      // Get the public URL for the file
      const { data: urlData } = supabase.storage
        .from(bucket)
        .getPublicUrl(filePath);

      if (!urlData || !urlData.publicUrl) {
        throw new Error('Failed to generate public URL for uploaded file');
      }

      // Ensure we have a full URL with the correct domain
      let publicUrl = urlData.publicUrl;

      // If the URL doesn't start with http, it might be a relative path
      if (!publicUrl.startsWith('http')) {
        // Construct the full Supabase URL
        publicUrl = `https://dpicnvisvxpmgjtbeicf.supabase.co/storage/v1/object/public/${bucket}/${filePath}`;
      }

      console.log(`Generated public URL: ${publicUrl}`);
      return publicUrl;
    } catch (error) {
      console.error('Error in uploadFile:', error);
      throw error;
    }
  };

  /**
   * Upload a base64 data URL to Supabase Storage
   * @param dataUrl The base64 data URL to upload
   * @param bucket The storage bucket to upload to (default: 'imagefiles')
   * @param folder The folder within the bucket (default: 'posts')
   * @returns The public URL of the uploaded file
   */
  const uploadDataUrl = async (
    dataUrl: string,
    bucket: string = 'imagefiles',
    folder: string = 'posts'
  ): Promise<string> => {
    try {
      // Validate the data URL format
      if (!dataUrl || typeof dataUrl !== 'string' || !dataUrl.startsWith('data:')) {
        throw new Error('Invalid data URL format');
      }

      // Extract the MIME type from the data URL
      const mimeMatch = dataUrl.match(/data:([^;]+);/);
      const mimeType = mimeMatch ? mimeMatch[1] : 'image/png';

      // Determine file extension based on MIME type
      let fileExt = 'png';
      if (mimeType === 'image/jpeg' || mimeType === 'image/jpg') {
        fileExt = 'jpg';
      } else if (mimeType === 'image/gif') {
        fileExt = 'gif';
      } else if (mimeType === 'image/webp') {
        fileExt = 'webp';
      }

      // Convert data URL to File object
      const res = await fetch(dataUrl);
      const blob = await res.blob();
      const fileName = `image-${Date.now()}.${fileExt}`;
      const file = new File([blob], fileName, { type: mimeType });

      console.log(`Converting data URL to file: ${fileName} (${mimeType})`);

      // Upload the file
      return await uploadFile(file, bucket, folder);
    } catch (error) {
      console.error('Error in uploadDataUrl:', error);
      throw error;
    }
  };

  /**
   * Delete a file from Supabase Storage
   * @param url The public URL of the file to delete
   * @param bucket The storage bucket the file is in (default: 'imagefiles')
   * @returns True if the file was deleted successfully
   */
  const deleteFile = async (
    url: string,
    bucket: string = 'imagefiles'
  ): Promise<boolean> => {
    try {
      // Extract the file path from the URL
      const urlObj = new URL(url);
      const pathParts = urlObj.pathname.split('/');
      const filePath = pathParts.slice(pathParts.indexOf(bucket) + 1).join('/');

      // Delete the file
      const { error } = await supabase.storage
        .from(bucket)
        .remove([filePath]);

      if (error) {
        console.error('Error deleting file:', error);
        throw error;
      }

      return true;
    } catch (error) {
      console.error('Error in deleteFile:', error);
      return false;
    }
  };

  return {
    uploadFile,
    uploadDataUrl,
    deleteFile
  };
};
