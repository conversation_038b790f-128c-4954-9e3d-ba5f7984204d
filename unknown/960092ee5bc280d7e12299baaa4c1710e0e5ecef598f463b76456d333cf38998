// Test script for mentor profile creation
import { supabase } from '../lib/supabase';

async function testMentorProfile() {
  console.log('Testing mentor profile creation and update...');
  
  try {
    // 1. Check if the mentor_profiles table exists
    console.log('Checking mentor_profiles table...');
    const { data: tableExists, error: tableError } = await supabase.rpc('exec_sql', {
      sql: `
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = 'mentor_profiles'
        );
      `
    });
    
    if (tableError) {
      console.error('Error checking mentor_profiles table:', tableError);
      return;
    }
    
    console.log('mentor_profiles table exists:', tableExists);
    
    // 2. Check if the completion_percentage column exists
    console.log('Checking completion_percentage column...');
    const { data: columnExists, error: columnError } = await supabase.rpc('exec_sql', {
      sql: `
        SELECT EXISTS (
          SELECT FROM information_schema.columns 
          WHERE table_schema = 'public' 
          AND table_name = 'mentor_profiles' 
          AND column_name = 'completion_percentage'
        );
      `
    });
    
    if (columnError) {
      console.error('Error checking completion_percentage column:', columnError);
      return;
    }
    
    console.log('completion_percentage column exists:', columnExists);
    
    // 3. If the column doesn't exist, add it
    if (!columnExists) {
      console.log('Adding completion_percentage column...');
      const { error: addColumnError } = await supabase.rpc('exec_sql', {
        sql: `
          ALTER TABLE public.mentor_profiles 
          ADD COLUMN completion_percentage INTEGER DEFAULT 0;
        `
      });
      
      if (addColumnError) {
        console.error('Error adding completion_percentage column:', addColumnError);
        return;
      }
      
      console.log('completion_percentage column added successfully!');
    }
    
    console.log('Test completed successfully!');
  } catch (err) {
    console.error('Error testing mentor profile:', err);
  }
}

testMentorProfile();
