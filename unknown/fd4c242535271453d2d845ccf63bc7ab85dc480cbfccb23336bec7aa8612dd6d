<template>
  <div class="profile-creation-helper">
    <!-- Loading state -->
    <div v-if="loading" class="q-pa-md flex flex-center">
      <q-spinner color="primary" size="3em" />
      <div class="q-ml-md">{{ loadingMessage }}</div>
    </div>

    <!-- Profile creation form -->
    <div v-else>
      <div class="text-h6 q-mb-md">Create Your Profile</div>
      
      <q-form @submit="createProfile" class="q-gutter-md">
        <!-- Profile Name -->
        <q-input
          v-model="profileName"
          label="Profile Name *"
          outlined
          :rules="[val => !!val || 'Profile name is required']"
        >
          <template v-slot:prepend>
            <unified-icon name="badge" />
          </template>
        </q-input>
        
        <!-- Profile Type Selection -->
        <div class="text-subtitle1 q-my-md">Select Profile Category:</div>
        <div class="row q-col-gutter-md">
          <div
            v-for="option in profileTypeOptions"
            :key="option.value"
            class="col-12 col-md-4"
          >
            <q-card
              :class="{
                'profile-type-card': true,
                'selected': profileType === option.value
              }"
              clickable
              @click="profileType = option.value"
            >
              <q-card-section>
                <div class="text-h6">{{ option.label }}</div>
                <div class="text-caption">{{ getProfileTypeDescription(option.value) }}</div>
              </q-card-section>
            </q-card>
          </div>
        </div>
        
        <!-- Submit Button -->
        <div class="q-mt-lg">
          <q-btn
            type="submit"
            color="primary"
            label="Create Profile"
            :loading="creating"
            :disable="!profileName || !profileType"
          />
        </div>
      </q-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useProfileStore } from '../../stores/profile'
import { useNotificationStore } from '../../stores/notifications'
import UnifiedIcon from '../ui/UnifiedIcon.vue'
import { getProfileTypeOptions } from '../../services/profileTypes'

// Props and emits
const emit = defineEmits(['profile-created', 'loading-change'])

// Router and stores
const router = useRouter()
const profileStore = useProfileStore()
const notifications = useNotificationStore()

// State
const profileName = ref('')
const profileType = ref('')
const loading = ref(false)
const creating = ref(false)
const loadingMessage = ref('Loading profile options...')

// Options
const profileTypeOptions = getProfileTypeOptions()

// Methods
function getProfileTypeDescription(type) {
  const descriptions = {
    'innovator': 'Entrepreneurs, startups, and innovators with ideas or products.',
    'investor': 'Angel investors, VCs, and investment firms looking for opportunities.',
    'mentor': 'Experienced professionals offering guidance and mentorship.',
    'professional': 'Industry professionals looking to network and collaborate.',
    'industry_expert': 'Subject matter experts with specialized knowledge.',
    'academic_student': 'Students looking for opportunities and connections.',
    'academic_institution': 'Universities, colleges, and research institutions.',
    'organisation': 'Companies, NGOs, and other organizations.'
  }
  
  return descriptions[type] || ''
}

async function createProfile() {
  if (!profileName.value || !profileType.value) {
    notifications.warning('Please provide a profile name and select a profile type')
    return
  }
  
  try {
    creating.value = true
    loadingMessage.value = 'Creating your profile...'
    emit('loading-change', true)
    
    console.log('Creating profile:', { name: profileName.value, type: profileType.value })
    
    // Create the profile using the profile store
    const profileId = await profileStore.createProfile(
      profileType.value,
      profileName.value
    )
    
    if (profileId) {
      console.log('Profile created successfully with ID:', profileId)
      notifications.success('Profile created successfully!')
      
      // Emit event to parent component
      emit('profile-created', {
        id: profileId,
        name: profileName.value,
        type: profileType.value
      })
      
      // Navigate to the profile edit page
      router.push({
        name: 'profile-edit',
        params: { id: profileId }
      })
    } else {
      throw new Error('Failed to create profile - no profile ID returned')
    }
  } catch (error) {
    console.error('Error creating profile:', error)
    notifications.error('Failed to create profile. Please try again.')
  } finally {
    creating.value = false
    emit('loading-change', false)
  }
}
</script>

<style scoped>
.profile-creation-helper {
  max-width: 1200px;
  margin: 0 auto;
}

.profile-type-card {
  height: 100%;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.profile-type-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
}

.profile-type-card.selected {
  border-color: var(--q-primary);
  background-color: rgba(var(--q-primary-rgb), 0.05);
}
</style>
