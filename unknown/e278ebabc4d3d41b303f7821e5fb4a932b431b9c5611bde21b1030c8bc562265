// Minimal email sending Edge Function
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Credentials': 'true',
  'Access-Control-Max-Age': '86400'
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get API key from environment
    const SENDGRID_API_KEY = Deno.env.get('SENDGRID_API_KEY')
    if (!SENDGRID_API_KEY) {
      throw new Error('SendGrid API key is not configured')
    }

    // Parse request body
    const { to, subject, message } = await req.json()

    // Validate required fields
    if (!to || !subject || !message) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields: to, subject, message' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Log the request
    console.log('Sending email to:', to)

    // Prepare SendGrid request
    const sendgridUrl = 'https://api.sendgrid.com/v3/mail/send'
    const sendgridPayload = {
      personalizations: [
        {
          to: [{ email: to }],
          subject: subject
        }
      ],
      from: {
        email: '<EMAIL>',
        name: 'ZB Innovation Hub'
      },
      content: [
        {
          type: 'text/plain',
          value: message
        },
        {
          type: 'text/html',
          value: `<p>${message}</p>`
        }
      ]
    }

    // Send the email
    const sendgridResponse = await fetch(sendgridUrl, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${SENDGRID_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(sendgridPayload)
    })

    // Handle SendGrid response
    if (!sendgridResponse.ok) {
      let errorMessage = `Failed to send email: ${sendgridResponse.status} ${sendgridResponse.statusText}`
      try {
        const errorData = await sendgridResponse.json()
        errorMessage = `${errorMessage} - ${JSON.stringify(errorData)}`
      } catch (e) {
        // Ignore JSON parsing error
      }
      throw new Error(errorMessage)
    }

    // Return success response
    return new Response(
      JSON.stringify({ success: true, message: `Email sent to ${to}` }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  } catch (error) {
    // Handle errors
    console.error('Error sending email:', error)

    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'Failed to send email'
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})
