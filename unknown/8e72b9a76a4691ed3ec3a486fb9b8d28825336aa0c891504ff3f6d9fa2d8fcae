<template>
  <q-card class="profile-header-card q-mb-lg">
    <q-card-section class="bg-primary text-white profile-header-section">
      <div class="row items-center q-col-gutter-md">
        <!-- Avatar -->
        <div class="col-12 col-sm-auto text-center">
          <q-avatar size="100px" class="profile-avatar bg-white q-mb-sm-none q-mb-md">
            <img v-if="profile.avatar_url" :src="profile.avatar_url" alt="Profile Avatar">
            <user-avatar
              v-else
              :name="profile.displayName"
              :email="profile.email"
              :user-id="profile.user_id || profile.id"
              size="100px"
              :clickable="false"
            />
          </q-avatar>
        </div>

        <!-- Profile Info -->
        <div class="col-12 col-sm profile-info">
          <div class="profile-name">{{ profile.displayName || getDisplayName() }}</div>
          <div class="profile-type q-mt-sm">{{ profile.formattedProfileType || formatProfileType(profile.profile_type) }}</div>
          <div class="profile-email q-mt-xs">{{ profile.email }}</div>
          
          <!-- Location and Join Date -->
          <div class="profile-meta q-mt-sm">
            <div v-if="getLocation()" class="text-caption">
              <q-icon name="location_on" size="xs" class="q-mr-xs" />
              {{ getLocation() }}
            </div>
            <div v-if="profile.created_at" class="text-caption">
              <q-icon name="event" size="xs" class="q-mr-xs" />
              Joined {{ formatDate(profile.created_at) }}
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div v-if="showInteractions && !isCurrentUser" class="col-12 col-sm-auto">
          <div class="row q-gutter-sm justify-center">
            <!-- Message Button -->
            <q-btn
              color="white"
              text-color="white"
              icon="message"
              label="Message"
              outline
              @click="$emit('message', profile.user_id || profile.id)"
            >
              <q-tooltip>Send Message</q-tooltip>
            </q-btn>

            <!-- Connect Button -->
            <q-btn
              :key="`connect-${userId}-${connectionButton.connectionStatus.value}`"
              :color="getConnectButtonColor()"
              text-color="white"
              :icon="connectionButton.buttonConfig.value.icon"
              :label="connectionButton.buttonConfig.value.label"
              :disabled="connectionButton.buttonConfig.value.disabled"
              :loading="connectionButton.buttonConfig.value.loading"
              @click="connectionButton.handleConnect"
            >
              <q-tooltip>{{ connectionButton.tooltipText.value }}</q-tooltip>
            </q-btn>
          </div>
        </div>

        <!-- Edit Button (for current user in dashboard) -->
        <div v-else-if="isCurrentUser && context === 'dashboard'" class="col-12 col-sm-auto">
          <div class="row q-gutter-sm justify-center">
            <q-btn
              color="white"
              text-color="primary"
              icon="edit"
              label="Edit Profile"
              :to="{ name: 'profile-edit', params: { id: profile.user_id || profile.id } }"
            >
              <q-tooltip>Edit Your Profile</q-tooltip>
            </q-btn>
          </div>
        </div>
      </div>
    </q-card-section>

    <!-- Profile Completion (for current user) -->
    <q-card-section v-if="isCurrentUser" class="q-pb-none">
      <div class="profile-completion">
        <div class="row items-center justify-between q-mb-sm">
          <div class="text-subtitle2">Profile Completion</div>
          <div class="col-auto">
            <q-circular-progress
              :value="getCompletionPercentage()"
              size="50px"
              :thickness="0.2"
              :color="getCompletionColor()"
              track-color="grey-3"
              class="q-mr-md"
            >
              <div class="text-caption">{{ getCompletionPercentage() }}%</div>
            </q-circular-progress>
          </div>
        </div>
        <q-linear-progress
          :value="getCompletionPercentage() / 100"
          :color="getCompletionColor()"
          class="q-mb-sm"
          size="8px"
          rounded
        />
        <div class="row items-center justify-between">
          <div class="text-caption text-grey-7">
            {{ getCompletionMessage() }}
          </div>
          <q-btn
            v-if="getCompletionPercentage() < 100"
            size="sm"
            color="primary"
            label="Complete Profile"
            :to="{ name: 'profile-edit', params: { id: profile.user_id || profile.id } }"
            class="q-ml-sm"
          />
        </div>
      </div>
    </q-card-section>

    <!-- Bio Section -->
    <q-card-section v-if="getBio()">
      <div class="text-h6">About</div>
      <p class="q-mt-sm">{{ getBio() }}</p>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">

import { date } from 'quasar'
import { formatProfileType } from '../../services/profileTypes'
import { getNameFromEmail } from '../../utils/nameUtils'
import { useConnectionButton } from '../../composables/useConnectionButton'
import UserAvatar from '../common/UserAvatar.vue'

// Props
const props = defineProps<{
  profile: any
  context: 'dashboard' | 'public'
  showInteractions?: boolean
  isCurrentUser?: boolean
}>()

// Emits
const emit = defineEmits<{
  message: [userId: string]
  connect: [userId: string]
}>()

// Use unified connection button logic
const userId = props.profile.user_id || props.profile.id;
const connectionButton = useConnectionButton(userId);

// Debug logging
console.log('ProfileHeader: Initial connection button config:', {
  userId,
  showInteractions: props.showInteractions,
  isCurrentUser: props.isCurrentUser,
  buttonConfig: connectionButton.buttonConfig.value,
  tooltipText: connectionButton.tooltipText.value
});
function getDisplayName(): string {
  if (!props.profile) return 'Unknown User'

  const fullName = `${props.profile.first_name || ''} ${props.profile.last_name || ''}`.trim()
  if (fullName) return fullName

  if (props.profile.profile_name) return props.profile.profile_name
  if (props.profile.email) return getNameFromEmail(props.profile.email)

  // For profiles with minimal data, show a more user-friendly message
  return 'User Profile'
}

function getLocation(): string {
  if (!props.profile) return ''
  
  const locations = [
    props.profile.location,
    props.profile.city,
    props.profile.state_province,
    props.profile.country
  ].filter(Boolean)
  
  return locations.join(', ')
}

function getBio(): string {
  if (!props.profile) return ''
  
  return props.profile.bio || 
         props.profile.about || 
         props.profile.description || 
         ''
}

function formatDate(dateString: string): string {
  if (!dateString) return ''

  try {
    return date.formatDate(new Date(dateString), 'MMMM YYYY')
  } catch {
    return ''
  }
}

function getConnectButtonColor(): string {
  const configColor = connectionButton.buttonConfig.value.color

  // Map the connection button colors to high-contrast colors
  switch (configColor) {
    case 'green':
      return 'positive' // Dark green
    case 'orange':
      return 'warning' // Orange
    case 'blue':
      return 'info' // Blue
    case 'grey':
    case 'gray':
      return 'dark' // Dark grey
    default:
      return 'positive' // Default to dark green
  }
}

function getCompletionPercentage(): number {
  if (!props.profile) return 0
  return Math.round(props.profile.profile_completion || 0)
}

function getCompletionColor(): string {
  const completion = getCompletionPercentage()
  if (completion < 30) return 'red'
  if (completion < 70) return 'orange'
  if (completion < 100) return 'blue'
  return 'green'
}

function getCompletionMessage(): string {
  const percentage = getCompletionPercentage()
  if (percentage === 0) return 'Get started by completing your profile'
  if (percentage < 30) return 'Just getting started! Add more details'
  if (percentage < 70) return 'Good progress! Keep going'
  if (percentage < 100) return 'Almost there! Just a few more fields'
  return 'Your profile is complete!'
}
</script>

<style scoped>
.profile-header-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.profile-header-section {
  background: linear-gradient(135deg, var(--q-primary) 0%, #1976d2 100%);
}

.profile-avatar {
  border: 4px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.profile-name {
  font-size: 1.8rem;
  font-weight: 600;
  line-height: 1.2;
}

.profile-type {
  font-size: 1.1rem;
  opacity: 0.9;
  font-weight: 500;
}

.profile-email {
  font-size: 0.9rem;
  opacity: 0.8;
}

.profile-meta {
  opacity: 0.9;
}

.profile-completion {
  background: rgba(25, 118, 210, 0.05);
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid var(--q-primary);
}

@media (max-width: 767px) {
  .profile-name {
    font-size: 1.5rem;
    text-align: center;
  }
  
  .profile-type,
  .profile-email {
    text-align: center;
  }
  
  .profile-meta {
    text-align: center;
  }
}
</style>
