// Academic Student Profile Questions
import { ProfileType } from '../types';
import { commonOptions } from '../common';

// Academic Student profile questions
export const academicStudentProfile: ProfileType = {
  type: 'academic_student',
  displayName: 'Academic Student',
  sections: [
    {
      title: 'Personal Information',
      icon: 'person',
      description: 'Tell us about yourself',
      questions: [
        {
          id: 'full_name',
          name: 'full_name',
          label: 'Full Name',
          type: 'text',
          required: true,
          hint: 'Your full name'
        },
        {
          id: 'bio',
          name: 'bio',
          label: 'Executive Summary',
          type: 'text',
          fullWidth: true,
          hint: 'Brief introduction about your academic interests and career goals'
        },
        {
          id: 'languages',
          name: 'languages',
          label: 'Languages',
          type: 'multi-select',
          options: 'languageOptions',
          hint: 'Languages you speak'
        },
        {
          id: 'date_of_birth',
          name: 'date_of_birth',
          label: 'Date of Birth',
          type: 'text',
          hint: 'Your date of birth (optional)'
        }
      ]
    },
    {
      title: 'Academic Information',
      icon: 'school',
      description: 'Tell us about your academic background',
      questions: [
        {
          id: 'institution',
          name: 'institution',
          label: 'Institution',
          type: 'text',
          required: true,
          fullWidth: true,
          hint: 'Name of your university or academic institution'
        },
        {
          id: 'degree_program',
          name: 'degree_program',
          label: 'Degree Program',
          type: 'select',
          required: true,
          options: 'degreeProgramOptions',
          hint: 'Type of degree you\'re pursuing'
        },
        {
          id: 'field_of_study',
          name: 'field_of_study',
          label: 'Field of Study',
          type: 'select',
          required: true,
          options: 'fieldOfStudyOptions',
          hint: 'Your major or primary field of study'
        },
        {
          id: 'graduation_year',
          name: 'graduation_year',
          label: 'Expected Graduation Year',
          type: 'number',
          required: true,
          hint: 'Year you expect to graduate'
        },
        {
          id: 'current_year',
          name: 'current_year',
          label: 'Current Year of Study',
          type: 'select',
          options: 'yearOfStudyOptions',
          hint: 'Your current year in your program'
        },
        {
          id: 'gpa',
          name: 'gpa',
          label: 'GPA/Academic Standing',
          type: 'text',
          hint: 'Your current GPA or academic standing (optional)'
        }
      ]
    },
    {
      title: 'Research & Projects',
      icon: 'science',
      description: 'Tell us about your research and projects',
      questions: [
        {
          id: 'research_interests',
          name: 'research_interests',
          label: 'Research Interests',
          type: 'multi-select',
          options: 'researchInterestsOptions',
          fullWidth: true,
          hint: 'Areas of research you\'re interested in'
        },
        {
          id: 'current_research',
          name: 'current_research',
          label: 'Current Research',
          type: 'text',
          fullWidth: true,
          hint: 'Describe any current research you\'re involved in'
        },
        {
          id: 'academic_projects',
          name: 'academic_projects',
          label: 'Academic Projects',
          type: 'text',
          fullWidth: true,
          hint: 'Describe notable academic projects you\'ve worked on'
        },
        {
          id: 'publications',
          name: 'publications',
          label: 'Publications',
          type: 'multi-select-free',
          fullWidth: true,
          hint: 'List any academic publications you\'ve contributed to'
        },
        {
          id: 'thesis_topic',
          name: 'thesis_topic',
          label: 'Thesis/Dissertation Topic',
          type: 'text',
          fullWidth: true,
          hint: 'Topic of your thesis or dissertation (if applicable)'
        }
      ]
    },
    {
      title: 'Skills & Achievements',
      icon: 'emoji_events',
      description: 'Tell us about your skills and achievements',
      questions: [
        {
          id: 'skills',
          name: 'skills',
          label: 'Skills',
          type: 'multi-select',
          options: 'studentSkillsOptions',
          fullWidth: true,
          hint: 'Select your key skills'
        },
        {
          id: 'technical_skills',
          name: 'technical_skills',
          label: 'Technical Skills',
          type: 'multi-select-free',
          fullWidth: true,
          hint: 'List specific technical skills (programming languages, software, etc.)'
        },
        {
          id: 'academic_achievements',
          name: 'academic_achievements',
          label: 'Academic Achievements',
          type: 'multi-select-free',
          fullWidth: true,
          hint: 'List notable academic achievements, awards, or honors'
        },
        {
          id: 'extracurricular_activities',
          name: 'extracurricular_activities',
          label: 'Extracurricular Activities',
          type: 'text',
          fullWidth: true,
          hint: 'Describe your involvement in extracurricular activities'
        },
        {
          id: 'leadership_roles',
          name: 'leadership_roles',
          label: 'Leadership Roles',
          type: 'multi-select-free',
          fullWidth: true,
          hint: 'List any leadership roles you\'ve held'
        }
      ]
    },
    {
      title: 'Career & Collaboration',
      icon: 'work',
      description: 'Tell us about your career interests and collaboration goals',
      questions: [
        {
          id: 'career_interests',
          name: 'career_interests',
          label: 'Career Interests',
          type: 'multi-select',
          options: 'careerInterestsOptions',
          fullWidth: true,
          hint: 'Select your career interests'
        },
        {
          id: 'looking_for',
          name: 'looking_for',
          label: 'Looking For',
          type: 'multi-select',
          options: 'lookingForOptions',
          fullWidth: true,
          hint: 'What are you looking for on this platform?'
        },
        {
          id: 'work_experience',
          name: 'work_experience',
          label: 'Work Experience',
          type: 'text',
          fullWidth: true,
          hint: 'Describe any relevant work experience'
        },
        {
          id: 'internship_interests',
          name: 'internship_interests',
          label: 'Internship Interests',
          type: 'text',
          fullWidth: true,
          hint: 'Describe the types of internships you\'re interested in'
        },
        {
          id: 'collaboration_interests',
          name: 'collaboration_interests',
          label: 'Collaboration Interests',
          type: 'text',
          fullWidth: true,
          hint: 'Describe the types of collaborations you\'re interested in'
        },
        {
          id: 'seeking_mentorship',
          name: 'seeking_mentorship',
          label: 'Seeking Mentorship',
          type: 'boolean',
          hint: 'Are you looking for a mentor?'
        }
      ]
    },
    {
      title: 'Contact Information',
      icon: 'contact_mail',
      description: 'Tell us how to contact you',
      questions: [
        {
          id: 'academic_email',
          name: 'academic_email',
          label: 'Academic Email',
          type: 'text',
          hint: 'Your academic email (if different from account email)'
        },
        {
          id: 'contact_phone',
          name: 'contact_phone',
          label: 'Contact Phone',
          type: 'text',
          hint: 'Phone number for contact'
        }
      ]
    },
    {
      title: 'Location Information',
      icon: 'location_on',
      description: 'Tell us where you are based',
      questions: [
        {
          id: 'country',
          name: 'country',
          label: 'Country',
          type: 'select',
          required: true,
          options: 'countryOptions',
          hint: 'Country where you are based'
        },
        {
          id: 'city',
          name: 'city',
          label: 'City',
          type: 'text',
          required: true,
          hint: 'City where you are based'
        },
        {
          id: 'campus_address',
          name: 'campus_address',
          label: 'Campus Address',
          type: 'text',
          fullWidth: true,
          hint: 'Your address on campus (if applicable)'
        }
      ]
    },
    {
      title: 'Online Presence',
      icon: 'public',
      description: 'Tell us about your online presence',
      questions: [
        {
          id: 'website',
          name: 'website',
          label: 'Personal Website',
          type: 'text',
          fullWidth: true,
          hint: 'Your personal or academic website'
        },
        {
          id: 'linkedin',
          name: 'linkedin',
          label: 'LinkedIn',
          type: 'text',
          hint: 'Your LinkedIn profile URL'
        },
        {
          id: 'github',
          name: 'github',
          label: 'GitHub',
          type: 'text',
          hint: 'Your GitHub profile URL'
        },
        {
          id: 'researchgate',
          name: 'researchgate',
          label: 'ResearchGate',
          type: 'text',
          hint: 'Your ResearchGate profile URL'
        },
        {
          id: 'other_social',
          name: 'other_social',
          label: 'Other Social Media',
          type: 'text',
          fullWidth: true,
          hint: 'Other relevant social media profiles'
        }
      ]
    }
  ],
  options: {
    ...commonOptions,
    degreeProgramOptions: [
      'Certificate', 'Diploma', 'Associate\'s Degree',
      'Bachelor\'s Degree', 'Master\'s Degree', 'Doctoral Degree',
      'Post-Doctoral', 'Professional Degree', 'Other'
    ],
    fieldOfStudyOptions: [
      'Computer Science', 'Information Technology', 'Engineering',
      'Business Administration', 'Finance', 'Economics',
      'Marketing', 'Accounting', 'Management',
      'Mathematics', 'Statistics', 'Physics',
      'Chemistry', 'Biology', 'Environmental Science',
      'Medicine', 'Nursing', 'Pharmacy',
      'Law', 'Political Science', 'International Relations',
      'Psychology', 'Sociology', 'Anthropology',
      'Education', 'Communications', 'Journalism',
      'Arts', 'Design', 'Architecture',
      'Agriculture', 'Food Science', 'Veterinary Science',
      'Other'
    ],
    yearOfStudyOptions: [
      'First Year', 'Second Year', 'Third Year', 'Fourth Year',
      'Fifth Year', 'Sixth Year', 'Seventh Year', 'Eighth Year+'
    ],
    researchInterestsOptions: [
      'Artificial Intelligence', 'Machine Learning', 'Data Science',
      'Blockchain', 'Cybersecurity', 'Cloud Computing',
      'Internet of Things', 'Robotics', 'Automation',
      'Renewable Energy', 'Sustainability', 'Climate Change',
      'Biotechnology', 'Genetics', 'Neuroscience',
      'Public Health', 'Epidemiology', 'Pharmaceuticals',
      'Finance', 'Economics', 'Business Strategy',
      'Marketing Analytics', 'Consumer Behavior', 'E-commerce',
      'Education Technology', 'Online Learning', 'Curriculum Development',
      'Social Justice', 'Public Policy', 'International Development',
      'Urban Planning', 'Transportation', 'Infrastructure',
      'Other'
    ],
    studentSkillsOptions: [
      'Research', 'Data Analysis', 'Programming',
      'Writing', 'Public Speaking', 'Project Management',
      'Leadership', 'Teamwork', 'Problem Solving',
      'Critical Thinking', 'Creativity', 'Communication',
      'Time Management', 'Organization', 'Adaptability',
      'Other'
    ],
    careerInterestsOptions: [
      'Academia', 'Research', 'Industry',
      'Entrepreneurship', 'Consulting', 'Government',
      'Non-profit', 'International Organizations', 'Startups',
      'Corporate', 'Public Service', 'Other'
    ],
    lookingForOptions: [
      'Internship', 'Job Opportunities', 'Research Collaboration',
      'Mentorship', 'Networking', 'Funding',
      'Project Partners', 'Study Groups', 'Knowledge Exchange',
      'Industry Connections', 'Academic Guidance', 'Other'
    ]
  }
};
