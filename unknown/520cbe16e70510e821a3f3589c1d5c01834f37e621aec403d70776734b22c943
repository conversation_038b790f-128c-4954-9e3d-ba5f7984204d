// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'
import { SendGridClient } from '../_shared/sendgrid.ts'

const SENDGRID_API_KEY = Deno.env.get('SENDGRID_API_KEY') || ''
const SUPABASE_URL = Deno.env.get('ZB_SUPABASE_URL') || 'https://dpicnvisvxpmgjtbeicf.supabase.co'
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get('ZB_SUPABASE_SERVICE_ROLE_KEY') || ''
const SENDER_EMAIL = '<EMAIL>'
const SITE_URL = 'https://zbinnovation.co.zw'

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create a Supabase client with the service role key
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

    // Get the request body
    const { record } = await req.json()

    if (!record || !record.id || !record.email) {
      return new Response(
        JSON.stringify({ error: 'Missing required user data' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
      )
    }

    // Get user metadata if available
    const { data: userData, error: userError } = await supabase
      .from('personal_details')
      .select('first_name, last_name')
      .eq('user_id', record.id)
      .single()

    if (userError && userError.code !== 'PGRST116') {
      console.error('Error fetching user data:', userError)
    }

    // Create SendGrid client
    const sendgrid = new SendGridClient(SENDGRID_API_KEY)

    // Prepare email content
    const firstName = userData?.first_name || ''
    const greeting = firstName ? `Hi ${firstName},` : `Hi there,`

    const emailContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>Welcome to ZB Innovation Hub</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
          }
          .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
          }
          .header {
            background-color: #0D8A3E;
            padding: 20px;
            text-align: center;
          }
          .header h1 {
            color: white;
            margin: 0;
          }
          .content {
            padding: 20px;
            background-color: #f9f9f9;
          }
          .footer {
            text-align: center;
            padding: 20px;
            font-size: 12px;
            color: #666;
          }
          .button {
            display: inline-block;
            background-color: #0D8A3E;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 4px;
            margin-top: 20px;
          }
          .secondary-button {
            display: inline-block;
            background-color: #a4ca39;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 4px;
            margin-top: 20px;
            margin-left: 10px;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Welcome to ZB Innovation Hub</h1>
          </div>
          <div class="content">
            <p>${greeting}</p>
            <p>Thank you for joining the ZB Innovation Hub platform. We're excited to have you on board!</p>
            <p>Our platform connects innovators, mentors, organizations, and academic institutions to foster collaboration and drive innovation in Zimbabwe.</p>
            <p>To get started, please complete your profile to unlock all the features of our platform:</p>
            <div style="text-align: center;">
              <a href="${SITE_URL}/dashboard" class="button">Go to Dashboard</a>
              <a href="${SITE_URL}/dashboard/profile" class="secondary-button">Complete Profile</a>
            </div>
            <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
            <p>Best regards,<br>The ZB Innovation Hub Team</p>
          </div>
          <div class="footer">
            <p>© ${new Date().getFullYear()} ZB Innovation Hub. All rights reserved.</p>
            <p>This email was sent to ${record.email}</p>
          </div>
        </div>
      </body>
      </html>
    `

    // Send the welcome email
    const result = await sendgrid.send({
      to: record.email,
      from: SENDER_EMAIL,
      subject: 'Welcome to ZB Innovation Hub',
      html: emailContent,
    })

    if (!result.success) {
      throw new Error(result.error || 'Failed to send welcome email')
    }

    // Return success response
    return new Response(
      JSON.stringify({ success: true, message: 'Welcome email sent successfully' }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 200 }
    )

  } catch (error) {
    console.error('Error sending welcome email:', error)

    return new Response(
      JSON.stringify({ error: error.message || 'Internal server error' }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
    )
  }
})
