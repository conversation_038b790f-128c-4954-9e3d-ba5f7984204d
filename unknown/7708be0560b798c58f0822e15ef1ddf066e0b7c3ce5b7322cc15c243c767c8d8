// Profile Questions Types
// Shared type definitions for profile questions

// Define types for profile questions
export interface ProfileQuestion {
  // Common properties
  id: string;                 // Unique identifier for the question
  name: string;               // Field name in the database
  label: string;              // Display label
  type: 'text' | 'number' | 'select' | 'multi-select' | 'boolean' | 'conditional' | 'textarea' | 'toggle' | 'multi-select-free';
  required?: boolean;         // Whether the field is required
  
  // UI properties
  fullWidth?: boolean;        // Whether the field takes full width
  hint?: string;              // Help text
  helpText?: string;          // Additional help text (same as hint)
  placeholder?: string;       // Placeholder text
  
  // Options for select/multi-select
  options?: string | string[]; // Either option key or array of options
  
  // Conditional display
  condition?: {
    field: string;
    value: any;
  };
}

export interface ProfileSection {
  title: string;              // Section title
  icon?: string;              // Section icon
  description?: string;       // Section description
  questions: ProfileQuestion[]; // Questions in this section
}

export interface ProfileType {
  type: string;               // Profile type identifier
  displayName: string;        // Display name
  sections: ProfileSection[]; // Sections in this profile type
  options: Record<string, string[]>; // Options for select/multi-select fields
}
