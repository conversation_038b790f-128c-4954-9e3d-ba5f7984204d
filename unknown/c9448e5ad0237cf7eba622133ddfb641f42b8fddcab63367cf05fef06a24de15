<template>
  <div class="sent-connection-requests">
    <div v-if="title" class="text-h6 q-mb-md">{{ title }}</div>

    <div v-if="loading" class="text-center q-pa-md">
      <q-spinner color="primary" size="2em" />
      <p>Loading sent requests...</p>
    </div>

    <div v-else-if="sentRequests.length === 0" class="text-center q-pa-md text-grey">
      <q-icon name="info" size="2em" />
      <p>No sent connection requests to display</p>
    </div>

    <div v-else>
      <div class="row q-col-gutter-md">
        <div
          v-for="request in sentRequests"
          :key="request.id"
          class="col-12"
        >
          <q-card class="request-card">
            <q-item>
              <q-item-section avatar>
                <q-avatar>
                  <img
                    v-if="request.connected_user?.avatar_url"
                    :src="request.connected_user.avatar_url"
                  />
                  <div v-else class="bg-primary text-white flex flex-center full-height">
                    {{ getInitials(request.connected_user) }}
                  </div>
                </q-avatar>
              </q-item-section>

              <q-item-section>
                <q-item-label>
                  {{ getUserName(request.connected_user) }}
                </q-item-label>
                <q-item-label caption>
                  {{ request.connection_type }}
                </q-item-label>
                <q-item-label caption>
                  Sent {{ formatDate(request.created_at) }}
                </q-item-label>
                <q-item-label caption class="text-orange">
                  Status: {{ formatStatus(request.connection_status) }}
                </q-item-label>
              </q-item-section>

              <q-item-section side>
                <q-btn
                  flat
                  round
                  color="primary"
                  icon="person"
                  :to="{ name: 'user-profile', params: { id: request.connected_user_id } }"
                >
                  <q-tooltip>View Profile</q-tooltip>
                </q-btn>

                <q-btn
                  flat
                  round
                  color="negative"
                  icon="delete"
                  @click="handleCancel(request.id)"
                  :loading="cancelingId === request.id"
                >
                  <q-tooltip>Cancel Request</q-tooltip>
                </q-btn>
              </q-item-section>
            </q-item>
          </q-card>
        </div>
      </div>

      <div v-if="hasMore" class="text-center q-mt-md">
        <q-btn
          outline
          color="primary"
          label="Load More"
          :loading="loadingMore"
          @click="loadMore"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { date, Notify } from 'quasar';
import { getUniversalUsername } from '../../utils/userUtils';
import { supabase } from '../../lib/supabase';

const props = defineProps({
  title: {
    type: String,
    default: 'Sent Connection Requests'
  },
  limit: {
    type: Number,
    default: 10
  }
});

const sentRequests = ref([]);
const loading = ref(true);
const currentPage = ref(1);
const hasMore = ref(false);
const cancelingId = ref(null);
const loadingMore = ref(false);

onMounted(async () => {
  await loadSentRequests();
});

async function loadSentRequests() {
  try {
    loading.value = true;
    console.log('Loading sent connection requests...');

    // Check if the user is authenticated
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.error('User not authenticated when loading sent connection requests');
      return;
    }

    console.log('Current user ID:', user.id);

    const from = (currentPage.value - 1) * props.limit;
    const to = from + props.limit - 1;

    // First, get the basic connection requests without joins
    const { data: simpleRequests, error: simpleError } = await supabase
      .from('user_connections')
      .select('*')
      .eq('user_id', user.id)
      .eq('connection_status', 'pending')
      .order('created_at', { ascending: false })
      .range(from, to);

    if (simpleError) {
      console.error('Error fetching sent connection requests:', simpleError);
      return;
    }

    console.log('Found sent connection requests:', simpleRequests?.length || 0);

    if (!simpleRequests || simpleRequests.length === 0) {
      console.log('No sent connection requests found');
      sentRequests.value = [];
      return;
    }

    // Log the raw connection requests
    console.log('Raw sent connection requests:', simpleRequests);

    // Now fetch user data for each request
    const enhancedRequests = await Promise.all(
      simpleRequests.map(async (req) => {
        try {
          // First try to get user data from auth.users
          const { data: authUserData, error: authUserError } = await supabase
            .from('auth.users')
            .select('id, email')
            .eq('id', req.connected_user_id)
            .single();

          // If that fails, try personal_details
          if (authUserError) {
            console.log(`Couldn't get auth user data for ${req.connected_user_id}, trying personal_details`);
            const { data: personalData, error: personalError } = await supabase
              .from('personal_details')
              .select('first_name, last_name, email, profile_name')
              .eq('user_id', req.connected_user_id)
              .single();

            if (personalError) {
              console.log(`Couldn't get personal details for ${req.connected_user_id}, using basic data`);
              // If both fail, use a basic user object with the ID
              return {
                ...req,
                connected_user: {
                  id: req.connected_user_id,
                  email: `user-${req.connected_user_id.substring(0, 8)}@zbinnovation.com`, // Fallback email
                  first_name: 'ZB',
                  last_name: 'User',
                  profile_name: 'ZB User'
                }
              };
            }

            return {
              ...req,
              connected_user: {
                id: req.connected_user_id,
                ...personalData
              }
            };
          }

          // If we got auth user data, use that
          return {
            ...req,
            connected_user: authUserData
          };
        } catch (err) {
          console.error(`Error enhancing sent request ${req.id}:`, err);
          return {
            ...req,
            connected_user: {
              id: req.connected_user_id,
              email: `user-${req.connected_user_id.substring(0, 8)}@zbinnovation.com`, // Fallback email
              first_name: 'ZB',
              last_name: 'User',
              profile_name: 'ZB User'
            }
          };
        }
      })
    );

    console.log('Enhanced sent requests:', enhancedRequests);
    sentRequests.value = enhancedRequests;
    hasMore.value = enhancedRequests.length === props.limit;
  } catch (error) {
    console.error('Error in loadSentRequests:', error);
  } finally {
    loading.value = false;
  }
}

async function loadMore() {
  try {
    loadingMore.value = true;
    currentPage.value++;

    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    const from = (currentPage.value - 1) * props.limit;
    const to = from + props.limit - 1;

    // First, get the basic connection requests without joins
    const { data: simpleRequests, error: simpleError } = await supabase
      .from('user_connections')
      .select('*')
      .eq('user_id', user.id)
      .eq('connection_status', 'pending')
      .order('created_at', { ascending: false })
      .range(from, to);

    if (simpleError) {
      console.error('Error fetching more sent connection requests:', simpleError);
      throw simpleError;
    }

    if (!simpleRequests || simpleRequests.length === 0) {
      hasMore.value = false;
      return;
    }

    // Now fetch user data for each request
    const enhancedRequests = await Promise.all(
      simpleRequests.map(async (req) => {
        try {
          // First try to get user data from auth.users
          const { data: authUserData, error: authUserError } = await supabase
            .from('auth.users')
            .select('id, email')
            .eq('id', req.connected_user_id)
            .single();

          // If that fails, try personal_details
          if (authUserError) {
            const { data: personalData, error: personalError } = await supabase
              .from('personal_details')
              .select('first_name, last_name, email, profile_name')
              .eq('user_id', req.connected_user_id)
              .single();

            if (personalError) {
              // If both fail, use a basic user object with the ID
              return {
                ...req,
                connected_user: {
                  id: req.connected_user_id,
                  email: `user-${req.connected_user_id.substring(0, 8)}@zbinnovation.com`, // Fallback email
                  first_name: 'ZB',
                  last_name: 'User',
                  profile_name: 'ZB User'
                }
              };
            }

            return {
              ...req,
              connected_user: {
                id: req.connected_user_id,
                ...personalData
              }
            };
          }

          // If we got auth user data, use that
          return {
            ...req,
            connected_user: authUserData
          };
        } catch (err) {
          console.error(`Error enhancing sent request ${req.id}:`, err);
          return {
            ...req,
            connected_user: {
              id: req.connected_user_id,
              email: `user-${req.connected_user_id.substring(0, 8)}@zbinnovation.com`, // Fallback email
              first_name: 'ZB',
              last_name: 'User',
              profile_name: 'ZB User'
            }
          };
        }
      })
    );

    if (enhancedRequests.length > 0) {
      sentRequests.value = [...sentRequests.value, ...enhancedRequests];
    }

    hasMore.value = enhancedRequests.length === props.limit;
  } catch (error) {
    console.error('Error loading more sent connection requests:', error);
    currentPage.value = Math.max(1, currentPage.value - 1);
  } finally {
    loadingMore.value = false;
  }
}

async function handleCancel(requestId) {
  try {
    cancelingId.value = requestId;

    // Delete the connection request
    const { error } = await supabase
      .from('user_connections')
      .delete()
      .eq('id', requestId);

    if (error) throw error;

    // Remove from the list
    sentRequests.value = sentRequests.value.filter(req => req.id !== requestId);

    Notify.create({
      color: 'info',
      message: 'Connection request canceled',
      icon: 'info',
      position: 'top',
      timeout: 2000
    });
  } catch (error) {
    console.error('Error canceling connection request:', error);

    Notify.create({
      color: 'negative',
      message: 'Failed to cancel connection request',
      icon: 'error',
      position: 'top',
      timeout: 2000
    });
  } finally {
    cancelingId.value = null;
  }
}

function getInitials(user) {
  if (!user) return '?';

  if (user.first_name && user.last_name) {
    return `${user.first_name[0]}${user.last_name[0]}`.toUpperCase();
  }

  if (user.email) {
    return user.email[0].toUpperCase();
  }

  return '?';
}

function getUserName(user) {
  return getUniversalUsername(user);
}

function formatDate(dateString) {
  return date.formatDate(dateString, 'MMM D, YYYY');
}

function formatStatus(status) {
  return status.charAt(0).toUpperCase() + status.slice(1);
}

// Expose the loadSentRequests function to the parent component
defineExpose({
  loadSentRequests
});
</script>

<style scoped>
.sent-connection-requests {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
}

.request-card {
  margin-bottom: 8px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.request-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

@media (max-width: 599px) {
  .sent-connection-requests {
    padding: 12px;
  }
}
</style>
