<template>
  <div class="connection-test q-pa-md">
    <q-card class="connection-card">
      <q-card-section>
        <div class="text-h6">Supabase Connection Status</div>
        <div class="q-mt-md">
          <div v-if="loading" class="row items-center">
            <q-spinner color="primary" size="2em" />
            <span class="q-ml-sm">Testing connection...</span>
          </div>
          <div v-else class="row items-center">
            <icons
              :name="isConnected ? 'check_circle' : 'error'"
              class="text-2xl"
              :class="isConnected ? 'text-positive' : 'text-negative'"
            />
            <span class="q-ml-sm">{{ connectionStatus }}</span>
          </div>
        </div>
      </q-card-section>
      <q-card-actions align="right">
        <q-btn
          flat
          color="primary"
          label="Test Again"
          @click="testConnection"
          :loading="loading"
        />
      </q-card-actions>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { testSupabaseConnection } from '../lib/supabase'
import Icons from './ui/Icons.vue'

const loading = ref(false)
const isConnected = ref(false)
const connectionStatus = ref('Not tested')

const testConnection = async () => {
  loading.value = true
  connectionStatus.value = 'Testing connection...'

  try {
    const result = await testSupabaseConnection()
    isConnected.value = result
    connectionStatus.value = result
      ? 'Successfully connected to Supabase!'
      : 'Failed to connect to Supabase. Check your credentials.'
  } catch (error) {
    isConnected.value = false
    connectionStatus.value = 'Connection error occurred'
    console.error('Connection test error:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  testConnection()
})
</script>

<style scoped>
.connection-card {
  max-width: 400px;
  margin: 0 auto;
}
</style>