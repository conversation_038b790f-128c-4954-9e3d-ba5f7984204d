import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { quasar, transformAssetUrls } from '@quasar/vite-plugin'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  // Base URL for Apache deployment
  base: '/',

  plugins: [
    vue({
      template: { transformAssetUrls }
    }),
    quasar({
      sassVariables: 'src/css/quasar.variables.scss'
    })
  ],

  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },

  build: {
    // Output directory
    outDir: 'dist',

    // Optimize for Apache deployment
    assetsDir: 'assets',

    // Target modern browsers
    target: 'es2015',

    // Chunk size warning limit
    chunkSizeWarningLimit: 1000
  }
})
