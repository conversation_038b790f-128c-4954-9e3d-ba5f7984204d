interface Organization {
  "@type": "Organization";
  name: string;
  url: string;
  logo: string;
  sameAs: string[];
  description?: string;
  address?: PostalAddress;
  contactPoint?: ContactPoint[];
  foundingDate?: string;
}

interface PostalAddress {
  "@type": "PostalAddress";
  streetAddress: string;
  addressLocality: string;
  addressRegion?: string;
  postalCode: string;
  addressCountry: string;
}

interface ContactPoint {
  "@type": "ContactPoint";
  telephone: string;
  contactType: string;
  email?: string;
  areaServed?: string;
  availableLanguage?: string[];
}

interface WebSite {
  "@type": "WebSite";
  name: string;
  url: string;
  potentialAction?: SearchAction;
  description?: string;
  publisher?: Organization;
}

interface SearchAction {
  "@type": "SearchAction";
  target: string;
  "query-input": string;
}

interface BreadcrumbList {
  "@type": "BreadcrumbList";
  itemListElement: BreadcrumbItem[];
}

interface BreadcrumbItem {
  "@type": "ListItem";
  position: number;
  name: string;
  item: string;
}

interface Event {
  "@type": "Event";
  name: string;
  startDate: string;
  endDate?: string;
  location: Place | VirtualLocation;
  description?: string;
  image?: string;
  url?: string;
  organizer?: Organization;
  offers?: Offer[];
  performer?: Person[];
}

interface Place {
  "@type": "Place";
  name: string;
  address: PostalAddress;
}

interface VirtualLocation {
  "@type": "VirtualLocation";
  url: string;
}

interface Offer {
  "@type": "Offer";
  price: string;
  priceCurrency: string;
  availability: string;
  url?: string;
  validFrom?: string;
}

interface Person {
  "@type": "Person";
  name: string;
  url?: string;
}

export function generateOrganizationSchema(data: Partial<Organization> = {}): string {
  const schema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: "ZbInnovation",
    url: "https://ZbInnovation.co.zw",
    logo: "/logo.png",
    description: "Zimbabwe's premier ecosystem for innovators, investors, and entrepreneurs. Connect, collaborate, and grow with our state-of-the-art physical and virtual community.",
    sameAs: [
      "https://twitter.com/ZBInnovation",
      "https://facebook.com/ZBInnovation",
      "https://linkedin.com/company/ZbInnovation"
    ],
    address: {
      "@type": "PostalAddress",
      streetAddress: "ZB Centre, Corner First Street and Kwame Nkrumah Avenue",
      addressLocality: "Harare",
      postalCode: "00263",
      addressCountry: "Zimbabwe"
    },
    contactPoint: [
      {
        "@type": "ContactPoint",
        telephone: "+263-**********",
        contactType: "customer service",
        email: "<EMAIL>",
        areaServed: "Zimbabwe",
        availableLanguage: ["English", "Shona", "Ndebele"]
      }
    ],
    ...data
  };

  return JSON.stringify(schema);
}

export function generateWebsiteSchema(data: Partial<WebSite> = {}): string {
  const schema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    name: "ZbInnovation",
    url: "https://ZbInnovation.co.zw",
    description: "Zimbabwe's premier ecosystem for innovators, investors, and entrepreneurs.",
    potentialAction: {
      "@type": "SearchAction",
      target: "https://ZbInnovation.co.zw/search?q={search_term_string}",
      "query-input": "required name=search_term_string"
    },
    ...data
  };

  return JSON.stringify(schema);
}

export function generateBreadcrumbSchema(items: { name: string; url: string }[]): string {
  const itemListElement = items.map((item, index) => ({
    "@type": "ListItem",
    position: index + 1,
    name: item.name,
    item: item.url
  }));

  const schema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement
  };

  return JSON.stringify(schema);
}

export function generateEventSchema(event: Partial<Event>): string {
  const schema = {
    "@context": "https://schema.org",
    "@type": "Event",
    ...event
  };

  return JSON.stringify(schema);
}

export function generateArticleSchema(article: {
  headline: string;
  description: string;
  image: string;
  datePublished: string;
  dateModified?: string;
  author: string;
  publisher?: string;
  url: string;
}): string {
  const schema = {
    "@context": "https://schema.org",
    "@type": "Article",
    headline: article.headline,
    description: article.description,
    image: article.image,
    datePublished: article.datePublished,
    dateModified: article.dateModified || article.datePublished,
    author: {
      "@type": "Person",
      name: article.author
    },
    publisher: {
      "@type": "Organization",
      name: article.publisher || "ZbInnovation",
      logo: {
        "@type": "ImageObject",
        url: "/logo.png"
      }
    },
    mainEntityOfPage: {
      "@type": "WebPage",
      "@id": article.url
    }
  };

  return JSON.stringify(schema);
}

export function injectStructuredData(jsonLD: string, id?: string): void {
  // If an ID is provided, look for a script with that ID
  let script = id
    ? document.querySelector(`script#${id}[type="application/ld+json"]`)
    : document.querySelector(`script[type="application/ld+json"]`);

  if (!script) {
    script = document.createElement('script');
    script.setAttribute('type', 'application/ld+json');
    if (id) script.setAttribute('id', id);
    document.head.appendChild(script);
  }

  script.textContent = jsonLD;
}

// Helper function to inject multiple structured data objects
export function injectMultipleStructuredData(schemas: { id: string; data: string }[]): void {
  schemas.forEach(schema => {
    injectStructuredData(schema.data, schema.id);
  });
}
