# Matchmaking System Design Document

## 1. Overview

This document outlines the comprehensive design for implementing a matchmaking system across the ZB Innovation platform. The system will leverage profile data, content metadata, and user interactions to create meaningful connections between users, content, and opportunities.

## 2. Profile Types Analysis

### 2.1 Complete Profile Type Inventory

The platform supports the following profile types, each with unique attributes:

1. **Innovator**
   - Dashboard Components: Project Showcase, Find Investors, Find Mentors, Resources
   - Unique Attributes: innovation_stage, market_focus, funding_needs, project_details

2. **Investor**
   - Dashboard Components: Discover Projects, Investment Portfolio, Connect with Innovators, Market Trends
   - Unique Attributes: investment_focus, investment_stages, ticket_size, portfolio_companies

3. **Mentor**
   - Dashboard Components: Mentorship Opportunities, Mentorship Sessions, Mentor Community, Impact Tracking
   - Unique Attributes: expertise_areas, mentoring_approach, years_of_experience, mentorship_philosophy

4. **Professional**
   - Dashboard Components: Discover Opportunities, Network, Events, Resources
   - Unique Attributes: skills, job_title, industry_experience, services_offered

5. **Industry Expert**
   - Dashboard Components: Discover Opportunities, Network, Events, Resources
   - Unique Attributes: industry, expertise_areas, years_of_experience, publications

6. **Academic Student**
   - Dashboard Components: Discover Opportunities, Network, Events, Resources
   - Unique Attributes: field_of_study, academic_level, research_interests, skills

7. **Academic Institution**
   - Dashboard Components: Discover Opportunities, Network, Events, Resources
   - Unique Attributes: research_areas, academic_programs, industry_partnerships, collaboration_types

8. **Organisation**
   - Dashboard Components: Discover Opportunities, Network, Events, Resources
   - Unique Attributes: organization_type, industry_focus, size, collaboration_interests

### 2.2 Common Profile Attributes

All profiles share these common attributes for matchmaking:

- **Goals & Interests**:
  - short_term_goals
  - long_term_goals
  - current_challenges
  - looking_for
  - collaboration_interests
  - sdg_alignment

- **Location Information**:
  - country
  - city
  - willing_to_relocate
  - preferred_locations

## 3. Content Types Analysis

### 3.1 Post Types

1. **General Posts**
   - Attributes: content, tags, visibility

2. **Opportunity Posts**
   - Attributes: title, content, category, opportunityType, opportunityDeadline, applicationUrl, tags

3. **Blog Posts**
   - Attributes: title, content, blogCategory, readTime, tags

4. **Event Posts**
   - Attributes: title, content, eventType, eventFormat, eventDate, eventLocation, tags

5. **Group Posts**
   - Attributes: title, content, groupCategory, memberRange, tags

6. **Marketplace Posts**
   - Attributes: title, content, listingType, priceRange, tags

### 3.2 Filter Categories

1. **Feed Filters**:
   - postTypes, categories, opportunityTypes

2. **Profile Filters**:
   - profileTypes, expertiseAreas

3. **Blog Filters**:
   - blogCategories, readTime

4. **Event Filters**:
   - eventTypes, eventFormat, eventDate

5. **Group Filters**:
   - groupCategories, memberRange

6. **Marketplace Filters**:
   - listingTypes, priceRange

## 4. Matchmaking Strategy

### 4.1 Core Matchmaking Types

1. **Profile-to-Profile Matching**
   - Match users based on complementary profile data
   - Consider profile type-specific matching rules
   - Prioritize goals and interests alignment

2. **Profile-to-Content Matching**
   - Match content to users based on relevance to their profile
   - Consider content type and user interests
   - Prioritize recent and popular content

3. **Dashboard Component Population**
   - Populate dashboard components with relevant matched entities
   - Tailor recommendations to each component's purpose
   - Consider user interaction history with components

### 4.2 Profile-Specific Matching Strategies

#### 4.2.1 Innovator Matching

- **Find Investors**:
  - Match with investors based on:
    - Industry alignment (innovator.market_focus ↔ investor.investment_focus)
    - Stage compatibility (innovator.innovation_stage ↔ investor.investment_stages)
    - Funding needs (innovator.funding_needs ↔ investor.ticket_size)
    - Geographic preferences (innovator.preferred_locations ↔ investor.preferred_locations)

- **Find Mentors**:
  - Match with mentors based on:
    - Expertise needs (innovator.current_challenges ↔ mentor.expertise_areas)
    - Industry alignment (innovator.industry ↔ mentor.industry_experience)
    - Stage-appropriate mentoring (innovator.innovation_stage ↔ mentor.preferred_mentee_stage)

- **Project Showcase**:
  - Recommend similar projects based on:
    - Industry similarity
    - Technology overlap
    - Stage similarity
    - User engagement patterns

#### 4.2.2 Investor Matching

- **Discover Projects**:
  - Match with projects based on:
    - Investment focus (investor.investment_focus ↔ project.industry)
    - Investment stage (investor.investment_stages ↔ project.stage)
    - Ticket size (investor.ticket_size ↔ project.funding_needs)
    - Geographic preferences (investor.preferred_locations ↔ project.location)

- **Connect with Innovators**:
  - Match with innovators based on:
    - Investment criteria alignment
    - Complementary goals
    - Geographic proximity
    - Network connections

- **Market Trends**:
  - Personalize trend insights based on:
    - Investment focus areas
    - Current portfolio composition
    - Expressed interests

#### 4.2.3 Mentor Matching

- **Mentorship Opportunities**:
  - Match with potential mentees based on:
    - Expertise alignment (mentor.expertise_areas ↔ mentee.current_challenges)
    - Industry match (mentor.industry_experience ↔ mentee.industry)
    - Mentoring style preferences (mentor.mentoring_approach ↔ mentee.looking_for)

- **Mentor Community**:
  - Connect with other mentors based on:
    - Complementary expertise
    - Similar mentoring approaches
    - Geographic proximity
    - Shared interests

#### 4.2.4 Default Dashboard Matching (Professional, Industry Expert, Academic, Organisation)

- **Discover Opportunities**:
  - Match opportunities based on:
    - Profile type-specific relevance
    - Skills and expertise alignment
    - Industry relevance
    - Goal alignment

- **Network**:
  - Suggest connections based on:
    - Complementary skills and expertise
    - Similar industries or interests
    - Potential collaboration value
    - Geographic proximity

- **Events**:
  - Recommend events based on:
    - Topic relevance to profile
    - Industry alignment
    - Geographic accessibility
    - Professional development value

- **Resources**:
  - Suggest resources based on:
    - Relevance to profile type
    - Skill development needs
    - Industry-specific knowledge
    - Goal alignment

## 5. Technical Implementation Plan

### 5.1 Database Schema Enhancements

1. **Matchmaking Rules Table**
   ```sql
   CREATE TABLE IF NOT EXISTS public.matchmaking_rules (
     id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
     source_profile_type VARCHAR(50) NOT NULL,
     target_profile_type VARCHAR(50) NOT NULL,
     rule_name VARCHAR(100) NOT NULL,
     source_fields JSONB NOT NULL,
     target_fields JSONB NOT NULL,
     weight FLOAT DEFAULT 1.0,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );
   ```

2. **Dashboard Components Configuration Table**
   ```sql
   CREATE TABLE IF NOT EXISTS public.dashboard_components (
     id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
     component_key VARCHAR(50) NOT NULL,
     component_name VARCHAR(100) NOT NULL,
     profile_type VARCHAR(50) NOT NULL,
     description TEXT,
     icon VARCHAR(50),
     color VARCHAR(50),
     data_requirements JSONB,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     UNIQUE(component_key, profile_type)
   );
   ```

3. **Matchmaking Results Table**
   ```sql
   CREATE TABLE IF NOT EXISTS public.matchmaking_results (
     id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
     user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
     matched_entity_id UUID NOT NULL,
     entity_type VARCHAR(50) NOT NULL,
     match_score FLOAT NOT NULL,
     match_reasons JSONB,
     is_viewed BOOLEAN DEFAULT false,
     is_saved BOOLEAN DEFAULT false,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     UNIQUE(user_id, matched_entity_id, entity_type)
   );
   ```

4. **User Interests Table**
   ```sql
   CREATE TABLE IF NOT EXISTS public.user_interests (
     id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
     user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
     interest_type VARCHAR(50) NOT NULL,
     interest_value VARCHAR(100) NOT NULL,
     weight FLOAT DEFAULT 1.0,
     source VARCHAR(50) NOT NULL,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     UNIQUE(user_id, interest_type, interest_value)
   );
   ```

5. **Component Interactions Table**
   ```sql
   CREATE TABLE IF NOT EXISTS public.component_interactions (
     id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
     user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
     component_key VARCHAR(50) NOT NULL,
     entity_id UUID,
     interaction_type VARCHAR(50) NOT NULL,
     interaction_data JSONB,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );
   ```

### 5.2 Service Architecture

1. **Core Matchmaking Service**
   - Responsible for generating matches across all types
   - Handles scoring and ranking of matches
   - Manages match persistence and retrieval

2. **Profile-Specific Matching Services**
   - Specialized matching logic for each profile type
   - Implements profile-specific scoring algorithms
   - Handles unique attributes and matching rules

3. **Dashboard Component Service**
   - Populates dashboard components with relevant data
   - Manages component configuration and personalization
   - Tracks component interactions for refinement

4. **Interest Extraction Service**
   - Analyzes profile data to extract interests
   - Processes user interactions to infer interests
   - Maintains weighted interest profiles

5. **Recommendation Engine**
   - Combines multiple signals for recommendations
   - Balances diversity and relevance
   - Implements feedback loops for improvement

### 5.3 Implementation Phases

#### Phase 1: Foundation (3 weeks)
- Create database schema enhancements
- Implement core matchmaking service
- Develop basic matching algorithms
- Set up component configuration system

#### Phase 2: Profile-Specific Implementation (4 weeks)
- Implement Innovator-Investor matching
- Implement Innovator-Mentor matching
- Implement Mentor-Mentee matching
- Implement comprehensive profile-to-profile matching for all profile types

#### Phase 3: Dashboard Integration (3 weeks)
- Implement dashboard component population
- Create UI for displaying matches
- Develop interaction tracking
- Implement match feedback mechanisms

#### Phase 4: Advanced Features (4 weeks)
- Implement interest extraction and inference
- Develop recommendation diversity mechanisms
- Create analytics for matchmaking effectiveness
- Implement personalization options

#### Phase 5: Optimization and Scaling (2 weeks)
- Optimize database queries
- Implement caching strategies
- Set up background processing for matches
- Create monitoring and alerting

## 6. Implementation Status

### 6.1 Profile-to-Profile Matching Implementation

The platform now has comprehensive profile-to-profile matchmaking implemented for all profile types. The following matcher functions have been developed and are available in the system:

#### 6.1.1 Innovator-Focused Matchers
- **Innovator to Investor**: Matches innovators with potential investors based on industry alignment, stage compatibility, funding needs, location, and goals.
- **Innovator to Mentor**: Connects innovators with mentors based on expertise needs, industry alignment, stage compatibility, goals, and mentoring approach.
- **Innovator to Professional**: Links innovators with professionals based on skills needed, industry alignment, service compatibility, and goals.
- **Innovator to Industry Expert**: Matches innovators with industry experts based on industry alignment, expertise needs, stage compatibility, and goals.
- **Innovator to Organisation**: Connects innovators with supporting organizations based on industry focus, innovation area, stage compatibility, and partnership goals.

#### 6.1.2 Investor-Focused Matchers
- **Investor to Innovator**: Matches investors with promising innovators, with additional evaluation of traction, team quality, and market potential.
- **Organisation to Investor**: Connects organizations with potential investors based on industry alignment, stage compatibility, funding needs, and partnership goals.

#### 6.1.3 Mentor-Focused Matchers
- **Mentor to Innovator**: Matches mentors with innovators they can effectively support, with additional evaluation of innovator potential and mentorship fit.
- **Mentor to Academic Student**: Connects mentors with students based on academic expertise, field alignment, career guidance, and learning preferences.

#### 6.1.4 Academic-Focused Matchers
- **Academic Student to Mentor**: Matches students with appropriate mentors based on research interests, field of study, career interests, and learning preferences.
- **Academic Student to Institution**: Connects students with academic institutions based on field of study, research interests, program fit, location, and academic goals.
- **Institution to Student**: Matches institutions with potential students, with additional evaluation of student fit and admission criteria match.

#### 6.1.5 Professional and Expert Matchers
- **Professional to Innovator**: Matches professionals with innovators they can support, with additional evaluation of project fit and business potential.
- **Industry Expert to Innovator**: Connects industry experts with relevant innovators based on industry expertise, specific knowledge areas, stage compatibility, and goals.

#### 6.1.6 Organisation Matchers
- **Organisation to Innovator**: Matches organizations with innovators they can support based on industry focus, innovation interests, stage compatibility, and partnership goals.

Each matcher function follows a consistent pattern:
1. Evaluates multiple criteria with appropriate weights
2. Calculates a normalized score between 0 and 1
3. Returns both the overall score and individual reason scores for transparency
4. Handles missing data gracefully with fallbacks

The implementation includes helper functions for specific evaluations such as:
- Evaluating project fit
- Assessing business potential
- Measuring partnership value
- Evaluating resource fit
- Assessing academic potential
- Evaluating mentorship fit
- Measuring admission criteria match

### 6.2 Testing and Simulation

A comprehensive test suite has been implemented to verify the functionality of all matcher functions. The test suite includes:
- Mock profiles for all profile types
- Tests for all matcher combinations
- Validation of score ranges and reason properties

The matchmaking simulation environment has been updated to support all profile type combinations, allowing for testing and refinement of the algorithms without affecting production data.

## 7. User Experience Considerations

### 7.1 Match Presentation

- Display match score and reasons for transparency
- Provide clear call-to-action for each match
- Allow users to save, dismiss, or provide feedback on matches
- Implement progressive disclosure of match details

### 7.2 Personalization

- Allow users to adjust matching preferences
- Provide options to emphasize certain matching criteria
- Enable users to hide or prioritize specific match types
- Remember user preferences across sessions

### 7.3 Privacy Considerations

- Respect profile visibility settings in matching
- Provide clear opt-out options for matchmaking
- Implement appropriate data retention policies
- Ensure transparency in how matches are generated

## 8. Success Metrics

### 8.1 Engagement Metrics

- Match click-through rate
- Connection request rate from matches
- Time spent on matched content
- Return rate to matchmaking features

### 8.2 Quality Metrics

- Match feedback scores
- Connection acceptance rate
- Long-term engagement with matched entities
- User-reported match relevance

### 8.3 Business Metrics

- Increase in connections formed
- Growth in content engagement
- Improvement in user retention
- Expansion of user network size

## 9. Next Steps

1. ✅ Implement comprehensive profile-to-profile matching for all profile types
2. ✅ Create test suite for all matcher functions
3. ✅ Update documentation with implementation details
4. Integrate matchmaking into the user interface
5. Implement profile-to-content matching for all profile types
6. Develop dashboard component population based on matches
7. Create analytics for matchmaking effectiveness
8. Optimize database queries and implement caching strategies
