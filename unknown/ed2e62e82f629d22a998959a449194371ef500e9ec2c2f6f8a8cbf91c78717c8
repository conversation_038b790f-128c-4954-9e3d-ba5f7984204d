import { supabase } from '../../lib/supabase'
import type { BaseProfile, SpecializedProfile } from '../profileService'

/**
 * Fetch a user's profile by user ID
 * 
 * @param userId The user ID to fetch the profile for
 * @returns The user's profile data
 */
export async function fetchUserProfile(userId: string) {
  const { data, error } = await supabase
    .from('personal_details')
    .select('*')
    .eq('user_id', userId)
    .single()
    
  if (error) {
    throw error
  }
  
  return data
}

/**
 * Fetch a specialized profile by profile ID and type
 * 
 * @param profileId The profile ID
 * @param profileType The profile type
 * @returns The specialized profile data
 */
export async function fetchSpecializedProfile(profileId: string, profileType: string) {
  const tableName = `${profileType}_profiles`
  
  const { data, error } = await supabase
    .from(tableName)
    .select('*')
    .eq('id', profileId)
    .single()
    
  if (error) {
    throw error
  }
  
  return data
}

/**
 * Create a new profile
 * 
 * @param profile The profile data to create
 * @returns The created profile
 */
export async function createProfile(profile: BaseProfile) {
  const { data, error } = await supabase
    .from('personal_details')
    .insert(profile)
    .select()
    .single()
    
  if (error) {
    throw error
  }
  
  return data
}

/**
 * Update a profile
 * 
 * @param profileId The profile ID to update
 * @param profile The profile data to update
 * @returns The updated profile
 */
export async function updateProfile(profileId: string, profile: Partial<BaseProfile>) {
  const { data, error } = await supabase
    .from('personal_details')
    .update(profile)
    .eq('id', profileId)
    .select()
    .single()
    
  if (error) {
    throw error
  }
  
  return data
}

/**
 * Update a specialized profile
 * 
 * @param profileId The profile ID to update
 * @param profileType The profile type
 * @param profile The profile data to update
 * @returns The updated specialized profile
 */
export async function updateSpecializedProfile(
  profileId: string, 
  profileType: string, 
  profile: Partial<SpecializedProfile>
) {
  const tableName = `${profileType}_profiles`
  
  const { data, error } = await supabase
    .from(tableName)
    .update(profile)
    .eq('id', profileId)
    .select()
    .single()
    
  if (error) {
    throw error
  }
  
  return data
}
