# ZbInnovation Platform

## Overview

The ZbInnovation platform is designed to bridge the gap between startups, innovators, and supporting organizations within Zimbabwe's growing innovation ecosystem. The platform is being developed in three phases:

1. **Phase 1: Pre-launch** - A landing platform to generate interest and collect early adopters (✅ Completed, awaiting stakeholder feedback)
2. **Phase 2: Soft-launch** - Core functionality for early adopters (🔄 Planning)
3. **Phase 3: Full-launch** - Complete platform with all planned features (📅 Future)

### Current Status

- **Phase 1 Development**: Core functionality complete and awaiting stakeholder feedback
- **Remaining Phase 1 Tasks**:
  - Production deployment setup (VPN, Linux server, Docker, database)
  - Third-party integrations (email platforms, headless WordPress)
  - Documentation updates
- **Phase 2 Planning**: In progress, pending completion of Phase 1

## Technology Stack

### Frontend
- **Framework**: Vue 3 with TypeScript
- **UI Framework**: Quasar Framework
- **State Management**: Pinia
- **Build Tools**: Vite

### Backend
- **Primary Backend**: Java
- **Backend-as-a-Service**: Supabase
- **Database**: PostgreSQL (via Supabase)
- **Authentication**: Supabase Auth
- **Storage**: Supabase Storage
- **Realtime**: Supabase Realtime

### Development & Deployment
- **Development Environment**: Google Cloud Platform (GCP)
- **Testing Environment**: Google Cloud Platform (GCP)
- **Production Environment**: Private Linux server (planned)
- **Containerization**: Docker (planned for production)

### Project Management & Version Control
- **Project Management**: Jira
- **Version Control**: Bitbucket
- **CI/CD**: (To be determined)

## Documentation

Comprehensive documentation is available in the `/docs` directory:

- **Project Documentation**: `/docs/project/` - Project requirements, phases, and roadmap
- **Technical Documentation**: `/docs/technical/` - Architecture, database, and API documentation
- **User Documentation**: `/docs/user/` - User flows, profile management, and dashboard guide
- **Deployment Documentation**: `/docs/deployment/` - Deployment guides and server configuration
- **Database Documentation**: `/docs/database-transition-plan.md` - Plan for migrating from mock data to Supabase
- **Feed Documentation**: `/docs/custom-feed-implementation-plan.md` - Strategy for personalized content feeds

Start with the [Documentation Overview](/docs/README.md) for a complete guide to the available documentation.

## Getting Started

### Prerequisites

- Node.js 16.x or higher
- npm 8.x or higher
- Supabase account

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/your-organization/ZbInnovation.git
   cd ZbInnovation
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables:
   - Create a `.env` file in the project root
   - Add the following variables:
     ```
     VITE_SUPABASE_URL=your_supabase_url
     VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
     ```

4. Start the development server:
   ```bash
   npm run dev
   ```

### Database Setup

Follow the [Database Setup Guide](/docs/setup/database-setup.md) to set up the database structure.

## Development Workflow

The project follows a structured development workflow managed through Jira. See the [Jira Workflow Documentation](/docs/project/jira-workflow.md) for details.

## Deployment

For deployment instructions, refer to the [Deployment Guide](/docs/deployment/guide.md).

## Contributing

Please read the [Coding Standards](/docs/coding-standards.md) before contributing to the project.

## License

[Specify the license here]
