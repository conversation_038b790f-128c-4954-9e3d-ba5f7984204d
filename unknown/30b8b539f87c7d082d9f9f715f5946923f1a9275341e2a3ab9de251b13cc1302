import { defineStore } from 'pinia'
import { ref } from 'vue'

interface NotificationAction {
  label: string
  color?: string
  handler: () => void
}

interface Notification {
  id: number
  type: 'success' | 'error' | 'warning' | 'info'
  message: string
  caption?: string
  icon?: string
  color?: string
  timeout?: number
  actions?: NotificationAction[]
}

export const useNotificationStore = defineStore('notifications', () => {
  const notifications = ref<Notification[]>([])
  let nextId = 0

  function add(notification: Omit<Notification, 'id'>) {
    // Check for duplicate notifications (same type and message)
    const isDuplicate = notifications.value.some(
      n => n.type === notification.type && n.message === notification.message
    )

    // If it's a duplicate, don't add it
    if (isDuplicate) {
      console.log('Duplicate notification prevented:', notification.message)
      return
    }

    // Clear any existing notifications with opposite type (success/error) for sign-in/registration
    if (
      (notification.message.includes('signed in') || notification.message.includes('Successfully signed in')) &&
      notification.type === 'success'
    ) {
      // Remove any error notifications related to registration
      const errorsToRemove = notifications.value.filter(
        n => n.type === 'error' &&
        (n.message.includes('registered') || n.message.includes('Registration'))
      )

      errorsToRemove.forEach(n => remove(n.id))
    }

    const id = nextId++
    notifications.value.push({
      ...notification,
      id,
      timeout: notification.timeout || 3000
    })

    // Auto remove notification after timeout
    if (notification.timeout !== 0) {
      setTimeout(() => {
        remove(id)
      }, notification.timeout || 3000)
    }
  }

  function remove(id: number) {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  function success(messageOrConfig: string | Omit<Notification, 'id' | 'type'>, timeout?: number) {
    if (typeof messageOrConfig === 'string') {
      add({ type: 'success', message: messageOrConfig, timeout })
    } else {
      add({ ...messageOrConfig, type: 'success' })
    }
  }

  function error(messageOrConfig: string | Omit<Notification, 'id' | 'type'>, timeout?: number) {
    if (typeof messageOrConfig === 'string') {
      add({ type: 'error', message: messageOrConfig, timeout })
    } else {
      add({ ...messageOrConfig, type: 'error' })
    }
  }

  function warning(messageOrConfig: string | Omit<Notification, 'id' | 'type'>, timeout?: number) {
    if (typeof messageOrConfig === 'string') {
      add({ type: 'warning', message: messageOrConfig, timeout })
    } else {
      add({ ...messageOrConfig, type: 'warning' })
    }
  }

  function info(messageOrConfig: string | Omit<Notification, 'id' | 'type'>, timeout?: number) {
    if (typeof messageOrConfig === 'string') {
      add({ type: 'info', message: messageOrConfig, timeout })
    } else {
      add({ ...messageOrConfig, type: 'info' })
    }
  }

  return {
    notifications,
    add,
    remove,
    success,
    error,
    warning,
    info
  }
})