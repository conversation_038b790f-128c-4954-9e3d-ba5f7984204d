<template>
  <div class="q-pa-md">
    <q-card class="connection-test-card">
      <q-card-section>
        <div class="text-h6">JIRA Connection Status</div>
        <div class="q-mt-md">
          <template v-if="isLoading">
            <q-spinner color="primary" size="2em" />
            <span class="q-ml-sm">Testing connection...</span>
          </template>
          <template v-else>
            <q-icon
              :name="isConnected ? 'check_circle' : 'error'"
              :color="isConnected ? 'positive' : 'negative'"
              size="2em"
            />
            <span class="q-ml-sm">{{ connectionStatus }}</span>
          </template>
        </div>
      </q-card-section>
      <q-card-actions align="right">
        <q-btn
          flat
          color="primary"
          label="Test Connection"
          @click="testConnection"
          :loading="isLoading"
        />
      </q-card-actions>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { testJiraConnection } from '../lib/jira'

const isLoading = ref(false)
const isConnected = ref(false)
const connectionStatus = ref('Not tested')

async function testConnection() {
  isLoading.value = true
  connectionStatus.value = 'Testing connection...'

  try {
    const result = await testJiraConnection()
    isConnected.value = result
    connectionStatus.value = result
      ? 'Successfully connected to JIRA'
      : 'Failed to connect to JIRA'
  } catch (error) {
    isConnected.value = false
    connectionStatus.value = 'Error connecting to JIRA'
    console.error('JIRA connection error:', error)
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  testConnection()
})
</script>

<style scoped>
.connection-test-card {
  max-width: 400px;
  margin: 0 auto;
}
</style> 