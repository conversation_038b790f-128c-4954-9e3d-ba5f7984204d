<template>
  <q-card v-if="hasSocialMedia" class="q-mb-md section-card">
    <q-card-section class="section-header bg-primary text-white">
      <div class="text-h6">
        <unified-icon name="share" class="q-mr-sm" />
        Social Media
      </div>
    </q-card-section>
    <q-card-section>
      <div class="row q-col-gutter-md">
        <!-- LinkedIn -->
        <div class="col-12 col-md-6" v-if="profile.linkedin">
          <div class="text-subtitle2">LinkedIn</div>
          <div>
            <a :href="profile.linkedin" target="_blank" class="text-primary">
              {{ profile.linkedin }}
            </a>
          </div>
        </div>

        <!-- Twitter -->
        <div class="col-12 col-md-6" v-if="profile.twitter">
          <div class="text-subtitle2">Twitter</div>
          <div>
            <a :href="profile.twitter" target="_blank" class="text-primary">
              {{ profile.twitter }}
            </a>
          </div>
        </div>

        <!-- Facebook -->
        <div class="col-12 col-md-6" v-if="profile.facebook">
          <div class="text-subtitle2">Facebook</div>
          <div>
            <a :href="profile.facebook" target="_blank" class="text-primary">
              {{ profile.facebook }}
            </a>
          </div>
        </div>

        <!-- Instagram -->
        <div class="col-12 col-md-6" v-if="profile.instagram">
          <div class="text-subtitle2">Instagram</div>
          <div>
            <a :href="profile.instagram" target="_blank" class="text-primary">
              {{ profile.instagram }}
            </a>
          </div>
        </div>
      </div>

      <!-- Social Media Buttons -->
      <div v-if="hasSocialMediaLinks" class="q-mt-md">
        <div class="text-subtitle2 q-mb-sm">Quick Links</div>
        <div class="row q-col-gutter-sm">
          <div v-if="profile.linkedin" class="col-auto">
            <q-btn
              :href="profile.linkedin"
              target="_blank"
              round
              flat
              color="primary"
              size="md"
              class="social-btn"
            >
              <q-icon name="img:data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%230D8A3E' d='M19 3a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14m-.5 15.5v-5.3a3.26 3.26 0 0 0-3.26-3.26c-.85 0-1.84.52-2.32 1.3v-1.11h-2.79v8.37h2.79v-4.93c0-.77.62-1.4 1.39-1.4a1.4 1.4 0 0 1 1.4 1.4v4.93h2.79M6.88 8.56a1.68 1.68 0 0 0 1.68-1.68c0-.93-.75-1.69-1.68-1.69a1.69 1.69 0 0 0-1.69 1.69c0 .93.76 1.68 1.69 1.68m1.39 9.94v-8.37H5.5v8.37h2.77z'/%3E%3C/svg%3E" size="24px" />
              <q-tooltip>LinkedIn</q-tooltip>
            </q-btn>
          </div>
          <div v-if="profile.twitter" class="col-auto">
            <q-btn
              :href="profile.twitter"
              target="_blank"
              round
              flat
              color="primary"
              size="md"
              class="social-btn"
            >
              <q-icon name="img:data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%230D8A3E' d='M22.46 6c-.77.35-1.6.58-**********-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .***********.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z'/%3E%3C/svg%3E" size="24px" />
              <q-tooltip>Twitter</q-tooltip>
            </q-btn>
          </div>
          <div v-if="profile.facebook" class="col-auto">
            <q-btn
              :href="profile.facebook"
              target="_blank"
              round
              flat
              color="primary"
              size="md"
              class="social-btn"
            >
              <q-icon name="img:data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%230D8A3E' d='M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z'/%3E%3C/svg%3E" size="24px" />
              <q-tooltip>Facebook</q-tooltip>
            </q-btn>
          </div>
          <div v-if="profile.instagram" class="col-auto">
            <q-btn
              :href="profile.instagram"
              target="_blank"
              round
              flat
              color="primary"
              size="md"
              class="social-btn"
            >
              <q-icon name="img:data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%230D8A3E' d='M7.8 2h8.4C19.4 2 22 4.6 22 7.8v8.4a5.8 5.8 0 0 1-5.8 5.8H7.8C4.6 22 2 19.4 2 16.2V7.8A5.8 5.8 0 0 1 7.8 2m-.2 2A3.6 3.6 0 0 0 4 7.6v8.8C4 18.39 5.61 20 7.6 20h8.8a3.6 3.6 0 0 0 3.6-3.6V7.6C20 5.61 18.39 4 16.4 4H7.6m9.65 1.5a1.25 1.25 0 0 1 1.25 1.25A1.25 1.25 0 0 1 17.25 8 1.25 1.25 0 0 1 16 6.75a1.25 1.25 0 0 1 1.25-1.25M12 7a5 5 0 0 1 5 5 5 5 0 0 1-5 5 5 5 0 0 1-5-5 5 5 0 0 1 5-5m0 2a3 3 0 0 0-3 3 3 3 0 0 0 3 3 3 3 0 0 0 3-3 3 3 0 0 0-3-3z'/%3E%3C/svg%3E" size="24px" />
              <q-tooltip>Instagram</q-tooltip>
            </q-btn>
          </div>
        </div>
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import UnifiedIcon from '../ui/UnifiedIcon.vue'

// Props
const props = defineProps<{
  profile: any
}>()

// Computed
const hasSocialMedia = computed(() => {
  return !!(
    props.profile.linkedin ||
    props.profile.twitter ||
    props.profile.facebook ||
    props.profile.instagram
  )
})

const socialLinks = computed(() => {
  if (!props.profile) return {}

  const links = {}
  const socialFields = ['linkedin', 'twitter', 'facebook', 'instagram', 'youtube', 'github', 'medium']

  socialFields.forEach(field => {
    if (props.profile[field]) {
      links[field] = props.profile[field]
    }
  })

  if (props.profile.other_social) {
    links.other = props.profile.other_social
  }

  return links
})

const hasSocialMediaLinks = computed(() => {
  return Object.keys(socialLinks.value).length > 0
})

// Helper functions copied from ProfileDisplay.vue
function getSocialIcon(platform) {
  const icons = {
    linkedin: 'linkedin',
    twitter: 'twitter',
    facebook: 'facebook',
    instagram: 'instagram',
    youtube: 'youtube',
    github: 'github',
    medium: 'medium',
    other: 'link'
  }

  return icons[platform] || 'link'
}

function formatSocialName(platform) {
  const names = {
    linkedin: 'LinkedIn',
    twitter: 'Twitter',
    facebook: 'Facebook',
    instagram: 'Instagram',
    youtube: 'YouTube',
    github: 'GitHub',
    medium: 'Medium',
    other: 'Other'
  }

  return names[platform] || platform
}

function formatSocialLink(platform, value) {
  // If the value already includes http:// or https://, return it as is
  if (value.startsWith('http://') || value.startsWith('https://')) {
    return value
  }

  // Otherwise, add the appropriate prefix based on the platform
  const prefixes = {
    linkedin: 'https://linkedin.com/in/',
    twitter: 'https://twitter.com/',
    facebook: 'https://facebook.com/',
    instagram: 'https://instagram.com/',
    youtube: 'https://youtube.com/',
    github: 'https://github.com/',
    medium: 'https://medium.com/@'
  }

  const prefix = prefixes[platform]
  return prefix ? prefix + value : value
}
</script>

<style scoped>
.section-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.social-btn {
  transition: all 0.3s ease;
}

.social-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.section-header {
  border-radius: 8px 8px 0 0;
}

.social-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
</style>
