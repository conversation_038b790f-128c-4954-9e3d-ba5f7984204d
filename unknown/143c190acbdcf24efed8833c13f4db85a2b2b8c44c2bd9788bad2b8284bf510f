<template>
  <q-page class="article-view-page q-pa-md">
    <div class="container">
      <div class="row justify-center">
        <div class="col-12 col-md-8">
          <q-card flat bordered>
            <q-card-section>
              <div class="text-h4 q-mb-md">{{ article ? article.title : 'Loading article...' }}</div>

              <div v-if="loading" class="text-center q-pa-lg">
                <q-spinner color="primary" size="3em" />
                <p>Loading article...</p>
              </div>

              <div v-else-if="error" class="text-center q-pa-lg text-negative">
                <p>{{ error }}</p>
                <q-btn color="primary" label="Go Back" @click="$router.go(-1)" />
              </div>

              <template v-else-if="article">
                <div class="row items-center q-mb-md">
                  <q-avatar size="40px">
                    <img :src="article.authorAvatar || 'https://cdn.quasar.dev/img/avatar.png'" />
                  </q-avatar>
                  <div class="q-ml-sm">
                    <div class="text-weight-bold">{{ article.author }}</div>
                    <div class="text-caption">{{ article.date }}</div>
                  </div>
                  <q-space />
                  <q-badge :color="getCategoryColor(article.category)">{{ article.category }}</q-badge>
                </div>

                <q-img
                  :src="article.image"
                  :ratio="16/9"
                  class="rounded-borders q-mb-md"
                />

                <div class="article-content q-mb-xl">
                  <!-- This would be replaced with actual article content -->
                  <p>{{ article.excerpt }}</p>
                  <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod, nisl eget aliquam ultricies, nunc nisl aliquet nunc, quis aliquam nisl nunc quis nisl. Nullam euismod, nisl eget aliquam ultricies, nunc nisl aliquet nunc, quis aliquam nisl nunc quis nisl.</p>
                  <p>Nullam euismod, nisl eget aliquam ultricies, nunc nisl aliquet nunc, quis aliquam nisl nunc quis nisl. Nullam euismod, nisl eget aliquam ultricies, nunc nisl aliquet nunc, quis aliquam nisl nunc quis nisl.</p>
                  <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod, nisl eget aliquam ultricies, nunc nisl aliquet nunc, quis aliquam nisl nunc quis nisl. Nullam euismod, nisl eget aliquam ultricies, nunc nisl aliquet nunc, quis aliquam nisl nunc quis nisl.</p>
                </div>

                <div class="row q-gutter-sm">
                  <q-btn flat color="primary" icon="thumb_up" label="Like" />
                  <q-btn flat color="primary" icon="bookmark" label="Save" />
                  <q-btn flat color="primary" icon="share" label="Share" />
                </div>
              </template>
            </q-card-section>
          </q-card>

          <!-- Related Articles -->
          <div v-if="article && relatedArticles.length" class="q-mt-lg">
            <div class="text-h5 q-mb-md">Related Articles</div>
            <div class="row q-col-gutter-md">
              <div v-for="relatedArticle in relatedArticles" :key="relatedArticle.id" class="col-12 col-sm-6">
                <q-card class="blog-card" @click="viewArticle(relatedArticle.slug)">
                  <q-img :src="relatedArticle.image" :ratio="16/9" />
                  <q-card-section>
                    <div class="text-h6">{{ relatedArticle.title }}</div>
                    <div class="text-caption q-mb-sm">{{ relatedArticle.date }} | {{ relatedArticle.author }}</div>
                    <p class="text-body2">{{ relatedArticle.excerpt }}</p>
                  </q-card-section>
                  <q-card-actions align="right">
                    <q-btn flat color="primary" label="Read More" />
                  </q-card-actions>
                </q-card>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
// Sample data imports removed

const route = useRoute();
const router = useRouter();
const slug = route.params.slug as string;

const article = ref(null);
const loading = ref(true);
const error = ref(null);
const relatedArticles = ref([]);

onMounted(async () => {
  try {
    // In a real implementation, this would be an API call
    // For now, we'll just use the sample data
    loading.value = true;

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    const foundArticle = sampleArticles.find(a => a.slug === slug);

    if (foundArticle) {
      article.value = foundArticle;

      // Get related articles (same category)
      relatedArticles.value = sampleArticles
        .filter(a => a.category === foundArticle.category && a.id !== foundArticle.id)
        .slice(0, 2);
    } else {
      error.value = 'Article not found';
    }
  } catch (err) {
    console.error('Error fetching article:', err);
    error.value = 'Failed to load article';
  } finally {
    loading.value = false;
  }
});

function viewArticle(articleSlug: string) {
  router.push({ name: 'article', params: { slug: articleSlug } });
}

// Helper function to get category color
function getCategoryColor(category: string): string {
  const categoryColors: Record<string, string> = {
    'Events': 'green',
    'News': 'blue',
    'Success Stories': 'orange',
    'Funding': 'green-9',
    'Collaboration': 'blue-7',
    'Mentorship': 'purple-7',
    'Innovation': 'deep-orange',
    'Research': 'teal',
    'Training': 'indigo'
  };

  return categoryColors[category] || 'primary';
}
</script>

<style scoped>
.article-view-page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.article-content {
  font-size: 1.1rem;
  line-height: 1.6;
}

.blog-card {
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
}

.blog-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}
</style>
