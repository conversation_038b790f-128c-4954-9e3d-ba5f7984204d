<template>
  <q-page class="marketplace-edit-page q-pa-md">
    <div class="container">
      <div v-if="loading" class="text-center q-pa-lg">
        <q-spinner color="primary" size="3em" />
        <p>Loading listing...</p>
      </div>

      <div v-else-if="error" class="text-center q-pa-lg text-negative">
        <p>{{ error }}</p>
        <q-btn color="primary" label="Go Back" @click="$router.go(-1)" />
      </div>

      <template v-else>
        <div class="row q-col-gutter-md">
          <div class="col-12">
            <q-card class="edit-header-card">
              <q-card-section>
                <div class="text-h4">Edit Marketplace Listing</div>
                <p class="text-body1 q-mt-md">
                  Update your product or service listing information.
                </p>
              </q-card-section>
            </q-card>
          </div>

          <div class="col-12">
            <q-card>
              <q-card-section>
                <q-form @submit="onSubmit" class="q-gutter-md">
                  <!-- Listing Type -->
                  <div class="row q-col-gutter-md">
                    <div class="col-12 col-md-6">
                      <q-select
                        v-model="formData.type"
                        :options="listingTypeOptions"
                        label="Listing Type *"
                        outlined
                        emit-value
                        map-options
                        :rules="[val => !!val || 'Please select a listing type']"
                      />
                    </div>
                    <div class="col-12 col-md-6">
                      <q-select
                        v-model="formData.category"
                        :options="categoryOptions"
                        label="Category *"
                        outlined
                        emit-value
                        map-options
                        :rules="[val => !!val || 'Please select a category']"
                      />
                    </div>
                  </div>

                  <!-- Title and Price -->
                  <div class="row q-col-gutter-md">
                    <div class="col-12 col-md-8">
                      <q-input
                        v-model="formData.title"
                        label="Title *"
                        outlined
                        :rules="[val => !!val || 'Title is required']"
                      />
                    </div>
                    <div class="col-12 col-md-4">
                      <q-input
                        v-model="formData.price"
                        label="Price"
                        outlined
                        placeholder="e.g. $100, Contact for pricing, etc."
                      />
                    </div>
                  </div>

                  <!-- Description -->
                  <q-input
                    v-model="formData.description"
                    label="Description *"
                    outlined
                    type="textarea"
                    rows="6"
                    :rules="[val => !!val || 'Description is required']"
                  />

                  <!-- Location -->
                  <q-input
                    v-model="formData.location"
                    label="Location"
                    outlined
                    placeholder="e.g. Harare, Zimbabwe or Remote"
                  />

                  <!-- Image URL -->
                  <q-input
                    v-model="formData.image"
                    label="Image URL"
                    outlined
                    placeholder="https://example.com/image.jpg"
                  />

                  <!-- Tags -->
                  <q-select
                    v-model="formData.tags"
                    label="Tags"
                    outlined
                    use-input
                    use-chips
                    multiple
                    hide-dropdown-icon
                    input-debounce="0"
                    new-value-mode="add-unique"
                    hint="Press Enter to add a tag"
                  />

                  <!-- Submit Buttons -->
                  <div class="row justify-end q-gutter-sm">
                    <q-btn
                      label="Cancel"
                      color="grey-7"
                      flat
                      @click="$router.go(-1)"
                      :disable="submitting"
                    />
                    <q-btn
                      label="Save Changes"
                      type="submit"
                      color="primary"
                      :loading="submitting"
                    />
                  </div>
                </q-form>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </template>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { useAuthStore } from '../../stores/auth';
import { usePostsStore } from '../../stores/posts';
import { filterOptions } from '../../services/filterOptionsService';

const route = useRoute();
const router = useRouter();
const $q = useQuasar();
const authStore = useAuthStore();
const postsStore = usePostsStore();

const listingId = route.params.id as string;
const loading = ref(true);
const error = ref<string | null>(null);
const submitting = ref(false);

// Form data
const formData = ref({
  title: '',
  description: '',
  price: '',
  type: '',
  category: '',
  location: '',
  image: '',
  tags: []
});

// Options for dropdowns - use filter service for consistency
const listingTypeOptions = filterOptions.listingTypeOptions;

const categoryOptions = [
  { label: 'Technology', value: 'Technology' },
  { label: 'Agriculture', value: 'Agriculture' },
  { label: 'Education', value: 'Education' },
  { label: 'Healthcare', value: 'Healthcare' },
  { label: 'Energy', value: 'Energy' },
  { label: 'Finance', value: 'Finance' },
  { label: 'Manufacturing', value: 'Manufacturing' },
  { label: 'Retail', value: 'Retail' },
  { label: 'Transportation', value: 'Transportation' },
  { label: 'Other', value: 'Other' }
];

// Fetch the listing data
onMounted(async () => {
  await fetchListing();
});

async function fetchListing() {
  loading.value = true;
  error.value = null;

  try {
    // Check if user is authenticated
    if (!authStore.isAuthenticated) {
      throw new Error('You must be logged in to edit a listing');
    }

    // Get the post by ID
    const post = await postsStore.getPostById(Number(listingId));

    if (!post) {
      throw new Error('Listing not found');
    }

    // Check if the user owns this post
    if (post.userId !== authStore.currentUser?.id) {
      throw new Error('You do not have permission to edit this listing');
    }

    // Populate the form data
    formData.value = {
      title: post.title || '',
      description: post.content || '',
      price: post.price || '',
      type: post.subType || 'Product',
      category: post.category || '',
      location: post.location || '',
      image: post.imageUrl || post.featuredImage || '',
      tags: post.tags || []
    };
  } catch (err: any) {
    console.error('Error fetching listing:', err);
    error.value = err.message || 'Failed to load listing';
  } finally {
    loading.value = false;
  }
}

async function onSubmit() {
  submitting.value = true;

  try {
    // Check if user is authenticated
    if (!authStore.isAuthenticated) {
      throw new Error('You must be logged in to update a listing');
    }

    // Prepare the updated post data
    const updatedPost = {
      id: Number(listingId),
      title: formData.value.title,
      content: formData.value.description,
      price: formData.value.price,
      subType: formData.value.type,
      category: formData.value.category,
      location: formData.value.location,
      featuredImage: formData.value.image,
      tags: formData.value.tags
    };

    // Update the post
    const success = await postsStore.updatePost(updatedPost);

    if (!success) {
      throw new Error('Failed to update listing');
    }

    // Show success notification
    $q.notify({
      color: 'positive',
      message: 'Listing updated successfully',
      icon: 'check_circle',
      position: 'top',
      timeout: 2000
    });

    // Navigate back to the listing view
    router.push({ name: 'marketplace-listing', params: { id: listingId } });
  } catch (err: any) {
    console.error('Error updating listing:', err);

    // Show error notification
    $q.notify({
      color: 'negative',
      message: err.message || 'Failed to update listing',
      icon: 'error',
      position: 'top',
      timeout: 3000
    });
  } finally {
    submitting.value = false;
  }
}
</script>

<style scoped>
.marketplace-edit-page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.edit-header-card {
  background-color: var(--q-primary);
  color: white;
  margin-bottom: 1rem;
}
</style>
