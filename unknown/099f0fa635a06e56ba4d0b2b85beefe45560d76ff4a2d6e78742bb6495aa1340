<template>
  <q-card class="success-story-card">
    <q-img :src="storyImage" :ratio="16/9" @error="handleImageError" no-spinner no-transition>
      <div class="absolute-bottom text-subtitle2 text-center bg-transparent">
        <q-badge color="orange" text-color="white">
          Success Story
        </q-badge>
      </div>
      <template v-slot:error>
        <div class="absolute-full flex flex-center bg-grey-3 text-grey-8">
          <div class="text-center">
            <q-icon name="broken_image" size="3em" />
            <div>Image failed to load</div>
            <q-btn
              v-if="isImageUrlFixable"
              flat
              color="primary"
              label="Try Fix URL"
              class="q-mt-sm"
              @click="tryFixImageUrl"
            />
          </div>
        </div>
      </template>
    </q-img>
    <q-card-section>
      <div class="text-h6">{{ story.title }}</div>
      <div class="text-caption q-mb-sm">
        {{ formatDate(story.createdAt) }} | {{ story.author || 'Anonymous' }}
        <span v-if="story.authorRole" class="q-ml-sm">
          <q-icon name="business" size="xs" /> {{ story.authorRole }}
        </span>
      </div>
      <p class="text-body2">{{ story.excerpt || (story.content ? truncateContent(story.content) : 'No content available') }}</p>
    </q-card-section>
    <q-card-actions align="right">
      <q-btn flat color="grey" icon="bookmark_border" @click="handleSave" />
      <q-btn flat color="grey" icon="share" @click="handleShare" />
      <q-btn flat color="primary" label="Read More" @click="handleReadMore" />
    </q-card-actions>
  </q-card>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, computed, ref } from 'vue';
import { useRouter } from 'vue-router';
import { date } from 'quasar';
import { truncateText } from '../../../utils/textUtils';

// Local state for fixed image URL
const fixedImageUrl = ref('');

const props = defineProps({
  story: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['read', 'share', 'save']);
const router = useRouter();

// Get the story image
const storyImage = computed(() => {
  // If we have a fixed URL from a previous fix attempt, use that
  if (fixedImageUrl.value) {
    return fixedImageUrl.value;
  }

  const imageUrl = props.story.featuredImage || props.story.image || '';
  console.log('Success story image URL for story ID:', props.story.id, 'URL:', imageUrl);

  // Log detailed information about the image sources
  console.log('Success story image source data:', {
    featuredImage: props.story.featuredImage,
    image: props.story.image,
    post_id: props.story.id,
    post_type: props.story.post_type,
    category: props.story.category
  });

  return imageUrl;
});

// Check if the image URL is potentially fixable
const isImageUrlFixable = computed(() => {
  const url = props.story.featuredImage || props.story.image || '';

  // If it's empty, it's not fixable
  if (!url) return false;

  // If it already has the correct full Supabase URL format, it's not fixable
  if (url.includes('dpicnvisvxpmgjtbeicf.supabase.co/storage/v1/object/public/imagefiles/') &&
      !url.includes('imagefiles/imagefiles/')) {
    return false;
  }

  // If it's an external URL (not Supabase), it's not fixable
  if (url.startsWith('http') && !url.includes('supabase') && !url.includes('imagefiles')) {
    return false;
  }

  // If it has the wrong format (includes imagefiles but not in the right place), it's fixable
  if (url.includes('imagefiles/') &&
      (!url.includes('storage/v1/object/public/imagefiles/') ||
       url.includes('imagefiles/imagefiles/'))) {
    return true;
  }

  // Otherwise, we might be able to fix it
  return true;
});

// Methods
function handleReadMore() {
  emit('read', props.story.id);

  // Navigate to the story detail page
  router.push({
    name: 'post-details',
    params: { id: props.story.id },
    query: { type: 'success-story' }
  });
}

function handleShare() {
  emit('share', props.story.id);
}

function handleSave() {
  emit('save', props.story.id);
}

function truncateContent(content: string): string {
  // Extract content from JSON if needed
  if (typeof content === 'string' && (content.startsWith('{') || content.startsWith('['))) {
    try {
      const parsedContent = JSON.parse(content);

      // Handle different JSON structures
      if (parsedContent.description) {
        content = parsedContent.description;
      } else if (parsedContent.storyDetails && parsedContent.storyDetails.description) {
        content = parsedContent.storyDetails.description;
      } else if (parsedContent.content) {
        content = parsedContent.content;
      } else if (typeof parsedContent === 'object') {
        // If it's an object but doesn't have expected fields, stringify it
        content = JSON.stringify(parsedContent);
      }
    } catch (e) {
      // If parsing fails, it's not valid JSON, keep the original content
      console.log('Failed to parse content as JSON:', e);
    }
  }

  return truncateText(content, 250);
}

function formatDate(dateString: string): string {
  if (!dateString) return 'Unknown date';
  return date.formatDate(dateString, 'MMM D, YYYY');
}

// Handle image loading errors
function handleImageError(err: Error) {
  console.error('Image failed to load for story ID:', props.story.id, 'Error:', err);
  console.error('Failed image URL:', storyImage.value);

  // Log detailed information about the story and image
  console.error('Story details:', {
    id: props.story.id,
    title: props.story.title,
    featuredImage: props.story.featuredImage,
    image: props.story.image,
    category: props.story.category
  });

  // If the URL is from Supabase but doesn't have the full path, suggest a fix
  if (isImageUrlFixable.value) {
    console.log('This image URL might be fixable. Try clicking the "Try Fix URL" button.');
  }
}

// Try to fix the image URL by adding the Supabase storage prefix
function tryFixImageUrl() {
  const originalUrl = props.story.featuredImage || props.story.image || '';

  // If the URL is empty, there's nothing to fix
  if (!originalUrl) return;

  console.log('Attempting to fix image URL:', originalUrl);

  // If it's a relative path or partial Supabase path
  if (!originalUrl.startsWith('http')) {
    // If it already includes the bucket name
    if (originalUrl.includes('imagefiles/')) {
      // Extract the file path after 'imagefiles/'
      const match = originalUrl.match(/imagefiles\/(.+)$/);
      if (match && match[1]) {
        fixedImageUrl.value = `https://dpicnvisvxpmgjtbeicf.supabase.co/storage/v1/object/public/imagefiles/${match[1]}`;
      } else {
        fixedImageUrl.value = `https://dpicnvisvxpmgjtbeicf.supabase.co/storage/v1/object/public/${originalUrl}`;
      }
    } else {
      // Assume it's in the imagefiles bucket
      fixedImageUrl.value = `https://dpicnvisvxpmgjtbeicf.supabase.co/storage/v1/object/public/imagefiles/${originalUrl}`;
    }
  } else if (originalUrl.includes('supabase')) {
    if (!originalUrl.includes('/storage/v1/object/public/')) {
      // It's a Supabase URL but missing the storage path
      const parts = originalUrl.split('/');
      const fileName = parts[parts.length - 1];
      fixedImageUrl.value = `https://dpicnvisvxpmgjtbeicf.supabase.co/storage/v1/object/public/imagefiles/${fileName}`;
    } else if (originalUrl.includes('imagefiles/') && !originalUrl.includes('/storage/v1/object/public/imagefiles/')) {
      // It has the wrong format for the bucket path
      const match = originalUrl.match(/imagefiles\/(.+)$/);
      if (match && match[1]) {
        fixedImageUrl.value = `https://dpicnvisvxpmgjtbeicf.supabase.co/storage/v1/object/public/imagefiles/${match[1]}`;
      }
    }
  }

  console.log('Fixed image URL:', fixedImageUrl.value);
}
</script>

<style scoped>
.success-story-card {
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.success-story-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

@media (max-width: 599px) {
  .success-story-card {
    margin: 0 8px 16px 8px;
    width: calc(100% - 16px);
  }
}
</style>
