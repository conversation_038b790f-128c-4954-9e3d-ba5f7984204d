// Common Social Media Section
// This section can be included in all profile types

import { ProfileSection } from '../types';

export const socialMediaSection: ProfileSection = {
  title: 'Social Media & Online Presence',
  icon: 'public',
  description: 'Share your online presence and social media profiles',
  questions: [
    {
      id: 'website',
      name: 'website',
      label: 'Website',
      type: 'text',
      fullWidth: true,
      hint: 'Your personal or organization website URL'
    },
    {
      id: 'linkedin',
      name: 'linkedin',
      label: 'LinkedIn',
      type: 'text',
      hint: 'Your LinkedIn profile URL'
    },
    {
      id: 'twitter',
      name: 'twitter',
      label: 'Twitter/X',
      type: 'text',
      hint: 'Your Twitter/X profile URL'
    },
    {
      id: 'facebook',
      name: 'facebook',
      label: 'Facebook',
      type: 'text',
      hint: 'Your Facebook profile or page URL'
    },
    {
      id: 'instagram',
      name: 'instagram',
      label: 'Instagram',
      type: 'text',
      hint: 'Your Instagram profile URL'
    },
    {
      id: 'youtube',
      name: 'youtube',
      label: 'YouTube',
      type: 'text',
      hint: 'Your YouTube channel URL'
    },
    {
      id: 'github',
      name: 'github',
      label: 'GitHub',
      type: 'text',
      hint: 'Your GitHub profile URL'
    },
    {
      id: 'medium',
      name: 'medium',
      label: 'Medium',
      type: 'text',
      hint: 'Your Medium profile URL'
    },
    {
      id: 'other_social',
      name: 'other_social',
      label: 'Other Social Media',
      type: 'text',
      fullWidth: true,
      hint: 'Other relevant social media profiles (comma separated)'
    }
  ]
};
