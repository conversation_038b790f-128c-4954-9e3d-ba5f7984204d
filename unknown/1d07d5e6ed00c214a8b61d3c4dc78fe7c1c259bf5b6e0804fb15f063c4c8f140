<template>
  <q-page class="flex flex-center bg-grey-2">
    <q-card class="password-reset-card q-pa-lg">
      <q-card-section class="text-center">
        <div class="text-h4 q-mb-md">Set New Password</div>
        <div class="text-subtitle1 text-grey-7">
          Please enter your new password
        </div>
      </q-card-section>

      <q-card-section>
        <div v-if="passwordChanged" class="text-center q-pa-md">
          <q-icon name="check_circle" color="positive" size="48px" />
          <p class="text-body1 q-mb-md">
            Your password has been successfully changed. You can now sign in with your new password.
          </p>
          <q-btn
            flat
            color="primary"
            label="Go to Sign In"
            class="q-mt-md"
            to="/sign-in"
          />
        </div>

        <div v-else-if="!hasValidToken" class="text-center q-pa-md">
          <q-icon name="error" color="warning" size="48px" />
          <p class="text-body1 q-mb-md">
            Invalid or expired password reset link. Please request a new one.
          </p>
          <q-btn
            flat
            color="primary"
            label="Request New Link"
            class="q-mt-md"
            to="/password-reset"
          />
        </div>

        <div v-else>
          <q-form @submit="handlePasswordChange" class="q-gutter-md">
            <q-input
              v-model="password"
              label="New Password"
              :type="isPwd ? 'password' : 'text'"
              outlined
              :rules="[
                (val) => !!val || 'Password is required',
                (val) => val.length >= 8 || 'Password must be at least 8 characters'
              ]"
            >
              <template v-slot:prepend>
                <q-icon name="lock" />
              </template>
              <template v-slot:append>
                <q-icon
                  :name="isPwd ? 'visibility_off' : 'visibility'"
                  class="cursor-pointer"
                  @click="isPwd = !isPwd"
                />
              </template>
            </q-input>

            <q-input
              v-model="confirmPassword"
              label="Confirm Password"
              :type="isConfirmPwd ? 'password' : 'text'"
              outlined
              :rules="[
                (val) => !!val || 'Please confirm your password',
                (val) => val === password || 'Passwords do not match'
              ]"
            >
              <template v-slot:prepend>
                <q-icon name="lock" />
              </template>
              <template v-slot:append>
                <q-icon
                  :name="isConfirmPwd ? 'visibility_off' : 'visibility'"
                  class="cursor-pointer"
                  @click="isConfirmPwd = !isConfirmPwd"
                />
              </template>
            </q-input>

            <div class="q-mt-lg">
              <q-btn
                type="submit"
                color="primary"
                label="Change Password"
                class="full-width"
                :loading="loading"
              />
            </div>

            <div class="text-center q-mt-md">
              <q-btn
                flat
                color="primary"
                label="Back to Sign In"
                to="/sign-in"
                :disable="loading"
              />
            </div>
          </q-form>
        </div>
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { supabase } from '../lib/supabase'
import { useNotificationStore } from '../stores/notifications'

const router = useRouter()
const route = useRoute()
const notificationStore = useNotificationStore()
const password = ref('')
const confirmPassword = ref('')
const isPwd = ref(true)
const isConfirmPwd = ref(true)
const loading = ref(false)
const passwordChanged = ref(false)
const hasValidToken = ref(false)

// Check if we have a valid token in the URL
onMounted(async () => {
  try {
    // Log the URL and query parameters for debugging
    console.log('Current URL:', window.location.href)
    console.log('Route query params:', route.query)
    console.log('Route hash:', route.hash)

    // First check for Supabase auth parameters in hash or query params
    const hasSupabaseAuthParams = (route.hash && route.hash.includes('access_token')) ||
                      (route.query.token && route.query.type === 'recovery' && !route.query.email)

    // Then check for our custom token approach
    const hasCustomToken = route.query.token && route.query.email && route.query.type === 'recovery'

    if (hasSupabaseAuthParams) {
      console.log('Supabase auth parameters found in URL')

      // Check if we have a valid session
      const { data: { session } } = await supabase.auth.getSession()
      console.log('Current session:', session)

      if (session) {
        // We already have a valid session
        console.log('Active session found, user can reset password')
        hasValidToken.value = true
        return
      }

      console.log('No active session found, attempting to set session from URL')

      // Check if we have hash parameters (SPA redirect)
      if (route.hash && route.hash.includes('access_token')) {
        // Try to set the session from the URL hash
        const hashParams = new URLSearchParams(route.hash.substring(1))
        const accessToken = hashParams.get('access_token')
        const refreshToken = hashParams.get('refresh_token')

        if (accessToken) {
          console.log('Found access token in URL hash, setting session')
          const { data, error } = await supabase.auth.setSession({
            access_token: accessToken,
            refresh_token: refreshToken || ''
          })

          console.log('Set session result:', { data, error })

          if (error) {
            console.error('Error setting session:', error)
            notificationStore.error('Error validating your reset link. Please request a new one.')
          } else {
            hasValidToken.value = true
          }
        }
      }
      // Check if we have query parameters (email link)
      else if (route.query.token && route.query.type === 'recovery' && !route.query.email) {
        console.log('Found Supabase recovery token in URL query params')
        const token = route.query.token as string

        // For email links, we need to verify the token
        try {
          // Use the token to verify the user
          const { data, error } = await supabase.auth.verifyOtp({
            token_hash: token,
            type: 'recovery'
          })

          console.log('Verify OTP result:', { data, error })

          if (error) {
            console.error('Error verifying recovery token:', error)
            notificationStore.error('Error validating your reset link. Please request a new one.')
          } else {
            hasValidToken.value = true
          }
        } catch (err) {
          console.error('Exception verifying recovery token:', err)
          notificationStore.error('Error processing your reset link. Please request a new one.')
        }
      }
    }
    // Handle our custom token approach
    else if (hasCustomToken) {
      console.log('Custom token parameters found in URL')
      const token = route.query.token as string
      const email = route.query.email as string

      // Validate the token (simple validation for now)
      try {
        // Decode the token
        const decodedToken = atob(token)
        const [tokenEmail, timestampStr] = decodedToken.split(':')
        const timestamp = parseInt(timestampStr)

        // Check if token is valid (email matches and not expired)
        const isEmailMatch = tokenEmail === email
        const isNotExpired = Date.now() - timestamp < 24 * 60 * 60 * 1000 // 24 hours

        console.log('Token validation:', { isEmailMatch, isNotExpired, tokenAge: (Date.now() - timestamp) / 1000 / 60 + ' minutes' })

        if (isEmailMatch && isNotExpired) {
          // Try to sign in the user with magic link
          try {
            // Try to get a session for this user
            const { data, error } = await supabase.auth.signInWithOtp({
              email: email,
              options: {
                shouldCreateUser: false
              }
            })

            console.log('Sign in with OTP result:', { data, error })

            if (error) {
              console.warn('Could not sign in with OTP')
              notificationStore.error('Could not authenticate your account. Please request a new password reset link.')
              setTimeout(() => {
                router.push('/password-reset')
              }, 3000)
            } else {
              hasValidToken.value = true
            }
          } catch (signInErr) {
            console.error('Error signing in with OTP:', signInErr)
            notificationStore.error('An error occurred while processing your password reset. Please try again.')
            setTimeout(() => {
              router.push('/password-reset')
            }, 3000)
          }
        } else {
          if (!isEmailMatch) {
            console.error('Token email does not match URL email')
          }
          if (!isNotExpired) {
            console.error('Token has expired')
          }
          notificationStore.error('Invalid or expired password reset link. Please request a new one.')
          setTimeout(() => {
            router.push('/password-reset')
          }, 3000)
        }
      } catch (tokenErr) {
        console.error('Error decoding or validating token:', tokenErr)
        notificationStore.error('Invalid password reset link. Please request a new one.')
        setTimeout(() => {
          router.push('/password-reset')
        }, 3000)
      }
    } else {
      console.log('No auth parameters found in URL')
      notificationStore.warning('Invalid or expired password reset link. Please request a new one.')
      // Redirect to password reset page after a short delay
      setTimeout(() => {
        router.push('/password-reset')
      }, 3000)
    }
  } catch (error) {
    console.error('Error processing reset password confirmation:', error)
    notificationStore.error('An error occurred while processing your password reset. Please try again.')
    // Redirect to password reset page after a short delay
    setTimeout(() => {
      router.push('/password-reset')
    }, 3000)
  }
})

const handlePasswordChange = async () => {
  try {
    loading.value = true

    // Basic validation
    if (!password.value) {
      throw new Error('Password is required')
    }

    if (password.value.length < 8) {
      throw new Error('Password must be at least 8 characters')
    }

    if (password.value !== confirmPassword.value) {
      throw new Error('Passwords do not match')
    }

    // Log the current session state
    const { data: sessionData } = await supabase.auth.getSession()
    console.log('Current session before password update:', sessionData)

    // Check if we have a session
    if (sessionData.session) {
      // We have a session, use the standard approach
      console.log('Active session found, updating password')

      // Update the password using Supabase
      const { data, error } = await supabase.auth.updateUser({
        password: password.value
      })

      console.log('Password update response:', { data, error })

      if (error) {
        console.error('Error updating password:', error)
        throw error
      }

      // Show success message
      passwordChanged.value = true
      notificationStore.success('Password changed successfully')
    } else {
      // No session, check if we have an email from our custom approach
      const resetEmail = localStorage.getItem('passwordResetEmail')

      if (!resetEmail) {
        console.error('No active session or reset email found, cannot update password')
        notificationStore.error('Your session has expired. Please request a new password reset link.')

        // Redirect to password reset page after a short delay
        setTimeout(() => {
          router.push('/password-reset')
        }, 3000)
        return
      }

      console.log('Using custom approach with email:', resetEmail)

      // Try to sign in with the email and new password
      try {
        // First, try to sign in with OTP
        const { data: otpData, error: otpError } = await supabase.auth.signInWithOtp({
          email: resetEmail,
          options: {
            shouldCreateUser: false
          }
        })

        console.log('OTP sign-in result:', { otpData, otpError })

        if (otpError) {
          console.warn('OTP sign-in failed')
          throw new Error('Could not authenticate your account. Please request a new password reset link.')
        } else {
          // OTP sign-in worked, now we can update the password
          const { data: updateData, error: updateError } = await supabase.auth.updateUser({
            password: password.value
          })

          console.log('Password update response:', { updateData, updateError })

          if (updateError) {
            console.error('Error updating password:', updateError)
            throw updateError
          }

          // Show success message
          passwordChanged.value = true
          notificationStore.success('Password changed successfully')
        }
      } catch (signInError) {
        console.error('Error during sign-in for password reset:', signInError)
        throw new Error('Could not authenticate your account. Please request a new password reset link.')
      }
    }

    // Clear any URL parameters to prevent reuse of the reset link
    if (window.history && window.history.replaceState) {
      window.history.replaceState({}, document.title, '/reset-password-confirm')
    }

    // Clear the stored email
    localStorage.removeItem('passwordResetEmail')

  } catch (error: any) {
    console.error('Password change error:', error)
    notificationStore.error(error.message || 'Failed to change password. Please try again.')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.password-reset-card {
  width: 100%;
  max-width: 400px;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

@media (max-width: 599px) {
  .password-reset-card {
    width: 90%;
  }
}
</style>
