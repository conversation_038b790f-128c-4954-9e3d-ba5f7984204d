// Academic Institution Profile Questions
import { ProfileType } from '../types';
import { commonOptions } from '../common';
import { bioSection, socialMediaSection, contactSection, locationSection, goalsSection } from '../sections/index';

// Academic Institution profile questions
export const academicInstitutionProfile: ProfileType = {
  type: 'academic_institution',
  displayName: 'Academic Institution',
  sections: [
    // Bio (common across all profiles)
    bioSection,
    {
      title: 'Institution Information',
      icon: 'school',
      description: 'Tell us about your institution',
      questions: [
        {
          id: 'institution_name',
          name: 'institution_name',
          label: 'Institution Name',
          type: 'text',
          required: true,
          fullWidth: true,
          hint: 'Full name of your academic institution'
        },
        {
          id: 'institution_type',
          name: 'institution_type',
          label: 'Institution Type',
          type: 'select',
          required: true,
          options: 'institutionTypeOptions',
          hint: 'Type of academic institution'
        },
        {
          id: 'your_position',
          name: 'your_position',
          label: 'Your Position in the Institution',
          type: 'select',
          required: true,
          options: 'academicPositionOptions',
          hint: 'Your role or position in the academic institution'
        },
        {
          id: 'founding_year',
          name: 'founding_year',
          label: 'Year Founded',
          type: 'number',
          hint: 'Year the institution was established'
        },
        {
          id: 'institution_description',
          name: 'institution_description',
          label: 'Institution Description',
          type: 'text',
          required: true,
          fullWidth: true,
          hint: 'Brief description of your institution'
        },
        {
          id: 'accreditation',
          name: 'accreditation',
          label: 'Accreditation',
          type: 'text',
          fullWidth: true,
          hint: 'List your institution\'s accreditations'
        }
      ]
    },
    {
      title: 'Academic Programs',
      icon: 'menu_book',
      description: 'Tell us about your academic programs',
      questions: [
        {
          id: 'academic_programs',
          name: 'academic_programs',
          label: 'Academic Programs',
          type: 'multi-select',
          options: 'academicProgramsOptions',
          fullWidth: true,
          required: true,
          hint: 'Select the types of academic programs offered by your institution'
        },
        {
          id: 'student_population',
          name: 'student_population',
          label: 'Student Population',
          type: 'number',
          hint: 'Total number of students enrolled'
        },
        {
          id: 'faculty_count',
          name: 'faculty_count',
          label: 'Faculty Count',
          type: 'number',
          hint: 'Total number of faculty members'
        }
      ]
    },
    {
      title: 'Research & Innovation',
      icon: 'science',
      description: 'Tell us about your research activities',
      questions: [
        {
          id: 'research_areas',
          name: 'research_areas',
          label: 'Research Areas *',
          type: 'multi-select',
          options: 'extendedResearchAreasOptions',
          fullWidth: true,
          required: true,
          hint: 'Please select at least one research area'
        },
        {
          id: 'other_research_areas',
          name: 'other_research_areas',
          label: 'Other Research Areas',
          type: 'multi-select-free',
          fullWidth: true,
          hint: 'List any additional research areas not covered above'
        },
        {
          id: 'research_centers',
          name: 'research_centers',
          label: 'Research Centers/Labs',
          type: 'multi-select-free',
          fullWidth: true,
          hint: 'List any specialized research centers or laboratories'
        },
        {
          id: 'research_achievements',
          name: 'research_achievements',
          label: 'Notable Research Achievements',
          type: 'textarea',
          fullWidth: true,
          hint: 'Describe notable research achievements or breakthroughs'
        },
        {
          id: 'innovation_programs',
          name: 'innovation_programs',
          label: 'Innovation Programs',
          type: 'textarea',
          fullWidth: true,
          hint: 'Describe any innovation programs, incubators, or accelerators at your institution'
        },
        {
          id: 'has_tto',
          name: 'has_tto',
          label: 'Has Technology Transfer Office',
          type: 'boolean',
          hint: 'Does your institution have a technology transfer office?'
        },
        {
          id: 'research_funding',
          name: 'research_funding',
          label: 'Research Funding',
          type: 'text',
          fullWidth: true,
          hint: 'Describe research funding sources or amounts'
        }
      ]
    },
    {
      title: 'Industry Collaboration',
      icon: 'handshake',
      description: 'Tell us about your industry collaborations',
      questions: [
        {
          id: 'industry_partnerships',
          name: 'industry_partnerships',
          label: 'Industry Partnerships',
          type: 'multi-select-free',
          fullWidth: true,
          hint: 'List key industry partnerships or collaborations'
        },
        {
          id: 'collaboration_types',
          name: 'collaboration_types',
          label: 'Collaboration Types',
          type: 'multi-select',
          options: 'collaborationTypeOptions',
          fullWidth: true,
          hint: 'Select the types of collaborations your institution engages in'
        },
        {
          id: 'collaboration_interests',
          name: 'collaboration_interests',
          label: 'Collaboration Interests',
          type: 'text',
          fullWidth: true,
          hint: 'Describe the types of collaborations your institution is interested in'
        },
        {
          id: 'internship_programs',
          name: 'internship_programs',
          label: 'Internship Programs',
          type: 'text',
          fullWidth: true,
          hint: 'Describe any internship or placement programs'
        },
        {
          id: 'industry_events',
          name: 'industry_events',
          label: 'Industry Events',
          type: 'text',
          fullWidth: true,
          hint: 'Describe industry events hosted by your institution'
        }
      ]
    },
    {
      title: 'Contact Information',
      icon: 'contact_mail',
      description: 'Tell us how to contact your institution',
      questions: [
        {
          id: 'contact_name',
          name: 'contact_name',
          label: 'Primary Contact Name',
          type: 'text',
          required: true,
          hint: 'Name of the primary contact person'
        },
        {
          id: 'contact_position',
          name: 'contact_position',
          label: 'Contact Position',
          type: 'select',
          required: true,
          options: 'organizationPositionOptions',
          hint: 'Position of the primary contact person in the institution'
        },
        {
          id: 'contact_function',
          name: 'contact_function',
          label: 'Contact Function',
          type: 'text',
          hint: 'Specific function or role of the contact person in the institution'
        },
        {
          id: 'contact_email',
          name: 'contact_email',
          label: 'Contact Email',
          type: 'text',
          required: true,
          hint: 'Email address for inquiries'
        },
        {
          id: 'contact_phone',
          name: 'contact_phone',
          label: 'Contact Phone',
          type: 'text',
          hint: 'Phone number for inquiries'
        },
        {
          id: 'general_email',
          name: 'general_email',
          label: 'General Email',
          type: 'text',
          hint: 'General email address for the institution'
        },
        {
          id: 'general_phone',
          name: 'general_phone',
          label: 'General Phone',
          type: 'text',
          hint: 'General phone number for the institution'
        }
      ]
    },
    {
      title: 'Location Information',
      icon: 'location_on',
      description: 'Tell us where your institution is located',
      questions: [
        {
          id: 'country',
          name: 'country',
          label: 'Country',
          type: 'select',
          required: true,
          options: 'countryOptions',
          hint: 'Country where the institution is located'
        },
        {
          id: 'city',
          name: 'city',
          label: 'City',
          type: 'text',
          required: true,
          hint: 'City where the institution is located'
        },
        {
          id: 'address',
          name: 'address',
          label: 'Address',
          type: 'text',
          fullWidth: true,
          required: true,
          hint: 'Physical address of the institution'
        },
        {
          id: 'has_multiple_campuses',
          name: 'has_multiple_campuses',
          label: 'Has Multiple Campuses',
          type: 'boolean',
          hint: 'Does your institution have multiple campuses?'
        },
        {
          id: 'number_of_campuses',
          name: 'number_of_campuses',
          label: 'Number of Campuses',
          type: 'select',
          options: 'branchesCountOptions',
          condition: {
            field: 'has_multiple_campuses',
            value: true
          },
          hint: 'How many campuses does your institution have?'
        }
      ]
    },
    {
      title: 'References',
      icon: 'people',
      description: 'Provide references who can verify your institution',
      questions: [
        {
          id: 'reference1_name',
          name: 'reference1_name',
          label: 'Reference 1: Name',
          type: 'text',
          required: true,
          hint: 'Name of the first reference person'
        },
        {
          id: 'reference1_position',
          name: 'reference1_position',
          label: 'Reference 1: Position',
          type: 'text',
          required: true,
          hint: 'Position of the first reference person'
        },
        {
          id: 'reference1_email',
          name: 'reference1_email',
          label: 'Reference 1: Email',
          type: 'text',
          required: true,
          hint: 'Email address of the first reference person'
        },
        {
          id: 'reference1_phone',
          name: 'reference1_phone',
          label: 'Reference 1: Phone',
          type: 'text',
          hint: 'Phone number of the first reference person'
        },
        {
          id: 'reference2_name',
          name: 'reference2_name',
          label: 'Reference 2: Name',
          type: 'text',
          required: true,
          hint: 'Name of the second reference person'
        },
        {
          id: 'reference2_position',
          name: 'reference2_position',
          label: 'Reference 2: Position',
          type: 'text',
          required: true,
          hint: 'Position of the second reference person'
        },
        {
          id: 'reference2_email',
          name: 'reference2_email',
          label: 'Reference 2: Email',
          type: 'text',
          required: true,
          hint: 'Email address of the second reference person'
        },
        {
          id: 'reference2_phone',
          name: 'reference2_phone',
          label: 'Reference 2: Phone',
          type: 'text',
          hint: 'Phone number of the second reference person'
        }
      ]
    },
    {
      title: 'Online Presence',
      icon: 'public',
      description: 'Tell us about your online presence',
      questions: [
        {
          id: 'website',
          name: 'website',
          label: 'Website',
          type: 'text',
          required: true,
          fullWidth: true,
          hint: 'Official website of the institution'
        },
        {
          id: 'linkedin',
          name: 'linkedin',
          label: 'LinkedIn',
          type: 'text',
          hint: 'LinkedIn page of the institution'
        },
        {
          id: 'twitter',
          name: 'twitter',
          label: 'Twitter/X',
          type: 'text',
          hint: 'Twitter/X profile of the institution'
        },
        {
          id: 'facebook',
          name: 'facebook',
          label: 'Facebook',
          type: 'text',
          hint: 'Facebook page of the institution'
        },
        {
          id: 'youtube',
          name: 'youtube',
          label: 'YouTube',
          type: 'text',
          hint: 'YouTube channel of the institution'
        },
        {
          id: 'instagram',
          name: 'instagram',
          label: 'Instagram',
          type: 'text',
          hint: 'Instagram profile of the institution'
        },
        {
          id: 'other_social',
          name: 'other_social',
          label: 'Other Social Media',
          type: 'text',
          fullWidth: true,
          hint: 'Other social media profiles'
        }
      ]
    }
  ],
  options: {
    ...commonOptions,
    institutionTypeOptions: [
      'University', 'College', 'Technical Institute',
      'Community College', 'Vocational School', 'Research Institute',
      'Professional School', 'Online Institution', 'Other'
    ],
    collaborationTypeOptions: [
      'Research Partnerships', 'Industry Projects', 'Student Internships',
      'Faculty Exchange', 'Technology Transfer', 'Curriculum Development',
      'Joint Programs', 'Sponsored Research', 'Consulting Services',
      'Continuing Education', 'Executive Education', 'Other'
    ],
    academicProgramsOptions: [
      'Undergraduate Degrees', 'Graduate Degrees', 'Doctoral Programs',
      'Professional Certificates', 'Diploma Programs', 'Short Courses',
      'Online Learning', 'Distance Education', 'Executive Education',
      'Continuing Education', 'Vocational Training', 'Technical Training',
      'Research Programs', 'International Exchange Programs', 'Other'
    ],
    extendedResearchAreasOptions: [
      // STEM Fields
      'Computer Science & IT', 'Artificial Intelligence', 'Machine Learning',
      'Data Science & Analytics', 'Cybersecurity', 'Robotics & Automation',
      'Software Engineering', 'Electrical Engineering', 'Mechanical Engineering',
      'Civil Engineering', 'Chemical Engineering', 'Aerospace Engineering',
      'Materials Science', 'Nanotechnology', 'Mathematics & Statistics',
      'Physics', 'Chemistry', 'Biology', 'Biochemistry',

      // Health & Medicine
      'Medicine', 'Public Health', 'Epidemiology', 'Pharmacology',
      'Nursing', 'Dentistry', 'Veterinary Science', 'Neuroscience',
      'Genetics & Genomics', 'Biotechnology', 'Medical Devices',
      'Healthcare Management', 'Telemedicine', 'Mental Health',

      // Environment & Agriculture
      'Environmental Science', 'Climate Change', 'Renewable Energy',
      'Sustainable Development', 'Conservation', 'Agriculture',
      'Food Science', 'Water Resources', 'Forestry', 'Marine Science',
      'Ecology', 'Biodiversity', 'Waste Management',

      // Business & Economics
      'Business Administration', 'Economics', 'Finance', 'Marketing',
      'Management', 'Entrepreneurship', 'International Trade',
      'Supply Chain Management', 'Human Resources', 'Accounting',
      'Business Analytics', 'E-commerce', 'Digital Transformation',

      // Social Sciences & Humanities
      'Psychology', 'Sociology', 'Anthropology', 'Political Science',
      'International Relations', 'Law', 'Education', 'History',
      'Philosophy', 'Languages & Linguistics', 'Literature',
      'Media Studies', 'Communication', 'Journalism',
      'Cultural Studies', 'Gender Studies', 'Religious Studies',

      // Arts & Design
      'Fine Arts', 'Design', 'Architecture', 'Music',
      'Theater & Performance', 'Film & Television', 'Digital Media',
      'Game Design', 'Fashion Design', 'Industrial Design',

      // Interdisciplinary Fields
      'Urban Studies & Planning', 'Transportation', 'Tourism & Hospitality',
      'Sports Science', 'Gerontology', 'Disaster Management',
      'Peace & Conflict Studies', 'Development Studies',
      'Innovation & Technology Management', 'Science Policy',
      'Digital Humanities', 'Cognitive Science',

      'Other'
    ],
    organizationPositionOptions: commonOptions.organizationPositionOptions,
    branchesCountOptions: [
      '1 (Single location)', '2-5', '6-10', '11-20',
      '21-50', '51-100', 'More than 100'
    ]
  }
};
