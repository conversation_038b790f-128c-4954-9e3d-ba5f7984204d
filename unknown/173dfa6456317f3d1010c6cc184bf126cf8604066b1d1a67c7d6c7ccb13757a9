<template>
  <q-page class="flex flex-center bg-grey-2">
    <q-card class="password-reset-card q-pa-lg">
      <q-card-section class="text-center">
        <div class="text-h4 q-mb-md">Set New Password</div>
        <div class="text-subtitle1 text-grey-7">
          Please enter your new password
        </div>
      </q-card-section>

      <q-card-section>
        <div v-if="passwordChanged" class="text-center q-pa-md">
          <q-icon name="check_circle" color="positive" size="48px" />
          <p class="text-body1 q-mb-md">
            Your password has been successfully changed. You can now sign in with your new password.
          </p>
          <q-btn
            flat
            color="primary"
            label="Go to Sign In"
            class="q-mt-md"
            to="/sign-in"
          />
        </div>

        <div v-else-if="!hasValidToken" class="text-center q-pa-md">
          <q-icon name="error" color="warning" size="48px" />
          <p class="text-body1 q-mb-md">
            Invalid or expired password reset link. Please request a new one.
          </p>
          <q-btn
            flat
            color="primary"
            label="Request New Link"
            class="q-mt-md"
            to="/password-reset"
          />
        </div>

        <div v-else>
          <q-form @submit="handlePasswordChange" class="q-gutter-md">
            <q-input
              v-model="password"
              label="New Password"
              :type="isPwd ? 'password' : 'text'"
              outlined
              :rules="[
                (val) => !!val || 'Password is required',
                (val) => val.length >= 8 || 'Password must be at least 8 characters'
              ]"
            >
              <template v-slot:prepend>
                <q-icon name="lock" />
              </template>
              <template v-slot:append>
                <q-icon
                  :name="isPwd ? 'visibility_off' : 'visibility'"
                  class="cursor-pointer"
                  @click="isPwd = !isPwd"
                />
              </template>
            </q-input>

            <q-input
              v-model="confirmPassword"
              label="Confirm Password"
              :type="isConfirmPwd ? 'password' : 'text'"
              outlined
              :rules="[
                (val) => !!val || 'Please confirm your password',
                (val) => val === password || 'Passwords do not match'
              ]"
            >
              <template v-slot:prepend>
                <q-icon name="lock" />
              </template>
              <template v-slot:append>
                <q-icon
                  :name="isConfirmPwd ? 'visibility_off' : 'visibility'"
                  class="cursor-pointer"
                  @click="isConfirmPwd = !isConfirmPwd"
                />
              </template>
            </q-input>

            <div class="q-mt-lg">
              <q-btn
                type="submit"
                color="primary"
                label="Change Password"
                class="full-width"
                :loading="loading"
              />
            </div>

            <div class="text-center q-mt-md">
              <q-btn
                flat
                color="primary"
                label="Back to Sign In"
                to="/sign-in"
                :disable="loading"
              />
            </div>
          </q-form>
        </div>
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { supabase } from '../../../lib/supabase'
import { useNotificationStore } from '../../../stores/notifications'

const router = useRouter()
const route = useRoute()
const notificationStore = useNotificationStore()
const password = ref('')
const confirmPassword = ref('')
const isPwd = ref(true)
const isConfirmPwd = ref(true)
const loading = ref(false)
const passwordChanged = ref(false)
const hasValidToken = ref(false)

// Check if we have a valid token in the URL
onMounted(async () => {
  try {
    // Log the URL and query parameters for debugging
    console.log('Current URL:', window.location.href)
    console.log('Route query params:', route.query)
    console.log('Route hash:', route.hash)

    // Check for Supabase auth parameters - could be in hash or query params
    const hasAuthParams = (route.hash && route.hash.includes('access_token')) ||
                        (route.query.token && route.query.type === 'recovery') ||
                        (route.query.email && route.query.timestamp)

    if (hasAuthParams) {
      console.log('Auth parameters found in URL')

      // Check if we have a valid session
      const { data: { session } } = await supabase.auth.getSession()
      console.log('Current session:', session)

      if (session) {
        // We already have a valid session
        console.log('Active session found, user can reset password')
        hasValidToken.value = true
        return
      }

      console.log('No active session found, attempting to set session from URL')

      // Check if we have hash parameters (SPA redirect)
      if (route.hash && route.hash.includes('access_token')) {
        // Try to set the session from the URL hash
        const hashParams = new URLSearchParams(route.hash.substring(1))
        const accessToken = hashParams.get('access_token')
        const refreshToken = hashParams.get('refresh_token')

        if (accessToken) {
          console.log('Found access token in URL hash, setting session')
          const { data, error } = await supabase.auth.setSession({
            access_token: accessToken,
            refresh_token: refreshToken || ''
          })

          console.log('Set session result:', { data, error })

          if (error) {
            console.error('Error setting session:', error)
            notificationStore.error('Error validating your reset link. Please request a new one.')
          } else {
            hasValidToken.value = true
          }
        }
      }
      // Check if we have query parameters (email link)
      else if (route.query.token && route.query.type === 'recovery') {
        console.log('Found recovery token in URL query params')
        const token = route.query.token as string

        // For email links, we need to verify the token
        try {
          // Use the token to verify the user
          const { data, error } = await supabase.auth.verifyOtp({
            token_hash: token,
            type: 'recovery'
          })

          console.log('Verify OTP result:', { data, error })

          if (error) {
            console.error('Error verifying recovery token:', error)
            notificationStore.error('Error validating your reset link. Please request a new one.')
          } else {
            hasValidToken.value = true
          }
        } catch (err) {
          console.error('Exception verifying recovery token:', err)
          notificationStore.error('Error processing your reset link. Please request a new one.')
        }
      }
      // Check if we have our custom reset link parameters
      else if (route.query.email && route.query.timestamp) {
        console.log('Found custom reset link parameters')
        const email = route.query.email as string
        const timestamp = parseInt(route.query.timestamp as string, 10)
        const currentTime = Date.now()

        // Check if the link is expired (24 hours)
        const isExpired = currentTime - timestamp > 24 * 60 * 60 * 1000

        if (isExpired) {
          console.error('Reset link has expired')
          notificationStore.error('Your password reset link has expired. Please request a new one.')
        } else {
          // For our custom reset link, we need to sign in the user with a magic link
          try {
            const { data, error } = await supabase.auth.signInWithOtp({
              email: email
            })

            console.log('Sign in with OTP result:', { data, error })

            if (error) {
              console.error('Error signing in with OTP:', error)
              notificationStore.error('Error validating your reset link. Please request a new one.')
            } else {
              hasValidToken.value = true
              // Store the email for later use
              localStorage.setItem('resetPasswordEmail', email)
            }
          } catch (err) {
            console.error('Exception signing in with OTP:', err)
            notificationStore.error('Error processing your reset link. Please request a new one.')
          }
        }
      }
    } else {
      console.log('No auth parameters found in URL')
      notificationStore.warning('Invalid or expired password reset link. Please request a new one.')
      // Redirect to password reset page after a short delay
      setTimeout(() => {
        router.push('/password-reset')
      }, 3000)
    }
  } catch (error) {
    console.error('Error processing reset password confirmation:', error)
    notificationStore.error('An error occurred while processing your password reset. Please try again.')
    // Redirect to password reset page after a short delay
    setTimeout(() => {
      router.push('/password-reset')
    }, 3000)
  }
})

const handlePasswordChange = async () => {
  try {
    loading.value = true

    // Basic validation
    if (!password.value) {
      throw new Error('Password is required')
    }

    if (password.value.length < 8) {
      throw new Error('Password must be at least 8 characters')
    }

    if (password.value !== confirmPassword.value) {
      throw new Error('Passwords do not match')
    }

    // Log the current session state
    const { data: sessionData } = await supabase.auth.getSession()
    console.log('Current session before password update:', sessionData)

    // Check if we have a session or a stored email from our custom reset link
    const resetPasswordEmail = localStorage.getItem('resetPasswordEmail')

    if (!sessionData.session && !resetPasswordEmail) {
      console.error('No active session or reset email found, cannot update password')
      notificationStore.error('Your session has expired. Please request a new password reset link.')

      // Redirect to password reset page after a short delay
      setTimeout(() => {
        router.push('/password-reset')
      }, 3000)
      return
    }

    // Update the password using Supabase
    const { data, error } = await supabase.auth.updateUser({
      password: password.value
    })

    console.log('Password update response:', { data, error })

    if (error) {
      console.error('Error updating password:', error)
      throw error
    }

    // Show success message
    passwordChanged.value = true
    notificationStore.success('Password changed successfully')

    // Clear any URL parameters to prevent reuse of the reset link
    if (window.history && window.history.replaceState) {
      window.history.replaceState({}, document.title, '/reset-password-confirm')
    }

    // Clear any stored email
    localStorage.removeItem('resetPasswordEmail')
  } catch (error: any) {
    console.error('Password change error:', error)
    notificationStore.error(error.message || 'Failed to change password. Please try again.')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.password-reset-card {
  width: 100%;
  max-width: 400px;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

@media (max-width: 599px) {
  .password-reset-card {
    width: 90%;
  }
}
</style>
