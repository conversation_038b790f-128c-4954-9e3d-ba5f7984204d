<template>
  <q-page class="q-pa-md">
    <div class="row q-col-gutter-md">
      <div class="col-12">
        <q-card class="content-header-card bg-primary text-white">
          <q-card-section>
            <div class="text-h4">Events</div>
            <p class="text-body1 q-mt-md">
              Discover upcoming events, workshops, and networking opportunities.
            </p>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-12">
        <!-- Events component would go here -->
        <q-card>
          <q-card-section>
            <div class="text-h6">Upcoming Events</div>
          </q-card-section>

          <q-separator />

          <q-list>
            <q-item v-for="(event, index) in events" :key="index" clickable>
              <q-item-section avatar>
                <q-avatar color="primary" text-color="white">
                  <unified-icon name="event" />
                </q-avatar>
              </q-item-section>

              <q-item-section>
                <q-item-label>{{ event.title }}</q-item-label>
                <q-item-label caption>{{ event.date }} • {{ event.location }}</q-item-label>
              </q-item-section>

              <q-item-section side>
                <q-btn flat round color="primary" icon="arrow_forward" />
              </q-item-section>
            </q-item>
          </q-list>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { useAuthStore } from '../../stores/auth';
import UnifiedIcon from '../../components/ui/UnifiedIcon.vue';

const router = useRouter();
const $q = useQuasar();
const authStore = useAuthStore();

// Sample events data
const events = ref([
  {
    title: 'Innovation Workshop',
    date: 'July 15, 2025',
    location: 'Virtual',
    description: 'Learn about the latest innovation techniques and methodologies.'
  },
  {
    title: 'Networking Mixer',
    date: 'August 22, 2025',
    location: 'ZbInnovation',
    description: 'Connect with other entrepreneurs and innovators in your area.'
  },
  {
    title: 'Funding Pitch Day',
    date: 'September 5, 2025',
    location: 'ZbInnovation',
    description: 'Present your ideas to potential investors and funding partners.'
  }
]);

// Check if user is authenticated
onMounted(() => {
  if (!authStore.isAuthenticated) {
    router.push('/login');
    $q.notify({
      color: 'negative',
      message: 'You must be logged in to view this page',
      icon: 'error'
    });
  }
});
</script>

<style scoped>
.content-header-card {
  border-radius: 8px;
  overflow: hidden;
}
</style>
