/* Material Icons Font */
@font-face {
  font-family: 'Material Icons';
  font-style: normal;
  font-weight: 400;
  src: url(https://fonts.gstatic.com/s/materialicons/v140/flUhRq6tzZclQEJ-Vdg-IuiaDsNcIhQ8tQ.woff2) format('woff2');
  font-display: block;
}

.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

/* Fix for icon in q-btn */
.q-btn__content .q-icon {
  font-size: 1.4em;
}

/* Fix for icon text fallback */
.q-icon.material-icons {
  font-family: 'Material Icons', sans-serif !important;
}

/* Direct fix for icon display in buttons */
.q-btn[icon]:before {
  font-family: 'Material Icons', sans-serif !important;
  content: attr(icon);
  display: inline-block;
  margin-right: 0.5em;
}

/* Fix for q-icon with name attribute */
.q-icon:not(.material-icons)[name]:before {
  font-family: 'Material Icons', sans-serif !important;
  content: attr(name);
}

/* Fix for material icons in q-btn */
.q-btn .material-icons {
  font-size: 1.4em;
  margin-right: 0.25em;
}

/* Direct fix for icon display in buttons */
.q-btn[icon]:before {
  font-family: 'Material Icons', sans-serif !important;
  content: attr(icon);
  display: inline-block;
  margin-right: 0.5em;
}

/* Fix for q-icon with name attribute */
.q-icon:not(.material-icons)[name]:before {
  font-family: 'Material Icons', sans-serif !important;
  content: attr(name);
}
