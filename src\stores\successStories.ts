import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { supabase } from '../lib/supabase';
import { usePostsStore } from './posts';
import { useNotificationStore } from './notifications';
import { useActivityTrackingService } from '../services/activityTrackingService';

export interface SuccessStoryStat {
  value: string;
  label: string;
}

export interface SuccessStory {
  id?: number;
  title: string;
  description: string;
  image?: string;
  category?: string;
  badgeColor?: string;
  authorName: string;
  authorRole?: string;
  authorAvatar?: string;
  stats?: SuccessStoryStat[];
  created_at?: string;
  updated_at?: string;
  status?: 'pending' | 'approved' | 'rejected';
}

export interface SuccessStorySubmission {
  name: string;
  email: string;
  company: string;
  title: string;
  description: string;
}

export const useSuccessStoriesStore = defineStore('successStories', () => {
  // Use the stores and services
  const postsStore = usePostsStore();
  const notifications = useNotificationStore();
  const activityService = useActivityTrackingService();

  // Local state
  const error = ref<string | null>(null);
  const loading = ref(false);

  // Helper function to determine badge color based on category
  function getBadgeColor(category?: string): string {
    if (!category) return 'primary';

    switch (category.toLowerCase()) {
      case 'startup':
        return 'green';
      case 'research':
        return 'blue';
      case 'growth':
        return 'orange';
      default:
        return 'primary';
    }
  }

  // Helper function to parse stats
  function parseStats(statsData: any): SuccessStoryStat[] {
    if (!statsData) return [];

    if (typeof statsData === 'string') {
      try {
        return JSON.parse(statsData);
      } catch (e) {
        console.error('Error parsing stats:', e);
        return [];
      }
    }

    return statsData;
  }

  // Map posts to success stories
  const stories = computed(() => {
    return postsStore.successStories.map(post => {
      return {
        id: post.id,
        title: post.title || '',
        description: post.content || '',
        image: post.featuredImage || '',
        category: post.subType || 'Success Story',
        badgeColor: getBadgeColor(post.subType),
        authorName: post.author || 'Anonymous',
        authorRole: post.authorRole || 'Community Member',
        authorAvatar: post.authorAvatar || '',
        stats: parseStats(post.successStats),
        created_at: post.createdAt,
        updated_at: post.updatedAt,
        status: 'approved'
      };
    });
  });

  // We now have our own loading state

  // Getters
  const approvedStories = computed(() => {
    return stories.value.filter(story => story.status === 'approved');
  });

  // Actions
  async function fetchSuccessStories(): Promise<void> {
    try {
      error.value = null;
      loading.value = true;

      // Fetch success stories from posts store
      await postsStore.fetchSuccessStories();

      // Also ensure they appear in the blog section by fetching with the right filter
      await postsStore.fetchPosts({
        postTypes: ['SUCCESS_STORY'],
        includeInBlog: true
      });
    } catch (err: any) {
      console.error('Error fetching success stories:', err);
      error.value = err.message || 'Failed to fetch success stories';
      notifications.error('Failed to load success stories: ' + error.value);
    } finally {
      loading.value = false;
    }
  }

  async function submitStory(storyData: SuccessStorySubmission): Promise<boolean> {
    try {
      error.value = null;
      loading.value = true;

      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();

      // Create a post object for the posts store
      const newPost = {
        title: storyData.title,
        content: storyData.description,
        postType: 'SUCCESS_STORY',
        subType: 'User Story',
        status: 'PENDING',
        author: storyData.name,
        authorRole: storyData.company || 'Community Member',
        authorEmail: storyData.email,
        userId: user?.id || null,
        // Add fields to make it appear in blog section
        category: 'Success Stories',
        excerpt: storyData.description.substring(0, 150) + '...',
        tags: ['success', 'story', 'community'],
        // Add achievement details for success story specific fields
        achievementType: 'User Achievement',
        achievementDetails: storyData.description
      };

      // Use the posts store to create the post
      const result = await postsStore.createPost(newPost);

      if (result) {
        // Track activity if user is authenticated
        if (user) {
          await activityService.trackActivity('success_story_submit', {
            post_id: result.id,
            title: storyData.title,
            content_preview: storyData.description.substring(0, 100)
          });
        }

        notifications.success('Your success story has been submitted for review!');
        return true;
      } else {
        throw new Error('Failed to submit success story');
      }
    } catch (err: any) {
      console.error('Error submitting success story:', err);
      error.value = err.message || 'Failed to submit success story';
      notifications.error('Failed to submit success story: ' + error.value);
      return false;
    } finally {
      loading.value = false;
    }
  }

  return {
    // State
    loading,
    error,
    stories,

    // Getters
    approvedStories,

    // Actions
    fetchSuccessStories,
    submitStory
  };
});
