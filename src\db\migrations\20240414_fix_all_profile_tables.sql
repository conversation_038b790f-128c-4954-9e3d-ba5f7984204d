-- Fix all profile tables structure
-- Add completion_percentage column if it doesn't exist
--
-- IMPORTANT: This migration is safe and will NOT reset or clear any existing data.
-- It only adds a new column if it doesn't exist and sets a default value of 0.

-- Define a function to check and fix profile tables
CREATE OR REPLACE FUNCTION fix_profile_tables() RETURNS VOID AS $$
DECLARE
    profile_types TEXT[] := ARRAY['innovator', 'investor', 'mentor', 'professional', 'industry_expert', 'academic_student', 'academic_institution', 'organisation'];
    profile_type TEXT;
    table_name TEXT;
BEGIN
    FOREACH profile_type IN ARRAY profile_types
    LOOP
        table_name := profile_type || '_profiles';

        -- Check if the table exists
        IF EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_schema = 'public'
            AND table_name = table_name
        ) THEN
            -- Add completion_percentage column if it doesn't exist
            IF NOT EXISTS (
                SELECT FROM information_schema.columns
                WHERE table_schema = 'public'
                AND table_name = table_name
                AND column_name = 'completion_percentage'
            ) THEN
                EXECUTE format('ALTER TABLE public.%I ADD COLUMN completion_percentage INTEGER DEFAULT 0', table_name);
                RAISE NOTICE 'Added completion_percentage column to %', table_name;
            ELSE
                RAISE NOTICE 'completion_percentage column already exists in %', table_name;
            END IF;
        ELSE
            RAISE NOTICE 'Table % does not exist', table_name;
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Execute the function
SELECT fix_profile_tables();

-- Drop the function after use
DROP FUNCTION IF EXISTS fix_profile_tables();

-- Refresh schema cache
SELECT pg_catalog.set_config('search_path', 'public', false);
