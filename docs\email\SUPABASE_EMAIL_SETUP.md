# Supabase Email Setup Guide

This guide explains how to set up email functionality in the ZB Innovation Hub application using Supabase Auth Webhooks and Edge Functions.

## Overview

The application uses Supabase Auth Webhooks to trigger email sending when certain events occur, such as when a new user signs up. The emails are sent using SendGrid through Supabase Edge Functions.

## Components

1. **Auth Webhook Function**: Receives webhooks from Supabase Auth and sends welcome emails to new users
2. **Email Verification Function**: Handles other types of emails like verification, password reset, etc.

## Setup Instructions

### 1. Deploy the Edge Functions

Run the deployment scripts to deploy the edge functions:

```bash
# Deploy the auth webhook function
./deploy-auth-webhook.bat

# Deploy the email verification function
./deploy-email-verification.bat
```

### 2. Configure Supabase Auth Webhook

After deploying the functions, you need to configure the webhook in the Supabase dashboard:

1. Go to your Supabase project dashboard
2. Navigate to Authentication > Webhooks
3. Click "Add a new webhook"
4. Select the "User Created" event
5. Enter the function URL: `https://<your-project-id>.functions.supabase.co/auth-webhook`
6. Save the webhook configuration

### 3. Set Environment Variables

Make sure the following environment variables are set as secrets in your Supabase project:

```
SENDGRID_API_KEY=your_sendgrid_api_key
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=ZB Innovation Hub
SITE_URL=https://zbinnovation.co.zw
```

You can set these using the Supabase CLI:

```bash
supabase secrets set SENDGRID_API_KEY=your_sendgrid_api_key --project-ref your-project-ref
supabase secrets set SENDGRID_FROM_EMAIL=<EMAIL> --project-ref your-project-ref
supabase secrets set SENDGRID_FROM_NAME="ZB Innovation Hub" --project-ref your-project-ref
supabase secrets set SITE_URL=https://zbinnovation.co.zw --project-ref your-project-ref
```

### 4. Disable Email Confirmation in Supabase Auth

Since we're handling welcome emails ourselves, you should disable the built-in email confirmation in Supabase Auth:

1. Go to your Supabase project dashboard
2. Navigate to Authentication > Providers
3. Under "Email", turn off "Confirm email"

## Testing

To test the email functionality:

1. Create a new user in your application
2. The auth webhook should trigger and send a welcome email
3. Check the logs in the Supabase dashboard to verify the function was called

## Troubleshooting

If emails are not being sent:

1. Check the function logs in the Supabase dashboard
2. Verify that the webhook is configured correctly
3. Make sure the SendGrid API key is valid and has the necessary permissions
4. Test the SendGrid integration using the test-email function
