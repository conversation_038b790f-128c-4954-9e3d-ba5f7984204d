-- Migration: Fix Profile RLS and Create Public View
-- Description: Fixes RLS policies for profiles and creates a public view for profile display

-- 1. First, drop any conflicting policies
DROP POLICY IF EXISTS "Users can view their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can view their own profiles" ON public.profiles;
DROP POLICY IF EXISTS "Users can view visible profiles" ON public.profiles;
DROP POLICY IF EXISTS "Public can view public profiles" ON public.profiles;

-- 2. Create a policy that allows viewing profiles based on visibility
CREATE POLICY "Anyone can view public profiles"
  ON public.profiles
  FOR SELECT
  USING (
    profile_visibility = 'public' OR 
    user_id = auth.uid() OR
    (profile_visibility = 'connections_only' AND 
      EXISTS (
        SELECT 1 FROM user_connections 
        WHERE (user_id = auth.uid() AND connected_user_id = profiles.user_id) OR
              (connected_user_id = auth.uid() AND user_id = profiles.user_id)
      )
    )
  );

-- 3. Create policies for specialized profile tables
DO $$
DECLARE
  profile_tables TEXT[] := ARRAY[
    'innovator_profiles', 
    'investor_profiles', 
    'mentor_profiles',
    'professional_profiles', 
    'industry_expert_profiles', 
    'academic_student_profiles',
    'academic_institution_profiles', 
    'organisation_profiles'
  ];
  t TEXT;
BEGIN
  FOREACH t IN ARRAY profile_tables
  LOOP
    -- Drop existing policies
    EXECUTE format('DROP POLICY IF EXISTS "Users can view their own %s" ON public.%s', t, t);
    EXECUTE format('DROP POLICY IF EXISTS "Public can view public %s profiles" ON public.%s', t, t);
    
    -- Create new policies
    -- 1. Owner can view their own profile
    EXECUTE format('
      CREATE POLICY "Users can view their own %s"
        ON public.%s
        FOR SELECT
        USING (
          user_id = auth.uid()
        )
    ', t, t);
    
    -- 2. Anyone can view public profiles
    EXECUTE format('
      CREATE POLICY "Anyone can view public %s"
        ON public.%s
        FOR SELECT
        USING (
          is_public = true
        )
    ', t, t);
  END LOOP;
END$$;

-- 4. Create a comprehensive public profiles view that joins personal_details with specialized profile tables
CREATE OR REPLACE VIEW public.public_profiles_view AS
SELECT
  pd.user_id,
  pd.first_name,
  pd.last_name,
  pd.email,
  pd.profile_name,
  pd.profile_type,
  pd.profile_state,
  pd.profile_visibility,
  pd.profile_completion,
  pd.bio AS base_bio,
  pd.avatar_url,
  pd.created_at,
  pd.updated_at,
  
  -- Specialized profile fields (using COALESCE to handle NULLs)
  CASE
    WHEN pd.profile_type = 'innovator' THEN ip.bio
    WHEN pd.profile_type = 'investor' THEN inv.bio
    WHEN pd.profile_type = 'mentor' THEN mp.bio
    WHEN pd.profile_type = 'professional' THEN pp.bio
    WHEN pd.profile_type = 'industry_expert' THEN iep.bio
    WHEN pd.profile_type = 'academic_student' THEN asp.bio
    WHEN pd.profile_type = 'academic_institution' THEN aip.bio
    WHEN pd.profile_type = 'organisation' THEN op.bio
    ELSE pd.bio
  END AS specialized_bio,
  
  -- Social media links
  CASE
    WHEN pd.profile_type = 'innovator' THEN ip.website
    WHEN pd.profile_type = 'investor' THEN inv.website
    WHEN pd.profile_type = 'mentor' THEN mp.website
    WHEN pd.profile_type = 'professional' THEN pp.website
    WHEN pd.profile_type = 'industry_expert' THEN iep.website
    WHEN pd.profile_type = 'academic_student' THEN asp.website
    WHEN pd.profile_type = 'academic_institution' THEN aip.website
    WHEN pd.profile_type = 'organisation' THEN op.website
    ELSE NULL
  END AS website,
  
  CASE
    WHEN pd.profile_type = 'innovator' THEN ip.linkedin
    WHEN pd.profile_type = 'investor' THEN inv.linkedin
    WHEN pd.profile_type = 'mentor' THEN mp.linkedin
    WHEN pd.profile_type = 'professional' THEN pp.linkedin
    WHEN pd.profile_type = 'industry_expert' THEN iep.linkedin
    WHEN pd.profile_type = 'academic_student' THEN asp.linkedin
    WHEN pd.profile_type = 'academic_institution' THEN aip.linkedin
    WHEN pd.profile_type = 'organisation' THEN op.linkedin
    ELSE NULL
  END AS linkedin,
  
  -- Contact information
  CASE
    WHEN pd.profile_type = 'innovator' THEN ip.contact_email
    WHEN pd.profile_type = 'investor' THEN inv.contact_email
    WHEN pd.profile_type = 'mentor' THEN mp.contact_email
    WHEN pd.profile_type = 'professional' THEN pp.contact_email
    WHEN pd.profile_type = 'industry_expert' THEN iep.contact_email
    WHEN pd.profile_type = 'academic_student' THEN asp.contact_email
    WHEN pd.profile_type = 'academic_institution' THEN aip.contact_email
    WHEN pd.profile_type = 'organisation' THEN op.contact_email
    ELSE pd.email
  END AS contact_email
  
FROM
  personal_details pd
LEFT JOIN innovator_profiles ip ON pd.user_id = ip.user_id AND pd.profile_type = 'innovator'
LEFT JOIN investor_profiles inv ON pd.user_id = inv.user_id AND pd.profile_type = 'investor'
LEFT JOIN mentor_profiles mp ON pd.user_id = mp.user_id AND pd.profile_type = 'mentor'
LEFT JOIN professional_profiles pp ON pd.user_id = pp.user_id AND pd.profile_type = 'professional'
LEFT JOIN industry_expert_profiles iep ON pd.user_id = iep.user_id AND pd.profile_type = 'industry_expert'
LEFT JOIN academic_student_profiles asp ON pd.user_id = asp.user_id AND pd.profile_type = 'academic_student'
LEFT JOIN academic_institution_profiles aip ON pd.user_id = aip.user_id AND pd.profile_type = 'academic_institution'
LEFT JOIN organisation_profiles op ON pd.user_id = op.user_id AND pd.profile_type = 'organisation'
WHERE
  -- Only include profiles that are public or have at least 50% completion
  (pd.profile_visibility = 'public' AND pd.profile_completion >= 50)
  OR
  -- Also include the current user's profile regardless of visibility
  (pd.user_id = auth.uid());

-- Grant permissions on the view
GRANT SELECT ON public.public_profiles_view TO authenticated, anon, service_role;
