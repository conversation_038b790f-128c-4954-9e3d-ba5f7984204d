import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { supabase } from '../lib/supabase'
import { useNotificationStore } from './notifications'

export interface MembershipRequest {
  id?: string
  name: string
  email: string
  phone: string
  company: string
  membership_type: string
  message: string
  status?: 'pending' | 'approved' | 'rejected'
  created_at?: string
}

export interface EventSpaceRequest {
  id?: string
  name: string
  email: string
  phone: string
  organization: string
  event_name: string
  event_type: string
  event_date: string
  attendees: number
  requirements: string
  status?: 'pending' | 'approved' | 'rejected'
  created_at?: string
}

export const useHubFacilitiesStore = defineStore('hubFacilities', () => {
  const notifications = useNotificationStore()
  
  // State
  const loading = ref(false)
  const error = ref<string | null>(null)
  const membershipRequests = ref<MembershipRequest[]>([])
  const eventSpaceRequests = ref<EventSpaceRequest[]>([])
  
  // Actions
  
  /**
   * Submit a membership request
   * 
   * @param request The membership request data
   * @returns True if the request was submitted successfully
   */
  async function submitMembershipRequest(request: MembershipRequest): Promise<boolean> {
    loading.value = true
    error.value = null
    
    try {
      const { data, error: err } = await supabase
        .from('membership_requests')
        .insert({
          name: request.name,
          email: request.email,
          phone: request.phone,
          company: request.company,
          membership_type: request.membership_type,
          message: request.message,
          status: 'pending'
        })
        .select()
        .single()
      
      if (err) {
        throw err
      }
      
      notifications.success('Membership request submitted successfully!')
      return true
    } catch (err: any) {
      console.error('Error submitting membership request:', err)
      error.value = err.message
      notifications.error('Failed to submit membership request: ' + err.message)
      return false
    } finally {
      loading.value = false
    }
  }
  
  /**
   * Submit an event space request
   * 
   * @param request The event space request data
   * @returns True if the request was submitted successfully
   */
  async function submitEventSpaceRequest(request: EventSpaceRequest): Promise<boolean> {
    loading.value = true
    error.value = null
    
    try {
      const { data, error: err } = await supabase
        .from('event_space_requests')
        .insert({
          name: request.name,
          email: request.email,
          phone: request.phone,
          organization: request.organization,
          event_name: request.event_name,
          event_type: request.event_type,
          event_date: request.event_date,
          attendees: request.attendees,
          requirements: request.requirements,
          status: 'pending'
        })
        .select()
        .single()
      
      if (err) {
        throw err
      }
      
      notifications.success('Event space request submitted successfully!')
      return true
    } catch (err: any) {
      console.error('Error submitting event space request:', err)
      error.value = err.message
      notifications.error('Failed to submit event space request: ' + err.message)
      return false
    } finally {
      loading.value = false
    }
  }
  
  /**
   * Register for an event
   * Simple implementation for event registration
   *
   * @param eventId The event identifier
   * @returns True if registration was successful
   */
  async function registerForEvent(eventId: string): Promise<boolean> {
    loading.value = true
    error.value = null

    try {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser()

      if (!user) {
        throw new Error('User must be authenticated to register for events')
      }

      // Check if user is already registered
      const { data: existingRegistration, error: checkError } = await supabase
        .from('event_registrations')
        .select('*')
        .eq('user_id', user.id)
        .eq('event_id', eventId)
        .single()

      if (checkError && checkError.code !== 'PGRST116') { // PGRST116 is "no rows returned"
        throw checkError
      }

      if (existingRegistration) {
        notifications.warning('You are already registered for this event')
        return false
      }

      // Register for the event
      const { error: insertError } = await supabase
        .from('event_registrations')
        .insert({
          user_id: user.id,
          event_id: eventId,
          registration_status: 'registered',
          registration_data: {}
        })

      if (insertError) {
        throw insertError
      }

      notifications.success('Successfully registered for event!')
      return true
    } catch (err: any) {
      console.error('Error registering for event:', err)
      error.value = err.message
      notifications.error('Failed to register for event: ' + err.message)
      return false
    } finally {
      loading.value = false
    }
  }

  return {
    // State
    loading,
    error,
    membershipRequests,
    eventSpaceRequests,

    // Actions
    submitMembershipRequest,
    submitEventSpaceRequest,
    registerForEvent
  }
})
