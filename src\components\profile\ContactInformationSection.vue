<template>
  <q-card v-if="hasContactInfo" class="q-mb-md section-card">
    <q-card-section class="section-header bg-primary text-white">
      <div class="text-h6">
        <unified-icon name="contact_mail" class="q-mr-sm" />
        Contact Information
      </div>
    </q-card-section>
    <q-card-section>
      <div class="row q-col-gutter-md">
        <!-- Website -->
        <div class="col-12 col-md-6" v-if="profile.website">
          <div class="text-subtitle2">Website</div>
          <div>
            <a :href="profile.website" target="_blank" class="text-primary">
              {{ profile.website }}
            </a>
          </div>
        </div>

        <!-- Contact Email -->
        <div class="col-12 col-md-6" v-if="profile.contact_email">
          <div class="text-subtitle2">Contact Email</div>
          <div>
            <a :href="`mailto:${profile.contact_email}`" class="text-primary">
              {{ profile.contact_email }}
            </a>
          </div>
        </div>

        <!-- Contact Phone -->
        <div class="col-12 col-md-6" v-if="profile.contact_phone">
          <div class="text-subtitle2">Contact Phone</div>
          <div>{{ profile.contact_phone }}</div>
        </div>

        <!-- WhatsApp -->
        <div class="col-12 col-md-6" v-if="profile.whatsapp">
          <div class="text-subtitle2">WhatsApp</div>
          <div>{{ profile.whatsapp }}</div>
        </div>

        <!-- Telegram -->
        <div class="col-12 col-md-6" v-if="profile.telegram">
          <div class="text-subtitle2">Telegram</div>
          <div>{{ profile.telegram }}</div>
        </div>

        <!-- Preferred Contact Method -->
        <div class="col-12 col-md-6" v-if="profile.preferred_contact_method">
          <div class="text-subtitle2">Preferred Contact Method</div>
          <div>{{ profile.preferred_contact_method }}</div>
        </div>
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import UnifiedIcon from '../ui/UnifiedIcon.vue'

// Props
const props = defineProps<{
  profile: any
}>()

// Computed
const hasContactInfo = computed(() => {
  return !!(
    props.profile.website ||
    props.profile.contact_email ||
    props.profile.contact_phone ||
    props.profile.whatsapp ||
    props.profile.telegram ||
    props.profile.preferred_contact_method
  )
})
</script>

<style scoped>
.section-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  border-radius: 8px 8px 0 0;
}
</style>
