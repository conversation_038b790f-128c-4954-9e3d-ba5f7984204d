<template>
  <q-card class="new-user-welcome">
    <q-card-section class="bg-primary text-white">
      <div class="text-h5">Welcome to ZbInnovation!</div>
    </q-card-section>

    <q-card-section class="q-pa-lg">
      <div class="row items-center">
        <div class="col-12 col-md-4 text-center q-mb-md-none q-mb-lg">
          <q-icon name="person_add" size="150px" color="grey-3" />
        </div>
        <div class="col-12 col-md-8">
          <div class="text-h6 q-mb-md">You haven't created a profile yet</div>
          <p class="text-body1 q-mb-md">
            Creating a profile is the first step to connect with other innovators, investors,
            mentors, and professionals in our ecosystem.
          </p>
          <p class="text-body1 q-mb-lg">
            Your profile helps us match you with the right opportunities and connections
            based on your interests and expertise.
          </p>

          <div class="text-subtitle1 text-weight-medium q-mb-sm">What happens next?</div>
          <ol class="q-mb-lg">
            <li>Choose your profile type (Innovator, Investor, Mentor, etc.)</li>
            <li>Fill in your personal details</li>
            <li>Complete profile-specific information</li>
          </ol>
        </div>
      </div>
    </q-card-section>

    <q-card-actions align="center" class="q-pa-md">
      <q-btn
        color="primary"
        label="Create Your Profile"
        size="lg"
        class="q-px-xl"
        :to="{ name: 'profile-create' }"
      >
        <template v-slot:prepend>
          <q-icon name="person_add" class="q-mr-sm" />
        </template>
      </q-btn>
    </q-card-actions>
  </q-card>
</template>

<script setup lang="ts">
defineEmits(['create-profile']);
</script>

<style scoped>
.new-user-welcome {
  max-width: 900px;
  margin: 0 auto;
  border-radius: 8px;
}

ol {
  padding-left: 20px;
}

ol li {
  margin-bottom: 8px;
}
</style>
