<template>
  <div class="unified-profile-view">
    <!-- Loading State -->
    <div v-if="loading" class="q-pa-md flex flex-center">
      <q-spinner color="primary" size="3em" />
      <div class="q-ml-md">Loading profile data...</div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="q-pa-md flex flex-center column">
      <q-card class="q-pa-lg text-center" style="max-width: 500px">
        <q-card-section>
          <unified-icon name="error" color="negative" size="4em" />
          <div class="text-h5 q-mt-md">Profile Error</div>
          <div class="text-subtitle1 q-mt-sm">
            {{ getErrorMessage(error) }}
          </div>
        </q-card-section>
        <q-card-actions align="center" class="q-gutter-md">
          <q-btn
            v-if="isCurrentUser && error.includes('category')"
            color="primary"
            label="Create Profile"
            :to="{ name: 'profile-create' }"
            class="q-mt-md"
            icon-right="person_add"
          />
          <q-btn
            color="light-green-8"
            :label="context === 'dashboard' ? 'Back to Dashboard' : 'Back'"
            :to="context === 'dashboard' ? '/dashboard' : { name: 'virtual-community' }"
            class="q-mt-md"
            icon-right="arrow_back"
          />
        </q-card-actions>
      </q-card>
    </div>

    <!-- Profile Content -->
    <div v-else-if="combinedProfile">
      <!-- Profile Header -->
      <profile-header 
        :profile="combinedProfile"
        :context="context"
        :show-interactions="showInteractions"
        :is-current-user="isCurrentUser"
        @message="$emit('message', $event)"
        @connect="$emit('connect', $event)"
      />
      
      <!-- Profile Sections -->
      <profile-sections
        :profile="combinedProfile"
        :context="context"
        :is-current-user="isCurrentUser"
        :show-debug="showDebugInfo"
      />
      
      <!-- Activity Feed (for public context) -->
      <q-card v-if="showActivityFeed && context === 'public'" class="q-mb-md section-card">
        <q-card-section class="section-header bg-primary text-white">
          <div class="text-h6">
            <unified-icon name="timeline" class="q-mr-sm" />
            Recent Activity
          </div>
        </q-card-section>
        <q-card-section>
          <activity-feed :user-id="profileId" :limit="5" title="" />
        </q-card-section>
      </q-card>

      <!-- Network Stats (for public context) -->
      <q-card v-if="context === 'public'" class="q-mb-md section-card">
        <q-card-section class="section-header bg-primary text-white">
          <div class="text-h6">
            <unified-icon name="people" class="q-mr-sm" />
            Network
          </div>
        </q-card-section>
        <q-card-section>
          <div class="row q-mt-sm">
            <div class="col-6 col-md-3 q-pa-sm text-center">
              <div class="text-h5">{{ userConnectionCount }}</div>
              <div class="text-caption">Connections</div>
            </div>
            <div class="col-6 col-md-3 q-pa-sm text-center">
              <div class="text-h5">{{ combinedProfile.posts || 0 }}</div>
              <div class="text-caption">Posts</div>
            </div>
            <div class="col-6 col-md-3 q-pa-sm text-center">
              <div class="text-h5">{{ combinedProfile.events || 0 }}</div>
              <div class="text-caption">Events</div>
            </div>
            <div class="col-6 col-md-3 q-pa-sm text-center">
              <div class="text-h5">{{ combinedProfile.groups || 0 }}</div>
              <div class="text-caption">Groups</div>
            </div>
          </div>
        </q-card-section>
      </q-card>
      
      <!-- Context-specific Actions -->
      <profile-actions
        :context="context"
        :is-current-user="isCurrentUser"
        :profile-id="profileId"
      />
    </div>

    <!-- No Profile State -->
    <div v-else-if="!loading" class="q-pa-md flex flex-center column">
      <q-card class="q-pa-lg text-center" style="max-width: 500px">
        <q-card-section>
          <unified-icon name="person_off" color="grey" size="4em" />
          <div class="text-h5 q-mt-md">No Profile Found</div>
          <div class="text-subtitle1 q-mt-sm">
            This user hasn't created a profile yet or it's not publicly visible.
          </div>
        </q-card-section>
      </q-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useProfileStore } from '../../stores/profile'
import { useAuthStore } from '../../stores/auth'
import { useNotificationStore } from '../../stores/notifications'
import { useConnectionsStore } from '../../stores/connections'
import { formatProfileType } from '../../services/profileTypes'
import { getNameFromEmail } from '../../utils/nameUtils'
import ProfileHeader from './ProfileHeader.vue'
import ProfileSections from './ProfileSections.vue'
import ProfileActions from './ProfileActions.vue'
import ActivityFeed from '../activity/ActivityFeed.vue'
import UnifiedIcon from '../ui/UnifiedIcon.vue'

// Props
const props = defineProps<{
  profileId: string
  context: 'dashboard' | 'public'
  isCurrentUser?: boolean
  showInteractions?: boolean
  showActivityFeed?: boolean
  showDebugInfo?: boolean
}>()

// Emits
const emit = defineEmits<{
  message: [userId: string]
  connect: [userId: string]
}>()

// Stores
const profileStore = useProfileStore()
const authStore = useAuthStore()
const notifications = useNotificationStore()
const connectionsStore = useConnectionsStore()

// State
const loading = ref(false)
const error = ref<string | null>(null)

// Computed
const combinedProfile = computed(() => {
  console.log('UnifiedProfileView: Computing combined profile, context:', props.context, 'isCurrentUser:', props.isCurrentUser)
  console.log('UnifiedProfileView: Loading state:', loading.value, 'Error state:', error.value)

  if (props.context === 'dashboard' && props.isCurrentUser) {
    // For dashboard context, use current profile data
    const baseProfile = profileStore.currentProfile
    const specializedProfile = profileStore.currentSpecializedProfile

    console.log('UnifiedProfileView: Dashboard context - baseProfile:', baseProfile, 'specializedProfile:', specializedProfile)

    if (!baseProfile) return null

    return {
      ...baseProfile,
      ...(specializedProfile || {}),
      // Ensure we have a display name
      displayName: getDisplayName(baseProfile),
      formattedProfileType: baseProfile.profile_type ? formatProfileType(baseProfile.profile_type) : 'User'
    }
  } else {
    // For public context or viewing other users, use viewed profile data
    const viewedProfile = profileStore.getCombinedViewedProfile()
    console.log('UnifiedProfileView: Public context - viewedProfile:', viewedProfile)

    // If we have a viewed profile, return it with additional formatting
    // Even if it has minimal data (no profile_type, no name, etc.), we should still display it
    if (viewedProfile) {
      return {
        ...viewedProfile,
        displayName: getDisplayName(viewedProfile),
        formattedProfileType: viewedProfile.profile_type ? formatProfileType(viewedProfile.profile_type) : 'User'
      }
    }

    return viewedProfile
  }
})

// Connection count for the viewed user
const userConnectionCount = ref(0)

// Function to load connection count for the user
async function loadConnectionCount() {
  try {
    console.log('UnifiedProfileView: Loading connection count for user:', props.profileId, 'isCurrentUser:', props.isCurrentUser);

    if (props.isCurrentUser) {
      // For current user, ensure connections are loaded first, then use the store count
      await connectionsStore.fetchUserConnections();
      userConnectionCount.value = connectionsStore.connectionsCount;
      console.log('UnifiedProfileView: Current user connection count from store:', userConnectionCount.value);
    } else {
      // For other users, fetch their connection count
      const count = await connectionsStore.getUserConnectionCount(props.profileId);
      userConnectionCount.value = count;
      console.log('UnifiedProfileView: Other user connection count:', userConnectionCount.value);
    }
  } catch (error) {
    console.error('Error loading connection count:', error);
    userConnectionCount.value = 0;
  }
}

// Methods
// Standardized error message handling
function getErrorMessage(errorMsg: string): string {
  if (!errorMsg) return 'An unexpected error occurred while loading the profile.'

  // Standardize common error messages
  if (errorMsg.includes('not found') || errorMsg.includes('Profile not found')) {
    return 'The profile you\'re looking for could not be found or you may not have permission to view it.'
  }

  if (errorMsg.includes('permission') || errorMsg.includes('unauthorized')) {
    return 'You don\'t have permission to view this profile.'
  }

  if (errorMsg.includes('network') || errorMsg.includes('connection')) {
    return 'Unable to load profile due to network issues. Please check your connection and try again.'
  }

  if (errorMsg.includes('category') || errorMsg.includes('profile type')) {
    return 'This profile needs to be set up. Please complete the profile creation process.'
  }

  // Return the original error message if no specific case matches
  return errorMsg
}

function getDisplayName(profile: any): string {
  if (!profile) return 'Unknown User'

  const fullName = `${profile.first_name || ''} ${profile.last_name || ''}`.trim()
  if (fullName) return fullName

  if (profile.profile_name) return profile.profile_name
  if (profile.email) return getNameFromEmail(profile.email)

  // For profiles with minimal data, show a more user-friendly message
  return 'User Profile'
}

async function loadProfile() {
  if (!props.profileId) {
    error.value = 'No profile ID provided'
    return
  }

  loading.value = true
  error.value = null

  try {
    console.log('UnifiedProfileView: Loading profile for ID:', props.profileId)
    console.log('UnifiedProfileView: Current context:', props.context, 'isCurrentUser:', props.isCurrentUser)
    console.log('UnifiedProfileView: Store state before loading - viewedProfile:', profileStore.viewedProfile, 'viewedSpecializedProfile:', profileStore.viewedSpecializedProfile)

    // Clear viewed profile data to avoid showing cached data from previous profiles
    if (props.context === 'public') {
      console.log('UnifiedProfileView: Clearing viewed profile data for fresh load')
      profileStore.clearViewedProfile()
    }

    // Load the profile data fresh from database for public context
    const forceRefresh = props.context === 'public'
    const profile = await profileStore.fetchProfile(props.profileId, forceRefresh)

    console.log('UnifiedProfileView: Profile fetch result:', profile)
    console.log('UnifiedProfileView: Store state after loading - viewedProfile:', profileStore.viewedProfile, 'viewedSpecializedProfile:', profileStore.viewedSpecializedProfile)

    if (!profile) {
      console.error('UnifiedProfileView: Profile not found for ID:', props.profileId)
      error.value = 'Profile not found'
      return
    }

    console.log('UnifiedProfileView: Profile loaded successfully:', profile)
    console.log('UnifiedProfileView: Profile details:', {
      user_id: profile.user_id,
      email: profile.email,
      first_name: profile.first_name,
      last_name: profile.last_name,
      profile_name: profile.profile_name,
      profile_type: profile.profile_type,
      profile_completion: profile.profile_completion
    })

    // For public context, ensure specialized profile data is properly loaded
    if (props.context === 'public' && profile.profile_type) {
      console.log('UnifiedProfileView: Public context - ensuring specialized profile is loaded for type:', profile.profile_type)

      // Wait a bit for the specialized profile to be loaded by fetchProfile
      await new Promise(resolve => setTimeout(resolve, 500))

      // Check if specialized profile was loaded
      const combinedAfterWait = profileStore.getCombinedViewedProfile()
      console.log('UnifiedProfileView: Combined profile after wait:', combinedAfterWait)

      // If still no specialized data, try to load it explicitly
      if (!profileStore.viewedSpecializedProfile) {
        console.log('UnifiedProfileView: No specialized profile found, attempting explicit load')
        try {
          const { useProfileService } = await import('../../services/profileService')
          const profileService = useProfileService()
          const specializedProfile = await profileService.loadSpecializedProfile(props.profileId, profile.profile_type)

          if (specializedProfile) {
            console.log('UnifiedProfileView: Explicitly loaded specialized profile:', specializedProfile)
            profileStore.setSpecializedProfile(specializedProfile, true) // true for viewed profile
          } else {
            console.log('UnifiedProfileView: No specialized profile data found in database')
          }
        } catch (err) {
          console.error('UnifiedProfileView: Error explicitly loading specialized profile:', err)
        }
      }
    }

    console.log('UnifiedProfileView: Final combined viewed profile:', profileStore.getCombinedViewedProfile())

    // Load connections for the user if this is the current user
    if (props.isCurrentUser && authStore.isAuthenticated) {
      console.log('UnifiedProfileView: Loading connections for current user')
      await connectionsStore.fetchUserConnections()
    }

    // Load connection count for the user
    await loadConnectionCount()

    // Force a reactive update by waiting a tick
    await new Promise(resolve => setTimeout(resolve, 100))
    console.log('UnifiedProfileView: After timeout - Combined viewed profile:', profileStore.getCombinedViewedProfile())
  } catch (err: any) {
    console.error('UnifiedProfileView: Error loading profile:', err)
    error.value = err.message || 'Failed to load profile'

    // Only show error notification in dashboard context to avoid duplicate notifications
    if (props.context === 'dashboard') {
      notifications.error(error.value)
    }
  } finally {
    loading.value = false
  }
}

// Watch for changes in combined profile
watch(combinedProfile, (newProfile) => {
  console.log('UnifiedProfileView: Combined profile changed:', newProfile)
}, { immediate: true })

// Watch for changes in viewed profile from store
watch(() => profileStore.viewedProfile, (newViewedProfile) => {
  console.log('UnifiedProfileView: Store viewed profile changed:', newViewedProfile)
}, { immediate: true })

// Watch for changes in viewed specialized profile from store
watch(() => profileStore.viewedSpecializedProfile, (newViewedSpecializedProfile) => {
  console.log('UnifiedProfileView: Store viewed specialized profile changed:', newViewedSpecializedProfile)
}, { immediate: true })

// Watch for changes in connections count for current user
watch(() => connectionsStore.connectionsCount, (newCount) => {
  if (props.isCurrentUser) {
    console.log('UnifiedProfileView: Connections count changed for current user:', newCount)
    userConnectionCount.value = newCount
  }
}, { immediate: true })

// Watch for changes in profile ID to reload connection count
watch(() => props.profileId, async (newProfileId) => {
  if (newProfileId) {
    console.log('UnifiedProfileView: Profile ID changed, reloading connection count for:', newProfileId)
    await loadConnectionCount()
  }
}, { immediate: false })

// Lifecycle
onMounted(() => {
  console.log('UnifiedProfileView: Component mounted with profileId:', props.profileId)
  if (props.profileId) {
    loadProfile()
  }
})

// Watch for profile ID changes
watch(() => props.profileId, (newProfileId, oldProfileId) => {
  console.log('UnifiedProfileView: Profile ID changed from', oldProfileId, 'to', newProfileId)
  if (newProfileId && newProfileId !== oldProfileId) {
    loadProfile()
  }
}, { immediate: true })
</script>

<style scoped>
.unified-profile-view {
  max-width: 1200px;
  margin: 0 auto;
}

.section-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  border-radius: 8px 8px 0 0;
}

@media (max-width: 767px) {
  .unified-profile-view {
    margin: 0;
    padding: 0 8px;
  }
}
</style>
