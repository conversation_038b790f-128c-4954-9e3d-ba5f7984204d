# SendGrid Email Integration for ZbInnovation

## ⚠️ SECURITY UPDATE

**IMPORTANT**: Direct client-side SendGrid API calls have been disabled for security reasons. All email functionality now goes through Supabase Edge Functions to protect API keys.

This document explains how to use the SendGrid email integration in the ZbInnovation application.

## Overview

The application uses SendGrid to send emails for various purposes:

1. Password reset emails
2. Welcome emails
3. Email verification
4. Profile completion reminders
5. Custom emails

The integration is implemented using Supabase Edge Functions, which allows us to securely send emails without exposing the SendGrid API key in the client-side code.

## Configuration

The SendGrid API key and other configuration values are stored in:

1. Environment variables in the `.env` file (for local development)
2. Supabase secrets (for production)

### Local Development

For local development, the following environment variables are used (DISABLED for security):

```
# DISABLED - DO NOT USE IN CLIENT-SIDE CODE
# VITE_SENDGRID_API_KEY=*********************************************************************
# VITE_SENDGRID_FROM_EMAIL=<EMAIL>
# VITE_SENDGRID_FROM_NAME=ZbInnovation
```

**Note**: These environment variables are now disabled. All email sending is handled via Supabase Edge Functions.

### Production

For production, the Supabase Edge Function uses the following secrets:

```
SENDGRID_API_KEY=*********************************************************************
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=ZbInnovation
```

## Deployment

To deploy the Edge Function with the SendGrid API key, follow the instructions in the `DEPLOY_SENDGRID.md` file.

## Usage

### Password Reset

The application uses a fallback mechanism for password reset:

1. First, it tries to use Supabase Auth's built-in password reset functionality
2. If that fails (e.g., due to SMTP configuration issues), it falls back to using the SendGrid Edge Function

This ensures that password reset works even if the Supabase Auth email service is not configured.

### Sending Emails from the Application

To send emails from the application, use the `emailService.ts` module:

```typescript
import { sendWelcomeEmail, sendPasswordResetEmail, sendCustomEmail } from '../services/emailService'

// Send a welcome email
const result = await sendWelcomeEmail('<EMAIL>', 'John')

// Send a password reset email
const result = await sendPasswordResetEmail('<EMAIL>', 'https://example.com/reset', 'John')

// Send a custom email
const result = await sendCustomEmail('<EMAIL>', 'Subject', '<p>HTML content</p>')
```

## Troubleshooting

If emails are not being sent:

1. Check the Supabase Edge Function logs:
   ```bash
   npx supabase functions logs send-email --project-ref dpicnvisvxpmgjtbeicf
   ```

2. Verify the SendGrid API key is set correctly:
   ```bash
   npx supabase secrets list --project-ref dpicnvisvxpmgjtbeicf
   ```

3. Test the SendGrid API key using the test script:
   ```bash
   node test-sendgrid.js
   ```

4. Check the browser console for any errors related to the Edge Function invocation

## Email Templates

The Edge Function includes several email templates:

1. Welcome email
2. Password reset email
3. Email verification
4. Profile completion reminder

These templates are defined in the Edge Function code and can be customized as needed.

## Security Considerations

- The SendGrid API key is stored securely as a Supabase secret and is not exposed to the client
- For security reasons, password reset always shows a success message, even if the email doesn't exist
- The Edge Function includes CORS headers to restrict access to the API

## Future Improvements

- Add more email templates for different use cases
- Implement email tracking and analytics
- Add support for attachments
- Implement email scheduling
