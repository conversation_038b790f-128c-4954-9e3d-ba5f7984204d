/**
 * Passive Events Plugin
 * 
 * This plugin makes touch events passive by default to improve scrolling performance
 * and eliminate the "Added non-passive event listener" warning.
 */

// Add passive event listeners to improve performance
export default {
  install() {
    // Options to be passed to addEventListener
    const options = {
      passive: true
    };

    // Original addEventListener
    const originalAddEventListener = EventTarget.prototype.addEventListener;
    
    // Override addEventListener to make touch events passive by default
    EventTarget.prototype.addEventListener = function(type, listener, optionsOrCapture) {
      // Check if it's a touch event
      const isTouchEvent = type.startsWith('touch');
      
      // If it's a touch event and no options were provided, use passive options
      if (isTouchEvent && (optionsOrCapture === undefined || optionsOrCapture === false)) {
        return originalAddEventListener.call(this, type, listener, options);
      }
      
      // If options were provided or it's not a touch event, use original behavior
      return originalAddEventListener.call(this, type, listener, optionsOrCapture);
    };
  }
};
