/**
 * AI Service
 * 
 * This service handles AI-related functionality for the platform,
 * including chat assistance, content recommendations, and AI-powered features.
 */

import { supabase } from '../lib/supabase';

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp?: Date;
}

export interface ChatRequest {
  message: string;
  conversation_history?: ChatMessage[];
  user_context?: {
    profile_type?: string;
    user_id?: string;
  };
}

export interface ChatResponse {
  response: string;
  conversation_id?: string;
  error?: string;
}

/**
 * Send a message to the AI chat assistant
 */
export async function sendChatMessage(request: ChatRequest): Promise<ChatResponse> {
  try {
    const { data, error } = await supabase.functions.invoke('ai-chat', {
      body: request
    });

    if (error) {
      throw error;
    }

    return data;
  } catch (error: any) {
    console.error('AI Chat Service Error:', error);
    return {
      response: '',
      error: error.message || 'Failed to get AI response'
    };
  }
}

/**
 * Get AI-powered content recommendations based on user profile
 */
export async function getContentRecommendations(userId: string, profileType: string) {
  // This could be expanded to use AI for content recommendations
  // For now, return a placeholder
  return {
    recommendations: [],
    message: 'AI content recommendations coming soon!'
  };
}

/**
 * Get AI-powered matchmaking suggestions
 */
export async function getMatchmakingSuggestions(userId: string, profileType: string) {
  // This could be expanded to enhance the existing matchmaking with AI
  // For now, return a placeholder
  return {
    suggestions: [],
    message: 'AI-enhanced matchmaking coming soon!'
  };
}

/**
 * Analyze user profile and provide optimization suggestions
 */
export async function analyzeProfile(profileData: any) {
  try {
    const analysisPrompt = `
      Analyze this user profile and provide 3-5 specific suggestions for improvement:
      
      Profile Type: ${profileData.profile_type}
      Bio: ${profileData.bio || 'Not provided'}
      Skills: ${profileData.skills?.join(', ') || 'Not provided'}
      Industries: ${profileData.industries?.join(', ') || 'Not provided'}
      
      Provide actionable suggestions to make this profile more attractive to potential matches and collaborators.
    `;

    const response = await sendChatMessage({
      message: analysisPrompt,
      user_context: {
        profile_type: profileData.profile_type,
        user_id: profileData.user_id
      }
    });

    return {
      suggestions: response.response,
      error: response.error
    };
  } catch (error: any) {
    return {
      suggestions: '',
      error: error.message || 'Failed to analyze profile'
    };
  }
}

/**
 * Generate AI-powered content ideas based on user interests
 */
export async function generateContentIdeas(userInterests: string[], profileType: string) {
  try {
    const prompt = `
      Generate 5 content ideas for a ${profileType} interested in: ${userInterests.join(', ')}.
      
      The content should be relevant to Zimbabwe's innovation ecosystem and help build their professional presence.
      Format as a numbered list with brief descriptions.
    `;

    const response = await sendChatMessage({
      message: prompt,
      user_context: {
        profile_type: profileType
      }
    });

    return {
      ideas: response.response,
      error: response.error
    };
  } catch (error: any) {
    return {
      ideas: '',
      error: error.message || 'Failed to generate content ideas'
    };
  }
}

/**
 * Get AI assistance for writing better posts/content
 */
export async function improveContent(content: string, contentType: 'post' | 'bio' | 'description') {
  try {
    const prompt = `
      Improve this ${contentType} to make it more engaging and professional:
      
      "${content}"
      
      Provide a rewritten version that is more compelling, clear, and suitable for a professional innovation platform.
    `;

    const response = await sendChatMessage({
      message: prompt
    });

    return {
      improvedContent: response.response,
      error: response.error
    };
  } catch (error: any) {
    return {
      improvedContent: '',
      error: error.message || 'Failed to improve content'
    };
  }
}

/**
 * Get AI-powered insights about innovation trends
 */
export async function getInnovationInsights(industry?: string) {
  try {
    const prompt = industry 
      ? `Provide insights about current innovation trends in the ${industry} industry, particularly relevant to Zimbabwe and Africa.`
      : 'Provide insights about current innovation trends in Zimbabwe and Africa.';

    const response = await sendChatMessage({
      message: prompt
    });

    return {
      insights: response.response,
      error: response.error
    };
  } catch (error: any) {
    return {
      insights: '',
      error: error.message || 'Failed to get innovation insights'
    };
  }
}

// Export the service as default
export default {
  sendChatMessage,
  getContentRecommendations,
  getMatchmakingSuggestions,
  analyzeProfile,
  generateContentIdeas,
  improveContent,
  getInnovationInsights
};
