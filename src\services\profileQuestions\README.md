# Profile Questions Service

This service provides a single source of truth for all profile questions in the application. It uses a modular approach to organize profile questions by type, making it easier to maintain and extend.

## Structure

- `index.ts` - Main export file with helper functions and composable
- `types.ts` - Shared type definitions
- `common.ts` - Common options used across multiple profile types
- `profiles/` - Directory containing individual profile type definitions
  - `innovator.ts` - Innovator profile questions
  - `investor.ts` - Investor profile questions
  - `mentor.ts` - Mentor profile questions
  - `professional.ts` - Professional profile questions
  - (Additional profile types as needed)

## Usage

### Basic Usage

```typescript
import { useProfileQuestions } from '@/services/profileQuestions';

// In your component setup
const { setProfileType, currentQuestions, getFieldOptions } = useProfileQuestions();

// Set the profile type
setProfileType('innovator');

// Access questions for the current profile type
const questions = currentQuestions.value;

// Get options for a specific field
const innovationAreaOptions = getFieldOptions('innovationAreaOptions');
```

### Accessing Profile Sections

```typescript
import { getProfileSections } from '@/services/profileQuestions';

// Get sections for a specific profile type
const innovatorSections = getProfileSections('innovator');
```

### Accessing Profile Options

```typescript
import { getProfileOptions } from '@/services/profileQuestions';

// Get all options for a specific profile type
const innovatorOptions = getProfileOptions('innovator');
```

## Adding a New Profile Type

1. Create a new file in the `profiles/` directory (e.g., `academic.ts`)
2. Define the profile type using the `ProfileType` interface
3. Export the profile type
4. Add the profile type to the `profileQuestionsMap` in `index.ts`

## Backward Compatibility

For backward compatibility with existing code that uses `unifiedProfileQuestions`, the following aliases are provided:

- `getUnifiedProfileQuestions` → `getProfileQuestions`
- `getUnifiedProfileSections` → `getProfileSections`
- `getUnifiedProfileOptions` → `getProfileOptions`
- `useUnifiedProfileQuestions` → `useProfileQuestions`

## Database Field Mapping

Each question has both an `id` and a `name` property:
- `id` - Used for form identification and display
- `name` - Maps to the database field name

This ensures that the form data can be correctly mapped to the database schema.
