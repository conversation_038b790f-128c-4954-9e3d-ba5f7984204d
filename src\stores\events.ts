import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { usePostsStore } from './posts';
import { useNotificationStore } from './notifications';
import { date } from 'quasar';

export interface Event {
  id: number;
  title: string;
  description: string;
  date: string;
  location: string;
  type: string;
  format: string;
  image?: string;
  category?: string;
  tags?: string[];
  theme?: string;
  created_at?: string;
  updated_at?: string;
}

export const useEventsStore = defineStore('events', () => {
  // Use the posts store
  const postsStore = usePostsStore();
  const notifications = useNotificationStore();

  // Local state for error handling
  const error = ref<string | null>(null);

  // Map posts to events
  const events = computed(() => {
    // Filter posts to only include those with subType 'event' or event-specific fields
    return postsStore.events
      .filter(post => {
        // Primary check: look for 'event' in subType (case-insensitive)
        const isEventSubType = post.subType?.toLowerCase() === 'event';

        // Secondary checks if subType is not available
        const hasEventPostType = post.postType?.toUpperCase() === 'EVENT';
        const hasEventFields = post.eventTitle || post.eventLocation || post.eventStartDatetime;

        return isEventSubType || hasEventPostType || hasEventFields;
      })
      .map(post => {
        // Try to parse content if it's a JSON string
        let description = post.content || '';
        let eventDetails: Record<string, any> = {};

        if (typeof description === 'string' && (description.startsWith('{') || description.startsWith('['))) {
          try {
            const parsedContent = JSON.parse(description);
            if (parsedContent.description) {
              description = parsedContent.description;
            }
            if (parsedContent.eventDetails) {
              eventDetails = parsedContent.eventDetails;
            }
          } catch (e) {
            console.log('Failed to parse event JSON content:', e);
          }
        }

        // Get category from post if available
        const category = post.category || post.blogCategory || post.eventType || '';

        return {
          id: post.id,
          title: post.eventTitle || post.title || '',
          description: description,
          date: formatDate(post.eventStartDatetime || eventDetails.eventDate || post.createdAt),
          location: post.eventLocation || eventDetails.location || '',
          type: post.eventType || eventDetails.eventType || post.subType || 'General',
          format: post.subType || '',
          image: post.featuredImage || post.image || '',
          category: category,
          tags: post.tags || [],
          created_at: post.createdAt,
          updated_at: post.updatedAt
        };
      });
  });

  // Pass through loading state
  const loading = computed(() => postsStore.loading);

  // Getters
  const upcomingEvents = computed(() => {
    const now = new Date();
    return events.value
      .filter(event => new Date(event.date) >= now)
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
      .slice(0, 3);
  });

  const pastEvents = computed(() => {
    const now = new Date();
    return events.value
      .filter(event => new Date(event.date) < now)
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  });

  // Actions
  async function fetchEvents() {
    try {
      error.value = null;
      await postsStore.fetchEvents();
    } catch (err: any) {
      console.error('Error fetching events:', err);
      error.value = err.message || 'Failed to fetch events';
      notifications.error('Failed to load events: ' + error.value);
    }
  }

  async function getEventById(id: number): Promise<Event | null> {
    try {
      // First check if the event is already in our local state
      const localEvent = events.value.find(e => e.id === id);
      if (localEvent) {
        return localEvent;
      }

      // If not found locally, fetch from the posts store
      const post = await postsStore.getPostById(id);

      if (post && (post.postType === 'EVENT' || post.subType?.toLowerCase() === 'event' || post.eventTitle || post.eventLocation)) {
        // Try to parse content if it's a JSON string
        let description = post.content || '';
        let eventDetails: Record<string, any> = {};

        if (typeof description === 'string' && (description.startsWith('{') || description.startsWith('['))) {
          try {
            const parsedContent = JSON.parse(description);
            if (parsedContent.description) {
              description = parsedContent.description;
            }
            if (parsedContent.eventDetails) {
              eventDetails = parsedContent.eventDetails;
            }
          } catch (e) {
            console.log('Failed to parse event JSON content:', e);
          }
        }

        // Get category from post if available
        const category = post.category || post.blogCategory || post.eventType || '';

        return {
          id: post.id,
          title: post.eventTitle || post.title || '',
          description: description,
          date: formatDate(post.eventStartDatetime || eventDetails.eventDate || post.createdAt),
          location: post.eventLocation || eventDetails.location || '',
          type: post.eventType || eventDetails.eventType || post.subType || 'General',
          format: post.subType || '',
          image: post.featuredImage || post.image || '',
          category: category,
          tags: post.tags || [],
          created_at: post.createdAt,
          updated_at: post.updatedAt
        };
      }

      return null;
    } catch (err: any) {
      console.error('Error fetching event by ID:', err);
      error.value = err.message || 'Failed to fetch event';
      return null;
    }
  }

  // Helper function to format dates
  function formatDate(dateString: string): string {
    if (!dateString) return 'No date';

    try {
      return date.formatDate(dateString, 'MMMM D, YYYY');
    } catch (e) {
      return dateString;
    }
  }

  return {
    // State
    loading,
    error,
    events,

    // Getters
    upcomingEvents,
    pastEvents,

    // Actions
    fetchEvents,
    getEventById
  };
});
