interface EmailOptions {
  to: string
  from: string
  subject: string
  text?: string
  html?: string
}

interface SendResult {
  success: boolean
  error?: string
}

export class SendGridClient {
  private apiKey: string
  private baseUrl = 'https://api.sendgrid.com/v3/mail/send'

  constructor(apiKey: string) {
    this.apiKey = apiKey
  }

  async send(options: EmailOptions): Promise<SendResult> {
    try {
      if (!this.apiKey) {
        throw new Error('SendGrid API key is not configured')
      }

      if (!options.to || !options.from || !options.subject) {
        throw new Error('Missing required email fields')
      }

      if (!options.text && !options.html) {
        throw new Error('Either text or html content is required')
      }

      const payload = {
        personalizations: [
          {
            to: [{ email: options.to }]
          }
        ],
        from: { email: options.from },
        subject: options.subject,
        content: []
      }

      if (options.text) {
        payload.content.push({
          type: 'text/plain',
          value: options.text
        })
      }

      if (options.html) {
        payload.content.push({
          type: 'text/html',
          value: options.html
        })
      }

      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${this.apiKey}`
        },
        body: JSON.stringify(payload)
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        console.error('SendGrid API error:', {
          status: response.status,
          statusText: response.statusText,
          data: errorData
        })
        throw new Error(`SendGrid API error: ${response.status} ${response.statusText}`)
      }

      return { success: true }
    } catch (error) {
      console.error('Error sending email:', error)
      return {
        success: false,
        error: error.message || 'Unknown error sending email'
      }
    }
  }
}
