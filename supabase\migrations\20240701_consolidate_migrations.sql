-- Migration: Consolidate migrations from src/db/migrations
-- Description: This migration consolidates all the migrations from the src/db/migrations directory

-- 1. Fix Database Issues Migration
-- Create or replace the posts_with_authors view
CREATE OR REPLACE VIEW posts_with_authors AS
SELECT 
    p.*,
    prof.first_name,
    prof.last_name,
    prof.email,
    prof.avatar_url
FROM 
    posts p
LEFT JOIN 
    profiles prof ON p.user_id = prof.user_id;

-- Grant permissions on the view
GRANT SELECT ON posts_with_authors TO authenticated, service_role;

-- 2. Create user connections table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.user_connections (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  connected_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  connection_type VARCHAR(50) NOT NULL,
  connection_status VARCHAR(20) DEFAULT 'pending',
  connection_strength FLOAT DEFAULT 1.0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, connected_user_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_connections_user_id ON public.user_connections(user_id);
CREATE INDEX IF NOT EXISTS idx_user_connections_connected_user_id ON public.user_connections(connected_user_id);
CREATE INDEX IF NOT EXISTS idx_user_connections_status ON public.user_connections(connection_status);

-- Enable Row Level Security
ALTER TABLE public.user_connections ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY IF NOT EXISTS "Users can view their own connections"
  ON public.user_connections
  FOR SELECT
  USING (auth.uid() = user_id OR auth.uid() = connected_user_id);

CREATE POLICY IF NOT EXISTS "Users can create their own connections"
  ON public.user_connections
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can update their own connections"
  ON public.user_connections
  FOR UPDATE
  USING (auth.uid() = user_id OR auth.uid() = connected_user_id);

CREATE POLICY IF NOT EXISTS "Users can delete their own connections"
  ON public.user_connections
  FOR DELETE
  USING (auth.uid() = user_id);

-- 3. Update profiles table to include profile_completion field if not already done
ALTER TABLE profiles
ADD COLUMN IF NOT EXISTS profile_completion FLOAT DEFAULT 0.0,
ADD COLUMN IF NOT EXISTS profile_type VARCHAR(50),
ADD COLUMN IF NOT EXISTS profile_type_id UUID;

-- 4. Add profile_state enum type if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'profile_state_enum') THEN
        CREATE TYPE profile_state_enum AS ENUM ('in_progress', 'active', 'disabled', 'banned');
    END IF;
END$$;

-- 5. Add profile_state column to profiles table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'profile_state') THEN
        ALTER TABLE public.profiles ADD COLUMN profile_state profile_state_enum DEFAULT 'in_progress';
    END IF;
END$$;

-- 6. Create or update trigger function for updating updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 7. Create triggers for all profile tables
DO $$
DECLARE
    tables TEXT[] := ARRAY['profiles', 'innovator_profiles', 'investor_profiles', 'mentor_profiles',
                          'professional_profiles', 'industry_expert_profiles', 'academic_student_profiles',
                          'academic_institution_profiles', 'organisation_profiles'];
    t TEXT;
BEGIN
    FOREACH t IN ARRAY tables
    LOOP
        EXECUTE format('
            DROP TRIGGER IF EXISTS update_%s_updated_at ON public.%s;
            CREATE TRIGGER update_%s_updated_at
            BEFORE UPDATE ON public.%s
            FOR EACH ROW
            EXECUTE FUNCTION public.update_updated_at_column();
        ', t, t, t, t);
    END LOOP;
END$$;
