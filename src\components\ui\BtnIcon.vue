<template>
  <span class="q-icon material-icons">{{ name }}</span>
</template>

<script setup lang="ts">
defineProps({
  name: {
    type: String,
    required: true
  }
});
</script>

<style scoped>
.q-icon {
  line-height: 1;
  width: 1em;
  height: 1em;
  letter-spacing: normal;
  text-transform: none;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  text-align: center;
  position: relative;
  box-sizing: content-box;
}

.material-icons {
  font-family: 'Material Icons', sans-serif !important;
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  display: inline-block;
  line-height: 1;
  text-transform: none;
  letter-spacing: normal;
  word-wrap: normal;
  white-space: nowrap;
  direction: ltr;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: 'liga';
}
</style>
