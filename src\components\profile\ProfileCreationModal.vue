<template>
  <q-dialog
    v-model="showDialog"
    persistent
    maximized
    transition-show="slide-up"
    transition-hide="slide-down"
  >
    <div class="profile-creation-modal">
      <div class="profile-creation-modal-content">
        <div class="profile-creation-modal-header">
          <q-btn
            flat
            round
            icon="close"
            color="white"
            @click="closeModal"
            class="close-button"
          />
        </div>
        
        <div class="profile-creation-modal-body">
          <profile-type-selection
            @profile-created="handleProfileCreated"
            @close="closeModal"
          />
        </div>
      </div>
    </div>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import ProfileTypeSelection from './ProfileTypeSelection.vue'

// Props and emits
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'profile-created'])

// State
const showDialog = ref(props.modelValue)

// Watch for changes to modelValue prop
watch(() => props.modelValue, (newValue) => {
  showDialog.value = newValue
})

// Watch for changes to showDialog and emit update event
watch(showDialog, (newValue) => {
  emit('update:modelValue', newValue)
})

// Methods
function closeModal() {
  showDialog.value = false
}

function handleProfileCreated(profile) {
  emit('profile-created', profile)
  closeModal()
}
</script>

<style scoped>
.profile-creation-modal {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
}

.profile-creation-modal-content {
  position: relative;
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
}

.profile-creation-modal-header {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
}

.profile-creation-modal-body {
  padding: 20px;
}

.close-button {
  background-color: rgba(0, 0, 0, 0.3);
}

@media (max-width: 599px) {
  .profile-creation-modal-content {
    max-width: 100%;
    height: 100%;
  }
  
  .profile-creation-modal-body {
    padding: 10px;
    height: 100%;
    overflow-y: auto;
  }
}
</style>
