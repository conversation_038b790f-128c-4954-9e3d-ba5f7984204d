import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'
import { setupEnhancedRouteGuards } from './enhancedGuards'

// Import route modules
import publicRoutes from './public'
import dashboardRoutes from './dashboard'

// Combine all routes
const routes: RouteRecordRaw[] = [
  ...publicRoutes,
  ...dashboardRoutes,
  {
    path: '/:catchAll(.*)*',
    component: () => import(/* webpackChunkName: "error" */ '../views/public/ErrorNotFound.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  // Control scroll behavior when navigating between routes
  scrollBehavior(to, from, savedPosition) {
    // If the user is using the back/forward buttons, restore the saved position
    if (savedPosition) {
      return savedPosition;
    }

    // If the route has a hash, scroll to the element with that id
    if (to.hash) {
      return {
        el: to.hash,
        behavior: 'smooth',
      };
    }

    // Otherwise, scroll to the top of the page
    return { top: 0 };
  }
})

// Set up enhanced route guards
setupEnhancedRouteGuards(router)

export default router
