/**
 * Profile Matchers
 *
 * This file contains specialized matching algorithms for different profile type combinations.
 * These functions implement the specific matching logic described in the matchmaking design document.
 */

import { SupabaseClient } from '@supabase/supabase-js';
import {
  arrayIntersectionScore,
  numericRangeMatch,
  stageCompatibilityScore,
  approachCompatibilityScore,
  textSimilarityScore
} from './matchingUtils';

/**
 * Match an innovator to an investor
 * @param innovator Innovator profile
 * @param investor Investor profile
 * @returns Match score and reasons
 */
export function matchInnovatorToInvestor(innovator: any, investor: any): { score: number, reasons: Record<string, number> } {
  const scores: Record<string, number> = {
    industry: arrayIntersectionScore(
      innovator.industry || [],
      investor.investment_focus || []
    ),
    stage: arrayIntersectionScore(
      [innovator.innovation_stage] || [],
      investor.investment_stage || []
    ),
    funding: numericRangeMatch(
      innovator.funding_amount,
      {
        min: parseTicketSize(investor.ticket_size, 'min'),
        max: parseTicketSize(investor.ticket_size, 'max')
      }
    ),
    location: arrayIntersectionScore(
      innovator.preferred_locations || [],
      investor.preferred_locations || []
    ),
    goals: arrayIntersectionScore(
      innovator.short_term_goals || [],
      investor.collaboration_interests || []
    )
  };

  const weights = {
    industry: 0.3,
    stage: 0.3,
    funding: 0.2,
    location: 0.1,
    goals: 0.1
  };

  let totalScore = 0;
  let totalWeight = 0;

  for (const [key, weight] of Object.entries(weights)) {
    totalScore += scores[key] * weight;
    totalWeight += weight;
  }

  return {
    score: totalWeight > 0 ? totalScore / totalWeight : 0,
    reasons: scores
  };
}

/**
 * Match an innovator to a mentor
 * @param innovator Innovator profile
 * @param mentor Mentor profile
 * @returns Match score and reasons
 */
export function matchInnovatorToMentor(innovator: any, mentor: any): { score: number, reasons: Record<string, number> } {
  const scores: Record<string, number> = {
    expertise: arrayIntersectionScore(
      innovator.current_challenges || [],
      mentor.expertise_areas || []
    ),
    industry: arrayIntersectionScore(
      innovator.industry || [],
      mentor.industry_experience || []
    ),
    stage: stageCompatibilityScore(
      innovator.innovation_stage,
      mentor.preferred_mentee_stage
    ),
    goals: arrayIntersectionScore(
      innovator.short_term_goals || [],
      mentor.collaboration_interests || []
    ),
    approach: approachCompatibilityScore(
      innovator.looking_for,
      mentor.mentoring_approach
    )
  };

  const weights = {
    expertise: 0.35,
    industry: 0.2,
    stage: 0.15,
    goals: 0.15,
    approach: 0.15
  };

  let totalScore = 0;
  let totalWeight = 0;

  for (const [key, weight] of Object.entries(weights)) {
    totalScore += scores[key] * weight;
    totalWeight += weight;
  }

  return {
    score: totalWeight > 0 ? totalScore / totalWeight : 0,
    reasons: scores
  };
}

/**
 * Match an investor to an innovator
 * @param investor Investor profile
 * @param innovator Innovator profile
 * @returns Match score and reasons
 */
export function matchInvestorToInnovator(investor: any, innovator: any): { score: number, reasons: Record<string, number> } {
  // Reuse the innovator-to-investor logic but with potentially different weights
  const { score, reasons } = matchInnovatorToInvestor(innovator, investor);

  // Adjust the score based on investor-specific criteria
  const investorSpecificScores: Record<string, number> = {
    traction: evaluateTraction(innovator, investor),
    team: evaluateTeam(innovator, investor),
    market: evaluateMarket(innovator, investor)
  };

  const investorSpecificWeights = {
    traction: 0.15,
    team: 0.1,
    market: 0.15
  };

  let adjustedScore = score * 0.6; // Base score is 60% of the total
  let totalWeight = 0.6;

  for (const [key, weight] of Object.entries(investorSpecificWeights)) {
    adjustedScore += investorSpecificScores[key] * weight;
    totalWeight += weight;
    reasons[key] = investorSpecificScores[key];
  }

  return {
    score: totalWeight > 0 ? adjustedScore / totalWeight : 0,
    reasons
  };
}

/**
 * Match a mentor to an innovator
 * @param mentor Mentor profile
 * @param innovator Innovator profile
 * @returns Match score and reasons
 */
export function matchMentorToInnovator(mentor: any, innovator: any): { score: number, reasons: Record<string, number> } {
  // Reuse the innovator-to-mentor logic but with potentially different weights
  const { score, reasons } = matchInnovatorToMentor(innovator, mentor);

  // Adjust the score based on mentor-specific criteria
  const mentorSpecificScores: Record<string, number> = {
    potential: evaluatePotential(innovator, mentor),
    fit: evaluateMentorshipFit(innovator, mentor)
  };

  const mentorSpecificWeights = {
    potential: 0.2,
    fit: 0.2
  };

  let adjustedScore = score * 0.6; // Base score is 60% of the total
  let totalWeight = 0.6;

  for (const [key, weight] of Object.entries(mentorSpecificWeights)) {
    adjustedScore += mentorSpecificScores[key] * weight;
    totalWeight += weight;
    reasons[key] = mentorSpecificScores[key];
  }

  return {
    score: totalWeight > 0 ? adjustedScore / totalWeight : 0,
    reasons
  };
}

// Helper functions for specific evaluations

/**
 * Parse ticket size string into numeric value
 * @param ticketSize Ticket size string (e.g., "$50K-$500K")
 * @param type Whether to get min or max value
 * @returns Numeric value in dollars
 */
function parseTicketSize(ticketSize: string | null, type: 'min' | 'max'): number | undefined {
  if (!ticketSize) return undefined;

  // Handle different formats
  // Format: "$50K-$500K" or "50K-500K" or "$50,000-$500,000"
  const cleanedString = ticketSize.replace(/\$/g, '').replace(/,/g, '');
  const parts = cleanedString.split('-');

  if (parts.length !== 2) return undefined;

  const getValue = (str: string): number => {
    str = str.trim();
    if (str.endsWith('K') || str.endsWith('k')) {
      return parseFloat(str.slice(0, -1)) * 1000;
    }
    if (str.endsWith('M') || str.endsWith('m')) {
      return parseFloat(str.slice(0, -1)) * 1000000;
    }
    return parseFloat(str);
  };

  return type === 'min' ? getValue(parts[0]) : getValue(parts[1]);
}

/**
 * Evaluate traction of an innovator for an investor
 */
function evaluateTraction(innovator: any, investor: any): number {
  // Simple implementation - can be enhanced
  if (!innovator.current_traction) return 0.5; // Neutral score if no data

  // Check for key traction indicators
  const hasRevenue = innovator.current_revenue && innovator.current_revenue !== '0';
  const hasGrowth = innovator.growth_rate && innovator.growth_rate !== '0%';
  const hasUsers = innovator.current_traction.toLowerCase().includes('user') ||
                  innovator.current_traction.toLowerCase().includes('customer');

  let score = 0.5; // Start with neutral score

  if (hasRevenue) score += 0.2;
  if (hasGrowth) score += 0.2;
  if (hasUsers) score += 0.1;

  return Math.min(score, 1); // Cap at 1.0
}

/**
 * Evaluate team of an innovator for an investor
 */
function evaluateTeam(innovator: any, investor: any): number {
  // Simple implementation - can be enhanced
  if (!innovator.team_size || !innovator.team_description) return 0.5;

  const teamSize = parseInt(innovator.team_size, 10);

  // Evaluate based on team size and description
  let score = 0.5;

  // Team size evaluation
  if (teamSize >= 3) score += 0.2;
  else if (teamSize >= 2) score += 0.1;

  // Team description evaluation
  const teamDesc = innovator.team_description.toLowerCase();
  if (teamDesc.includes('experience') || teamDesc.includes('expert')) score += 0.2;
  if (teamDesc.includes('previous startup') || teamDesc.includes('founded')) score += 0.1;

  return Math.min(score, 1); // Cap at 1.0
}

/**
 * Evaluate market of an innovator for an investor
 */
function evaluateMarket(innovator: any, investor: any): number {
  // Simple implementation - can be enhanced
  if (!innovator.market_size || !innovator.target_market) return 0.5;

  let score = 0.5;

  // Market size evaluation
  const marketSize = innovator.market_size.toLowerCase();
  if (marketSize.includes('large') || marketSize.includes('billion')) score += 0.3;
  else if (marketSize.includes('medium') || marketSize.includes('million')) score += 0.2;

  // Target market clarity
  if (innovator.target_market.length > 50) score += 0.2; // Detailed description

  return Math.min(score, 1); // Cap at 1.0
}

/**
 * Evaluate potential of an innovator for a mentor
 */
function evaluatePotential(innovator: any, mentor: any): number {
  // Simple implementation - can be enhanced
  if (!innovator.innovation_description) return 0.5;

  let score = 0.5;

  // Innovation description evaluation
  const desc = innovator.innovation_description.toLowerCase();
  if (desc.length > 200) score += 0.2; // Detailed description

  // Check for key indicators of potential
  if (innovator.has_prototype) score += 0.2;
  if (innovator.achievements && innovator.achievements.length > 0) score += 0.1;

  return Math.min(score, 1); // Cap at 1.0
}

/**
 * Evaluate mentorship fit between innovator and mentor
 */
function evaluateMentorshipFit(innovator: any, mentor: any): number {
  // Simple implementation - can be enhanced
  if (!innovator.looking_for || !mentor.mentorship_philosophy) return 0.5;

  let score = 0.5;

  // Check if innovator's needs align with mentor's philosophy
  const innovatorNeeds = Array.isArray(innovator.looking_for)
    ? innovator.looking_for.join(' ').toLowerCase()
    : String(innovator.looking_for).toLowerCase();

  const mentorPhilosophy = mentor.mentorship_philosophy.toLowerCase();

  // Check for keyword matches
  const keywords = ['guidance', 'advice', 'experience', 'network', 'connections', 'strategy'];

  for (const keyword of keywords) {
    if (innovatorNeeds.includes(keyword) && mentorPhilosophy.includes(keyword)) {
      score += 0.1;
    }
  }

  return Math.min(score, 1); // Cap at 1.0
}

/**
 * Match an innovator to a professional
 * @param innovator Innovator profile
 * @param professional Professional profile
 * @returns Match score and reasons
 */
export function matchInnovatorToProfessional(innovator: any, professional: any): { score: number, reasons: Record<string, number> } {
  const scores: Record<string, number> = {
    skills: arrayIntersectionScore(
      innovator.current_challenges || [],
      professional.skills || []
    ),
    industry: arrayIntersectionScore(
      innovator.industry || [],
      professional.industry || []
    ),
    service: approachCompatibilityScore(
      innovator.looking_for || '',
      professional.services_offered || ''
    ),
    goals: arrayIntersectionScore(
      innovator.short_term_goals || [],
      professional.collaboration_interests || []
    ),
    location: arrayIntersectionScore(
      innovator.preferred_locations || [],
      professional.preferred_locations || []
    )
  };

  const weights = {
    skills: 0.35,
    industry: 0.25,
    service: 0.2,
    goals: 0.1,
    location: 0.1
  };

  let totalScore = 0;
  let totalWeight = 0;

  for (const [key, weight] of Object.entries(weights)) {
    totalScore += scores[key] * weight;
    totalWeight += weight;
  }

  return {
    score: totalWeight > 0 ? totalScore / totalWeight : 0,
    reasons: scores
  };
}

/**
 * Match a professional to an innovator
 * @param professional Professional profile
 * @param innovator Innovator profile
 * @returns Match score and reasons
 */
export function matchProfessionalToInnovator(professional: any, innovator: any): { score: number, reasons: Record<string, number> } {
  // Reuse the innovator-to-professional logic but with potentially different weights
  const { score, reasons } = matchInnovatorToProfessional(innovator, professional);

  // Adjust the score based on professional-specific criteria
  const professionalSpecificScores: Record<string, number> = {
    projectFit: evaluateProjectFit(innovator, professional),
    businessPotential: evaluateBusinessPotential(innovator, professional)
  };

  const professionalSpecificWeights = {
    projectFit: 0.2,
    businessPotential: 0.2
  };

  let adjustedScore = score * 0.6; // Base score is 60% of the total
  let totalWeight = 0.6;

  for (const [key, weight] of Object.entries(professionalSpecificWeights)) {
    adjustedScore += professionalSpecificScores[key] * weight;
    totalWeight += weight;
    reasons[key] = professionalSpecificScores[key];
  }

  return {
    score: totalWeight > 0 ? adjustedScore / totalWeight : 0,
    reasons
  };
}

/**
 * Evaluate project fit between innovator and professional
 */
function evaluateProjectFit(innovator: any, professional: any): number {
  // Simple implementation - can be enhanced
  if (!innovator.innovation_description || !professional.project_preferences) return 0.5;

  let score = 0.5;

  // Check if innovator's project aligns with professional's preferences
  const projectDesc = innovator.innovation_description.toLowerCase();
  const preferences = Array.isArray(professional.project_preferences)
    ? professional.project_preferences.join(' ').toLowerCase()
    : String(professional.project_preferences).toLowerCase();

  // Check for keyword matches
  const keywords = ['startup', 'innovation', 'technology', 'digital', 'sustainable', 'social impact'];

  for (const keyword of keywords) {
    if (projectDesc.includes(keyword) && preferences.includes(keyword)) {
      score += 0.1;
    }
  }

  return Math.min(score, 1); // Cap at 1.0
}

/**
 * Evaluate business potential for a professional
 */
function evaluateBusinessPotential(innovator: any, professional: any): number {
  // Simple implementation - can be enhanced
  if (!innovator.market_size || !innovator.business_model) return 0.5;

  let score = 0.5;

  // Market size evaluation
  const marketSize = innovator.market_size.toLowerCase();
  if (marketSize.includes('large') || marketSize.includes('billion')) score += 0.2;
  else if (marketSize.includes('medium') || marketSize.includes('million')) score += 0.1;

  // Business model clarity
  if (innovator.business_model && innovator.business_model.length > 50) score += 0.2;

  // Revenue potential
  if (innovator.revenue_model && innovator.revenue_model.length > 0) score += 0.1;

  return Math.min(score, 1); // Cap at 1.0
}

/**
 * Match an academic student to a mentor
 * @param student Academic student profile
 * @param mentor Mentor profile
 * @returns Match score and reasons
 */
export function matchAcademicStudentToMentor(student: any, mentor: any): { score: number, reasons: Record<string, number> } {
  const scores: Record<string, number> = {
    expertise: arrayIntersectionScore(
      student.research_interests || [],
      mentor.expertise_areas || []
    ),
    field: arrayIntersectionScore(
      [student.field_of_study] || [],
      mentor.industry_experience || []
    ),
    career: arrayIntersectionScore(
      student.career_interests || [],
      mentor.industry_experience || []
    ),
    learning: approachCompatibilityScore(
      student.learning_preferences || '',
      mentor.mentoring_approach || ''
    ),
    goals: arrayIntersectionScore(
      student.short_term_goals || [],
      mentor.collaboration_interests || []
    )
  };

  const weights = {
    expertise: 0.3,
    field: 0.2,
    career: 0.2,
    learning: 0.2,
    goals: 0.1
  };

  let totalScore = 0;
  let totalWeight = 0;

  for (const [key, weight] of Object.entries(weights)) {
    totalScore += scores[key] * weight;
    totalWeight += weight;
  }

  return {
    score: totalWeight > 0 ? totalScore / totalWeight : 0,
    reasons: scores
  };
}

/**
 * Match a mentor to an academic student
 * @param mentor Mentor profile
 * @param student Academic student profile
 * @returns Match score and reasons
 */
export function matchMentorToAcademicStudent(mentor: any, student: any): { score: number, reasons: Record<string, number> } {
  // Reuse the student-to-mentor logic but with potentially different weights
  const { score, reasons } = matchAcademicStudentToMentor(student, mentor);

  // Adjust the score based on mentor-specific criteria
  const mentorSpecificScores: Record<string, number> = {
    academicPotential: evaluateAcademicPotential(student, mentor),
    mentorshipFit: evaluateStudentMentorshipFit(student, mentor)
  };

  const mentorSpecificWeights = {
    academicPotential: 0.2,
    mentorshipFit: 0.2
  };

  let adjustedScore = score * 0.6; // Base score is 60% of the total
  let totalWeight = 0.6;

  for (const [key, weight] of Object.entries(mentorSpecificWeights)) {
    adjustedScore += mentorSpecificScores[key] * weight;
    totalWeight += weight;
    reasons[key] = mentorSpecificScores[key];
  }

  return {
    score: totalWeight > 0 ? adjustedScore / totalWeight : 0,
    reasons
  };
}

/**
 * Match an academic student to an academic institution
 * @param student Academic student profile
 * @param institution Academic institution profile
 * @returns Match score and reasons
 */
export function matchAcademicStudentToInstitution(student: any, institution: any): { score: number, reasons: Record<string, number> } {
  const scores: Record<string, number> = {
    field: arrayIntersectionScore(
      [student.field_of_study] || [],
      institution.offered_fields || []
    ),
    research: arrayIntersectionScore(
      student.research_interests || [],
      institution.research_areas || []
    ),
    program: evaluateProgramFit(student, institution),
    location: arrayIntersectionScore(
      student.preferred_locations || [],
      [institution.location] || []
    ),
    goals: arrayIntersectionScore(
      student.academic_goals || [],
      institution.program_outcomes || []
    )
  };

  const weights = {
    field: 0.3,
    research: 0.25,
    program: 0.2,
    location: 0.15,
    goals: 0.1
  };

  let totalScore = 0;
  let totalWeight = 0;

  for (const [key, weight] of Object.entries(weights)) {
    totalScore += scores[key] * weight;
    totalWeight += weight;
  }

  return {
    score: totalWeight > 0 ? totalScore / totalWeight : 0,
    reasons: scores
  };
}

/**
 * Evaluate academic potential of a student for a mentor
 */
function evaluateAcademicPotential(student: any, mentor: any): number {
  // Simple implementation - can be enhanced
  if (!student.academic_achievements || !student.research_interests) return 0.5;

  let score = 0.5;

  // Check for academic achievements
  if (Array.isArray(student.academic_achievements) && student.academic_achievements.length > 0) {
    score += 0.2;
  } else if (typeof student.academic_achievements === 'string' && student.academic_achievements.length > 20) {
    score += 0.2;
  }

  // Check for research interests depth
  if (Array.isArray(student.research_interests) && student.research_interests.length >= 3) {
    score += 0.2;
  }

  // Check for academic goals clarity
  if (student.academic_goals && student.academic_goals.length > 0) {
    score += 0.1;
  }

  return Math.min(score, 1); // Cap at 1.0
}

/**
 * Evaluate mentorship fit between student and mentor
 */
function evaluateStudentMentorshipFit(student: any, mentor: any): number {
  // Simple implementation - can be enhanced
  if (!student.learning_preferences || !mentor.mentoring_approach) return 0.5;

  let score = 0.5;

  // Check if student's learning preferences align with mentor's approach
  const learningPrefs = Array.isArray(student.learning_preferences)
    ? student.learning_preferences.join(' ').toLowerCase()
    : String(student.learning_preferences).toLowerCase();

  const mentoringApproach = Array.isArray(mentor.mentoring_approach)
    ? mentor.mentoring_approach.join(' ').toLowerCase()
    : String(mentor.mentoring_approach).toLowerCase();

  // Check for keyword matches
  const keywords = ['hands-on', 'theoretical', 'practical', 'research', 'guidance', 'independent'];

  for (const keyword of keywords) {
    if (learningPrefs.includes(keyword) && mentoringApproach.includes(keyword)) {
      score += 0.1;
    }
  }

  return Math.min(score, 1); // Cap at 1.0
}

/**
 * Evaluate program fit between student and institution
 */
function evaluateProgramFit(student: any, institution: any): number {
  // Simple implementation - can be enhanced
  if (!student.program_preferences || !institution.offered_programs) return 0.5;

  const studentPrefs = Array.isArray(student.program_preferences)
    ? student.program_preferences
    : [student.program_preferences];

  const institutionPrograms = Array.isArray(institution.offered_programs)
    ? institution.offered_programs
    : [institution.offered_programs];

  // Check for direct program matches
  const directMatches = studentPrefs.filter(pref =>
    institutionPrograms.some(prog =>
      prog.toLowerCase().includes(pref.toLowerCase())
    )
  );

  if (directMatches.length > 0) {
    return 0.5 + (0.5 * (directMatches.length / studentPrefs.length));
  }

  return 0.5; // Neutral score if no direct matches
}

/**
 * Match an academic institution to a student
 * @param institution Academic institution profile
 * @param student Academic student profile
 * @returns Match score and reasons
 */
export function matchInstitutionToStudent(institution: any, student: any): { score: number, reasons: Record<string, number> } {
  // Reuse the student-to-institution logic but with potentially different weights
  const { score, reasons } = matchAcademicStudentToInstitution(student, institution);

  // Adjust the score based on institution-specific criteria
  const institutionSpecificScores: Record<string, number> = {
    studentFit: evaluateStudentFit(student, institution),
    admissionCriteria: evaluateAdmissionCriteria(student, institution)
  };

  const institutionSpecificWeights = {
    studentFit: 0.2,
    admissionCriteria: 0.2
  };

  let adjustedScore = score * 0.6; // Base score is 60% of the total
  let totalWeight = 0.6;

  for (const [key, weight] of Object.entries(institutionSpecificWeights)) {
    adjustedScore += institutionSpecificScores[key] * weight;
    totalWeight += weight;
    reasons[key] = institutionSpecificScores[key];
  }

  return {
    score: totalWeight > 0 ? adjustedScore / totalWeight : 0,
    reasons
  };
}

/**
 * Match an industry expert to an innovator
 * @param expert Industry expert profile
 * @param innovator Innovator profile
 * @returns Match score and reasons
 */
export function matchIndustryExpertToInnovator(expert: any, innovator: any): { score: number, reasons: Record<string, number> } {
  const scores: Record<string, number> = {
    industry: arrayIntersectionScore(
      expert.industry_expertise || [],
      innovator.industry || []
    ),
    expertise: arrayIntersectionScore(
      expert.expertise_areas || [],
      innovator.current_challenges || []
    ),
    stage: stageCompatibilityScore(
      expert.preferred_project_stage || '',
      innovator.innovation_stage || ''
    ),
    goals: arrayIntersectionScore(
      expert.collaboration_interests || [],
      innovator.short_term_goals || []
    ),
    location: arrayIntersectionScore(
      expert.preferred_locations || [],
      innovator.preferred_locations || []
    )
  };

  const weights = {
    industry: 0.35,
    expertise: 0.3,
    stage: 0.15,
    goals: 0.1,
    location: 0.1
  };

  let totalScore = 0;
  let totalWeight = 0;

  for (const [key, weight] of Object.entries(weights)) {
    totalScore += scores[key] * weight;
    totalWeight += weight;
  }

  return {
    score: totalWeight > 0 ? totalScore / totalWeight : 0,
    reasons: scores
  };
}

/**
 * Match an innovator to an industry expert
 * @param innovator Innovator profile
 * @param expert Industry expert profile
 * @returns Match score and reasons
 */
export function matchInnovatorToIndustryExpert(innovator: any, expert: any): { score: number, reasons: Record<string, number> } {
  // Reuse the expert-to-innovator logic but with potentially different weights
  const { score, reasons } = matchIndustryExpertToInnovator(expert, innovator);

  // Adjust the score based on innovator-specific criteria
  const innovatorSpecificScores: Record<string, number> = {
    expertiseNeed: evaluateExpertiseNeed(innovator, expert),
    innovationRelevance: evaluateInnovationRelevance(innovator, expert)
  };

  const innovatorSpecificWeights = {
    expertiseNeed: 0.2,
    innovationRelevance: 0.2
  };

  let adjustedScore = score * 0.6; // Base score is 60% of the total
  let totalWeight = 0.6;

  for (const [key, weight] of Object.entries(innovatorSpecificWeights)) {
    adjustedScore += innovatorSpecificScores[key] * weight;
    totalWeight += weight;
    reasons[key] = innovatorSpecificScores[key];
  }

  return {
    score: totalWeight > 0 ? adjustedScore / totalWeight : 0,
    reasons
  };
}

/**
 * Evaluate student fit for an institution
 */
function evaluateStudentFit(student: any, institution: any): number {
  // Simple implementation - can be enhanced
  if (!student.academic_profile || !institution.student_profile) return 0.5;

  let score = 0.5;

  // Check if student's profile matches institution's target student profile
  const studentProfile = typeof student.academic_profile === 'string'
    ? student.academic_profile.toLowerCase()
    : '';

  const targetProfile = typeof institution.student_profile === 'string'
    ? institution.student_profile.toLowerCase()
    : '';

  // Check for keyword matches
  const keywords = ['research', 'practical', 'innovative', 'leadership', 'entrepreneurial', 'academic'];

  for (const keyword of keywords) {
    if (studentProfile.includes(keyword) && targetProfile.includes(keyword)) {
      score += 0.1;
    }
  }

  return Math.min(score, 1); // Cap at 1.0
}

/**
 * Evaluate admission criteria match
 */
function evaluateAdmissionCriteria(student: any, institution: any): number {
  // Simple implementation - can be enhanced
  if (!student.academic_achievements || !institution.admission_criteria) return 0.5;

  let score = 0.5;

  // Check for academic achievements that match admission criteria
  const achievements = Array.isArray(student.academic_achievements)
    ? student.academic_achievements.join(' ').toLowerCase()
    : String(student.academic_achievements).toLowerCase();

  const criteria = Array.isArray(institution.admission_criteria)
    ? institution.admission_criteria.join(' ').toLowerCase()
    : String(institution.admission_criteria).toLowerCase();

  // Check for keyword matches
  const keywords = ['gpa', 'score', 'test', 'degree', 'qualification', 'experience', 'project'];

  for (const keyword of keywords) {
    if (achievements.includes(keyword) && criteria.includes(keyword)) {
      score += 0.1;
    }
  }

  return Math.min(score, 1); // Cap at 1.0
}

/**
 * Evaluate expertise need for an innovator
 */
function evaluateExpertiseNeed(innovator: any, expert: any): number {
  // Simple implementation - can be enhanced
  if (!innovator.current_challenges || !expert.expertise_areas) return 0.5;

  let score = 0.5;

  // Check if innovator's challenges match expert's expertise
  const challenges = Array.isArray(innovator.current_challenges)
    ? innovator.current_challenges
    : [innovator.current_challenges];

  const expertise = Array.isArray(expert.expertise_areas)
    ? expert.expertise_areas
    : [expert.expertise_areas];

  // Calculate direct matches
  const directMatches = challenges.filter(challenge =>
    expertise.some(exp =>
      exp.toLowerCase().includes(challenge.toLowerCase()) ||
      challenge.toLowerCase().includes(exp.toLowerCase())
    )
  );

  if (directMatches.length > 0) {
    score += 0.5 * (directMatches.length / challenges.length);
  }

  return Math.min(score, 1); // Cap at 1.0
}

/**
 * Evaluate innovation relevance for an expert
 */
function evaluateInnovationRelevance(innovator: any, expert: any): number {
  // Simple implementation - can be enhanced
  if (!innovator.innovation_description || !expert.interests) return 0.5;

  let score = 0.5;

  // Check if innovation description matches expert's interests
  const description = innovator.innovation_description.toLowerCase();
  const interests = Array.isArray(expert.interests)
    ? expert.interests.join(' ').toLowerCase()
    : String(expert.interests).toLowerCase();

  // Check for keyword matches from expert's interests in innovation description
  const interestWords = interests.split(/\s+/).filter(word => word.length > 4);

  let matches = 0;
  for (const word of interestWords) {
    if (description.includes(word)) {
      matches++;
    }
  }

  if (interestWords.length > 0) {
    score += 0.5 * (matches / Math.min(interestWords.length, 10));
  }

  return Math.min(score, 1); // Cap at 1.0
}

/**
 * Match an organisation to an innovator
 * @param organisation Organisation profile
 * @param innovator Innovator profile
 * @returns Match score and reasons
 */
export function matchOrganisationToInnovator(organisation: any, innovator: any): { score: number, reasons: Record<string, number> } {
  const scores: Record<string, number> = {
    industry: arrayIntersectionScore(
      organisation.industry_focus || [],
      innovator.industry || []
    ),
    innovation: arrayIntersectionScore(
      organisation.innovation_interests || [],
      [innovator.innovation_area] || []
    ),
    stage: stageCompatibilityScore(
      organisation.preferred_partnership_stage || '',
      innovator.innovation_stage || ''
    ),
    goals: arrayIntersectionScore(
      organisation.partnership_goals || [],
      innovator.short_term_goals || []
    ),
    location: arrayIntersectionScore(
      organisation.locations || [],
      innovator.preferred_locations || []
    )
  };

  const weights = {
    industry: 0.3,
    innovation: 0.25,
    stage: 0.2,
    goals: 0.15,
    location: 0.1
  };

  let totalScore = 0;
  let totalWeight = 0;

  for (const [key, weight] of Object.entries(weights)) {
    totalScore += scores[key] * weight;
    totalWeight += weight;
  }

  return {
    score: totalWeight > 0 ? totalScore / totalWeight : 0,
    reasons: scores
  };
}

/**
 * Match an innovator to an organisation
 * @param innovator Innovator profile
 * @param organisation Organisation profile
 * @returns Match score and reasons
 */
export function matchInnovatorToOrganisation(innovator: any, organisation: any): { score: number, reasons: Record<string, number> } {
  // Reuse the organisation-to-innovator logic but with potentially different weights
  const { score, reasons } = matchOrganisationToInnovator(organisation, innovator);

  // Adjust the score based on innovator-specific criteria
  const innovatorSpecificScores: Record<string, number> = {
    partnershipValue: evaluatePartnershipValue(innovator, organisation),
    resourceFit: evaluateResourceFit(innovator, organisation)
  };

  const innovatorSpecificWeights = {
    partnershipValue: 0.2,
    resourceFit: 0.2
  };

  let adjustedScore = score * 0.6; // Base score is 60% of the total
  let totalWeight = 0.6;

  for (const [key, weight] of Object.entries(innovatorSpecificWeights)) {
    adjustedScore += innovatorSpecificScores[key] * weight;
    totalWeight += weight;
    reasons[key] = innovatorSpecificScores[key];
  }

  return {
    score: totalWeight > 0 ? adjustedScore / totalWeight : 0,
    reasons
  };
}

/**
 * Match an organisation to an investor
 * @param organisation Organisation profile
 * @param investor Investor profile
 * @returns Match score and reasons
 */
export function matchOrganisationToInvestor(organisation: any, investor: any): { score: number, reasons: Record<string, number> } {
  const scores: Record<string, number> = {
    industry: arrayIntersectionScore(
      organisation.industry_focus || [],
      investor.investment_focus || []
    ),
    stage: arrayIntersectionScore(
      [organisation.organisation_stage] || [],
      investor.investment_stage || []
    ),
    funding: numericRangeMatch(
      organisation.funding_needs || 0,
      {
        min: parseTicketSize(investor.ticket_size, 'min'),
        max: parseTicketSize(investor.ticket_size, 'max')
      }
    ),
    goals: arrayIntersectionScore(
      organisation.partnership_goals || [],
      investor.collaboration_interests || []
    ),
    location: arrayIntersectionScore(
      organisation.locations || [],
      investor.preferred_locations || []
    )
  };

  const weights = {
    industry: 0.3,
    stage: 0.25,
    funding: 0.2,
    goals: 0.15,
    location: 0.1
  };

  let totalScore = 0;
  let totalWeight = 0;

  for (const [key, weight] of Object.entries(weights)) {
    totalScore += scores[key] * weight;
    totalWeight += weight;
  }

  return {
    score: totalWeight > 0 ? totalScore / totalWeight : 0,
    reasons: scores
  };
}

/**
 * Evaluate partnership value between innovator and organisation
 */
function evaluatePartnershipValue(innovator: any, organisation: any): number {
  // Simple implementation - can be enhanced
  if (!innovator.looking_for || !organisation.partnership_offerings) return 0.5;

  let score = 0.5;

  // Check if innovator's needs match organisation's offerings
  const needs = Array.isArray(innovator.looking_for)
    ? innovator.looking_for.join(' ').toLowerCase()
    : String(innovator.looking_for).toLowerCase();

  const offerings = Array.isArray(organisation.partnership_offerings)
    ? organisation.partnership_offerings.join(' ').toLowerCase()
    : String(organisation.partnership_offerings).toLowerCase();

  // Check for keyword matches
  const keywords = ['funding', 'mentorship', 'resources', 'network', 'market access', 'expertise', 'technology'];

  for (const keyword of keywords) {
    if (needs.includes(keyword) && offerings.includes(keyword)) {
      score += 0.1;
    }
  }

  return Math.min(score, 1); // Cap at 1.0
}

/**
 * Evaluate resource fit between innovator and organisation
 */
function evaluateResourceFit(innovator: any, organisation: any): number {
  // Simple implementation - can be enhanced
  if (!innovator.current_challenges || !organisation.resources) return 0.5;

  let score = 0.5;

  // Check if organisation's resources address innovator's challenges
  const challenges = Array.isArray(innovator.current_challenges)
    ? innovator.current_challenges.join(' ').toLowerCase()
    : String(innovator.current_challenges).toLowerCase();

  const resources = Array.isArray(organisation.resources)
    ? organisation.resources.join(' ').toLowerCase()
    : String(organisation.resources).toLowerCase();

  // Check for keyword matches
  const keywords = ['technology', 'expertise', 'funding', 'facilities', 'equipment', 'data', 'market'];

  for (const keyword of keywords) {
    if (challenges.includes(keyword) && resources.includes(keyword)) {
      score += 0.1;
    }
  }

  return Math.min(score, 1); // Cap at 1.0
}