/**
 * This file contains functions to create a clean slate database schema
 * with tables that allow NULL values and follow a consistent naming convention
 */

import { supabase } from './supabase';

/**
 * Creates the profiles table with NULL values allowed for all fields
 */
export async function createProfilesTable(): Promise<{ success: boolean; message: string }> {
  try {
    console.log('Creating profiles table with NULL values allowed...');

    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        -- Create profiles table if it doesn't exist
        CREATE TABLE IF NOT EXISTS public.profiles (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
            email TEXT,
            first_name TEXT,
            last_name TEXT,
            profile_name TEXT,
            profile_state TEXT,
            profile_type TEXT,
            profile_visibility TEXT,
            role TEXT,
            is_verified BOOLEAN,
            profile_completion FLOAT,
            bio TEXT,
            phone TEXT,
            hear_about_us TEXT[],
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Enable Row Level Security
        ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

        -- Create basic RLS policies
        CREATE POLICY "Users can view their own profile"
            ON public.profiles FOR SELECT
            USING (auth.uid() = user_id);

        CREATE POLICY "Users can update their own profile"
            ON public.profiles FOR UPDATE
            USING (auth.uid() = user_id);

        CREATE POLICY "Users can insert their own profile"
            ON public.profiles FOR INSERT
            WITH CHECK (auth.uid() = user_id);

        -- Grant permissions
        GRANT ALL ON public.profiles TO authenticated;
        GRANT ALL ON public.profiles TO service_role;
      `
    });

    if (error) {
      console.error('Error creating profiles table:', error.message);
      return { success: false, message: error.message };
    }

    return { success: true, message: 'Profiles table created successfully' };
  } catch (err: any) {
    console.error('Error creating profiles table:', err.message);
    return { success: false, message: err.message };
  }
}

/**
 * Creates profile type tables with NULL values allowed for all fields
 */
export async function createProfileTypeTables(): Promise<{ success: boolean; message: string }> {
  try {
    console.log('Creating profile type tables with NULL values allowed...');

    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        -- Create innovator_profiles table
        CREATE TABLE IF NOT EXISTS public.innovator_profiles (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            profile_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
            innovation_area TEXT,
            innovation_stage TEXT,
            innovation_description TEXT,
            team_size INTEGER,
            team_description TEXT,
            funding_needs BOOLEAN,
            funding_amount NUMERIC,
            funding_stage TEXT,
            has_prototype BOOLEAN,
            prototype_description TEXT,
            goals TEXT[],
            challenges TEXT[],
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create investor_profiles table
        CREATE TABLE IF NOT EXISTS public.investor_profiles (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            profile_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
            investment_focus TEXT[],
            investment_stage TEXT[],
            investment_range TEXT,
            previous_investments INTEGER,
            investment_geography TEXT[],
            investment_criteria TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create mentor_profiles table
        CREATE TABLE IF NOT EXISTS public.mentor_profiles (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            profile_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
            areas_of_expertise TEXT[],
            years_of_experience INTEGER,
            mentorship_style TEXT,
            availability TEXT,
            previous_mentees INTEGER,
            expectations TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create professional_profiles table
        CREATE TABLE IF NOT EXISTS public.professional_profiles (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            profile_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
            job_title TEXT,
            company TEXT,
            industry TEXT,
            years_of_experience INTEGER,
            skills TEXT[],
            services_offered TEXT[],
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create industry_expert_profiles table
        CREATE TABLE IF NOT EXISTS public.industry_expert_profiles (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            profile_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
            industry TEXT,
            areas_of_expertise TEXT[],
            years_of_experience INTEGER,
            publications TEXT[],
            speaking_engagements TEXT[],
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create academic_student_profiles table
        CREATE TABLE IF NOT EXISTS public.academic_student_profiles (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            profile_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
            institution TEXT,
            field_of_study TEXT,
            degree_level TEXT,
            graduation_year INTEGER,
            research_interests TEXT[],
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create academic_institution_profiles table
        CREATE TABLE IF NOT EXISTS public.academic_institution_profiles (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            profile_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
            institution_name TEXT,
            institution_type TEXT,
            location TEXT,
            research_areas TEXT[],
            programs_offered TEXT[],

            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create organisation_profiles table
        CREATE TABLE IF NOT EXISTS public.organisation_profiles (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            profile_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
            organisation_name TEXT,
            organisation_type TEXT,
            industry TEXT,
            size TEXT,
            founding_year INTEGER,
            mission TEXT,
            services TEXT[],
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Enable Row Level Security for all profile type tables
        ALTER TABLE public.innovator_profiles ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.investor_profiles ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.mentor_profiles ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.professional_profiles ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.industry_expert_profiles ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.academic_student_profiles ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.academic_institution_profiles ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.organisation_profiles ENABLE ROW LEVEL SECURITY;

        -- Create basic RLS policies for all profile type tables
        DO $$
        DECLARE
            tables TEXT[] := ARRAY['innovator_profiles', 'investor_profiles', 'mentor_profiles',
                                'professional_profiles', 'industry_expert_profiles', 'academic_student_profiles',
                                'academic_institution_profiles', 'organisation_profiles'];
            t TEXT;
        BEGIN
            FOREACH t IN ARRAY tables
            LOOP
                EXECUTE format('
                    CREATE POLICY "Users can view their own %s"
                        ON public.%s FOR SELECT
                        USING (EXISTS (
                            SELECT 1 FROM public.profiles
                            WHERE profiles.id = %s.profile_id
                            AND profiles.user_id = auth.uid()
                        ));

                    CREATE POLICY "Users can update their own %s"
                        ON public.%s FOR UPDATE
                        USING (EXISTS (
                            SELECT 1 FROM public.profiles
                            WHERE profiles.id = %s.profile_id
                            AND profiles.user_id = auth.uid()
                        ));

                    CREATE POLICY "Users can insert their own %s"
                        ON public.%s FOR INSERT
                        WITH CHECK (EXISTS (
                            SELECT 1 FROM public.profiles
                            WHERE profiles.id = %s.profile_id
                            AND profiles.user_id = auth.uid()
                        ));

                    -- Grant permissions
                    GRANT ALL ON public.%s TO authenticated;
                    GRANT ALL ON public.%s TO service_role;
                ', t, t, t, t, t, t, t, t, t, t, t);
            END LOOP;
        END$$;

        -- Create updated_at trigger function if it doesn't exist
        CREATE OR REPLACE FUNCTION public.update_updated_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = NOW();
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;

        -- Create triggers for all profile tables
        DO $$
        DECLARE
            tables TEXT[] := ARRAY['profiles', 'innovator_profiles', 'investor_profiles', 'mentor_profiles',
                                'professional_profiles', 'industry_expert_profiles', 'academic_student_profiles',
                                'academic_institution_profiles', 'organisation_profiles'];
            t TEXT;
        BEGIN
            FOREACH t IN ARRAY tables
            LOOP
                EXECUTE format('
                    DROP TRIGGER IF EXISTS update_%s_updated_at ON public.%s;
                    CREATE TRIGGER update_%s_updated_at
                    BEFORE UPDATE ON public.%s
                    FOR EACH ROW
                    EXECUTE FUNCTION public.update_updated_at_column();
                ', t, t, t, t);
            END LOOP;
        END$$;
      `
    });

    if (error) {
      console.error('Error creating profile type tables:', error.message);
      return { success: false, message: error.message };
    }

    return { success: true, message: 'Profile type tables created successfully' };
  } catch (err: any) {
    console.error('Error creating profile type tables:', err.message);
    return { success: false, message: err.message };
  }
}

/**
 * Creates a clean slate database schema
 */
export async function createCleanSlateDatabase(): Promise<{ success: boolean; message: string }> {
  try {
    // Step 1: Create the profiles table
    const profilesResult = await createProfilesTable();
    if (!profilesResult.success) {
      return profilesResult;
    }

    // Step 2: Create the profile type tables
    const profileTypesResult = await createProfileTypeTables();
    if (!profileTypesResult.success) {
      return profileTypesResult;
    }

    // Step 3: Refresh the schema cache
    const { error } = await supabase.rpc('exec_sql', {
      sql: 'SELECT pg_catalog.set_config(\'search_path\', \'public\', false);'
    });

    if (error) {
      console.error('Error refreshing schema cache:', error.message);
      return { success: false, message: error.message };
    }

    return { success: true, message: 'Clean slate database created successfully' };
  } catch (err: any) {
    console.error('Error creating clean slate database:', err.message);
    return { success: false, message: err.message };
  }
}
