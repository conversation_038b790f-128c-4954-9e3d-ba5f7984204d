// Type definitions for Article
// This is a minimal type declaration file to fix the TypeScript error

declare interface Article {
  id?: number;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  author: string;
  authorRole?: string;
  date: string;
  category: string;
  categoryColor?: string;
  textColor?: string;
  readTime?: string;
  tags?: string[];
  coverImage?: string;
}
