<template>
  <q-btn
    :flat="flat"
    :dense="dense"
    :size="size"
    :color="color"
    :icon="icon"
    :label="displayLabel"
    @click="handleContact"
    :loading="loading"
  >
    <q-tooltip v-if="!displayLabel">{{ tooltip }}</q-tooltip>
  </q-btn>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useContentInteractions } from '../../composables/useContentInteractions';

const props = defineProps({
  // Content identification
  contentId: {
    type: [Number, String],
    required: true
  },
  contentType: {
    type: String,
    required: true,
    validator: (value: string) => ['post', 'listing', 'event', 'group', 'profile'].includes(value)
  },
  contentData: {
    type: Object,
    required: true
  },
  
  // Button styling
  flat: {
    type: Boolean,
    default: false
  },
  dense: {
    type: Boolean,
    default: false
  },
  size: {
    type: String,
    default: 'md'
  },
  color: {
    type: String,
    default: 'primary'
  },
  icon: {
    type: String,
    default: 'email'
  },
  label: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['contact']);

// Loading state
const loading = ref(false);

// Use content interactions composable
const contentInteractions = useContentInteractions();

// Computed properties
const tooltip = computed(() => {
  switch (props.contentType) {
    case 'listing':
      return 'Contact Seller';
    case 'post':
      return 'Contact Author';
    case 'event':
      return 'Contact Organizer';
    case 'group':
      return 'Contact Admin';
    case 'profile':
      return 'Send Message';
    default:
      return 'Contact';
  }
});

// Display label - use provided label or default based on content type
const displayLabel = computed(() => {
  if (props.label) return props.label;

  switch (props.contentType) {
    case 'listing':
      return 'Contact Seller';
    case 'post':
      return 'Contact Author';
    case 'event':
      return 'Contact Organizer';
    case 'group':
      return 'Contact Admin';
    case 'profile':
      return 'Send Message';
    default:
      return 'Contact';
  }
});

// Handler
async function handleContact() {
  loading.value = true;
  try {
    await contentInteractions.contactUser(
      props.contentId,
      props.contentType as any,
      props.contentData
    );
    emit('contact', props.contentId);
  } finally {
    loading.value = false;
  }
}
</script>

<style scoped>
/* Add any specific styling if needed */
</style>
