/**
 * Unified Cache Service
 * 
 * Provides a centralized caching system with consistent TTL management,
 * multiple storage backends, cache invalidation, and performance monitoring.
 */

import { ref, computed } from 'vue'

export type StorageBackend = 'memory' | 'localStorage' | 'sessionStorage'

export interface CacheConfig {
  ttl: number // Time to live in milliseconds
  storage: StorageBackend
  maxSize?: number // Maximum number of entries (for memory storage)
  invalidationPatterns?: string[] // Patterns for automatic invalidation
}

export interface CacheEntry<T = any> {
  value: T
  timestamp: number
  ttl: number
  accessCount: number
  lastAccessed: number
}

export interface CacheStats {
  hits: number
  misses: number
  hitRate: number
  totalEntries: number
  memoryEntries: number
  localStorageEntries: number
  sessionStorageEntries: number
  memoryUsage: number // Estimated memory usage in bytes
}

export interface InvalidationPattern {
  pattern: string
  regex: RegExp
}

// Default configurations for different data types
export const DEFAULT_CACHE_CONFIGS: Record<string, CacheConfig> = {
  profile: {
    ttl: 5 * 60 * 1000, // 5 minutes
    storage: 'memory',
    maxSize: 100,
    invalidationPatterns: ['profile:*', 'user:*']
  },
  userStats: {
    ttl: 2 * 60 * 1000, // 2 minutes
    storage: 'memory',
    maxSize: 50,
    invalidationPatterns: ['stats:*', 'user:*']
  },
  feed: {
    ttl: 30 * 1000, // 30 seconds
    storage: 'memory',
    maxSize: 20,
    invalidationPatterns: ['feed:*']
  },
  auth: {
    ttl: 5 * 60 * 1000, // 5 minutes
    storage: 'sessionStorage',
    invalidationPatterns: ['auth:*', 'user:*']
  },
  messaging: {
    ttl: 1 * 60 * 1000, // 1 minute
    storage: 'memory',
    maxSize: 30,
    invalidationPatterns: ['message:*', 'conversation:*']
  }
}

class UnifiedCacheService {
  private memoryCache = new Map<string, CacheEntry>()
  private stats = ref<CacheStats>({
    hits: 0,
    misses: 0,
    hitRate: 0,
    totalEntries: 0,
    memoryEntries: 0,
    localStorageEntries: 0,
    sessionStorageEntries: 0,
    memoryUsage: 0
  })
  private invalidationPatterns: InvalidationPattern[] = []
  private cleanupInterval: number | null = null

  constructor() {
    this.startCleanupInterval()
    this.loadInvalidationPatterns()
  }

  /**
   * Get a value from cache
   */
  get<T>(key: string, config?: Partial<CacheConfig>): T | null {
    const fullConfig = this.getConfig(key, config)
    const entry = this.getEntry<T>(key, fullConfig.storage)

    if (!entry) {
      this.recordMiss()
      return null
    }

    // Check if entry has expired
    if (this.isExpired(entry)) {
      this.delete(key, fullConfig.storage)
      this.recordMiss()
      return null
    }

    // Update access statistics
    entry.accessCount++
    entry.lastAccessed = Date.now()
    this.setEntry(key, entry, fullConfig.storage)
    
    this.recordHit()
    return entry.value
  }

  /**
   * Set a value in cache
   */
  set<T>(key: string, value: T, config?: Partial<CacheConfig>): void {
    const fullConfig = this.getConfig(key, config)
    
    const entry: CacheEntry<T> = {
      value,
      timestamp: Date.now(),
      ttl: fullConfig.ttl,
      accessCount: 1,
      lastAccessed: Date.now()
    }

    // Check memory limits for memory storage
    if (fullConfig.storage === 'memory' && fullConfig.maxSize) {
      this.enforceMemoryLimit(fullConfig.maxSize)
    }

    this.setEntry(key, entry, fullConfig.storage)
    this.updateStats()
  }

  /**
   * Delete a specific key from cache
   */
  delete(key: string, storage?: StorageBackend): boolean {
    if (storage) {
      return this.deleteFromStorage(key, storage)
    }

    // Delete from all storages if no specific storage provided
    let deleted = false
    deleted = this.deleteFromStorage(key, 'memory') || deleted
    deleted = this.deleteFromStorage(key, 'localStorage') || deleted
    deleted = this.deleteFromStorage(key, 'sessionStorage') || deleted
    
    if (deleted) {
      this.updateStats()
    }
    
    return deleted
  }

  /**
   * Invalidate cache entries matching a pattern
   */
  invalidate(pattern: string): number {
    let invalidatedCount = 0
    const regex = new RegExp(pattern.replace('*', '.*'))

    // Invalidate from memory cache
    for (const key of this.memoryCache.keys()) {
      if (regex.test(key)) {
        this.memoryCache.delete(key)
        invalidatedCount++
      }
    }

    // Invalidate from localStorage
    invalidatedCount += this.invalidateFromWebStorage(localStorage, regex)

    // Invalidate from sessionStorage
    invalidatedCount += this.invalidateFromWebStorage(sessionStorage, regex)

    this.updateStats()
    return invalidatedCount
  }

  /**
   * Clear all cache entries from specified storage or all storages
   */
  clear(storage?: StorageBackend): void {
    if (!storage) {
      this.memoryCache.clear()
      this.clearWebStorage(localStorage)
      this.clearWebStorage(sessionStorage)
    } else {
      switch (storage) {
        case 'memory':
          this.memoryCache.clear()
          break
        case 'localStorage':
          this.clearWebStorage(localStorage)
          break
        case 'sessionStorage':
          this.clearWebStorage(sessionStorage)
          break
      }
    }
    this.updateStats()
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    this.updateStats()
    return { ...this.stats.value }
  }

  /**
   * Add invalidation pattern
   */
  addInvalidationPattern(pattern: string): void {
    const regex = new RegExp(pattern.replace('*', '.*'))
    this.invalidationPatterns.push({ pattern, regex })
  }

  /**
   * Trigger invalidation based on data change
   */
  triggerInvalidation(dataType: string, entityId?: string): void {
    const patterns = [
      `${dataType}:*`,
      entityId ? `${dataType}:${entityId}` : null,
      entityId ? `*:${entityId}` : null
    ].filter(Boolean) as string[]

    patterns.forEach(pattern => this.invalidate(pattern))
  }

  /**
   * Cleanup expired entries and update statistics
   */
  cleanup(): void {
    let cleanedCount = 0

    // Cleanup memory cache
    for (const [key, entry] of this.memoryCache.entries()) {
      if (this.isExpired(entry)) {
        this.memoryCache.delete(key)
        cleanedCount++
      }
    }

    // Cleanup web storage (localStorage and sessionStorage)
    cleanedCount += this.cleanupWebStorage(localStorage)
    cleanedCount += this.cleanupWebStorage(sessionStorage)

    if (cleanedCount > 0) {
      console.log(`UnifiedCache: Cleaned up ${cleanedCount} expired entries`)
      this.updateStats()
    }
  }

  /**
   * Destroy the cache service and cleanup resources
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
    }
    this.clear()
  }

  // Private methods

  private getConfig(key: string, config?: Partial<CacheConfig>): CacheConfig {
    // Try to determine config from key prefix
    const keyPrefix = key.split(':')[0]
    const defaultConfig = DEFAULT_CACHE_CONFIGS[keyPrefix] || DEFAULT_CACHE_CONFIGS.profile
    
    return {
      ...defaultConfig,
      ...config
    }
  }

  private getEntry<T>(key: string, storage: StorageBackend): CacheEntry<T> | null {
    switch (storage) {
      case 'memory':
        return this.memoryCache.get(key) || null
      case 'localStorage':
        return this.getFromWebStorage<T>(localStorage, key)
      case 'sessionStorage':
        return this.getFromWebStorage<T>(sessionStorage, key)
      default:
        return null
    }
  }

  private setEntry<T>(key: string, entry: CacheEntry<T>, storage: StorageBackend): void {
    switch (storage) {
      case 'memory':
        this.memoryCache.set(key, entry)
        break
      case 'localStorage':
        this.setToWebStorage(localStorage, key, entry)
        break
      case 'sessionStorage':
        this.setToWebStorage(sessionStorage, key, entry)
        break
    }
  }

  private getFromWebStorage<T>(storage: Storage, key: string): CacheEntry<T> | null {
    try {
      const item = storage.getItem(`cache:${key}`)
      return item ? JSON.parse(item) : null
    } catch (error) {
      console.warn(`UnifiedCache: Error reading from ${storage === localStorage ? 'localStorage' : 'sessionStorage'}:`, error)
      return null
    }
  }

  private setToWebStorage<T>(storage: Storage, key: string, entry: CacheEntry<T>): void {
    try {
      storage.setItem(`cache:${key}`, JSON.stringify(entry))
    } catch (error) {
      console.warn(`UnifiedCache: Error writing to ${storage === localStorage ? 'localStorage' : 'sessionStorage'}:`, error)
    }
  }

  private deleteFromStorage(key: string, storage: StorageBackend): boolean {
    switch (storage) {
      case 'memory':
        return this.memoryCache.delete(key)
      case 'localStorage':
        return this.deleteFromWebStorage(localStorage, key)
      case 'sessionStorage':
        return this.deleteFromWebStorage(sessionStorage, key)
      default:
        return false
    }
  }

  private deleteFromWebStorage(storage: Storage, key: string): boolean {
    const fullKey = `cache:${key}`
    const existed = storage.getItem(fullKey) !== null
    storage.removeItem(fullKey)
    return existed
  }

  private invalidateFromWebStorage(storage: Storage, regex: RegExp): number {
    let count = 0
    const keysToDelete: string[] = []
    
    for (let i = 0; i < storage.length; i++) {
      const key = storage.key(i)
      if (key?.startsWith('cache:')) {
        const cacheKey = key.substring(6) // Remove 'cache:' prefix
        if (regex.test(cacheKey)) {
          keysToDelete.push(key)
        }
      }
    }
    
    keysToDelete.forEach(key => {
      storage.removeItem(key)
      count++
    })
    
    return count
  }

  private clearWebStorage(storage: Storage): void {
    const keysToDelete: string[] = []
    
    for (let i = 0; i < storage.length; i++) {
      const key = storage.key(i)
      if (key?.startsWith('cache:')) {
        keysToDelete.push(key)
      }
    }
    
    keysToDelete.forEach(key => storage.removeItem(key))
  }

  private cleanupWebStorage(storage: Storage): number {
    let count = 0
    const keysToDelete: string[] = []
    
    for (let i = 0; i < storage.length; i++) {
      const key = storage.key(i)
      if (key?.startsWith('cache:')) {
        try {
          const entry = JSON.parse(storage.getItem(key) || '{}')
          if (this.isExpired(entry)) {
            keysToDelete.push(key)
          }
        } catch (error) {
          // Invalid entry, remove it
          keysToDelete.push(key)
        }
      }
    }
    
    keysToDelete.forEach(key => {
      storage.removeItem(key)
      count++
    })
    
    return count
  }

  private isExpired(entry: CacheEntry): boolean {
    return Date.now() - entry.timestamp > entry.ttl
  }

  private enforceMemoryLimit(maxSize: number): void {
    if (this.memoryCache.size >= maxSize) {
      // Remove least recently used entries
      const entries = Array.from(this.memoryCache.entries())
      entries.sort((a, b) => a[1].lastAccessed - b[1].lastAccessed)
      
      const toRemove = Math.ceil(maxSize * 0.1) // Remove 10% of entries
      for (let i = 0; i < toRemove && entries.length > 0; i++) {
        this.memoryCache.delete(entries[i][0])
      }
    }
  }

  private recordHit(): void {
    this.stats.value.hits++
    this.updateHitRate()
  }

  private recordMiss(): void {
    this.stats.value.misses++
    this.updateHitRate()
  }

  private updateHitRate(): void {
    const total = this.stats.value.hits + this.stats.value.misses
    this.stats.value.hitRate = total > 0 ? this.stats.value.hits / total : 0
  }

  private updateStats(): void {
    this.stats.value.memoryEntries = this.memoryCache.size
    this.stats.value.localStorageEntries = this.countWebStorageEntries(localStorage)
    this.stats.value.sessionStorageEntries = this.countWebStorageEntries(sessionStorage)
    this.stats.value.totalEntries = 
      this.stats.value.memoryEntries + 
      this.stats.value.localStorageEntries + 
      this.stats.value.sessionStorageEntries
    
    // Estimate memory usage (rough calculation)
    this.stats.value.memoryUsage = this.estimateMemoryUsage()
  }

  private countWebStorageEntries(storage: Storage): number {
    let count = 0
    for (let i = 0; i < storage.length; i++) {
      const key = storage.key(i)
      if (key?.startsWith('cache:')) {
        count++
      }
    }
    return count
  }

  private estimateMemoryUsage(): number {
    let usage = 0
    for (const [key, entry] of this.memoryCache.entries()) {
      usage += key.length * 2 // Approximate string size
      usage += JSON.stringify(entry).length * 2 // Approximate entry size
    }
    return usage
  }

  private startCleanupInterval(): void {
    // Run cleanup every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup()
    }, 5 * 60 * 1000)
  }

  private loadInvalidationPatterns(): void {
    // Load default invalidation patterns
    Object.values(DEFAULT_CACHE_CONFIGS).forEach(config => {
      config.invalidationPatterns?.forEach(pattern => {
        this.addInvalidationPattern(pattern)
      })
    })
  }
}

// Create singleton instance
const unifiedCacheService = new UnifiedCacheService()

// Export composable function
export function useUnifiedCache() {
  return {
    get: unifiedCacheService.get.bind(unifiedCacheService),
    set: unifiedCacheService.set.bind(unifiedCacheService),
    delete: unifiedCacheService.delete.bind(unifiedCacheService),
    invalidate: unifiedCacheService.invalidate.bind(unifiedCacheService),
    clear: unifiedCacheService.clear.bind(unifiedCacheService),
    getStats: unifiedCacheService.getStats.bind(unifiedCacheService),
    triggerInvalidation: unifiedCacheService.triggerInvalidation.bind(unifiedCacheService),
    cleanup: unifiedCacheService.cleanup.bind(unifiedCacheService),
    stats: computed(() => unifiedCacheService.getStats())
  }
}

// Export service instance for direct access if needed
export { unifiedCacheService }

// Export types
export type { CacheConfig, CacheEntry, CacheStats, InvalidationPattern }
