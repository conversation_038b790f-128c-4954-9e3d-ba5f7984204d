import { supabase } from '../../lib/supabase'

/**
 * Fetch news articles
 * 
 * @param limit The maximum number of articles to fetch
 * @returns The news articles
 */
export async function fetchNews(limit = 10) {
  const { data, error } = await supabase
    .from('news')
    .select('*')
    .order('created_at', { ascending: false })
    .limit(limit)
    
  if (error) {
    throw error
  }
  
  return data || []
}

/**
 * Fetch a single news article by ID
 * 
 * @param id The news article ID
 * @returns The news article
 */
export async function fetchNewsById(id: string) {
  const { data, error } = await supabase
    .from('news')
    .select('*')
    .eq('id', id)
    .single()
    
  if (error) {
    throw error
  }
  
  return data
}

/**
 * Fetch events
 * 
 * @param limit The maximum number of events to fetch
 * @returns The events
 */
export async function fetchEvents(limit = 10) {
  const { data, error } = await supabase
    .from('events')
    .select('*')
    .order('date', { ascending: true })
    .limit(limit)
    
  if (error) {
    throw error
  }
  
  return data || []
}

/**
 * Fetch a single event by ID
 * 
 * @param id The event ID
 * @returns The event
 */
export async function fetchEventById(id: string) {
  const { data, error } = await supabase
    .from('events')
    .select('*')
    .eq('id', id)
    .single()
    
  if (error) {
    throw error
  }
  
  return data
}
