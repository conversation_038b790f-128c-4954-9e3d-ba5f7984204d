# AI Assistant Technical Implementation Guide

## Overview

This document provides detailed technical specifications and implementation steps for enhancing the ZbInnovation AI Assistant with advanced features including authentication integration, user profile access, internet search, call-to-action buttons, and predefined suggestions.

## Architecture Overview

```mermaid
graph TB
    A[AI Chat Component] --> B[Enhanced AI Service]
    B --> C[AI Enhanced Chat Function]
    B --> D[User Context Function]
    B --> E[Web Search Function]
    
    C --> F[DeepSeek API]
    D --> G[Supabase Database]
    E --> H[Google Search API]
    
    A --> I[CTA Button Component]
    A --> J[Suggestions Component]
    
    I --> K[Platform Actions]
    J --> L[Context Engine]
```

## Phase 1: Enhanced Context & Authentication

### 1.1 Enhanced AI Chat Edge Function

Create `supabase/functions/ai-enhanced-chat/index.ts`:

```typescript
import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';

const DEEPSEEK_API_URL = 'https://api.deepseek.com/v1/chat/completions';
const DEEPSEEK_API_KEY = Deno.env.get('DEEPSEEK_API_KEY') || '***********************************';

interface EnhancedChatRequest {
  message: string;
  conversation_history?: ChatMessage[];
  user_context: {
    is_authenticated: boolean;
    profile_type?: string;
    profile_completion?: number;
    current_page?: string;
    user_id?: string;
    profile_data?: any;
  };
}

interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

interface AIResponse {
  response: string;
  actions?: ActionButton[];
  suggestions?: string[];
  conversation_id: string;
}

interface ActionButton {
  type: 'navigation' | 'action' | 'external';
  label: string;
  icon: string;
  url?: string;
  action?: string;
  color?: string;
}

const buildEnhancedSystemPrompt = (userContext: any): string => {
  let prompt = `You are ZbInnovation AI Assistant, an intelligent companion for Zimbabwe's premier digital innovation ecosystem.

PLATFORM CONTEXT:
ZbInnovation connects innovators, investors, mentors, professionals, academic institutions, industry experts, and organizations. The platform facilitates:
- Intelligent matchmaking between different user types
- Innovation project collaboration and funding opportunities
- Mentorship connections and knowledge sharing
- Community building and networking

YOUR ENHANCED CAPABILITIES:
- Provide personalized guidance based on user profile and authentication status
- Suggest specific platform actions with clickable buttons
- Offer contextual recommendations based on current page
- Guide users through platform features and onboarding
- Provide real-time insights and information

RESPONSE FORMAT:
- Always provide helpful, actionable advice
- Include specific call-to-action buttons when relevant
- Suggest next steps based on user context
- Be encouraging and supportive of innovation efforts

USER CONTEXT:`;

  if (!userContext.is_authenticated) {
    prompt += `
- User is NOT authenticated
- Encourage sign-up/login to access full platform features
- Provide general platform information and benefits
- Include login/signup action buttons in responses`;
  } else {
    prompt += `
- User is authenticated
- Profile Type: ${userContext.profile_type || 'Not specified'}
- Profile Completion: ${userContext.profile_completion || 0}%
- Current Page: ${userContext.current_page || 'Unknown'}`;

    if (userContext.profile_completion < 50) {
      prompt += `
- PRIORITY: Encourage profile completion for better platform experience`;
    }

    if (userContext.profile_type) {
      prompt += `
- Tailor responses for ${userContext.profile_type} specific needs and goals`;
    }
  }

  prompt += `

TONE: Professional, encouraging, knowledgeable about African innovation, and specifically familiar with Zimbabwe's business environment.

Always include relevant action buttons in your responses using this format:
[ACTION:type:label:icon:url/action:color]

Example action buttons:
[ACTION:navigation:Complete Profile:person:dashboard/profile:primary]
[ACTION:action:Create Post:edit:create-post:secondary]
[ACTION:external:Learn More:info:https://example.com:accent]`;

  return prompt;
};

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? ''
    );

    const requestBody = await req.json();
    const { message, conversation_history = [], user_context }: EnhancedChatRequest = requestBody;

    if (!message || typeof message !== 'string') {
      return new Response(
        JSON.stringify({ error: 'Message is required and must be a string' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Build enhanced system prompt
    const systemPrompt = buildEnhancedSystemPrompt(user_context);

    // Build conversation messages
    const messages: ChatMessage[] = [
      { role: 'system', content: systemPrompt },
      ...conversation_history,
      { role: 'user', content: message }
    ];

    // Call DeepSeek API
    const response = await fetch(DEEPSEEK_API_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: messages,
        max_tokens: 1500,
        temperature: 0.7,
        stream: false
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`DeepSeek API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    const aiResponse = data.choices?.[0]?.message?.content;

    if (!aiResponse) {
      throw new Error('No response from AI model');
    }

    // Parse action buttons from response
    const actions = parseActionButtons(aiResponse);
    const cleanResponse = aiResponse.replace(/\[ACTION:.*?\]/g, '').trim();

    // Generate contextual suggestions
    const suggestions = generateContextualSuggestions(user_context);

    const result: AIResponse = {
      response: cleanResponse,
      actions,
      suggestions,
      conversation_id: crypto.randomUUID()
    };

    return new Response(
      JSON.stringify(result),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error: any) {
    console.error('Enhanced AI Chat error:', error);

    return new Response(
      JSON.stringify({
        error: 'Failed to process chat request',
        details: error.message,
        timestamp: new Date().toISOString()
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});

function parseActionButtons(response: string): ActionButton[] {
  const actionRegex = /\[ACTION:(.*?):(.*?):(.*?):(.*?):(.*?)\]/g;
  const actions: ActionButton[] = [];
  let match;

  while ((match = actionRegex.exec(response)) !== null) {
    const [, type, label, icon, urlOrAction, color] = match;
    actions.push({
      type: type as 'navigation' | 'action' | 'external',
      label,
      icon,
      url: type === 'external' ? urlOrAction : undefined,
      action: type !== 'external' ? urlOrAction : undefined,
      color: color || 'primary'
    });
  }

  return actions;
}

function generateContextualSuggestions(userContext: any): string[] {
  const suggestions: string[] = [];

  if (!userContext.is_authenticated) {
    suggestions.push(
      "How do I sign up for ZbInnovation?",
      "What are the benefits of joining the platform?",
      "Tell me about the innovation community"
    );
  } else {
    // Authenticated user suggestions
    if (userContext.profile_completion < 50) {
      suggestions.push("How can I complete my profile?");
    }

    switch (userContext.current_page) {
      case 'dashboard':
        suggestions.push(
          "How do I create my first post?",
          "Show me my recent activity",
          "How do I connect with other innovators?"
        );
        break;
      case 'community':
        suggestions.push(
          "Help me find relevant groups to join",
          "What events are coming up?",
          "How do I share my innovation story?"
        );
        break;
      case 'profile':
        suggestions.push(
          "How can I improve my profile visibility?",
          "What should I include in my bio?",
          "How do I showcase my innovations?"
        );
        break;
      default:
        suggestions.push(
          "What can I do on this platform?",
          "How do I get started?",
          "Show me platform features"
        );
    }

    // Profile type specific suggestions
    switch (userContext.profile_type) {
      case 'innovator':
        suggestions.push("How do I find investors for my innovation?");
        break;
      case 'investor':
        suggestions.push("How do I discover promising innovations?");
        break;
      case 'mentor':
        suggestions.push("How do I offer mentorship to innovators?");
        break;
    }
  }

  return suggestions.slice(0, 4); // Limit to 4 suggestions
}
```

### 1.2 Enhanced AI Service

Create `src/services/aiEnhancedService.ts`:

```typescript
import { supabase } from '../lib/supabase';
import { useAuthStore } from '../stores/auth';
import { useRoute } from 'vue-router';

export interface EnhancedChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp?: Date;
  actions?: ActionButton[];
}

export interface ActionButton {
  type: 'navigation' | 'action' | 'external';
  label: string;
  icon: string;
  url?: string;
  action?: string;
  color?: string;
}

export interface EnhancedChatRequest {
  message: string;
  conversation_history?: EnhancedChatMessage[];
  user_context?: UserContext;
}

export interface UserContext {
  is_authenticated: boolean;
  profile_type?: string;
  profile_completion?: number;
  current_page?: string;
  user_id?: string;
  profile_data?: any;
}

export interface EnhancedChatResponse {
  response: string;
  actions?: ActionButton[];
  suggestions?: string[];
  conversation_id?: string;
  error?: string;
}

export async function sendEnhancedChatMessage(request: EnhancedChatRequest): Promise<EnhancedChatResponse> {
  try {
    const { data, error } = await supabase.functions.invoke('ai-enhanced-chat', {
      body: request
    });

    if (error) {
      throw error;
    }

    return data;
  } catch (error: any) {
    console.error('Enhanced AI Chat Service Error:', error);
    return {
      response: '',
      error: error.message || 'Failed to get AI response'
    };
  }
}

export function buildUserContext(): UserContext {
  const authStore = useAuthStore();
  const route = useRoute();

  return {
    is_authenticated: authStore.isAuthenticated,
    profile_type: authStore.user?.user_metadata?.profile_type,
    user_id: authStore.user?.id,
    current_page: getCurrentPageContext(route.path),
    profile_completion: 0, // This would be fetched from profile store
  };
}

function getCurrentPageContext(path: string): string {
  if (path === '/') return 'landing';
  if (path.includes('/dashboard')) return 'dashboard';
  if (path.includes('/virtual-community')) return 'community';
  if (path.includes('/profile')) return 'profile';
  return 'other';
}

export async function executeAction(action: ActionButton): Promise<void> {
  switch (action.type) {
    case 'navigation':
      if (action.url) {
        // Use Vue Router for navigation
        const router = useRouter();
        router.push(action.url);
      }
      break;
    case 'action':
      await handlePlatformAction(action.action!);
      break;
    case 'external':
      if (action.url) {
        window.open(action.url, '_blank');
      }
      break;
  }
}

async function handlePlatformAction(actionType: string): Promise<void> {
  switch (actionType) {
    case 'create-post':
      // Trigger post creation dialog
      break;
    case 'complete-profile':
      // Navigate to profile completion
      break;
    case 'login':
      // Trigger login dialog
      break;
    case 'signup':
      // Trigger signup dialog
      break;
    default:
      console.warn('Unknown platform action:', actionType);
  }
}
```

## Phase 2: User Context Integration

### 2.1 User Context Edge Function

Create `supabase/functions/user-context/index.ts`:

```typescript
import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';

interface UserContextRequest {
  user_id: string;
  include_profile?: boolean;
  include_activity?: boolean;
  include_connections?: boolean;
}

interface UserContextResponse {
  user_id: string;
  profile?: any;
  activity?: any[];
  connections?: any[];
  stats?: {
    profile_completion: number;
    posts_count: number;
    connections_count: number;
    activity_count: number;
  };
}

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const { user_id, include_profile = true, include_activity = false, include_connections = false }: UserContextRequest = await req.json();

    if (!user_id) {
      return new Response(
        JSON.stringify({ error: 'User ID is required' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    const result: UserContextResponse = { user_id };

    // Fetch profile data
    if (include_profile) {
      const { data: profile, error: profileError } = await supabase
        .from('personal_details')
        .select('*')
        .eq('user_id', user_id)
        .single();

      if (!profileError && profile) {
        result.profile = profile;
      }
    }

    // Fetch activity data
    if (include_activity) {
      const { data: activity, error: activityError } = await supabase
        .from('user_activities')
        .select('*')
        .eq('user_id', user_id)
        .order('created_at', { ascending: false })
        .limit(10);

      if (!activityError && activity) {
        result.activity = activity;
      }
    }

    // Fetch connections data
    if (include_connections) {
      const { data: connections, error: connectionsError } = await supabase
        .from('connections')
        .select('*')
        .or(`user_id.eq.${user_id},connected_user_id.eq.${user_id}`)
        .eq('status', 'accepted')
        .limit(10);

      if (!connectionsError && connections) {
        result.connections = connections;
      }
    }

    // Calculate stats
    const stats = await calculateUserStats(supabase, user_id);
    result.stats = stats;

    return new Response(
      JSON.stringify(result),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error: any) {
    console.error('User Context error:', error);

    return new Response(
      JSON.stringify({
        error: 'Failed to fetch user context',
        details: error.message
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});

async function calculateUserStats(supabase: any, userId: string) {
  const stats = {
    profile_completion: 0,
    posts_count: 0,
    connections_count: 0,
    activity_count: 0
  };

  try {
    // Profile completion
    const { data: profile } = await supabase
      .from('personal_details')
      .select('profile_completion')
      .eq('user_id', userId)
      .single();

    if (profile) {
      stats.profile_completion = profile.profile_completion || 0;
    }

    // Posts count
    const { count: postsCount } = await supabase
      .from('posts')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    stats.posts_count = postsCount || 0;

    // Connections count
    const { count: connectionsCount } = await supabase
      .from('connections')
      .select('*', { count: 'exact', head: true })
      .or(`user_id.eq.${userId},connected_user_id.eq.${userId}`)
      .eq('status', 'accepted');

    stats.connections_count = connectionsCount || 0;

    // Activity count
    const { count: activityCount } = await supabase
      .from('user_activities')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    stats.activity_count = activityCount || 0;

  } catch (error) {
    console.error('Error calculating user stats:', error);
  }

  return stats;
}
```

## Phase 3: Web Search Integration

### 3.1 Web Search Edge Function

Create `supabase/functions/web-search/index.ts`:

```typescript
import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { corsHeaders } from '../_shared/cors.ts';

const GOOGLE_SEARCH_API_KEY = Deno.env.get('GOOGLE_SEARCH_API_KEY');
const GOOGLE_SEARCH_ENGINE_ID = Deno.env.get('GOOGLE_SEARCH_ENGINE_ID');

interface SearchRequest {
  query: string;
  search_type?: 'general' | 'news' | 'innovation' | 'zimbabwe';
  max_results?: number;
}

interface SearchResult {
  title: string;
  link: string;
  snippet: string;
  displayLink: string;
}

interface SearchResponse {
  results: SearchResult[];
  total_results: number;
  search_time: number;
}

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { query, search_type = 'general', max_results = 5 }: SearchRequest = await req.json();

    if (!query || typeof query !== 'string') {
      return new Response(
        JSON.stringify({ error: 'Query is required and must be a string' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    if (!GOOGLE_SEARCH_API_KEY || !GOOGLE_SEARCH_ENGINE_ID) {
      return new Response(
        JSON.stringify({ error: 'Search API not configured' }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    const enhancedQuery = enhanceQuery(query, search_type);
    const startTime = Date.now();

    const searchUrl = `https://www.googleapis.com/customsearch/v1?key=${GOOGLE_SEARCH_API_KEY}&cx=${GOOGLE_SEARCH_ENGINE_ID}&q=${encodeURIComponent(enhancedQuery)}&num=${max_results}`;

    const response = await fetch(searchUrl);
    
    if (!response.ok) {
      throw new Error(`Search API error: ${response.status}`);
    }

    const data = await response.json();
    const searchTime = Date.now() - startTime;

    const results: SearchResult[] = (data.items || []).map((item: any) => ({
      title: item.title,
      link: item.link,
      snippet: item.snippet,
      displayLink: item.displayLink
    }));

    const searchResponse: SearchResponse = {
      results,
      total_results: parseInt(data.searchInformation?.totalResults || '0'),
      search_time: searchTime
    };

    return new Response(
      JSON.stringify(searchResponse),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error: any) {
    console.error('Web Search error:', error);

    return new Response(
      JSON.stringify({
        error: 'Failed to perform search',
        details: error.message
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});

function enhanceQuery(query: string, searchType: string): string {
  switch (searchType) {
    case 'news':
      return `${query} news latest`;
    case 'innovation':
      return `${query} innovation technology startup`;
    case 'zimbabwe':
      return `${query} Zimbabwe African innovation`;
    default:
      return query;
  }
}
```

## Implementation Steps

### Step 1: Deploy Enhanced Edge Functions
```bash
# Deploy the enhanced AI chat function
supabase functions deploy ai-enhanced-chat

# Deploy the user context function
supabase functions deploy user-context

# Deploy the web search function
supabase functions deploy web-search

# Set environment variables
supabase secrets set DEEPSEEK_API_KEY=your-deepseek-key
supabase secrets set GOOGLE_SEARCH_API_KEY=your-google-key
supabase secrets set GOOGLE_SEARCH_ENGINE_ID=your-search-engine-id
```

### Step 2: Update Frontend Components
1. Modify `src/components/ai/AIChatAssistant.vue`
2. Create `src/components/ai/AIActionButton.vue`
3. Create `src/components/ai/AISuggestions.vue`
4. Update `src/services/aiService.ts`

### Step 3: Testing and Validation
1. Test authentication integration
2. Validate user context data
3. Test web search functionality
4. Verify CTA button actions
5. Test suggestion system

## Next Steps

Continue with Phase 4 (Call-to-Action System) and Phase 5 (Predefined Suggestions) implementation details in the next sections of this technical guide.
