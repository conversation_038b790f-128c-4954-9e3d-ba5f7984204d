import { defineStore } from 'pinia';
import { useRouter } from 'vue-router';

export const useLayoutStore = defineStore('layout', {
  state: () => ({
    isSignupVisible: false
  }),
  
  actions: {
    async scrollToSignup() {
      const router = useRouter();
      const currentRoute = router.currentRoute.value;
      
      if (currentRoute.path !== '/') {
        // If not on home page, navigate to home first
        await router.push('/');
      }

      // Increase wait time to ensure DOM is fully updated and components are mounted
      await new Promise(resolve => setTimeout(resolve, 500));

      const signupSection = document.getElementById('signup-section');
      if (signupSection) {
        signupSection.scrollIntoView({ 
          behavior: 'smooth',
          block: 'start'  // Ensures the element is aligned at the top
        });
        this.isSignupVisible = true;
      }
    }
  }
}); 