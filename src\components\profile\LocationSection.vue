<template>
  <q-card v-if="hasLocationInfo" class="q-mb-md section-card">
    <q-card-section class="section-header bg-primary text-white">
      <div class="text-h6">
        <unified-icon name="location_on" class="q-mr-sm" />
        Location
      </div>
    </q-card-section>
    <q-card-section>
      <div class="row q-col-gutter-md">
        <!-- Address -->
        <div class="col-12" v-if="profile.address">
          <div class="text-subtitle2">Address</div>
          <div>{{ profile.address }}</div>
        </div>

        <!-- City -->
        <div class="col-12 col-md-6" v-if="profile.city">
          <div class="text-subtitle2">City</div>
          <div>{{ profile.city }}</div>
        </div>

        <!-- State/Province -->
        <div class="col-12 col-md-6" v-if="profile.state_province">
          <div class="text-subtitle2">State/Province</div>
          <div>{{ profile.state_province }}</div>
        </div>

        <!-- Country -->
        <div class="col-12 col-md-6" v-if="profile.country">
          <div class="text-subtitle2">Country</div>
          <div>{{ profile.country }}</div>
        </div>

        <!-- Postal Code -->
        <div class="col-12 col-md-6" v-if="profile.postal_code">
          <div class="text-subtitle2">Postal Code</div>
          <div>{{ profile.postal_code }}</div>
        </div>

        <!-- Location (general field) -->
        <div class="col-12" v-if="profile.location && !profile.city">
          <div class="text-subtitle2">Location</div>
          <div>{{ profile.location }}</div>
        </div>
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import UnifiedIcon from '../ui/UnifiedIcon.vue'

// Props
const props = defineProps<{
  profile: any
}>()

// Computed
const hasLocationInfo = computed(() => {
  return !!(
    props.profile.address ||
    props.profile.city ||
    props.profile.state_province ||
    props.profile.country ||
    props.profile.postal_code ||
    (props.profile.location && !props.profile.city) // Only show general location if no specific city
  )
})
</script>

<style scoped>
.section-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  border-radius: 8px 8px 0 0;
}
</style>
