# Content Database Schema and TypeScript Types

## Overview

This document details the database schema and TypeScript types for the content management system in the ZB Innovation Hub platform. It provides a comprehensive reference for developers implementing the system.

## Database Schema

### 1. Posts Table

```sql
CREATE TABLE IF NOT EXISTS public.posts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  post_type VARCHAR(50) NOT NULL,
  title TEXT,
  content TEXT NOT NULL,
  excerpt TEXT,
  image_url TEXT,
  category VARCHAR(50),
  sub_category VARCHAR(50),
  tags TEXT[],
  visibility VARCHAR(20) DEFAULT 'public',
  is_featured BOOLEAN DEFAULT false,
  is_automated BOOLEAN DEFAULT false,
  likes_count INTEGER DEFAULT 0,
  comments_count INTEGER DEFAULT 0,
  slug TEXT,

  -- Event fields
  event_date TIMESTAMP WITH TIME ZONE,
  event_location TEXT,
  event_type VARCHAR(50),
  event_theme VARCHAR(50),

  -- Opportunity fields
  opportunity_deadline TIMESTAMP WITH TIME ZONE,
  opportunity_type VARCHAR(50),
  application_url TEXT,

  -- Resource fields
  resource_type VARCHAR(50),
  file_type VARCHAR(20),
  file_size INTEGER,
  download_url TEXT,

  -- Success Story fields
  achievement_type VARCHAR(50),
  achievement_details TEXT,

  -- Question/Help fields
  topic_area VARCHAR(50),
  is_resolved BOOLEAN DEFAULT false,

  -- Job/Talent fields
  company TEXT,
  location TEXT,
  job_type VARCHAR(50),
  compensation TEXT,

  -- Innovation Challenge fields
  prize TEXT,
  deadline TIMESTAMP WITH TIME ZONE,
  submission_url TEXT,

  -- Admin Announcement fields
  is_pinned BOOLEAN DEFAULT false,

  -- Automated Post fields
  activity_type VARCHAR(50),
  related_entity_id UUID,

  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster queries
CREATE INDEX idx_posts_post_type ON public.posts(post_type);
CREATE INDEX idx_posts_category ON public.posts(category);
CREATE INDEX idx_posts_created_at ON public.posts(created_at);
CREATE INDEX idx_posts_user_id ON public.posts(user_id);
```

### 2. Post Comments Table

```sql
CREATE TABLE IF NOT EXISTS public.post_comments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  post_id UUID REFERENCES public.posts(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  likes_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster queries
CREATE INDEX idx_post_comments_post_id ON public.post_comments(post_id);
CREATE INDEX idx_post_comments_user_id ON public.post_comments(user_id);
```

### 3. Post Likes Table

```sql
CREATE TABLE IF NOT EXISTS public.post_likes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  post_id UUID REFERENCES public.posts(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(post_id, user_id)
);

-- Create index for faster queries
CREATE INDEX idx_post_likes_post_id ON public.post_likes(post_id);
CREATE INDEX idx_post_likes_user_id ON public.post_likes(user_id);
```

### 4. Comment Likes Table

```sql
CREATE TABLE IF NOT EXISTS public.comment_likes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  comment_id UUID REFERENCES public.post_comments(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(comment_id, user_id)
);

-- Create index for faster queries
CREATE INDEX idx_comment_likes_comment_id ON public.comment_likes(comment_id);
CREATE INDEX idx_comment_likes_user_id ON public.comment_likes(user_id);
```

## TypeScript Types

### Post Types

```typescript
// src/types/post.ts

export type PostType =
  | 'GENERAL'                  // General post
  | 'FUNDING_OPPORTUNITY'      // Funding opportunity
  | 'COLLABORATION_OPPORTUNITY' // Collaboration opportunity
  | 'MENTORSHIP_OPPORTUNITY'   // Mentorship opportunity
  | 'BLOG_ARTICLE'             // Blog article
  | 'EVENT'                    // Event
  | 'RESOURCE'                 // Resource post
  | 'SUCCESS_STORY'            // Success story post
  | 'QUESTION_HELP'            // Question/help post
  | 'JOB_TALENT'               // Job/talent post
  | 'INNOVATION_CHALLENGE'     // Innovation challenge post
  | 'AUTOMATED'                // Automated system post
  | 'ADMIN_ANNOUNCEMENT';      // Admin announcement

export type PostCategory =
  | 'GENERAL'
  | 'FUNDING'
  | 'COLLABORATION'
  | 'MENTORSHIP'
  | 'INNOVATION'
  | 'RESEARCH'
  | 'TRAINING'
  | 'ANNOUNCEMENT'
  | 'NEWS'
  | 'RESOURCE'
  | 'SUCCESS_STORY'
  | 'QUESTION'
  | 'JOB'
  | 'TALENT'
  | 'CHALLENGE'
  | 'FINTECH'
  | 'AGRITECH'
  | 'HEALTHTECH'
  | 'EDTECH'
  | 'CLEANTECH';

export type EventTheme =
  | 'FUNDING'
  | 'TRAINING'
  | 'INVESTMENT'
  | 'PITCH_COMPETITION'
  | 'NETWORKING'
  | 'WORKSHOP'
  | 'CONFERENCE'
  | 'HACKATHON'
  | 'DEMO_DAY'
  | 'PRODUCT_LAUNCH';

export type PostVisibility = 'public' | 'private' | 'connections';

export interface Post {
  id: string;
  userId: string;
  postType: PostType;
  title?: string;
  content: string;
  excerpt?: string;
  imageUrl?: string;
  category?: PostCategory;
  subCategory?: string;
  tags?: string[];
  visibility: PostVisibility;
  isFeatured: boolean;
  isAutomated: boolean;
  likesCount: number;
  commentsCount: number;
  slug?: string;
  createdAt: string;
  updatedAt: string;

  // UI-specific properties (not stored in DB)
  author?: {
    name: string;
    avatar: string;
    profileType?: string;
  };
  isLiked?: boolean;
}

export interface BlogArticle extends Post {
  postType: 'BLOG_ARTICLE';
  title: string;
  excerpt: string;
  category: PostCategory;
  readTime?: string;
}

export interface EventPost extends Post {
  postType: 'EVENT';
  title: string;
  eventDate: string;
  eventLocation: string;
  eventType: 'PHYSICAL' | 'VIRTUAL';
  eventTheme?: EventTheme;
  applicationUrl?: string;
}

export interface OpportunityPost extends Post {
  postType: 'FUNDING_OPPORTUNITY' | 'COLLABORATION_OPPORTUNITY' | 'MENTORSHIP_OPPORTUNITY';
  title: string;
  opportunityDeadline?: string;
  opportunityType: string;
  applicationUrl?: string;
}

export interface AutomatedPost extends Post {
  postType: 'AUTOMATED';
  activityType: 'PROFILE_CREATED' | 'PROFILE_UPDATED' | 'GROUP_CREATED' | 'MARKETPLACE_LISTING';
  relatedEntityId?: string;
}

export interface AdminPost extends Post {
  postType: 'ADMIN_ANNOUNCEMENT';
  title: string;
  isPinned?: boolean;
}

export interface ResourcePost extends Post {
  postType: 'RESOURCE';
  title: string;
  resourceType: string;
  fileType?: string;
  fileSize?: number;
  downloadUrl?: string;
}

export interface SuccessStoryPost extends Post {
  postType: 'SUCCESS_STORY';
  title: string;
  achievementType: string;
  achievementDetails?: string;
}

export interface QuestionHelpPost extends Post {
  postType: 'QUESTION_HELP';
  title: string;
  topicArea: string;
  isResolved?: boolean;
}

export interface JobTalentPost extends Post {
  postType: 'JOB_TALENT';
  title: string;
  company?: string;
  location: string;
  jobType: 'FULL_TIME' | 'PART_TIME' | 'CONTRACT' | 'INTERNSHIP' | 'FREELANCE';
  compensation?: string;
  applicationUrl?: string;
}

export interface InnovationChallengePost extends Post {
  postType: 'INNOVATION_CHALLENGE';
  title: string;
  prize?: string;
  deadline: string;
  submissionUrl?: string;
}

export interface Comment {
  id: string;
  postId: string;
  userId: string;
  content: string;
  likesCount: number;
  createdAt: string;
  updatedAt: string;

  // UI-specific properties
  author?: {
    name: string;
    avatar: string;
  };
  isLiked?: boolean;
}
```

## Database to TypeScript Mapping

When fetching data from the database, it needs to be transformed to match the TypeScript interfaces:

```typescript
// Example transformation function
function transformPostFromDatabase(dbPost: any): Post {
  return {
    id: dbPost.id,
    userId: dbPost.user_id,
    postType: dbPost.post_type,
    title: dbPost.title,
    content: dbPost.content,
    excerpt: dbPost.excerpt,
    imageUrl: dbPost.image_url,
    category: dbPost.category,
    subCategory: dbPost.sub_category,
    tags: dbPost.tags,
    visibility: dbPost.visibility,
    isFeatured: dbPost.is_featured,
    isAutomated: dbPost.is_automated,
    likesCount: dbPost.likes_count,
    commentsCount: dbPost.comments_count,
    slug: dbPost.slug,
    createdAt: dbPost.created_at,
    updatedAt: dbPost.updated_at,

    // Additional fields based on post type
    ...(dbPost.post_type === 'EVENT' && {
      eventDate: dbPost.event_date,
      eventLocation: dbPost.event_location,
      eventType: dbPost.event_type,
      eventTheme: dbPost.event_theme,
    }),

    ...(dbPost.post_type.includes('OPPORTUNITY') && {
      opportunityDeadline: dbPost.opportunity_deadline,
      opportunityType: dbPost.opportunity_type,
      applicationUrl: dbPost.application_url,
    }),

    ...(dbPost.post_type === 'RESOURCE' && {
      resourceType: dbPost.resource_type,
      fileType: dbPost.file_type,
      fileSize: dbPost.file_size,
      downloadUrl: dbPost.download_url,
    }),

    ...(dbPost.post_type === 'SUCCESS_STORY' && {
      achievementType: dbPost.achievement_type,
      achievementDetails: dbPost.achievement_details,
    }),

    ...(dbPost.post_type === 'QUESTION_HELP' && {
      topicArea: dbPost.topic_area,
      isResolved: dbPost.is_resolved,
    }),

    ...(dbPost.post_type === 'JOB_TALENT' && {
      company: dbPost.company,
      location: dbPost.location,
      jobType: dbPost.job_type,
      compensation: dbPost.compensation,
      applicationUrl: dbPost.application_url,
    }),

    ...(dbPost.post_type === 'INNOVATION_CHALLENGE' && {
      prize: dbPost.prize,
      deadline: dbPost.deadline,
      submissionUrl: dbPost.submission_url,
    }),

    // Author information if joined with profiles
    ...(dbPost.profiles && {
      author: {
        name: `${dbPost.profiles.first_name} ${dbPost.profiles.last_name}`,
        avatar: dbPost.profiles.avatar,
        profileType: dbPost.profiles.profile_type
      }
    })
  };
}
```

## Row Level Security Policies

```sql
-- Enable Row Level Security
ALTER TABLE public.posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.post_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.post_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comment_likes ENABLE ROW LEVEL SECURITY;

-- Posts policies
CREATE POLICY "Anyone can view public posts"
  ON public.posts
  FOR SELECT
  USING (visibility = 'public');

CREATE POLICY "Users can view their own posts"
  ON public.posts
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create posts"
  ON public.posts
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own posts"
  ON public.posts
  FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own posts"
  ON public.posts
  FOR DELETE
  USING (auth.uid() = user_id);

-- Similar policies for comments and likes
```

## Migration Strategy

1. Create the base tables
2. Add indexes for performance
3. Set up Row Level Security policies
4. Create initial seed data for testing

## Performance Considerations

- Use pagination for feed queries
- Consider materialized views for complex aggregations
- Implement caching for frequently accessed data
- Use efficient indexing strategies
