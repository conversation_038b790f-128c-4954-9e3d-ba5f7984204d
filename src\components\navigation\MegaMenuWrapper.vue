<template>
  <q-menu
    v-model="isMenuOpen"
    anchor="bottom middle"
    self="top middle"
    class="compact-mega-menu-wrapper"
    :offset="[0, 5]"
    transition-show="jump-down"
    transition-hide="jump-up"
    auto-close
  >
    <slot></slot>
  </q-menu>
</template>

<script setup lang="ts">
import { useMegaMenuStore } from '../../stores/megaMenu';
import { computed, watch, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';

const props = defineProps<{
  menuId: string;
}>();

const megaMenuStore = useMegaMenuStore();
const router = useRouter();

const isOpen = computed(() => {
  return megaMenuStore.isMenuOpen(props.menuId);
});

const isMenuOpen = computed({
  get: () => isOpen.value,
  set: (value) => {
    if (!value) {
      megaMenuStore.closeMenu();
    }
  }
});

// Close menu when clicking outside
watch(isMenuOpen, (newValue) => {
  if (!newValue) {
    megaMenuStore.closeMenu();
  }
});

// Close menu on route change
const routeChangeHandler = () => {
  megaMenuStore.closeMenu();
};

onMounted(() => {
  router.afterEach(routeChangeHandler);
});

onUnmounted(() => {
  // Clean up the event listener
  router.afterEach(() => {});
});
</script>

<style scoped>
.compact-mega-menu-wrapper {
  max-width: 600px;
  min-width: 500px;
  border-radius: 8px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

@media (max-width: 600px) {
  .compact-mega-menu-wrapper {
    min-width: 95vw;
    max-width: 95vw;
  }
}
</style>
