import { defineStore } from 'pinia';
import { ref, computed, onMounted } from 'vue';

export const useMegaMenuStore = defineStore('megaMenu', () => {
  // State
  const activeMegaMenu = ref<string | null>(null);
  const isAnyMenuOpen = ref(false);

  // Getters
  const isMenuOpen = computed(() => (menuId: string) => {
    return activeMegaMenu.value === menuId && isAnyMenuOpen.value;
  });

  // Actions
  function openMenu(menuId: string) {
    activeMegaMenu.value = menuId;
    isAnyMenuOpen.value = true;

    // Add click outside listener
    setTimeout(() => {
      document.addEventListener('click', handleClickOutside);
    }, 10);
  }

  function closeMenu() {
    isAnyMenuOpen.value = false;

    // Remove click outside listener
    document.removeEventListener('click', handleClickOutside);
  }

  function toggleMenu(menuId: string) {
    if (activeMegaMenu.value === menuId && isAnyMenuOpen.value) {
      closeMenu();
    } else {
      // Close any open menu first
      if (isAnyMenuOpen.value) {
        closeMenu();
      }
      // Then open the new one
      openMenu(menuId);
    }
  }

  // Handle click outside
  function handleClickOutside(event: MouseEvent) {
    const target = event.target as HTMLElement;

    // Check if click is outside mega menu
    if (!target.closest('.mega-menu-dropdown') && !target.closest('.nav-btn')) {
      closeMenu();
    }
  }

  // Note: In this Vue version, we'll rely on the browser's automatic cleanup
  // when the page unloads. For a more robust solution, components using this
  // store should handle cleanup in their own onUnmounted hooks.

  return {
    // State
    activeMegaMenu,
    isAnyMenuOpen,

    // Getters
    isMenuOpen,

    // Actions
    openMenu,
    closeMenu,
    toggleMenu
  };
});
