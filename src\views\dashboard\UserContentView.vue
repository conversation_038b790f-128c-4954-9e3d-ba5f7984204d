<template>
  <q-page class="q-pa-md">
    <div class="row q-col-gutter-md">
      <div class="col-12">
        <q-card class="content-header-card bg-primary text-white">
          <q-card-section>
            <div class="text-h4">My Content</div>
            <p class="text-body1 q-mt-md">
              Manage all your content in one place. View, edit, or delete your posts, groups, marketplace listings, and comments.
            </p>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-12">
        <user-content-management :full-page="true" :default-tab="activeTab" />
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useQuasar } from 'quasar';
import { useAuthStore } from '../../stores/auth';
import UserContentManagement from '../../components/dashboard/UserContentManagement.vue';

const router = useRouter();
const route = useRoute();
const $q = useQuasar();
const authStore = useAuthStore();

// Get the active tab from the URL query parameter
const activeTab = computed(() => {
  return route.query.tab?.toString() || 'posts';
});

// Watch for changes in the route query to update the active tab
const updateTabFromRoute = () => {
  // Update the URL when the tab changes
  const tab = route.query.tab?.toString() || 'posts';
  if (tab !== activeTab.value) {
    router.replace({
      path: route.path,
      query: { ...route.query, tab }
    });
  }
};

// Check if user is authenticated and handle tab parameter
onMounted(() => {
  if (!authStore.isAuthenticated) {
    router.push('/login');
    $q.notify({
      color: 'negative',
      message: 'You must be logged in to view this page',
      icon: 'error'
    });
    return;
  }

  // Make sure the URL has the tab parameter
  updateTabFromRoute();
});
</script>

<style scoped>
.content-header-card {
  border-radius: 8px;
  overflow: hidden;
}
</style>
