// Simple email sending Edge Function using fetch directly
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Credentials': 'true',
  'Access-Control-Max-Age': '86400'
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get API key from environment
    const SENDGRID_API_KEY = Deno.env.get('SENDGRID_API_KEY')
    if (!SENDGRID_API_KEY) {
      throw new Error('SendGrid API key is not configured')
    }

    // Parse request body
    const { to, subject, message } = await req.json()

    // Validate required fields
    if (!to || !subject || !message) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields: to, subject, message' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Log the request
    console.log('Sending email to:', to)

    // Create plain text version
    const plainText = message.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim()

    // Create HTML version (if message is not already HTML)
    const htmlContent = message.startsWith('<') ? message : `<p>${message}</p>`

    // Prepare SendGrid request
    const sendgridPayload = {
      personalizations: [
        {
          to: [{ email: to }],
          subject: subject
        }
      ],
      from: {
        email: '<EMAIL>',
        name: 'ZB Innovation Hub'
      },
      content: [
        {
          type: 'text/plain',
          value: plainText
        },
        {
          type: 'text/html',
          value: htmlContent
        }
      ]
    }

    // Send the email using fetch
    const sendgridResponse = await fetch('https://api.sendgrid.com/v3/mail/send', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SENDGRID_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(sendgridPayload)
    })

    // Check for errors
    if (!sendgridResponse.ok) {
      let errorText = `SendGrid API error: ${sendgridResponse.status} ${sendgridResponse.statusText}`

      try {
        const errorJson = await sendgridResponse.json()
        errorText += ` - ${JSON.stringify(errorJson)}`
      } catch (e) {
        // Ignore JSON parsing errors
      }

      throw new Error(errorText)
    }

    // Return success response
    return new Response(
      JSON.stringify({
        success: true,
        message: `Email sent successfully to ${to}`,
        details: {
          to: to,
          subject: subject,
          timestamp: new Date().toISOString()
        }
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  } catch (error) {
    // Log the error
    console.error('Error sending email:', error)

    // Return error response
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'Unknown error occurred',
        timestamp: new Date().toISOString()
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})
