# Feed Component Structure

## Overview

This document outlines the component structure for the feed and content management system in the ZB Innovation Hub platform. It provides a hierarchical view of components, their relationships, and implementation details.

## Component Hierarchy

```
FeedContainer
├── PostFilters
├── CreatePostForm
│   ├── PostTypeSelector
│   ├── GeneralPostForm
│   ├── OpportunityPostForm
│   ├── BlogPostForm
│   └── EventPostForm
├── TabNavigation
└── TabPanels
    ├── FeedTab
    │   └── FeedPost (multiple)
    │       ├── GeneralPost
    │       ├── OpportunityPost
    │       ├── BlogPostCard
    │       ├── EventCard
    │       ├── SystemPost
    │       └── AnnouncementPost
    ├── ProfilesTab
    │   └── ProfileCard (multiple)
    ├── BlogTab
    │   └── BlogPostCard (multiple)
    ├── EventsTab
    │   └── EventCard (multiple)
    ├── GroupsTab
    │   └── GroupCard (multiple)
    └── MarketplaceTab
        └── MarketplaceCard (multiple)
```

## Component Descriptions

### Main Container Components

#### `FeedContainer.vue`
- **Purpose**: Main container for the feed interface
- **Features**:
  - Manages tab state
  - Handles data fetching for all tabs
  - Coordinates filtering across tabs
  - Responsive layout management

#### `PostFilters.vue`
- **Purpose**: Provides filtering options for the feed
- **Features**:
  - Category filters
  - Date range filters
  - Profile type filters
  - Search functionality

#### `CreatePostForm.vue`
- **Purpose**: Form for creating new posts
- **Features**:
  - Post type selection
  - Dynamic form based on selected post type
  - Image upload
  - Preview functionality
  - Form validation

### Post Type Components

#### `FeedPost.vue`
- **Purpose**: Generic post component that renders the appropriate post type
- **Features**:
  - Determines which specific post component to render
  - Handles common post interactions (like, comment, share)
  - Manages comments section visibility

#### `GeneralPost.vue`
- **Purpose**: Renders a standard post
- **Features**:
  - Basic post layout
  - Image display
  - Interaction buttons

#### `OpportunityPost.vue`
- **Purpose**: Renders opportunity posts
- **Features**:
  - Opportunity-specific styling and badges
  - Deadline display
  - Apply button when applicable

#### `BlogPostCard.vue`
- **Purpose**: Renders blog article posts
- **Features**:
  - Featured image
  - Title and excerpt
  - Category badge
  - Read time indicator
  - Read more button

#### `EventCard.vue`
- **Purpose**: Renders event posts
- **Features**:
  - Event details (date, location, theme)
  - Physical/Virtual badge
  - Register button
  - Calendar integration

#### `SystemPost.vue`
- **Purpose**: Renders automated system posts
- **Features**:
  - System notification styling
  - Activity-specific icons
  - Links to relevant entities

#### `AnnouncementPost.vue`
- **Purpose**: Renders admin announcements
- **Features**:
  - Official branding
  - Announcement badge
  - Pinning functionality

### Tab-Specific Components

#### `ProfileCard.vue`
- **Purpose**: Displays user profile information in the Profiles Directory
- **Features**:
  - Profile photo
  - Basic user information
  - Profile type badge
  - Profile completion indicator
  - View profile button

#### `GroupCard.vue`
- **Purpose**: Displays group information in the Groups tab
- **Features**:
  - Group image
  - Member count
  - Description
  - Join button

#### `MarketplaceCard.vue`
- **Purpose**: Displays marketplace listings
- **Features**:
  - Product/service image
  - Price information
  - Category badge
  - Contact seller button

## State Management

The feed system will use Pinia stores for state management:

### `postsStore`
- Manages all post data
- Handles post filtering and sorting
- Manages post interactions (likes, comments)

### `blogArticlesStore`
- Manages blog article-specific functionality
- Handles article categorization
- Manages article reading state

### `eventsStore`
- Manages event-specific functionality
- Handles event registration
- Provides calendar integration

### `profilesStore`
- Manages profile directory data
- Handles profile filtering
- Tracks profile completion

## Data Flow

1. `FeedContainer` initializes and loads initial data from stores
2. User interactions (tab changes, filtering) trigger data updates
3. Post interactions (like, comment) update the post state in the store
4. Creating new posts adds them to the store and updates the UI

## Responsive Design Strategy

- Desktop: Multi-column layout with sidebar filters
- Tablet: Two-column layout with collapsible filters
- Mobile: Single-column layout with filter button/drawer

## Implementation Plan

1. **Phase 1: Core Components**
   - Implement `FeedContainer` and tab structure
   - Create basic post components
   - Set up Pinia stores

2. **Phase 2: Post Type Components**
   - Implement specific post type components
   - Add interaction functionality
   - Create post creation forms

3. **Phase 3: Filtering and Search**
   - Implement filter components
   - Add search functionality
   - Create filter persistence

4. **Phase 4: Optimization and Polish**
   - Add lazy loading for feed items
   - Implement virtual scrolling for performance
   - Add animations and transitions
   - Polish UI details

## Testing Strategy

- Unit tests for individual components
- Integration tests for component interactions
- End-to-end tests for user flows
- Performance testing for feed rendering and scrolling
