<template>
  <q-card class="match-card q-mb-md" :class="{ 'new-match': !match.isViewed }">
    <q-card-section class="row items-center">
      <div class="col-auto">
        <q-avatar size="60px">
          <q-img
            v-if="match.user && match.user.avatarUrl"
            :src="match.user.avatarUrl"
            :alt="`${match.user.firstName} ${match.user.lastName}`"
          />
          <q-icon v-else name="person" size="40px" color="grey-7" />
        </q-avatar>
      </div>

      <div class="col q-ml-md">
        <div class="text-h6">
          {{ match.user ? `${match.user.firstName} ${match.user.lastName}` : 'Unknown User' }}
        </div>
        <div class="text-subtitle2">{{ formatProfileType(match.entityType) }}</div>
      </div>

      <div class="col-auto">
        <div class="row items-center">
          <q-circular-progress
            :value="match.matchScore * 100"
            size="50px"
            :thickness="0.2"
            color="primary"
            center-color="white"
            track-color="grey-3"
            class="q-mr-sm"
          >
            <div class="text-caption">{{ (match.matchScore * 100).toFixed(0) }}%</div>
          </q-circular-progress>

          <q-btn flat round color="primary" icon="info" size="sm">
            <q-tooltip max-width="300px">
              <div class="text-subtitle2 q-mb-sm">Match Criteria</div>
              <div v-for="(score, reason) in match.matchReasons" :key="reason" class="q-mb-xs">
                <div class="row items-center">
                  <div class="col-8">{{ formatReason(reason) }}:</div>
                  <div class="col-4 text-right">{{ (score * 100).toFixed(0) }}%</div>
                </div>
                <q-linear-progress :value="score" color="primary" class="q-mt-xs" />
              </div>
            </q-tooltip>
          </q-btn>

          <q-badge color="primary" v-if="!match.isViewed" class="q-ml-sm">New</q-badge>
        </div>
      </div>
    </q-card-section>

    <q-separator />

    <q-card-section>
      <div v-if="match.profile">
        <div v-if="match.profile.bio" class="text-body2 q-mb-sm">
          {{ truncateBio(match.profile.bio) }}
        </div>

        <div class="row q-col-gutter-sm q-mb-md">
          <div class="col-12 text-subtitle2 q-mb-xs">Why you matched:</div>
          <div class="col-12">
            <q-chip
              v-for="(score, reason) in sortedMatchReasons"
              :key="reason"
              :color="getReasonColor(score)"
              text-color="white"
              dense
            >
              {{ formatReason(reason) }}
            </q-chip>
          </div>
        </div>

        <div class="row q-gutter-sm">
          <!-- Display profile-specific information based on entity type -->
          <template v-if="match.entityType === 'innovator'">
            <q-chip v-if="match.profile.innovation_stage" dense>
              {{ match.profile.innovation_stage }}
            </q-chip>
            <q-chip v-if="match.profile.innovation_area" dense>
              {{ match.profile.innovation_area }}
            </q-chip>
          </template>

          <template v-else-if="match.entityType === 'investor'">
            <q-chip v-if="match.profile.investment_focus" dense>
              {{ Array.isArray(match.profile.investment_focus)
                ? match.profile.investment_focus[0]
                : match.profile.investment_focus }}
            </q-chip>
            <q-chip v-if="match.profile.investment_stage" dense>
              {{ Array.isArray(match.profile.investment_stage)
                ? match.profile.investment_stage[0]
                : match.profile.investment_stage }}
            </q-chip>
          </template>

          <template v-else-if="match.entityType === 'mentor'">
            <q-chip v-if="match.profile.expertise_areas" dense>
              {{ Array.isArray(match.profile.expertise_areas)
                ? match.profile.expertise_areas[0]
                : match.profile.expertise_areas }}
            </q-chip>
            <q-chip v-if="match.profile.years_of_experience" dense>
              {{ match.profile.years_of_experience }} years
            </q-chip>
          </template>
        </div>
      </div>
      <div v-else class="text-body2 text-italic">
        Profile information not available
      </div>
    </q-card-section>

    <q-separator />

    <q-card-actions align="right">
      <q-btn
        flat
        color="grey"
        icon="close"
        label="Dismiss"
        @click="dismissMatch"
        :disable="loading"
      />
      <q-btn
        flat
        :color="match.isSaved ? 'positive' : 'primary'"
        :icon="match.isSaved ? 'bookmark' : 'bookmark_border'"
        :label="match.isSaved ? 'Saved' : 'Save'"
        @click="saveMatch"
        :disable="loading"
      />
      <q-btn
        flat
        color="primary"
        icon="person_add"
        label="Connect"
        @click="connectWithMatch"
        :disable="loading"
      />
    </q-card-actions>
  </q-card>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useMatchmakingStore } from '@/stores/matchmaking';
import { useNotificationStore } from '@/stores/notifications';
import { EnrichedMatch } from '@/services/matchmakingService';

// Props
const props = defineProps<{
  match: EnrichedMatch;
}>();

// Stores
const matchmakingStore = useMatchmakingStore();
const notificationStore = useNotificationStore();

// State
const loading = ref(false);

// Computed
const sortedMatchReasons = computed(() => {
  if (!props.match.matchReasons) return {};

  // Sort reasons by score (highest first)
  const entries = Object.entries(props.match.matchReasons);
  entries.sort((a, b) => b[1] - a[1]);

  // Convert back to object
  const result: Record<string, number> = {};
  entries.forEach(([key, value]) => {
    result[key] = value;
  });

  return result;
});

// Methods
function formatProfileType(type: string): string {
  // Convert snake_case to Title Case
  return type
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

function formatReason(reason: string): string {
  // Convert snake_case to Title Case with spaces
  return reason
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

function getReasonColor(score: number): string {
  // Return color based on score
  if (score >= 0.8) return 'positive';
  if (score >= 0.6) return 'primary';
  if (score >= 0.4) return 'secondary';
  if (score >= 0.2) return 'orange';
  return 'grey';
}

function truncateBio(bio: string, maxLength = 150): string {
  if (bio.length <= maxLength) {
    return bio;
  }
  return bio.substring(0, maxLength) + '...';
}

async function saveMatch() {
  loading.value = true;
  try {
    const success = await matchmakingStore.updateMatch(props.match.id, !props.match.isSaved);

    if (success) {
      notificationStore.success(
        props.match.isSaved
          ? 'Match removed from saved matches'
          : 'Match saved successfully'
      );
    } else {
      notificationStore.error('Failed to update match');
    }
  } catch (error: any) {
    console.error('Error saving match:', error);
    notificationStore.error(`Error saving match: ${error.message}`);
  } finally {
    loading.value = false;
  }
}

async function dismissMatch() {
  loading.value = true;
  try {
    const success = await matchmakingStore.updateMatch(props.match.id, false);

    if (success) {
      notificationStore.success('Match dismissed');
    } else {
      notificationStore.error('Failed to dismiss match');
    }
  } catch (error: any) {
    console.error('Error dismissing match:', error);
    notificationStore.error(`Error dismissing match: ${error.message}`);
  } finally {
    loading.value = false;
  }
}

function connectWithMatch() {
  // This would be implemented in a future feature to send a connection request
  notificationStore.info('Connection feature coming soon!');
}
</script>

<style scoped>
.match-card {
  transition: all 0.3s ease;
}

.match-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.new-match {
  border-left: 4px solid var(--q-primary);
}
</style>
