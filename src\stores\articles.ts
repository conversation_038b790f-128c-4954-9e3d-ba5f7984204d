import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { usePostsStore } from './posts'
import { useNotificationStore } from './notifications'

export interface Article {
  id: number;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  coverImage?: string;
  publishedAt: string;
  author: {
    name: string;
    avatar?: string;
  };
  category?: string;
  tags?: string[];
}

export const useArticleStore = defineStore('articles', () => {
  // Use the posts store
  const postsStore = usePostsStore()
  const notifications = useNotificationStore()

  // Local state for error handling
  const error = ref<string | null>(null)

  // Map posts to articles
  const articles = computed(() => {
    return postsStore.articles.map(post => ({
      id: post.id,
      title: post.blogTitle || post.title || '',
      slug: post.slug || '',
      content: post.blogFullContent || post.content || '',
      excerpt: post.excerpt || '',
      coverImage: post.featuredImage || '',
      publishedAt: post.createdAt,
      author: {
        name: post.author || 'Anonymous',
        avatar: post.authorAvatar || ''
      },
      category: post.blogCategory || post.category || '',
      tags: post.tags || []
    }))
  })

  // Pass through loading state
  const loading = computed(() => postsStore.loading)

  async function fetchArticles() {
    try {
      error.value = null
      await postsStore.fetchArticles()
    } catch (err: any) {
      console.error('Error fetching articles:', err)
      error.value = err.message || 'Failed to fetch articles'
      notifications.error('Failed to load articles: ' + error.value)
    }
  }

  async function getArticleBySlug(slug: string): Promise<Article | null> {
    try {
      // First check if the article is in our local state
      const localArticle = articles.value.find(a => a.slug === slug)
      if (localArticle) return localArticle

      // If not found locally, fetch from the posts store
      const post = await postsStore.getPostBySlug(slug, 'BLOG')

      if (post && post.postType === 'BLOG') {
        return {
          id: post.id,
          title: post.blogTitle || post.title || '',
          slug: post.slug || '',
          content: post.blogFullContent || post.content || '',
          excerpt: post.excerpt || '',
          coverImage: post.featuredImage || '',
          publishedAt: post.createdAt,
          author: {
            name: post.author || 'Anonymous',
            avatar: post.authorAvatar || ''
          },
          category: post.blogCategory || post.category || '',
          tags: post.tags || []
        }
      }

      return null
    } catch (err) {
      console.error('Error fetching article:', err)
      error.value = 'Failed to fetch article'
      return null
    }
  }

  return {
    articles,
    loading,
    error,
    fetchArticles,
    getArticleBySlug
  }
})
