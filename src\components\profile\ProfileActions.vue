<template>
  <div class="profile-actions q-mt-md">
    <!-- Dashboard Context Actions -->
    <div v-if="context === 'dashboard'" class="text-center">
      <!-- Edit Button (for current user) -->
      <div v-if="isCurrentUser" class="q-mb-md">
        <q-btn
          color="primary"
          label="Edit Profile"
          icon="edit"
          :to="{ name: 'profile-edit', params: { id: profileId } }"
          class="q-mr-sm"
        />
        <q-btn
          flat
          color="primary"
          label="Back to Dashboard"
          icon="arrow_back"
          to="/dashboard"
        />
      </div>
      
      <!-- View Profile Button (for other users) -->
      <div v-else>
        <q-btn
          flat
          color="primary"
          label="Back to Dashboard"
          icon="arrow_back"
          to="/dashboard"
        />
      </div>
    </div>

    <!-- Public Context Actions -->
    <div v-else-if="context === 'public'" class="text-center">
      <q-btn
        flat
        color="primary"
        label="Back to Community"
        icon="arrow_back"
        :to="{ name: 'virtual-community' }"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
// Props
const props = defineProps<{
  context: 'dashboard' | 'public'
  isCurrentUser?: boolean
  profileId: string
}>()
</script>

<style scoped>
.profile-actions {
  padding: 16px 0;
}
</style>
