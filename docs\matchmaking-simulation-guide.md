# Matchmaking Simulation Environment Guide

## Overview

The matchmaking simulation environment provides a safe, isolated space for testing and refining matchmaking algorithms without affecting production data. This guide explains how to use the simulation environment, extend it with new profile matchers, and leverage it for development.

## Accessing the Simulator

The matchmaking simulator is available at `/dashboard/matchmaking-simulator` in the application. You can also access it from the main matchmaking page by clicking the "Open Simulator" button.

## Using the Simulator

### Basic Usage

1. **Select Source Profile Type**: Choose the type of profile that will be the source of the matching (e.g., "Innovator").
2. **Select Source Profile**: Choose a specific profile from the available mock profiles.
3. **Select Target Profile Type**: Choose the type of profile to match with (e.g., "Investor").
4. **Generate Matches**: Click the "Generate Matches" button to run the matching algorithm.
5. **View Results**: The matches will be displayed below, sorted by match score.
6. **Examine Match Reasons**: Hover over the information icon next to each match score to see a breakdown of the individual factors that contributed to the match score.

### Understanding Match Scores

Match scores are calculated based on multiple factors, each with its own weight. The system now supports comprehensive matching between all profile types. Here are some examples:

- For Innovator-Investor matching:
  - Industry alignment (30%)
  - Stage compatibility (30%)
  - Funding match (20%)
  - Location match (10%)
  - Goals alignment (10%)

- For Innovator-Mentor matching:
  - Expertise match (35%)
  - Industry match (20%)
  - Stage match (15%)
  - Goals match (15%)
  - Approach match (15%)

- For Academic Student-Institution matching:
  - Field match (30%)
  - Research match (25%)
  - Program match (20%)
  - Location match (15%)
  - Goals match (10%)

- For Professional-Innovator matching:
  - Skills match (35%)
  - Industry match (25%)
  - Service match (20%)
  - Goals match (10%)
  - Location match (10%)

The overall match score is a weighted average of these individual scores, ranging from 0 to 1 (displayed as 0-100%).

## Technical Implementation

### Architecture

The simulation environment consists of several key components:

1. **Field Access Utilities** (`src/lib/matchmaking/fieldAccessUtils.ts`): Provides safe methods for accessing profile fields, handling missing data, and type conversion.

2. **Simulator Module** (`src/lib/matchmaking/simulator.ts`): Contains mock profiles, simulated matching rules, and functions to simulate database operations.

3. **Enhanced Matchmaking Service** (`src/lib/matchmaking/matchmakingService.ts`): The core service with simulation mode support.

4. **Matchmaking Store Integration** (`src/stores/matchmaking.ts`): Store with simulation mode support.

5. **Simulator UI Component** (`src/components/matchmaking/MatchmakingSimulator.vue`): The user interface for the simulation environment.

### Simulation Mode

The matchmaking service can operate in two modes:

- **Production Mode**: Uses real database operations and real user profiles.
- **Simulation Mode**: Uses mock data and simulated operations.

To toggle simulation mode programmatically:

```typescript
// In a component
import { useMatchmakingStore } from '@/stores/matchmaking';

const matchmakingStore = useMatchmakingStore();
matchmakingStore.setSimulationMode(true); // Enable simulation mode
```

Or directly with the service:

```typescript
import { MatchmakingService } from '@/lib/matchmaking/matchmakingService';
import { supabase } from '@/lib/supabase';

const matchmakingService = new MatchmakingService(supabase);
matchmakingService.setSimulationMode(true); // Enable simulation mode
```

## Extending the Simulation Environment

### Adding New Mock Profiles

To add new mock profiles, edit the `mockProfiles` object in `src/lib/matchmaking/simulator.ts`:

```typescript
export const mockProfiles: Record<ProfileType, any[]> = {
  innovator: [
    // Existing profiles...
    {
      user_id: 'innovator-3',
      innovation_area: 'AI & Machine Learning',
      innovation_stage: 'Growth',
      industry: ['Technology', 'Artificial Intelligence'],
      funding_amount: 500000,
      current_challenges: ['Scaling', 'Market Expansion'],
      short_term_goals: ['Series A Funding', 'International Expansion'],
      preferred_locations: ['San Francisco', 'London'],
      looking_for: 'Strategic investors with global networks'
    }
  ],
  // Other profile types...
};
```

### Adding New Matching Rules

To add new matching rules, edit the `mockRules` object in `src/lib/matchmaking/simulator.ts`:

```typescript
export const mockRules: Record<string, MatchmakingRule[]> = {
  // Existing rules...
  'innovator-professional': [
    {
      id: 'rule-1',
      sourceProfileType: 'innovator',
      targetProfileType: 'professional',
      ruleName: 'skills_match',
      sourceFields: { path: 'current_challenges' },
      targetFields: { path: 'skills' },
      weight: 0.4
    },
    // Add more rules...
  ]
};
```

### Implementing New Profile Matchers

To implement a new profile matcher, add a new function to `src/lib/matchmaking/profileMatchers.ts`:

```typescript
/**
 * Match an innovator to a professional
 * @param innovator Innovator profile
 * @param professional Professional profile
 * @returns Match score and reasons
 */
export function matchInnovatorToProfessional(innovator: any, professional: any): { score: number, reasons: Record<string, number> } {
  const scores: Record<string, number> = {
    skills: arrayIntersectionScore(
      innovator.current_challenges || [],
      professional.skills || []
    ),
    industry: arrayIntersectionScore(
      innovator.industry || [],
      professional.industry || []
    ),
    service: approachCompatibilityScore(
      innovator.looking_for,
      professional.services_offered
    )
  };

  const weights = {
    skills: 0.4,
    industry: 0.3,
    service: 0.3
  };

  let totalScore = 0;
  let totalWeight = 0;

  for (const [key, weight] of Object.entries(weights)) {
    totalScore += scores[key] * weight;
    totalWeight += weight;
  }

  return {
    score: totalWeight > 0 ? totalScore / totalWeight : 0,
    reasons: scores
  };
}
```

### Matchable Profile Types

The system now supports comprehensive matching between all profile types. The current matchable profile types are defined in the `getSimulatedMatchableProfileTypes` function in `src/lib/matchmaking/simulator.ts`:

```typescript
export function getSimulatedMatchableProfileTypes(profileType: ProfileType): ProfileType[] {
  // Define which profile types can be matched with each profile type
  const matchMap: Record<ProfileType, ProfileType[]> = {
    innovator: ['investor', 'mentor', 'professional', 'industry_expert', 'organisation'],
    investor: ['innovator', 'organisation'],
    mentor: ['innovator', 'academic_student'],
    professional: ['innovator', 'organisation'],
    industry_expert: ['innovator', 'organisation'],
    academic_student: ['mentor', 'academic_institution'],
    academic_institution: ['academic_student', 'organisation'],
    organisation: ['innovator', 'investor', 'professional', 'industry_expert', 'academic_institution']
  };

  return matchMap[profileType] || [];
}
```

This configuration defines which profile types can be matched with each other. For example, innovators can be matched with investors, mentors, professionals, industry experts, and organisations.

## Best Practices

1. **Test Incrementally**: When implementing new matchers, test them individually before integrating them into the larger system.

2. **Use Field Access Utilities**: Always use the field access utilities to safely access profile fields, especially when dealing with potentially missing data.

3. **Maintain Consistent Weights**: Keep the weights for similar matching criteria consistent across different profile matchers.

4. **Document Match Reasons**: Ensure that match reasons are clearly documented and meaningful to users.

5. **Validate with Real Data**: After testing in the simulation environment, validate the algorithms with real data in a controlled environment before deploying to production.

## Troubleshooting

### Common Issues

1. **No Matches Generated**:
   - Check that the source and target profile types are compatible
   - Verify that matching rules exist for the selected profile types
   - Ensure the mock profiles have the necessary fields for matching

2. **Low Match Scores**:
   - Examine the individual match reasons to identify which factors are contributing to the low score
   - Check if the mock profiles have sufficient data in the relevant fields
   - Review the matching algorithm to ensure it's correctly evaluating the profiles

3. **Simulation Mode Not Working**:
   - Verify that simulation mode is properly enabled
   - Check the browser console for any errors
   - Ensure the mock data is properly loaded

For additional help or to report issues with the simulation environment, please contact the development team.
