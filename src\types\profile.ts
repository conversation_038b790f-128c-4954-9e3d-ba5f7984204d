/**
 * Profile Types
 * 
 * This file contains type definitions for the profile system.
 */

export interface ProfileQuestion {
  id: string;
  type: string;
  label: string;
  required?: boolean;
  options?: string[];
  condition?: {
    field: string;
    value: any;
  };
  hint?: string;
  fullWidth?: boolean;
}

export interface ProfileSection {
  title: string;
  icon?: string;
  description?: string;
  questions: ProfileQuestion[];
}

export interface ProfileSchema {
  sections: ProfileSection[];
  profileType: string;
}

// Re-export types from profileService for convenience
export type { BaseProfile, PersonalDetails, SpecializedProfile } from '../services/profileService';
