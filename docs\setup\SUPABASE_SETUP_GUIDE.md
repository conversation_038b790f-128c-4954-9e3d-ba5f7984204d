# Supabase Setup Guide

This guide will help you set up your new Supabase project and integrate it with your application.

## 1. Environment Variables

Create a `.env` file in your project root with the following variables:

```
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

Replace `your_supabase_url` and `your_supabase_anon_key` with the values from your Supabase dashboard (Settings > API).

## 2. Database Setup

Run the SQL scripts in the `supabase/migrations` folder in your Supabase SQL editor in the following order:

1. `01_create_profiles_table.sql`
2. `02_create_auth_triggers.sql`

These scripts will:
- Create the profiles table
- Set up Row Level Security (RLS) policies
- Create a trigger to automatically create profiles when users sign up

## 3. Authentication Setup

In your Supabase dashboard:

1. Go to Authentication > Settings
2. Configure Email Auth:
   - Enable Email confirmations: Optional (set to false for easier testing)
   - Enable Email Signups: Enabled
   - Customize email templates if needed

3. Disable Phone Auth for now
4. Disable External OAuth providers (Google, Facebook, etc.) for now

## 4. Testing

After completing the setup:

1. Try to sign up with email and password
2. Check if a profile is automatically created in the profiles table
3. Verify that you can view and update your profile

## Troubleshooting

If you encounter issues:

1. Check the browser console for errors
2. Verify that your environment variables are correctly set
3. Ensure the SQL scripts ran successfully
4. Check the Supabase logs for any errors
