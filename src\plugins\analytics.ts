import { App } from 'vue'
import { Router } from 'vue-router'

// Google Analytics 4 measurement ID
const GA_MEASUREMENT_ID = import.meta.env.VITE_GA_MEASUREMENT_ID || ''

// Interface for analytics events
interface AnalyticsEvent {
  action: string
  category?: string
  label?: string
  value?: number
  [key: string]: any
}

// Load Google Analytics script
function loadGoogleAnalytics() {
  if (!GA_MEASUREMENT_ID) {
    console.warn('Google Analytics Measurement ID not provided')
    return
  }

  // Create script element
  const script = document.createElement('script')
  script.async = true
  script.src = `https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`
  document.head.appendChild(script)

  // Initialize gtag
  window.dataLayer = window.dataLayer || []
  function gtag(...args: any[]) {
    window.dataLayer.push(arguments)
  }
  gtag('js', new Date())
  gtag('config', GA_MEASUREMENT_ID, { send_page_view: false })

  // Make gtag globally available
  window.gtag = gtag
}

// Track page views
function trackPageView(router: Router) {
  router.afterEach((to) => {
    if (!window.gtag || !GA_MEASUREMENT_ID) return

    window.gtag('config', GA_MEASUREMENT_ID, {
      page_path: to.fullPath,
      page_title: document.title
    })
  })
}

// Track events
function trackEvent(event: AnalyticsEvent) {
  if (!window.gtag) return

  window.gtag('event', event.action, {
    event_category: event.category || 'general',
    event_label: event.label,
    value: event.value,
    ...Object.fromEntries(
      Object.entries(event).filter(([key]) => 
        !['action', 'category', 'label', 'value'].includes(key)
      )
    )
  })
}

// Analytics plugin
export default {
  install: (app: App, options: { router?: Router } = {}) => {
    // Add global properties
    app.config.globalProperties.$analytics = {
      trackEvent
    }

    // Load Google Analytics
    loadGoogleAnalytics()

    // Set up page tracking if router is provided
    if (options.router) {
      trackPageView(options.router)
    }
  }
}

// Type declarations for global properties
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $analytics: {
      trackEvent: (event: AnalyticsEvent) => void
    }
  }
}

// Type declarations for window object
declare global {
  interface Window {
    dataLayer: any[]
    gtag: (...args: any[]) => void
  }
}
