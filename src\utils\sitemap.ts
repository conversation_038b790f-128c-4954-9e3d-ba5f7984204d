import { Router } from 'vue-router';

// Note: This file is for server-side use only
// fs and path imports removed for browser compatibility

const SITE_URL = import.meta.env.VITE_SITE_URL || 'https://ZbInnovation.co.zw';
// SITEMAP_PATH removed for browser compatibility

interface SitemapURL {
  loc: string;
  lastmod?: string;
  changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority?: number;
}

export function generateSitemapRoutes(router: Router): SitemapURL[] {
  const urls: SitemapURL[] = [];

  // Get all routes from the router
  const routes = router.getRoutes();

  for (const route of routes) {
    // Skip dynamic routes and excluded paths
    if (
      route.path.includes(':') ||
      route.path.includes('*') ||
      /^\/admin|^\/login|^\/private|^\/auth|^\/reset-password|^\/verify-email|^\/db-test/.test(route.path)
    ) {
      continue;
    }

    // Determine priority based on path depth
    let priority = 0.8;
    if (route.path === '/') {
      priority = 1.0;
    } else if (route.path.split('/').length <= 2) {
      priority = 0.9;
    } else if (route.path.split('/').length >= 4) {
      priority = 0.6;
    }

    // Determine change frequency based on content type
    let changefreq: SitemapURL['changefreq'] = 'weekly';
    if (route.path.includes('news') || route.path.includes('events')) {
      changefreq = 'daily';
    } else if (route.path === '/') {
      changefreq = 'daily';
    } else if (route.path.includes('articles')) {
      changefreq = 'monthly';
    }

    urls.push({
      loc: `${SITE_URL}${route.path}`,
      changefreq,
      priority,
      lastmod: new Date().toISOString()
    });
  }

  return urls;
}

export function generateSitemapXML(urls: SitemapURL[]): string {
  const urlsXML = urls
    .map(url => `
    <url>
      <loc>${url.loc}</loc>
      ${url.lastmod ? `<lastmod>${url.lastmod}</lastmod>` : ''}
      ${url.changefreq ? `<changefreq>${url.changefreq}</changefreq>` : ''}
      ${url.priority ? `<priority>${url.priority}</priority>` : ''}
    </url>`)
    .join('');

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9
        http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">
  ${urlsXML}
</urlset>`;
}

export async function writeSitemapToFile(content: string): Promise<void> {
  try {
    await fs.writeFile(SITEMAP_PATH, content, 'utf-8');
    console.log(`Sitemap written to ${SITEMAP_PATH}`);
  } catch (error) {
    throw new Error(`Failed to write sitemap: ${error}`);
  }
}

// Function to ping search engines about the updated sitemap
export async function pingSearchEngines(): Promise<void> {
  const engines = [
    `https://www.google.com/ping?sitemap=${encodeURIComponent(`${SITE_URL}/sitemap.xml`)}`,
    `https://www.bing.com/ping?sitemap=${encodeURIComponent(`${SITE_URL}/sitemap.xml`)}`
  ];

  try {
    console.log('Pinging search engines...');

    for (const engine of engines) {
      try {
        const response = await fetch(engine);
        if (response.ok) {
          console.log(`Successfully pinged: ${engine}`);
        } else {
          console.warn(`Failed to ping: ${engine}, status: ${response.status}`);
        }
      } catch (error) {
        console.error(`Error pinging ${engine}:`, error);
      }
    }
  } catch (error) {
    console.error('Error pinging search engines:', error);
  }
}
