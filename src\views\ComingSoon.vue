<template>
  <div class="coming-soon-page">
    <div class="container">
      <div class="content">
        <!-- Logo Section -->
        <div class="logo-section">
          <div class="logo-container">
            <img src="/ZbInnovation-logo.png" alt="ZbInnovation Logo" class="logo-image" />
            <h2 class="logo-text">ZbInnovation</h2>
          </div>
        </div>

        <!-- Main Title -->
        <div class="title-section">
          <h1 class="main-title">Coming Soon</h1>
          <div class="title-line"></div>
        </div>

        <!-- Message -->
        <div class="message-section">
          <h2 class="subtitle">We're Building Something Amazing</h2>
          <p class="description">
            ZbInnovation is currently undergoing an exciting transformation. 
            We're updating our platform with fresh branding, enhanced features, 
            and an improved user experience to better serve Zimbabwe's innovation ecosystem.
          </p>
        </div>

        <!-- Progress -->
        <div class="progress-section">
          <div class="progress-info">
            <span class="progress-label">Rebranding Progress</span>
            <span class="progress-percent">75%</span>
          </div>
          <div class="progress-bar">
            <div class="progress-fill"></div>
          </div>
        </div>

        <!-- Features -->
        <div class="features-section">
          <h3 class="features-title">What's Coming</h3>
          <div class="features-grid">
            <div class="feature-card">
              <div class="feature-icon">🎨</div>
              <span class="feature-text">Fresh Brand Identity</span>
            </div>
            <div class="feature-card">
              <div class="feature-icon">⚡</div>
              <span class="feature-text">Enhanced Performance</span>
            </div>
            <div class="feature-card">
              <div class="feature-icon">👥</div>
              <span class="feature-text">Better User Experience</span>
            </div>
            <div class="feature-card">
              <div class="feature-icon">📈</div>
              <span class="feature-text">New Features</span>
            </div>
          </div>
        </div>

        <!-- Contact -->
        <div class="contact-section">
          <p class="contact-text">Need immediate assistance?</p>
          <a href="mailto:<EMAIL>" class="contact-email">
            <EMAIL>
          </a>
        </div>

        <!-- Timeline -->
        <div class="timeline-section">
          <div class="timeline-card">
            <div class="timeline-icon">⏰</div>
            <div class="timeline-content">
              <span class="timeline-label">Expected Launch</span>
              <span class="timeline-date">Coming Soon</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading Animation -->
    <div class="loading-dots">
      <div class="dot"></div>
      <div class="dot"></div>
      <div class="dot"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Simple setup without complex logic
</script>

<style scoped>
.coming-soon-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fffe 0%, #e8f5f0 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.container {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.content {
  text-align: center;
  animation: fadeInUp 1s ease-out;
}

/* Logo Styles */
.logo-section {
  margin-bottom: 2rem;
}

.logo-container {
  display: inline-block;
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  border: 2px solid #0D8A3E;
  box-shadow: 0 4px 12px rgba(13, 138, 62, 0.2);
}

.logo-image {
  max-width: 50px;
  max-height: 50px;
  width: auto;
  height: auto;
  margin-bottom: 0.5rem;
  object-fit: contain;
}

.logo-text {
  font-size: 2.5rem;
  font-weight: 600;
  color: #0D8A3E;
  margin: 0;
  letter-spacing: 1px;
}

/* Title Styles */
.title-section {
  margin-bottom: 2rem;
}

.main-title {
  font-size: 3.5rem;
  font-weight: 300;
  color: #0D8A3E;
  margin: 0 0 1rem 0;
  letter-spacing: 2px;
}

.title-line {
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, #0D8A3E, #4CAF50);
  margin: 0 auto;
  border-radius: 2px;
}

/* Message Styles */
.message-section {
  margin-bottom: 3rem;
}

.subtitle {
  font-size: 1.8rem;
  font-weight: 400;
  color: #2c3e50;
  margin: 0 0 1rem 0;
}

.description {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #5a6c7d;
  margin: 0;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

/* Progress Styles */
.progress-section {
  margin-bottom: 3rem;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.progress-label {
  font-weight: 500;
  color: #2c3e50;
}

.progress-percent {
  font-weight: 600;
  color: #0D8A3E;
  font-size: 1.1rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(13, 138, 62, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  width: 75%;
  height: 100%;
  background: linear-gradient(90deg, #0D8A3E, #4CAF50);
  border-radius: 4px;
  animation: progressFill 2s ease-out;
}

/* Features Styles */
.features-section {
  margin-bottom: 3rem;
}

.features-title {
  font-size: 1.5rem;
  font-weight: 500;
  color: #2c3e50;
  margin: 0 0 1.5rem 0;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  max-width: 500px;
  margin: 0 auto;
}

.feature-card {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  border: 1px solid rgba(13, 138, 62, 0.1);
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
}

.feature-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.feature-text {
  font-weight: 500;
  color: #2c3e50;
  font-size: 0.9rem;
}

/* Contact Styles */
.contact-section {
  margin-bottom: 3rem;
}

.contact-text {
  color: #5a6c7d;
  margin: 0 0 0.5rem 0;
}

.contact-email {
  color: #0D8A3E;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: color 0.3s ease;
}

.contact-email:hover {
  color: #4CAF50;
  text-decoration: underline;
}

/* Timeline Styles */
.timeline-section {
  margin-bottom: 2rem;
}

.timeline-card {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  border: 1px solid rgba(13, 138, 62, 0.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-width: 300px;
  margin: 0 auto;
}

.timeline-icon {
  font-size: 1.5rem;
}

.timeline-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.timeline-label {
  font-size: 0.9rem;
  color: #5a6c7d;
  font-weight: 500;
}

.timeline-date {
  font-size: 1.1rem;
  color: #0D8A3E;
  font-weight: 600;
}

/* Loading Dots */
.loading-dots {
  position: fixed;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 0.5rem;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #0D8A3E;
  animation: pulse 1.5s ease-in-out infinite;
}

.dot:nth-child(1) { animation-delay: 0s; }
.dot:nth-child(2) { animation-delay: 0.3s; }
.dot:nth-child(3) { animation-delay: 0.6s; }

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes progressFill {
  from { width: 0%; }
  to { width: 75%; }
}

@keyframes pulse {
  0%, 100% { 
    opacity: 0.3; 
    transform: scale(1); 
  }
  50% { 
    opacity: 1; 
    transform: scale(1.2); 
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-title {
    font-size: 2.5rem;
  }
  
  .subtitle {
    font-size: 1.4rem;
  }
  
  .description {
    font-size: 1rem;
  }
  
  .logo-image {
  max-width: 50px;
  max-height: 50px;
  width: auto;
  height: auto;
  margin-bottom: 0.5rem;
  object-fit: contain;
}

.logo-text {
    font-size: 2rem;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .timeline-card {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .coming-soon-page {
    padding: 1rem 0.5rem;
  }
  
  .main-title {
    font-size: 2rem;
    letter-spacing: 1px;
  }
  
  .subtitle {
    font-size: 1.2rem;
  }
  
  .logo-image {
  max-width: 50px;
  max-height: 50px;
  width: auto;
  height: auto;
  margin-bottom: 0.5rem;
  object-fit: contain;
}

.logo-text {
    font-size: 1.5rem;
  }
  
  .logo-container {
    padding: 0.75rem 1.5rem;
  }
}
</style>
