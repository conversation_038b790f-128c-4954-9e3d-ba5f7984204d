import { ref, readonly, computed } from 'vue'

// Loading state by operation type
const loadingStates = ref<Record<string, boolean>>({})

// Loading messages by operation type
const loadingMessages = ref<Record<string, string>>({})

/**
 * Set loading state for an operation
 * @param operation The operation name
 * @param isLoading Whether the operation is loading
 * @param message Optional loading message
 */
function setLoading(operation: string, isLoading: boolean, message?: string): void {
  loadingStates.value = {
    ...loadingStates.value,
    [operation]: isLoading
  }

  if (isLoading && message) {
    loadingMessages.value = {
      ...loadingMessages.value,
      [operation]: message
    }
  } else if (!isLoading) {
    // Remove message when operation is complete
    const { [operation]: _, ...rest } = loadingMessages.value
    loadingMessages.value = rest
  }
}

/**
 * Check if an operation is loading
 */
function isLoading(operation: string): boolean {
  return !!loadingStates.value[operation]
}

/**
 * Check if any operation is loading
 */
function isAnyLoading(): boolean {
  return Object.values(loadingStates.value).some(state => state)
}

/**
 * Get all loading states
 */
function getAllLoadingStates(): Record<string, boolean> {
  return loadingStates.value
}

/**
 * Reset all loading states
 */
function resetLoadingStates(): void {
  loadingStates.value = {}
}

/**
 * Get loading message for an operation
 */
function getLoadingMessage(operation: string): string | undefined {
  return loadingMessages.value[operation]
}

/**
 * Get current loading message (from any active operation)
 */
function getCurrentLoadingMessage(): string | undefined {
  const activeOperations = Object.keys(loadingStates.value).filter(op => loadingStates.value[op])
  if (activeOperations.length === 0) return undefined

  // Return the message of the most recent operation
  for (let i = activeOperations.length - 1; i >= 0; i--) {
    const message = loadingMessages.value[activeOperations[i]]
    if (message) return message
  }

  return undefined
}

/**
 * Execute an operation with loading state
 * @param operation The operation name
 * @param callback The async function to execute
 * @param message Optional loading message
 * @returns The result of the callback
 */
async function withLoading<T>(
  operation: string,
  callback: () => Promise<T>,
  message?: string
): Promise<T> {
  try {
    setLoading(operation, true, message)
    return await callback()
  } finally {
    setLoading(operation, false)
  }
}

/**
 * Execute an operation with loading state and retry on failure
 * @param operation The operation name
 * @param callback The async function to execute
 * @param options Options for retry
 * @returns The result of the callback
 */
async function withLoadingAndRetry<T>(
  operation: string,
  callback: () => Promise<T>,
  options: { message?: string; maxRetries?: number; initialDelay?: number } = {}
): Promise<T> {
  const { message, maxRetries = 3, initialDelay = 300 } = options
  let retries = 0
  let lastError: any = null

  setLoading(operation, true, message)

  try {
    while (retries <= maxRetries) {
      try {
        return await callback()
      } catch (error) {
        lastError = error
        retries++

        if (retries > maxRetries) {
          break
        }

        // Calculate delay with exponential backoff
        const delay = initialDelay * Math.pow(2, retries - 1)

        // Add some randomness to prevent all retries happening at the same time
        const jitter = Math.random() * 100

        // Update loading message to indicate retry
        const retryMessage = message
          ? `${message} (Retry ${retries}/${maxRetries})`
          : `Retrying operation (${retries}/${maxRetries})`

        setLoading(operation, true, retryMessage)

        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, delay + jitter))
      }
    }

    throw lastError
  } finally {
    setLoading(operation, false)
  }
}

export function useLoadingState() {
  // Computed property for current loading message
  const currentLoadingMessage = computed(() => getCurrentLoadingMessage())

  return {
    loadingStates: readonly(loadingStates),
    loadingMessages: readonly(loadingMessages),
    currentLoadingMessage,
    setLoading,
    isLoading,
    isAnyLoading,
    getAllLoadingStates,
    resetLoadingStates,
    getLoadingMessage,
    getCurrentLoadingMessage,
    withLoading,
    withLoadingAndRetry
  }
}
