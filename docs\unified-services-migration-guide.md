# Unified Services Migration Guide

## Overview

This guide provides step-by-step instructions for migrating existing components to use the new unified caching and messaging services.

## Migration Benefits

- **Consistent Caching**: Unified TTL management and invalidation patterns
- **Reduced Memory Usage**: Centralized cache with LRU eviction
- **Better Performance**: Reduced duplicate database calls and optimized real-time subscriptions
- **Improved Reliability**: Standardized error handling and retry logic
- **Enhanced Monitoring**: Cache statistics and real-time connection health

## Phase 1: Migrate ProfileManager to Unified Caching

### Current Implementation
```typescript
// src/services/ProfileManager.ts
private cache = new Map<string, { data: BaseProfile; timestamp: number }>()
private CACHE_TTL = 5 * 60 * 1000 // 5 minutes

async getProfile(userId: string, options: ProfileLoadOptions = {}): Promise<BaseProfile | null> {
  const cacheKey = `${userId}-${context}`
  
  if (!forceRefresh && this.cache.has(cacheKey)) {
    const cached = this.cache.get(cacheKey)!
    if (Date.now() - cached.timestamp < this.CACHE_TTL) {
      return cached.data
    }
  }
  // ... rest of implementation
}
```

### Migrated Implementation
```typescript
// src/services/ProfileManager.ts
import { useUnifiedCache } from './unifiedCacheService'

export class ProfileManager {
  private cache = useUnifiedCache()
  
  async getProfile(userId: string, options: ProfileLoadOptions = {}): Promise<BaseProfile | null> {
    const { context = 'private', forceRefresh = false } = options
    const cacheKey = `profile:${userId}:${context}`
    
    // Check unified cache
    if (!forceRefresh) {
      const cached = this.cache.get<BaseProfile>(cacheKey)
      if (cached) {
        console.log(`ProfileManager: Cache hit for ${userId} (${context})`)
        return cached
      }
    }
    
    // Prevent duplicate requests
    if (this.loading.has(cacheKey)) {
      while (this.loading.has(cacheKey)) {
        await new Promise(resolve => setTimeout(resolve, 50))
      }
      return this.cache.get<BaseProfile>(cacheKey) || null
    }
    
    try {
      this.loading.add(cacheKey)
      
      // Load profile data
      const profile = await this.loadProfileData(userId, context)
      
      if (profile) {
        // Cache with appropriate config for profiles
        this.cache.set(cacheKey, profile, {
          ttl: 5 * 60 * 1000, // 5 minutes
          storage: 'memory',
          invalidationPatterns: [`profile:${userId}:*`, `user:${userId}:*`]
        })
      }
      
      return profile
    } finally {
      this.loading.delete(cacheKey)
    }
  }
  
  // Trigger cache invalidation when profile is updated
  async updateProfile(userId: string, updates: Partial<BaseProfile>): Promise<boolean> {
    const success = await this.saveProfileUpdates(userId, updates)
    
    if (success) {
      // Invalidate all cached profiles for this user
      this.cache.triggerInvalidation('profile', userId)
    }
    
    return success
  }
}
```

## Phase 2: Migrate MessagingView to Unified Services

### Current Implementation Issues
- Duplicate caching logic (store + component level)
- Manual subscription management
- Complex refresh logic with localStorage debouncing

### Migration Steps

#### Step 1: Update MessagingView Component
```vue
<!-- src/views/dashboard/MessagingView.vue -->
<script setup lang="ts">
import { useMessagingStore } from '@/stores/messaging'
import { useUnifiedCache } from '@/services/unifiedCacheService'
import { useUnifiedRealtime } from '@/services/unifiedRealtimeService'

const messagingStore = useMessagingStore()
const cache = useUnifiedCache()
const realtime = useUnifiedRealtime()

// Remove component-level caching logic
// const tabDataCache = ref<Record<string, { timestamp: number }>>({})
// const CACHE_DURATION = 30000

// Simplified loading logic
async function loadConversations(showLoading = true) {
  if (showLoading) {
    loadingConversations.value = true
  }
  
  try {
    // Use store method which now uses unified caching
    await messagingStore.loadConversations()
  } catch (error) {
    console.error('Error loading conversations:', error)
    notifications.error('Failed to load conversations')
  } finally {
    loadingConversations.value = false
  }
}

// Remove manual refresh intervals - real-time updates handle this
// Remove localStorage debouncing - unified cache handles this

// Simplified message loading
async function loadMessages(userId: string, showLoading = true) {
  if (showLoading) {
    loadingMessages.value = true
  }
  
  try {
    await messagingStore.loadMessages(userId)
  } catch (error) {
    console.error('Error loading messages:', error)
    notifications.error('Failed to load messages')
  } finally {
    loadingMessages.value = false
  }
}
</script>
```

#### Step 2: Update Messaging Store
```typescript
// src/stores/messaging.ts
import { useUnifiedCache } from '@/services/unifiedCacheService'
import { useUnifiedRealtime } from '@/services/unifiedRealtimeService'

export const useMessagingStore = defineStore('messaging', () => {
  const cache = useUnifiedCache()
  const realtime = useUnifiedRealtime()
  
  // Remove manual subscription management
  // let messageSubscription: any = null
  
  async function loadConversations(): Promise<void> {
    const cacheKey = 'messaging:conversations'
    
    // Check unified cache first
    const cached = cache.get<Conversation[]>(cacheKey)
    if (cached) {
      conversations.value = cached
      return
    }
    
    try {
      isLoading.value = true
      
      const { data, error } = await supabase
        .from('user_messages')
        .select(`
          *,
          sender:sender_id(*),
          recipient:recipient_id(*)
        `)
        .order('created_at', { ascending: false })
      
      if (error) throw error
      
      const processedConversations = processConversations(data || [])
      
      // Cache with messaging-specific config
      cache.set(cacheKey, processedConversations, {
        ttl: 60 * 1000, // 1 minute
        storage: 'memory'
      })
      
      conversations.value = processedConversations
    } finally {
      isLoading.value = false
    }
  }
  
  function initializeMessaging() {
    // Use unified real-time service
    const subscription = realtime.subscribe(
      {
        table: 'user_messages',
        event: '*',
        filter: `or(recipient_id.eq.${user.id},sender_id.eq.${user.id})`
      },
      handleRealtimeMessage,
      { deduplicate: true }
    )
    
    // Store subscription for cleanup
    messageSubscription = subscription
  }
  
  function handleRealtimeMessage(payload: any) {
    // Invalidate relevant cache entries
    cache.invalidate('messaging:*')
    
    // Update local state
    const newMessage = payload.new as Message
    // ... handle message update
  }
})
```

## Phase 3: Migrate FeedContainer to Unified Caching

### Current Implementation
```typescript
// src/components/feed/FeedContainer.vue
const tabDataCache = ref<Record<string, { timestamp: number }>>({})
const CACHE_DURATION = 30000

async function loadTabData(tab: string): Promise<void> {
  const now = Date.now()
  const cached = tabDataCache.value[tab]
  if (cached && (now - cached.timestamp) < CACHE_DURATION) {
    return
  }
  // ... load data
  tabDataCache.value[tab] = { timestamp: now }
}
```

### Migrated Implementation
```typescript
// src/components/feed/FeedContainer.vue
import { useUnifiedCache } from '@/services/unifiedCacheService'

const cache = useUnifiedCache()

async function loadTabData(tab: string): Promise<void> {
  const cacheKey = `feed:${tab}`
  
  // Check unified cache
  const cached = cache.get<any[]>(cacheKey)
  if (cached) {
    // Update component state with cached data
    updateTabData(tab, cached)
    return
  }
  
  try {
    setLoadingStateForTab(tab, true)
    
    // Load fresh data
    const data = await fetchTabData(tab)
    
    // Cache with feed-specific config
    cache.set(cacheKey, data, {
      ttl: 30 * 1000, // 30 seconds
      storage: 'memory'
    })
    
    updateTabData(tab, data)
  } finally {
    setLoadingStateForTab(tab, false)
  }
}

// Invalidate cache when user creates new content
function onContentCreated(contentType: string) {
  cache.invalidate(`feed:${contentType}`)
  cache.invalidate('feed:all')
}
```

## Phase 4: Update Route Guards

### Current Implementation
```typescript
// src/router/enhancedGuards.ts
const authCache = new Map<string, { result: boolean; timestamp: number }>()
const AUTH_CACHE_TTL = 5 * 60 * 1000

const cached = authCache.get(cacheKey)
if (!cached || (now - cached.timestamp) > AUTH_CACHE_TTL) {
  // Check user state
  authCache.set(cacheKey, { result: true, timestamp: now })
}
```

### Migrated Implementation
```typescript
// src/router/enhancedGuards.ts
import { useUnifiedCache } from '@/services/unifiedCacheService'

const cache = useUnifiedCache()

router.beforeEach(async (to, from, next) => {
  const userId = authStore.currentUser?.id
  if (userId) {
    const cacheKey = `auth:userState:${userId}`
    
    // Check unified cache
    const cached = cache.get<boolean>(cacheKey)
    if (cached !== null) {
      console.log('RouteGuards: Using cached user state')
      return next()
    }
    
    try {
      console.log('RouteGuards: Checking user state...')
      await checkUserState()
      
      // Cache auth state
      cache.set(cacheKey, true, {
        ttl: 5 * 60 * 1000, // 5 minutes
        storage: 'sessionStorage' // Use session storage for auth
      })
      
      next()
    } catch (error) {
      console.error('RouteGuards: Error checking user state:', error)
      next()
    }
  } else {
    next()
  }
})
```

## Migration Checklist

### Phase 1: Foundation
- [ ] Deploy unified caching service
- [ ] Deploy unified real-time service
- [ ] Update ProfileManager to use unified caching
- [ ] Test profile loading performance

### Phase 2: Messaging
- [ ] Update messaging store to use unified services
- [ ] Simplify MessagingView component
- [ ] Remove duplicate caching logic
- [ ] Test real-time message delivery

### Phase 3: Feed System
- [ ] Update FeedContainer to use unified caching
- [ ] Remove component-level caching
- [ ] Test feed loading performance
- [ ] Verify cache invalidation on content creation

### Phase 4: Authentication
- [ ] Update route guards to use unified caching
- [ ] Test authentication flow
- [ ] Verify session-based caching

### Phase 5: Testing & Optimization
- [ ] Performance testing with unified services
- [ ] Monitor cache hit rates
- [ ] Optimize cache configurations
- [ ] Document performance improvements

## Performance Monitoring

### Cache Statistics
```typescript
// Monitor cache performance
const cacheStats = cache.getStats()
console.log('Cache Performance:', {
  hitRate: `${(cacheStats.hitRate * 100).toFixed(1)}%`,
  totalEntries: cacheStats.totalEntries,
  memoryUsage: `${(cacheStats.memoryUsage / 1024).toFixed(1)} KB`
})
```

### Real-time Health
```typescript
// Monitor real-time connection health
const realtimeStats = realtime.getStats()
console.log('Real-time Health:', {
  connectionState: realtimeStats.connectionState,
  activeSubscriptions: realtimeStats.activeSubscriptions,
  averageLatency: `${realtimeStats.averageLatency.toFixed(1)}ms`
})
```

## Rollback Plan

If issues arise during migration:

1. **Immediate Rollback**: Revert to previous component implementations
2. **Partial Rollback**: Disable unified services for specific components
3. **Gradual Migration**: Migrate one component at a time with feature flags

## Success Metrics

- **Cache Hit Rate**: Target 90%+ for frequently accessed data
- **Memory Usage**: Reduce overall memory footprint by 30%
- **Database Calls**: Reduce redundant calls by 50%
- **Real-time Latency**: Maintain <1 second for message delivery
- **Code Complexity**: Reduce caching-related code by 60%
