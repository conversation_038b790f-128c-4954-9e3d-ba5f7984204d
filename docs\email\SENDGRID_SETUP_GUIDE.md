# SendGrid Setup Guide

This guide will help you set up SendGrid for email delivery in your ZB Innovation Hub application.

## 1. Create a SendGrid Account

If you don't already have a SendGrid account:

1. Go to [SendGrid's website](https://sendgrid.com/)
2. Sign up for a free account (allows 100 emails per day)
3. Complete the verification process

## 2. Create an API Key

1. Log in to your SendGrid account
2. Navigate to Settings > API Keys
3. Click "Create API Key"
4. Name your key (e.g., "ZB Innovation Hub Email")
5. Select "Full Access" or "Restricted Access" with at least "Mail Send" permissions
6. Click "Create & View"
7. Copy the API key (it will only be shown once)

## 3. Verify a Sender Identity

SendGrid requires you to verify the email address you'll be sending from:

1. Navigate to Settings > Sender Authentication
2. Choose either "Single Sender Verification" or "Domain Authentication"
   - For testing, Single Sender Verification is simpler
   - For production, Domain Authentication is recommended
3. Follow the steps to verify your sender identity

## 4. Update the Supabase Environment

1. Edit the `.env.sendgrid` file in the `supabase` directory
2. Replace `SG.your_actual_sendgrid_key_here` with your actual SendGrid API key
3. Make sure the `SENDGRID_FROM_EMAIL` is set to a verified sender email
4. Run the `update-sendgrid-key.bat` script to update the Supabase environment

## 5. Test Email Delivery

1. Open the `simple-test-email.html` file in your browser
2. Enter a recipient email address
3. Click "Send Test Email"
4. Check if the email is delivered successfully

## Troubleshooting

If emails are still not being delivered:

1. Check the SendGrid Activity Feed to see if the emails are being sent
2. Verify that your SendGrid account is not suspended or limited
3. Make sure the sender email is properly verified
4. Check if the API key has the correct permissions
5. Look for any error messages in the Supabase Edge Function logs

## SendGrid Documentation

For more information, refer to the official SendGrid documentation:

- [SendGrid API Documentation](https://docs.sendgrid.com/api-reference/how-to-use-the-sendgrid-v3-api)
- [SendGrid Mail Send API](https://docs.sendgrid.com/api-reference/mail-send/mail-send)
- [Sender Authentication](https://docs.sendgrid.com/ui/account-and-settings/how-to-set-up-domain-authentication)
